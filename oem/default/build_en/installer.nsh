!macro customInstall

; 创建 PowerShell 脚本来添加 Windows Defender 排除项
FileOpen $0 "$INSTDIR\add2Ignore.ps1" w

; 检查是否已经存在排除项，避免重复添加
StrCpy $R0 'try {$\n'
FileWrite $0 $R0
StrCpy $R0 '    $exePath = "$INSTDIR\${APP_EXECUTABLE_FILENAME}"$\n'
FileWrite $0 $R0
StrCpy $R0 '    $appDataPath = "$APPDATA\HuaYoung"$\n'
FileWrite $0 $R0
StrCpy $R0 '    $\n'
FileWrite $0 $R0
StrCpy $R0 '    # 获取当前的排除列表$\n'
FileWrite $0 $R0
StrCpy $R0 '    $currentExclusions = Get-MpPreference | Select-Object -ExpandProperty ExclusionProcess$\n'
FileWrite $0 $R0
StrCpy $R0 '    $currentPathExclusions = Get-MpPreference | Select-Object -ExpandProperty ExclusionPath$\n'
FileWrite $0 $R0
StrCpy $R0 '    $currentAllowedApps = Get-MpPreference | Select-Object -ExpandProperty ControlledFolderAccessAllowedApplications$\n'
FileWrite $0 $R0
StrCpy $R0 '    $\n'
FileWrite $0 $R0
StrCpy $R0 '    # 只有不存在时才添加$\n'
FileWrite $0 $R0
StrCpy $R0 '    if ($exePath -notin $currentExclusions) { Add-MpPreference -ExclusionProcess $exePath }$\n'
FileWrite $0 $R0
StrCpy $R0 '    if ($appDataPath -notin $currentPathExclusions) { Add-MpPreference -ExclusionPath $appDataPath }$\n'
FileWrite $0 $R0
StrCpy $R0 '    if ($exePath -notin $currentAllowedApps) { Add-MpPreference -ControlledFolderAccessAllowedApplications $exePath }$\n'
FileWrite $0 $R0
StrCpy $R0 '    $\n'
FileWrite $0 $R0
StrCpy $R0 '    Write-Host "Windows Defender 排除项配置完成"$\n'
FileWrite $0 $R0
StrCpy $R0 '} catch {$\n'
FileWrite $0 $R0
StrCpy $R0 '    Write-Host "配置 Windows Defender 排除项时出错: $_"$\n'
FileWrite $0 $R0
StrCpy $R0 '    exit 1$\n'
FileWrite $0 $R0
StrCpy $R0 '}$\n'
FileWrite $0 $R0

FileClose $0

; 等待文件写入完成
Sleep 500

; 执行 PowerShell 脚本（隐藏窗口）
ExpandEnvStrings $0 "%COMSPEC%"
ExecShell "" '"$0"' "/C powershell -ExecutionPolicy Bypass -File .\add2Ignore.ps1" SW_HIDE

; 清理临时文件
Sleep 2000
Delete "$INSTDIR\add2Ignore.ps1"

!macroend

!macro customUnInstall

; 删除缓存目录（如果存在）
IfFileExists "$APPDATA\HuaYoung\Cache" 0 +2
  RMDir /r "$APPDATA\HuaYoung\Cache"

; 检查 HuaYoung 目录是否为空，如果为空则删除
IfFileExists "$APPDATA\HuaYoung\*.*" 0 +2
  RMDir "$APPDATA\HuaYoung"

!macroend
