{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "5.0.0-beta.2", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=9000 --openssl-legacy-provider\" IS_PRODUCTION=true umi build", "build:client": "cross-env NODE_OPTIONS=\"--max-old-space-size=9000 --openssl-legacy-provider\"  IS_PRODUCTION=true UMI_ENV=client umi build", "build:browser": "cross-env NODE_OPTIONS=\"--max-old-space-size=9000 --openssl-legacy-provider\"  IS_PRODUCTION=true UMI_ENV=browser umi build", "build:development": "cross-env NODE_OPTIONS=\"--max-old-space-size=9000 --openssl-legacy-provider\" IS_PRODUCTION=false umi build", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp && patch-package", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "precommit": "lint-staged", "prettier": "prettier -c --write \"src/**/*\"", "start": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096 --openssl-legacy-provider\" UMI_ENV=dev ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION=site umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none REACT_APP_ENV=noMock UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev umi dev", "start:client": "cross-env UMI_ENV=client ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION=site umi dev", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc --noEmit", "downloadApiJsonFromSwagger": "node ./tools/downloadApiJsonFromSwagger.js && umi openapi && echo '使用以下命令提交代码：git add src/services/ config/api*'", "electron:start": "concurrently -n webpack,electron \"npm run electron:watch\" \"cross-env NODE_ENV=development electron --inspect .\" --kill-others", "electron:watch": "cross-env  NODE_OPTIONS=\"--max-old-space-size=4096 --openssl-legacy-provider\" NODE_ENV=development EXT_RPA_NAME=rpa-toolbox-********** EXT_AI_AGENT_NAME=ai-agent-********** webpack --config webpack.config.electron.js --watch", "electron:pack": "cross-env NPM_CONFIG_ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ PLATFORM=Mac electron-builder --mac --x64 --arm64 --config electron/electron-builder.json", "electron:build": "cross-env EXT_RPA_NAME=rpa-toolbox-********** EXT_AI_AGENT_NAME=ai-agent-********** node build-package.js", "electron:repairPackageJson": "node repair-packageJson.js", "electron:postinstall": "electron-builder install-app-deps", "ext:dev-userInfo": "cross-env NODE_ENV=development EXT_NAME=userInfo webpack --config webpack.config.extension.js --watch", "ext:dev-recorder": "cross-env NODE_ENV=development EXT_NAME=recorder webpack --config webpack.config.extension.js --watch", "ext:dev-rpa": "cross-env NODE_ENV=development EXT_NAME=rpa-toolbox-********** webpack --config webpack.config.extension.js --watch", "ext:dev": "concurrently -n userInfo,recorder \"yarn run ext:dev-userInfo\" \"yarn run ext:dev-recorder\"", "ext:build-userInfo": "cross-env NODE_OPTIONS='--openssl-legacy-provider' EXT_NAME=userInfo webpack --config webpack.config.extension.js", "ext:build-recorder": "cross-env NODE_OPTIONS='--openssl-legacy-provider' EXT_NAME=recorder webpack --config webpack.config.extension.js", "ext:build-rpa": "cross-env NODE_OPTIONS='--openssl-legacy-provider' EXT_NAME=rpa-toolbox-********** webpack --config webpack.config.extension.js", "ext:build-aiAgent": "cross-env NODE_OPTIONS='--openssl-legacy-provider' EXT_NAME=ai-agent-********** webpack --config webpack.config.extension.js", "ext:build": "yarn run ext:build-userInfo && yarn run ext:build-recorder && yarn run ext:build-rpa && yarn run ext:build-aiAgent", "electron:main": "cross-env EXT_RPA_NAME=rpa-toolbox-********** RELEASE_APP_VERSION=8.3.0 BUILD_NUMBER=9999 PORTAL_URL=https://dev.thinkoncloud.cn API_URL=https://dev.thinkoncloud.cn webpack --config webpack.config.electron.js --progress=profile"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "main": "electron/main.js", "dependencies": {"@ant-design/charts": "^1.2.14", "@ant-design/icons": "^4.5.0", "@ant-design/pro-card": "^1.14.15", "@ant-design/pro-descriptions": "^1.9.8", "@ant-design/pro-form": "^1.37.0", "@ant-design/pro-layout": "^6.24.3", "@ant-design/pro-list": "^1.12.3", "@ant-design/pro-table": "^2.50.1", "@codemirror/autocomplete": "^6.3.0", "@codemirror/commands": "^6.1.2", "@codemirror/lang-javascript": "^6.1.0", "@codemirror/tooltip": "^0.19.16", "@codemirror/view": "^6.3.1", "@dnd-kit/core": "^5.0.1", "@dnd-kit/sortable": "^6.0.0", "@fingerprintjs/fingerprintjs": "^3.3.0", "@langchain/openai": "^0.0.10", "@paddlejs-models/ocr": "^1.2.4", "@react-spring/web": "^9.5.4", "@umijs/hooks": "^1.9.3", "@umijs/openapi": "^1.1.14", "@umijs/plugin-openapi": "^1.2.0", "@umijs/route-utils": "^1.0.36", "@use-gesture/react": "^10.2.20", "acorn": "^8.8.2", "ali-oss": "^6.17.0", "android-versions": "^2.0.0", "antd": "^4.16.13", "antd-mobile": "^5.23.0", "antd5": "npm:antd@5.x", "appium-adb": "12.5.2", "axios": "^0.27.2", "blob-to-buffer": "^1.2.9", "bowser": "^2.11.0", "buffer": "^6.0.3", "bytes": "^3.1.2", "check-disk-space": "^3.4.0", "cheerio": "^1.0.0-rc.12", "classnames": "^2.2.6", "codemirror": "^6.0.1", "copy-to-clipboard": "^3.3.1", "crypto-js": "^4.1.1", "detect-file-encoding-and-language": "^2.4.0", "dompurify": "^2.4.3", "echarts": "^5.2.1", "echarts-for-react": "^3.0.1", "eml-format": "^0.6.1", "eml-parser": "^2.0.3", "escodegen": "^2.0.0", "esm": "^3.2.25", "estraverse": "^5.3.0", "event-source-polyfill": "^1.0.31", "fast-folder-size": "^2.2.0", "fast-xml-parser": "^5.0.8", "file-saver": "^2.0.5", "form-data": "^4.0.0", "h264-converter": "^0.1.4", "howler": "^2.2.3", "http-parser-js": "^0.5.6", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^5.0.1", "hy_excelize": "^4.4.9", "iconv-lite": "^0.4.24", "imapflow": "^1.0.148", "isomorphic-form-data": "^2.0.0", "jimp": "^0.22.12", "js-beautify": "^1.15.3", "js-cookie": "^3.0.1", "js-yaml": "^4.1.0", "keymirror": "^0.1.1", "langchain": "^0.1.0", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.11", "lru-cache": "^10.2.2", "marked": "^4.2.12", "mcc-mnc-list": "^1.1.11", "moment": "^2.25.3", "moment-timezone": "^0.5.33", "mrmime": "^1.0.1", "node-machine-id": "^1.1.12", "node-pop3": "^0.9.0", "nodemailer": "^6.9.1", "omit.js": "^2.0.2", "p-limit": "^4.0.0", "p-min-delay": "^4.0.1", "phone": "^3.1.41", "pretty-ms": "^7.0.1", "pureimage": "^0.4.18", "qrcode.react": "^1.0.1", "rc-overflow": "^1.4.1", "rc-segmented": "^2.2.2", "re-resizable": "^6.10.3", "react": "^17.0.0", "react-cropper": "^2.1.8", "react-dev-inspector": "^1.1.1", "react-dom": "^17.0.0", "react-draggable": "^4.4.3", "react-error-catcher": "^1.2.0", "react-fast-marquee": "^1.2.1", "react-helmet-async": "^1.0.4", "react-hotkeys-hook": "^4.3.8", "react-infinite-scroll-component": "^6.1.0", "react-inspector": "^6.0.1", "react-lottie": "^1.2.3", "react-player": "^2.9.0", "react-router-dom": "5.2.0", "react-script-hook": "^1.5.0", "socket.io-client": "^4.5.2", "styled-components": "^6.1.11", "swagger-typescript-api": "^9.1.2", "swiper": "^11.1.1", "tiny-pinyin": "^1.3.2", "turndown": "^7.2.0", "umi": "^3.5.0", "umi-request": "^1.0.8", "urijs": "^1.19.11", "usehooks-ts": "^3.0.1", "virtualizedtableforantd4": "^1.3.1", "vm2": "^3.9.10", "web-streams-polyfill": "^4.1.0", "webdriverio": "^8.38.0", "weui": "^2.5.16", "weui.js": "^1.2.17", "winattr": "^3.0.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.0.2", "@babel/plugin-proposal-class-static-block": "^7.21.0", "@babel/plugin-transform-runtime": "^7.15.0", "@dead50f7/adbkit": "^2.11.4", "@electron/notarize": "^2.1.0", "@electron/rebuild": "^3.2.13", "@koa/cors": "^4.0.0", "@koa/router": "^10.0.0", "@tsconfig/create-react-app": "^1.0.2", "@tsconfig/node14": "^1.0.1", "@types/ali-oss": "^6.16.7", "@types/android-versions": "^1.8.3", "@types/archiver": "^5.3.1", "@types/better-sqlite3": "^7.6.4", "@types/blob-to-buffer": "^1.2.2", "@types/bytes": "^3.1.1", "@types/chrome": "^0.0.154", "@types/classnames": "^2.2.7", "@types/crypto-js": "^4.0.2", "@types/dompurify": "^2.4.0", "@types/event-source-polyfill": "^1.0.1", "@types/express": "^4.17.0", "@types/file-saver": "^2.0.5", "@types/history": "^4.7.2", "@types/howler": "^2.2.7", "@types/http-proxy": "^1.17.6", "@types/jest": "^26.0.0", "@types/js-cookie": "^3.0.2", "@types/js-yaml": "^4.0.5", "@types/jsonwebtoken": "^8.5.9", "@types/jszip": "^3.4.1", "@types/keymirror": "^0.1.1", "@types/koa": "^2.13.4", "@types/koa-bodyparser": "^4.3.5", "@types/koa-static": "^4.0.2", "@types/koa__cors": "^3.3.0", "@types/koa__router": "^8.0.7", "@types/lodash": "^4.14.144", "@types/lowdb": "^1.0.10", "@types/marked": "^4.0.8", "@types/papaparse": "^5.3.2", "@types/prismjs": "^1.26.0", "@types/qrcode.react": "^1.0.1", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@types/react-infinite-scroll-component": "^5.0.0", "@types/react-lottie": "^1.2.6", "@types/ssh2": "^1.11.6", "@types/ua-parser-js": "^0.7.36", "@types/urijs": "^1.19.23", "@types/ws": "^7.4.7", "@umijs/fabric": "^2.5.2", "@umijs/plugin-blocks": "^2.0.5", "@umijs/plugin-esbuild": "^1.0.1", "@umijs/preset-ant-design-pro": "^1.2.0", "@umijs/preset-react": "^2.1.6", "@umijs/yorkie": "^2.0.3", "appium": "^2.11.3", "appium-uiautomator2-driver": "^3.7.7", "archiver": "^5.3.1", "autoprefixer": "^10.3.3", "babel-loader": "^8.2.2", "better-sqlite3": "^8.4.0", "carlo": "^0.9.46", "chokidar": "^3.5.3", "clean-webpack-plugin": "^4.0.0", "concurrently": "^6.2.0", "copy-webpack-plugin": "^9.0.1", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "css-loader": "^6.2.0", "detect-installer": "^1.0.1", "donkey-puppeteer-core": "21.7.2", "electron": "22.3.14", "electron-auto-launch": "^5.0.7", "electron-builder": "22.14.13", "electron-debug": "^3.2.0", "electron-log": "^4.3.5", "electron-updater": "^5.2.1", "enzyme": "^3.11.0", "eslint": "^7.1.0", "express": "^4.17.1", "extract-zip": "^2.0.1", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "gh-pages": "^3.0.0", "html-webpack-plugin": "^5.3.2", "jsdom-global": "^3.0.2", "jsonwebtoken": "^8.5.1", "koa": "^2.13.1", "koa-bodyparser": "^4.3.0", "koa-static": "^5.0.0", "less": "^4.2.2", "less-loader": "^12.2.0", "lint-staged": "^10.0.0", "lowdb": "^1.0.0", "mini-css-extract-plugin": "^2.2.2", "mockjs": "^1.0.1-beta3", "ms-wmic": "^1.0.4", "nodejs-file-downloader": "^4.7.4", "nugget": "^2.0.1", "papaparse": "^5.3.2", "patch-package": "^8.0.0", "postcss": "^8.3.6", "postcss-loader": "^6.1.1", "prettier": "^2.0.1", "ps-list": "7.2.0", "puppeteer-extra": "^3.3.4", "raw-loader": "^4.0.2", "resolve-url-loader": "^4.0.0", "socks": "^2.7.1", "socks-proxy-agent": "^7.0.0", "ssh2": "^1.15.0", "stylelint": "^13.0.0", "ts-loader": "^9.2.3", "ts-node": "^10.9.1", "typescript": "^4.3.5", "ua-parser-js": "^0.7.28", "url-loader": "^4.1.1", "webpack": "^5.45.1", "webpack-cli": "^4.7.2", "webpack-node-externals": "^3.0.0", "wext-manifest-loader": "^2.3.0", "wext-manifest-webpack-plugin": "^1.2.1", "wildcard-match": "^5.1.2", "ws": "^7.5.2", "yargs": "^17.7.2"}, "engines": {"node": ">=10.0.0"}}