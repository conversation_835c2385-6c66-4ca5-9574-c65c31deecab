const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const nugget = require('nugget');
const extract = require('extract-zip');
const { execSync, spawn } = require('child_process');

/**
 * 构建安装包脚本
 */

const CHROMIUM_URL = process.env.CHROMIUM_URL || 'https://dev.thinkoncloud.cn/downloads/';
const N_DRIVER_URL = 'https://dl.szdamai.com/downloads/ndriver/';
const IOS_URL = 'https://dl.szdamai.com/downloads/go-ios/';
const VPICK_URL = 'https://dl.szdamai.com/downloads/vpick/';
const BUNDLE_KERNEL_VERSION = process.env.BUNDLE_KERNEL_VERSION || '';
const APP_DOWNLOAD_URL = process.env.APP_DOWNLOAD_URL || 'https://dev.thinkoncloud.cn/downloads/';
const platform = process.env.PLATFORM || process.platform;
const ARCH = process.env.ARCH;
const BROWSER_BUILD_VERSION = process.env.BROWSER_BUILD_VERSION || 'latest';
const PORTAL_URL = process.env.PORTAL_URL || 'https://dev.thinkoncloud.cn';
const API_URL = process.env.API_URL || 'https://dev.thinkoncloud.cn';
const APP_DOWNLOAD_URL2 = process.env.APP_DOWNLOAD_URL2 || 'https://dev.thinkoncloud.cn/downloads/';
const PORTAL_URL2 = process.env.PORTAL_URL2 || 'https://app.thinkoncloud.com';
const API_URL2 = process.env.API_URL2 || 'https://api.thinkoncloud.com';
const WX_URL = process.env.WX_URL || 'https://dev.thinkoncloud.cn';
const RELEASE_APP_VERSION = process.env.RELEASE_APP_VERSION || '1.0.8';
const BUILD_NUMBER = process.env.BUILD_NUMBER || '9999';
const QUICK_MODE = process.env.QUICK_MODE || 'false';
const OEM_NAME = process.env.OEM_NAME || 'default';
const platform_simple = {
  windows: 'win',
  win32: 'win',
  Win10: 'win',
  Win7: 'win',
  macos: 'mac',
  darwin: 'mac',
  Mac: 'mac',
  linux: 'linux',
  Ubuntu: 'linux',
};
const assetsPath = path.resolve(__dirname, './extra');
const electronLibPath = path.resolve(__dirname, './electron/node_modules');
const isQuickMode = QUICK_MODE === 'true';
const appDownloadUrlObj = new URL(APP_DOWNLOAD_URL);
const appDownloadUrl2Obj = new URL(APP_DOWNLOAD_URL2);
let PROTOCOL_CODE = 'huayoung';
let PRODUCT_NAME = 'HuaYoung';
let author = `深圳市云上悦动科技有限公司 <<EMAIL>>`;
let skipNdriverDownload = false;
let skipUpdaterDownload = false;
let skipIOSDownload = false;
let skipVpickDownload = false;

if (OEM_NAME === 'gg') {
  console.log('构建GGBrowser安装包');
  PROTOCOL_CODE = 'ggbrowser';
  PRODUCT_NAME = 'GGBrowser';
  author = 'GGBrowser Inc. <<EMAIL>>';
  skipNdriverDownload = true;
  skipUpdaterDownload = true;
  skipIOSDownload = true;
  skipVpickDownload = true;
}

console.log('env', JSON.stringify({ env: process.env }));

const locale = {
  zh: {
    copyright: 'Copyright © 2025 深圳市云上悦动科技有限公司',
    readme: '安装说明.txt',
  },
  en: {
    copyright: 'Copyright © 2025 深圳市云上悦动科技有限公司',
    readme: 'README.txt',
  },
};

console.log(`${new Date().toLocaleString()} 开始执行构建安装包脚本`);

fs.copySync(
  path.resolve(__dirname, `./oem/${OEM_NAME}/build`),
  path.resolve(__dirname, `./electron/build`),
  {
    recursive: true,
    overwrite: true,
  },
);
fs.copySync(
  path.resolve(__dirname, `./oem/${OEM_NAME}/build_en`),
  path.resolve(__dirname, `./electron/build_en`),
  {
    recursive: true,
    overwrite: true,
  },
);
fs.copySync(
  path.resolve(__dirname, `./oem/${OEM_NAME}/electron-builder.json`),
  path.resolve(__dirname, `./electron/electron-builder.json`),
  { overwrite: true },
);
fs.copySync(
  path.resolve(__dirname, `./oem/${OEM_NAME}/electron-builder-en.json`),
  path.resolve(__dirname, `./electron/electron-builder-en.json`),
  { overwrite: true },
);
fs.copySync(
  path.resolve(__dirname, `./oem/${OEM_NAME}/public`),
  path.resolve(__dirname, `./public`),
  {
    recursive: true,
    overwrite: true,
  },
);

// 快速模式不编译render静态资源，使用上次构建的内容
if (!isQuickMode) {
  buildStatic();
}

// 记录上次的平台，相同平台不用重复下载Chrome.zip
let lastPlatformItem = null;
let platformList = [];
let modeList = [];
let languageList = [];

// 打包
// 允许跳过打包，仅编译 renderer 静态资源
if (process.env.SKIP_PACKAGE !== 'true') {
  // 判断需要打包的平台、语言和运行模式
  if (process.env.PLATFORM && process.env.PLATFORM.startsWith('Win')) {
    // Windows平台通过环境变量传递要打包的平台
    if (process.env.PLATFORM_Win10 === 'true') {
      platformList.push('Win10');
    }
    if (process.env.PLATFORM_Win7 === 'true') {
      platformList.push('Win7');
    }
  } else {
    // 非Windows平台写死要打包的平台
    platformList.push(platform);
  }
  if (process.env.MODE_normal === 'true') {
    modeList.push('normal');
  }
  if (process.env.MODE_runtime === 'true') {
    modeList.push('runtime');
  }
  if (process.env.LANG_zh === 'true') {
    languageList.push('zh');
  }
  if (process.env.LANG_en === 'true') {
    languageList.push('en');
  }

  if (platformList.length > 1) {
    console.error('不支持同时构建多个平台的客户端');
    process.exit(1);
  } else {
    packageAll();
  }
}

async function packageAll() {
  // 遍历所有参数进行打包
  for (let i in platformList) {
    let platformItem = platformList[i];
    for (let j in modeList) {
      let modeItem = modeList[j];
      for (let k in languageList) {
        let languageItem = languageList[k];
        console.log(
          `${new Date().toLocaleString()} 开始打包：${platformItem} - ${modeItem} - ${languageItem}`,
        );
        await packageInstaller(platformItem, modeItem, languageItem);
      }
    }
  }
}

async function buildStatic() {
  // 编译 renderer 静态资源
  console.log(`${new Date().toLocaleString()} 开始编译 renderer 静态资源`);
  // 这里只需要指定RUN_MODE为normal，编译好后可以给Runtime使用，输出内容在dist目录
  execSync(
    `cross-env PORTAL_URL=${PORTAL_URL} API_URL=${API_URL} RUN_MODE=normal OEM_NAME=${OEM_NAME} PRODUCT_NAME=${PRODUCT_NAME} npm run build:client`,
    {
      stdio: 'inherit',
    },
  );
  console.log(`${new Date().toLocaleString()} 开始npm run build:browser`);
  // 这里只需要指定RUN_MODE为normal，编译好后可以给Runtime使用，输出内容在browserPage目录
  execSync(
    `cross-env PORTAL_URL=${PORTAL_URL} API_URL=${API_URL} RUN_MODE=normal OEM_NAME=${OEM_NAME} PRODUCT_NAME=${PRODUCT_NAME} npm run build:browser`,
    {
      stdio: 'inherit',
    },
  );
  // 编译浏览器扩展程序，输出内容在extensions-dist目录
  console.log(`${new Date().toLocaleString()} 开始编译浏览器扩展程序`);
  execSync(`cross-env AUTHOR="${author}" npm run ext:build`);
}

async function packageInstaller(platform, mode, language) {
  process.env.PLATFORM = platform;
  process.env.MODE = mode;
  process.env.LANG = language;

  const electronBuilderPath = path.resolve(
    `./electron/electron-builder${language === 'en' ? '-en' : ''}.json`,
  );
  const builderJson = fs.readJSONSync(electronBuilderPath);
  let productName = builderJson.productName;
  const platformCode = platform_simple[platform] ?? 'win';
  let artifactName = `${productName}_${platform}_` + '${version}_' + language + '_setup.${ext}';
  let buildDir = path.resolve(`./electron/build`);
  let langLib;
  if (OEM_NAME === 'default') {
    artifactName = `HuaYoungApp_${platform}_` + '${version}_' + language + '_setup.${ext}';
    if (language === 'en') {
      productName = 'HuaYoung';
    }
    if (mode === 'runtime') {
      productName = '花漾运行时';
      artifactName = `HuaYoungRuntime_${platform}_` + '${version}_' + language + '_setup.${ext}';
      if (language === 'en') {
        productName = 'HuaYoungRuntime';
      }
    }
    langLib = locale[language] || locale.zh;
  }
  if (language === 'en') {
    buildDir = path.resolve(`./electron/build_en`);
  }
  if (fs.existsSync(path.join(buildDir, `background_${mode}.tiff`))) {
    fs.cpSync(
      path.join(buildDir, `background_${mode}.tiff`),
      path.join(buildDir, 'background.tiff'),
      { force: true },
    );
  } else {
    console.log('!!! can not found background tiff !!!');
    console.log(fs.readdirSync(buildDir).join('\n'));
  }
  if (ARCH && platform === 'Mac') {
    artifactName = artifactName.replace(`${platform}_`, `${platform}_` + '${arch}_');
  }

  /*
  输出目录示例
  win10_app_zh
  win7_runtime_en
  mac_arm64_app_zh
  */
  let outputDir = `${platform.toLowerCase()}_${mode.replace('normal', 'app')}_${language}`;
  if (ARCH && platform === 'Mac') {
    outputDir = `${platform.toLowerCase()}_${ARCH}_${mode.replace('normal', 'app')}_${language}`;
  }
  const appDistPath = path.resolve(__dirname, `./electron-dist/${outputDir}`);
  const updateUrl = APP_DOWNLOAD_URL + `${outputDir}/`;

  // 要下载的文件
  const chromiumTargetName = `chrome-${platformCode}_${BROWSER_BUILD_VERSION}${
    ARCH ? `-${ARCH}` : ''
  }.zip`;
  const chromiumSaveName = `chrome-${platformCode}.zip`;
  const updaterName = 'HYUpdater.exe';

  // 编译 main 源码
  console.log(`${new Date().toLocaleString()} 开始编译 main 源码`);
  try {
    const envParams = [
      `PLATFORM=${platform}`,
      `RELEASE_APP_VERSION=${RELEASE_APP_VERSION}`,
      `BUILD_NUMBER=${BUILD_NUMBER}`,
      `PORTAL_URL=${PORTAL_URL}`,
      `API_URL=${API_URL}`,
      `DL_URL=${appDownloadUrlObj.origin}`,
      `PORTAL_URL2=${PORTAL_URL2}`,
      `API_URL2=${API_URL2}`,
      `DL_URL2=${appDownloadUrl2Obj.origin}`,
      `WX_URL=${WX_URL}`,
      `RUN_MODE=${mode}`,
      `LANG=${language}`,
      `EXT_RPA_NAME=${process.env.EXT_RPA_NAME}`,
      `UPDATE_URL=${updateUrl}`,
      `BUNDLE_KERNEL_VERSION=${process.env.BUNDLE_KERNEL_VERSION}`,
      `BUNDLE_KERNEL_BUILD_NUMBER=${process.env.BUNDLE_KERNEL_BUILD_NUMBER}`,
      `OEM_NAME=${OEM_NAME}`,
      `PROTOCOL_CODE=${PROTOCOL_CODE}`,
    ];
    console.log('调试编译 main 源码时的envParams', JSON.stringify({ envParams }));
    execSync(
      `cross-env NODE_OPTIONS='--openssl-legacy-provider' ${envParams.join(
        ' ',
      )} webpack --config webpack.config.electron.js --progress=profile`,
      { stdio: 'inherit' },
    );
  } catch (error) {
    console.error('编译 main 源码异常：', error);
    process.exit(1);
  }

  // Chrome zip包的保存路径
  const chromeZipPath = `${assetsPath}/${chromiumSaveName}`;

  return new Promise((resolve) => {
    // 创建 assets 目录
    if (fs.existsSync(assetsPath)) {
      if (isQuickMode) {
        resolve();
        return;
      }
      resolve();
    } else {
      resolve(fs.mkdirSync(assetsPath, { recursive: true }));
    }
  })
    .then(() => {
      // 检查chrome.zip
      let assetsExists = true;
      if (!fs.existsSync(chromeZipPath)) {
        assetsExists = false;
      }

      if (!isQuickMode) {
        fs.emptyDirSync(electronLibPath);
      }

      // 快速模式不重复下载附件资源
      if (isQuickMode && assetsExists) {
        return;
      }
      // 相同平台不用重复下载附件资源
      if (platform === lastPlatformItem && assetsExists) {
        return;
      }
      // 记录上次的平台，相同平台不用重复下载附件资源
      lastPlatformItem = platform;

      // 清空electron/assets目录
      // 本目录存储的是chrome.zip，相同平台再次构建不用清空
      fs.emptyDirSync(assetsPath);

      let artifactBaseUrl = 'https://dev.thinkoncloud.cn/downloads/';

      // 下载 chromium
      let chromeBaseUrl = CHROMIUM_URL;
      // Win7的下载地址不同，固定为109版本且不可更改
      // 测试环境是https://dev.thinkoncloud.cn/downloads/Windows7-chrome/chrome_kernel-109/chrome-win_latest.zip
      // 生产环境是https://dl.szdamai.com/downloads/Windows7-chrome/chrome_kernel-109/chrome-win_latest.zip
      if (platform === 'Win7') {
        chromeBaseUrl = `${CHROMIUM_URL}Windows7-chrome/`;
      }
      if (BUNDLE_KERNEL_VERSION && platform !== 'Win7') {
        // 指定chrome内核版本
        chromeBaseUrl = `${CHROMIUM_URL}chrome_kernel-${BUNDLE_KERNEL_VERSION}/`;
      }
      const downloadArtifactJobs = [
        downloadArtifact(assetsPath, chromiumTargetName, chromiumSaveName, chromeBaseUrl),
      ];
      if (!skipUpdaterDownload) {
        if (platformCode === 'win') {
          if (platform === 'Win7') {
            artifactBaseUrl = `${artifactBaseUrl}/Windows7-chrome/`;
          }
          downloadArtifactJobs.push(
            downloadArtifact(assetsPath, updaterName, updaterName, artifactBaseUrl),
          );
        }
      }
      if (!skipNdriverDownload) {
        // 下载ndriver
        const ndriverFileName = platformCode === 'win' ? 'ndriver.exe' : 'ndriver';
        downloadArtifactJobs.push(
          downloadArtifact(
            assetsPath,
            ndriverFileName,
            ndriverFileName,
            `${N_DRIVER_URL}${platformCode}${platformCode === 'mac' ? `-${ARCH}` : ''}/`,
          ).then(() => {
            if (platformCode !== 'win') {
              fs.chmodSync(`${assetsPath}/${ndriverFileName}`, 0o755);
            }
          }),
        );
      }
      if (!skipIOSDownload) {
        // 下载 ios
        const iosFileName = platformCode === 'win' ? 'ios.exe' : 'ios';
        downloadArtifactJobs.push(
          downloadArtifact(
            assetsPath,
            iosFileName,
            iosFileName,
            `${IOS_URL}${platformCode}${platformCode === 'mac' ? `-${ARCH}` : ''}/`,
          ).then(() => {
            if (platformCode !== 'win') {
              fs.chmodSync(`${assetsPath}/${iosFileName}`, 0o755);
            }
          }),
        );
      }
      if (!skipVpickDownload) {
        // 下载 vpick
        downloadArtifactJobs.push(
          downloadArtifact(
            assetsPath,
            'vpick.tar.gz',
            'vpick.tar.gz',
            `${VPICK_URL}`,
          )
        );
      }
      return Promise.all(downloadArtifactJobs);
    })
    .then(async () => {
      // macOS的客户端需要预先把chrome.zip解压缩出来，并删除zip文件
      // 因为在公证时zip文件里面的内容会被认为是没有签名，即使是签名后再打成zip包
      if (platformCode === 'mac' && fs.existsSync(chromeZipPath)) {
        console.log('macOS的客户端需要预先把chrome.zip解压缩出来，并删除zip文件');
        await extractZipFile(chromeZipPath, `${assetsPath}/chrome-mac`);
        fs.removeSync(chromeZipPath);
      }

      // 校验Windows 7的浏览器内核是否有变动
      if (platform === 'Win7') {
        try {
          let correctMd5 = process.env.WIN7_CHROME_KERNEL_MD5 || '37aed4971e009696535c666cf3510df2';
          let newMd5 = await calculateMD5(chromeZipPath);
          console.log(
            `Windows 7的浏览器内核 MD5【${newMd5}】与【${correctMd5}】匹配${
              newMd5 === correctMd5 ? '成功' : '失败'
            }`,
          );
          if (newMd5 !== correctMd5) {
            console.log('Windows 7的浏览器内核有变动，取消构建');
            // process.exit('Windows 7的浏览器内核有变动，取消构建');
          }
        } catch (e) {
          console.log(e);
          process.exit('校验Windows 7的浏览器内核失败，取消构建');
        }
      }
    })
    .then(() => {
      // 修改 app version
      const json = fs.readJSONSync(path.resolve('./electron/package.json'));
      json.name = PRODUCT_NAME;
      json.version = RELEASE_APP_VERSION;
      json.description = `${productName}${language === 'en' ? ' Program' : '程序'}`;
      json.author = author;
      fs.writeJSONSync(path.resolve('./electron/package.json'), json, { spaces: '  ' });
      // 修改自动更新 url
      const builderJson = fs.readJSONSync(electronBuilderPath);
      builderJson.publish = [{ provider: 'generic', url: updateUrl }];
      builderJson.artifactName = artifactName;
      builderJson.productName = productName;
      if (langLib) {
        builderJson.copyright = langLib.copyright;
      }
      builderJson.nsis.shortcutName = productName;
      if (mode === 'runtime') {
        builderJson.nsis.createDesktopShortcut = false;
      }
      // 为每个类型的打包任务创建不同的输出目录
      builderJson.directories.output = `electron-dist/${outputDir}`;
      console.log(`输出目录：${builderJson.directories.output}`);
      // builderJson.dmg.contents[2].name = langLib.readme;
      fs.writeJSONSync(electronBuilderPath, builderJson, {
        spaces: '  ',
      });
    })
    .then(() => {
      // 清空文件
      fs.emptyDirSync(appDistPath);
      // 打包安装程序
      console.log(`${new Date().toLocaleString()} 开始打包安装程序`);

      function buildElectron(retryCount = 0) {
        return new Promise((resolve, reject) => {
          // macOS下使用electron/notarize进行公证时会将日志输出到stderr
          // 所以这里要关闭调试输出，以免构建任务被强制中止
          const child = spawn(
            'node',
            [
              path.resolve('./node_modules/electron-builder/cli.js'),
              '--config',
              electronBuilderPath,
              `--${platformCode}`,
            ].concat(ARCH ? [`--${ARCH}`] : []),
            {
              env: {
                ...process.env,
                ELECTRON_MIRROR: 'https://npmmirror.com/mirrors/electron/',
                ELECTRON_BUILDER_BINARIES_MIRROR:
                  'https://npmmirror.com/mirrors/electron-builder-binaries/',
                DEBUG: 'electron-notarize*',
              },
            },
          );

          child.stdout.on('data', (data) => {
            process.stdout.write(data);
            try {
              if (data.includes('Command failed: codesign')) {
                console.log('【stdout】Command failed: codesign');
                retry().then(resolve).catch(reject);
              }
            } catch (e) {}
          });

          child.stderr.on('data', (err) => {
            try {
              if (err.includes('Command failed: codesign')) {
                console.log('【stderr】Command failed: codesign');
                process.stdout.write(err);
                retry().then(resolve).catch(reject);
              }
            } catch (e) {}
            process.stdout.write(err);
            process.exit(1);
          });

          child.on('exit', (code) => {
            if (code === 0) {
              // 打包完成
              console.log(`${new Date().toLocaleString()} 打包完成`);
              process.stdout.write('Build process done!\n');
              resolve();
            } else {
              console.log('Build process failed with exit code:', code);
            }
          });

          function retry() {
            if (retryCount < 3) {
              console.log(
                `[${new Date().toLocaleString()}] electron-builder重试第 ${retryCount + 1} 次...`,
              );
              buildElectron(retryCount + 1);
            } else {
              console.log(`[${new Date().toLocaleString()}] 已达到最大重试次数，退出构建`);
              process.exit(1);
            }
          }
        });
      }

      return buildElectron().catch((err) => {
        console.error('构建失败:', err);
      });
    });
}

async function downloadArtifact(dest, targetName, saveName, artifactBaseUrl) {
  const url = `${artifactBaseUrl}${targetName}`;
  console.log(`Downloading ${url}`);
  let downloadError = false;
  await downloadWithRetry(url, dest, saveName).catch((err) => {
    console.log(`${url} could not be successfully downloaded.  Error was:`, err);
    downloadError = true;
  });
  if (!downloadError) {
    console.log(`Successfully downloaded ${targetName}.`);
  }
  if (!downloadError) {
    return true;
  }
  throw new Error('Download fail');
}

async function downloadWithRetry(url, directory, saveName) {
  let lastError;
  const downloadURL = `${url}`;
  for (let i = 0; i < 5; i++) {
    console.log(`Attempting to download ${url} - attempt #${i + 1}`);
    try {
      return await downloadFile(downloadURL, directory, saveName);
    } catch (err) {
      lastError = err;
      await new Promise((resolve, reject) => setTimeout(resolve, 30000));
    }
  }
  throw lastError;
}

async function downloadFile(url, directory, saveName) {
  return new Promise((resolve, reject) => {
    const nuggetOpts = {
      dir: directory,
      target: saveName,
    };
    nugget(url, nuggetOpts, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

// 解压缩zip文件
async function extractZipFile(zipPath, targetDir) {
  try {
    await extract(zipPath, {
      dir: targetDir,
    });
  } catch (e) {
    throw e;
  }
}

// 计算文件的md5信息
function calculateMD5(filePath) {
  const hash = crypto.createHash('md5');
  const stream = fs.createReadStream(filePath);

  return new Promise((resolve, reject) => {
    stream.on('data', (chunk) => {
      hash.update(chunk);
    });

    stream.on('end', () => {
      const md5sum = hash.digest('hex');
      resolve(md5sum);
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}
