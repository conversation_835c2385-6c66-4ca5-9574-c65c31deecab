<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <title>加载中...</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
    }
    body {
      font-family: Microsoft YaHei, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
      'Segoe UI Symbol', 'Noto Color Emoji';
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    .container {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 16px;
    }
    .title {
      font-size: 14px;
    }
    .loading {
      color: #0f7cf4;
      -webkit-animation-name: spin;
      animation-timing-function: linear;
      -webkit-animation-duration: 2s;
      -webkit-animation-iteration-count: infinite;
    }
    .loading > svg {
      width: 48px;
      height: 48px;
    }
    @-webkit-keyframes spin {
      0% {
        transform: rotate(0);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  </style>
  <script type="text/javascript">
    serverData = "SERVER_DATA";
    try {
      serverData = JSON.parse(serverData);
      window.isCn = (serverData["huayoung-language"] || '').startsWith('zh');
      document.title = window.isCn ? '加载中...' : 'Loading...'
    } catch (e) {
      serverData = {};
    }
  </script>
</head>
<body>
  <div class="container">
    <div class="icon-wrap">
      <div class="loading">
        <svg t="1693899619410" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9608" width="64" height="64"><path d="M608.475733 98.474667a101.632 101.632 0 0 1-48.810666 89.856 97.450667 97.450667 0 0 1-100.693334 0 101.632 101.632 0 0 1-48.768-89.856C411.6544 43.690667 455.601067 0 509.3184 0c53.76 0 97.706667 43.690667 99.114667 98.474667z m-99.2 748.501333c-47.872 0-86.698667 39.637333-86.698666 88.533333 0 48.853333 38.826667 88.490667 86.698666 88.490667 47.914667 0 86.741333-39.637333 86.741334-88.490667 0-48.896-38.826667-88.533333-86.741334-88.533333z m410.154667-266.794667c-34.218667 0-61.952-28.288-61.952-63.189333 0-34.901333 27.733333-63.232 61.952-63.232 34.218667 0 61.952 28.330667 61.952 63.232s-27.733333 63.189333-61.952 63.189333zM198.2784 516.992a101.632 101.632 0 0 0-48.768-89.856 97.450667 97.450667 0 0 0-100.650667 0 101.632 101.632 0 0 0-48.768 89.856c1.408 54.784 45.354667 98.474667 99.114667 98.474667 53.717333 0 97.664-43.690667 99.072-98.474667z m91.136-367.445333c25.429333 25.472 35.498667 62.890667 26.368 98.048A100.138667 100.138667 0 0 1 245.3824 319.445333a97.792 97.792 0 0 1-96.085333-26.88 102.613333 102.613333 0 0 1 0.64-142.378666 97.706667 97.706667 0 0 1 139.52-0.64z m457.344 609.706666a76.885333 76.885333 0 0 0-20.864 74.069334 75.093333 75.093333 0 0 0 53.418667 54.485333 73.386667 73.386667 0 0 0 72.533333-21.290667c27.733333-29.824 27.136-76.8-1.365333-105.813333a73.301333 73.301333 0 0 0-103.722667-1.450667zM834.353067 256.853333c-12.373333 13.482667-30.933333 18.986667-48.469334 14.378667a50.090667 50.090667 0 0 1-35.712-36.394667c-4.48-17.92 0.938667-36.864 14.08-49.493333a48.853333 48.853333 0 0 1 68.992 1.152c18.901333 19.285333 19.413333 50.432 1.109334 70.357333zM289.4144 741.376a97.834667 97.834667 0 0 0-96.64-28.032 100.181333 100.181333 0 0 0-71.04 72.490667 102.528 102.528 0 0 0 27.52 98.602666 97.706667 97.706667 0 0 0 138.538667-1.664c38.101333-38.826667 38.826667-101.632 1.621333-141.397333z" fill="currentColor" p-id="9609"></path></svg>
      </div>
    </div>
    <div>
      <div class="title"></div>
    </div>
  </div>
</body>
<script>
  const urlObj = new URL(location.href);
  document.querySelector('.title').innerText = urlObj.searchParams.get('title') || (window.isCn ? '正在为您检测浏览器指纹与出口IP...' : 'Checking browser fingerprint and IP...');
  const { transitList = [], channelList = [], ipCheckers = [{ provider: 'huayoung', isDefault: true }]  } = serverData;
  const primaryChannel = channelList.find((channel) => channel.primary);
  const providerCode = localStorage.getItem('default-ip-check-provider') || ipCheckers.find((checker) => checker.isDefault)?.provider || 'huayoung';
  const ipChecker = ipCheckers.find((checker) => checker.provider === providerCode) ?? ipCheckers[0];
  const responseIsJson = ipChecker.dataIndex && ipChecker.dataIndex.length > 0;
  let fetchingRemoteIp = true;
  let hyApiList = [];
  let otherApi = '';
  const ipv6TransitList = transitList.filter((t) => !t.jump && !!t.ipv6Endpoint);
  const ipv4TransitList = transitList.filter((t) => !t.jump && !!t.ipv4Endpoint);
  const fallbackTransitList = transitList.filter((t) => !t.jump);
  if ((primaryChannel?.ipSocks?.ipv6 ||
          primaryChannel?.ip?.ipv6 ||
          primaryChannel?.ippIp?.ipv6) && ipv6TransitList.length > 0) {
    hyApiList = ipv6TransitList.map((t) => resolveUrl(t.ipv6Endpoint, '/transitMyIp'));
  } else if (ipv4TransitList.length > 0) {
    hyApiList = ipv4TransitList.map((t) => resolveUrl(t.ipv4Endpoint, '/transitMyIp'));
  } else {
    hyApiList = fallbackTransitList.map((t) => resolveUrl(t.endpoints, '/transitMyIp'));
  }
  if (providerCode !== 'huayoung') {
    otherApi = ipChecker.url || 'https://ipapi.co/ip';
  }
  const hyApiListPromises = hyApiList.map((url, idx) => {
    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        resolve('');
      }, 3000)
      doFetchIp(url, idx, (ipStr) => {
        clearTimeout(timer);
        resolve(ipStr);
      });
    });
  });
  let otherApiPromise = Promise.resolve('');
  if (otherApi) {
    otherApiPromise = new Promise((resolve) => {
      const timer = setTimeout(() => {
        resolve('');
      }, 3000)
      doFetchIp(otherApi, 0, (ipStr) => {
        clearTimeout(timer);
        resolve(ipStr);
      });
    });
  }
  Promise.all([Promise.race(hyApiListPromises), otherApiPromise]).then(([hyApiDetectedIp, otherApiDetectedIp]) => {
    const ipStr = otherApiDetectedIp || hyApiDetectedIp;
    if (!ipStr) {
      window._checkCallback(0);
      // document.querySelector('.title').innerText = (window.isCn ? 'IP检测失败，为您打开检测页面...' : 'Checking IP has failed, Opening detection page...');
      // setTimeout(() => {
      //   window._checkCallback(0);
      // }, 1000);
    } else {
      const sqlIp = primaryChannel?.ip;
      const ippIp = primaryChannel?.ippIp;
      if (!primaryChannel || sqlIp?.dynamic || ippIp?.dynamic || (ippIp && !ippIp?.refTeamIp)) {
        window._checkCallback(1);
      } else {
        const ipToCompare = ippIp?.refTeamIp ? ippIp?.outboundIp || ippIp?.host : sqlIp?.ip;
        if (ipToCompare && ipStr !== ipToCompare) {
          window._checkCallback(2);
        } else {
          window._checkCallback(1);
        }
      }
    }
  }).finally(() => {
    fetchingRemoteIp = false;
  });

  function doFetchIp(url, idx, callback, count = 1) {
    return fetch(url)
      .then((res) => responseIsJson ? res.json() : res.text())
      .then((res) => {
        callback(responseIsJson ? ipChecker.dataIndex.reduce((acc, cur) => acc[cur], res) : res)
      })
      .catch((e) => {
        console.error(e);
        if (count < 3 && fetchingRemoteIp) {
          doFetchIp(url, idx, callback, count + 1);
        } else {
          callback('');
        }
      });
  }

  function resolveUrl(base, path) {
    try {
      return new URL(path, base).href;
    } catch (e) {
      return `${base}${path}`;
    }
  }
</script>
</html>
