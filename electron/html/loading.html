<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>正在加载，请稍候</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
    }
    .container {
      display: flex;
      gap: 16px;
      padding: 36px 24px;
    }
    .title {
      font-size: 14px;
      margin-bottom: 8px;
    }
    .desc {
      font-size: 12px;
      color: #999;
    }
    .loading {
      -webkit-animation-name: spin;
      animation-timing-function: linear;
      -webkit-animation-duration: 2s;
      -webkit-animation-iteration-count: infinite;
    }
    .loading > svg {
      width: 48px;
      height: 48px;
    }
    @-webkit-keyframes spin {
      0% {
        transform: rotate(0);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon-wrap">
      <div class="loading">
        <svg t="1693899619410" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9608" width="64" height="64"><path d="M608.475733 98.474667a101.632 101.632 0 0 1-48.810666 89.856 97.450667 97.450667 0 0 1-100.693334 0 101.632 101.632 0 0 1-48.768-89.856C411.6544 43.690667 455.601067 0 509.3184 0c53.76 0 97.706667 43.690667 99.114667 98.474667z m-99.2 748.501333c-47.872 0-86.698667 39.637333-86.698666 88.533333 0 48.853333 38.826667 88.490667 86.698666 88.490667 47.914667 0 86.741333-39.637333 86.741334-88.490667 0-48.896-38.826667-88.533333-86.741334-88.533333z m410.154667-266.794667c-34.218667 0-61.952-28.288-61.952-63.189333 0-34.901333 27.733333-63.232 61.952-63.232 34.218667 0 61.952 28.330667 61.952 63.232s-27.733333 63.189333-61.952 63.189333zM198.2784 516.992a101.632 101.632 0 0 0-48.768-89.856 97.450667 97.450667 0 0 0-100.650667 0 101.632 101.632 0 0 0-48.768 89.856c1.408 54.784 45.354667 98.474667 99.114667 98.474667 53.717333 0 97.664-43.690667 99.072-98.474667z m91.136-367.445333c25.429333 25.472 35.498667 62.890667 26.368 98.048A100.138667 100.138667 0 0 1 245.3824 319.445333a97.792 97.792 0 0 1-96.085333-26.88 102.613333 102.613333 0 0 1 0.64-142.378666 97.706667 97.706667 0 0 1 139.52-0.64z m457.344 609.706666a76.885333 76.885333 0 0 0-20.864 74.069334 75.093333 75.093333 0 0 0 53.418667 54.485333 73.386667 73.386667 0 0 0 72.533333-21.290667c27.733333-29.824 27.136-76.8-1.365333-105.813333a73.301333 73.301333 0 0 0-103.722667-1.450667zM834.353067 256.853333c-12.373333 13.482667-30.933333 18.986667-48.469334 14.378667a50.090667 50.090667 0 0 1-35.712-36.394667c-4.48-17.92 0.938667-36.864 14.08-49.493333a48.853333 48.853333 0 0 1 68.992 1.152c18.901333 19.285333 19.413333 50.432 1.109334 70.357333zM289.4144 741.376a97.834667 97.834667 0 0 0-96.64-28.032 100.181333 100.181333 0 0 0-71.04 72.490667 102.528 102.528 0 0 0 27.52 98.602666 97.706667 97.706667 0 0 0 138.538667-1.664c38.101333-38.826667 38.826667-101.632 1.621333-141.397333z" fill="#999999" p-id="9609"></path></svg>
      </div>
    </div>
    <div>
      <div class="title"></div>
      <div class="desc"></div>
    </div>
  </div>
  <script>
    const urlObj = new URL(location.href);
    document.querySelector('.title').innerText = urlObj.searchParams.get('title') || '正在加载，请稍候';
    document.querySelector('.desc').innerText = urlObj.searchParams.get('description') || '这可能需要一点时间，请稍候...';
  </script>
</body>
</html>
