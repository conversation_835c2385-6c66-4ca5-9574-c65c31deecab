<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GGBrowser Connection Check</title>
    <style>
        /* 基本样式重置和全局设置 */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #ffffff;
            color: #000000;
        }

        /* 主容器，实现垂直和水平居中 */
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* 主要内容区域 */
        #main-content {
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            width: 100%;
        }

        /* Logo 文本样式 */
        .logo {
            font-size: 3rem;
            font-weight: 700;
            color: #ef4444; /* red-500 */
            margin-bottom: 2rem;
            font-family: 'Poppins', sans-serif;
        }

        /* 加载中文本样式 */
        #loading-text {
            font-size: 1.125rem;
            margin-bottom: 0.5rem;
            height: 1.5rem; /* 固定高度防止跳动 */
        }

        /* 进度条容器 */
        .progress-container {
            width: 100%;
            max-width: 28rem;
            background-color: #e5e7eb; /* gray-200 */
            border-radius: 9999px;
            height: 0.75rem; /* 12px */
            margin-bottom: 1.5rem;
            border: 1px solid #d1d5db; /* gray-300 */
            box-sizing: border-box;
            overflow: hidden;
        }

        /* 进度条本身 */
        #progress-bar {
            background-color: #3b82f6; /* blue-500 */
            height: 100%;
            border-radius: 9999px;
            transition: width 0.5s ease-in-out, background-color 0.5s ease-in-out;
            width: 0%;
        }

        /* 状态检查列表 */
        .status-list {
            display: flex;
            flex-direction: column;
            gap: 1rem; /* 16px */
            text-align: left;
            width: 100%;
            max-width: 28rem;
            margin: 0 auto;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .status-text {
             font-size: 1.25rem;
        }

        .response-code {
            margin-left: auto;
            font-size: 0.9rem;
            color: #6b7280;
            text-align: right;
        }

        /* 图标的通用样式 */
        .icon {
            width: 1.5rem;
            height: 1.5rem;
            flex-shrink: 0;
        }
        .icon-container .icon { display: none; }
        .icon-container .spinner-icon { display: block; }

        .check-icon { color: #22c55e; /* green-500 */ }
        .cross-icon { color: #ef4444; /* red-500 */ }
        .warning-icon { color: #f59e0b; /* yellow-500 */ }
        .spinner-icon { color: #6b7280; /* gray-500 */ animation: spin 1s linear infinite; }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 最终错误信息 */
        #error-container {
            display: none;
            margin-top: 1.5rem;
            text-align: center;
        }

        #error-message {
            font-size: 1.125rem;
            color: #ef4444; /* red-500 */
            font-weight: 500;
            line-height: 1.5;
        }

        #proceed-anyway-button {
            display: none; /* 默认隐藏 */
            margin-top: 1rem;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            color: #ffffff;
            background-color: #ef4444; /* red-500 */
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        #proceed-anyway-button:hover {
            background-color: #dc2626; /* red-600 */
        }
    </style>
</head>
<body>

    <div id="main-content">
        <h1 class="logo">GGBrowser</h1>
        <p id="loading-text">Starting connection check...</p>
        <div class="progress-container">
            <div id="progress-bar"></div>
        </div>
        <div class="status-list">
            <div class="status-item" id="google-check-item">
                <div class="icon-container">
                    <svg class="icon spinner-icon" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" opacity="0.25"></circle><path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                    <svg class="icon check-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    <svg class="icon cross-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    <svg class="icon warning-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>
                </div>
                <span class="status-text">Network Status</span>
                <div id="network-response-code" class="response-code"></div>
            </div>
            <div class="status-item" id="tunnel-check-item">
                 <div class="icon-container">
                    <svg class="icon spinner-icon" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" opacity="0.25"></circle><path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                    <svg class="icon check-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    <svg class="icon cross-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    <svg class="icon warning-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>
                </div>
                <span class="status-text">Boost Tunnel</span>
                <div id="tunnel-response-code" class="response-code"></div>
            </div>
        </div>
        <div id="error-container">
            <p id="error-message"></p>
            <button id="proceed-anyway-button" onclick="proceedToPage()">Proceed to page anyway</button>
        </div>
    </div>

    <script>
        // --- State Management ---
        let isCheckInProgress = true;
        let failedUrls = [];

        // --- DOM Element Selection ---
        const progressBar = document.getElementById('progress-bar');
        const loadingText = document.getElementById('loading-text');
        const errorContainer = document.getElementById('error-container');
        const errorMessage = document.getElementById('error-message');
        const proceedButton = document.getElementById('proceed-anyway-button');
        const googleCheckItem = document.getElementById('google-check-item');
        const tunnelCheckItem = document.getElementById('tunnel-check-item');
        const networkResponseCode = document.getElementById('network-response-code');
        const tunnelResponseCode = document.getElementById('tunnel-response-code');

        // --- Helper Functions ---

        /**
         * Manages the icon visibility for a status item.
         * @param {HTMLElement} item - The status item container.
         * @param {'spinner' | 'check' | 'cross' | 'warning'} iconType - The type of icon to show.
         */
        function setIcon(item, iconType) {
            const container = item.querySelector('.icon-container');
            container.querySelectorAll('.icon').forEach(icon => icon.style.display = 'none');
            container.querySelector(`.${iconType}-icon`).style.display = 'block';
        }

        /**
         * Finalizes the check with a failure state.
         * @param {string} message - The detailed error message to show.
         * @param {boolean} showProceedButton - Whether to show the "Proceed Anyway" button.
         */
        function setFailureState(message, showProceedButton = false) {
            if (!isCheckInProgress) return;
            isCheckInProgress = false;
            progressBar.style.backgroundColor = '#ef4444'; // red
            progressBar.style.width = '100%'; // On failure, fill the bar
            loadingText.textContent = 'Check Failed';
            errorMessage.innerHTML = message;
            errorContainer.style.display = 'block';
            if (showProceedButton) {
                proceedButton.style.display = 'inline-block';
            } else {
                proceedButton.style.display = 'none';
            }
        }

        /**
         * Navigates to the failed URLs.
         */
        function proceedToPage() {
            // if (failedUrls.length > 0) {
            //     // This function can be linked to an external callback if needed,
            //     // for now, it opens the failed URLs.
            //     failedUrls.forEach(url => {
            //         window.open(url, '_blank');
            //     });
            // }
             // If you need to call the external _checkCallback, you can do it here.
             window._checkCallback?.(1);
        }

        // --- Main Logic ---

        /**
         * The main callback function to update the UI based on check results.
         * @param {object} result - The result object.
         * @param {'google' | 'tunnel'} result.step - The step being reported.
         * @param {'success' | 'failure' | 'partial_failure' | 'total_failure'} result.status - The status of the step.
         */
        window.updateUI = function(result) {
            if (!isCheckInProgress) {
                return;
            }
            const { step, status } = result;

            if (step === 'google') {
                if (status === 'success') {
                    setIcon(googleCheckItem, 'check');
                    progressBar.style.width = '50%';
                    checkTunnelStatus(); // Automatically start the next check
                } else {
                    setIcon(googleCheckItem, 'cross');
                    setIcon(tunnelCheckItem, 'cross'); // Also fail the next step visually
                    const msg = "Network status check failed. There might be an issue with your network environment. Please contact GamsGo customer service to report the problem.";
                    setFailureState(msg, false); // Do not show proceed button
                }
            } else if (step === 'tunnel') {
                switch (status) {
                    case 'success':
                        setIcon(tunnelCheckItem, 'check');
                        progressBar.style.backgroundColor = '#22c55e'; // green
                        progressBar.style.width = '100%';
                        loadingText.textContent = 'Check Complete!';
                        isCheckInProgress = false;
                        // Optional: callback after a short delay on success
                        setTimeout(() => {
                          window._checkCallback?.(1);
                        }, 1000);
                        break;
                    case 'partial_failure':
                        setIcon(tunnelCheckItem, 'warning');
                        const partialMsg = "Some services in the boost tunnel are unavailable. Please try the 'Proceed to page anyway' button.<br>If the issue persists, contact GamsGo customer service.";
                        setFailureState(partialMsg, true); // Show proceed button
                        break;
                    case 'total_failure':
                        setIcon(tunnelCheckItem, 'cross');
                        const totalMsg = "Boost tunnel connection failed completely. Please check your network or contact GamsGo customer service.";
                        setFailureState(totalMsg, false); // Do not show proceed button
                        break;
                }
            }
        };

        /**
         * Initializes the UI to the starting state.
         */
        function initializeUI() {
            isCheckInProgress = true;
            failedUrls = [];
            progressBar.style.width = '5%';
            progressBar.style.backgroundColor = '#3b82f6';
            loadingText.textContent = 'Starting automatic check...';
            errorContainer.style.display = 'none';
            proceedButton.style.display = 'none'; // Ensure button is hidden on reset
            networkResponseCode.textContent = '';
            tunnelResponseCode.textContent = '';
            setIcon(googleCheckItem, 'spinner');
            setIcon(tunnelCheckItem, 'spinner');
        }

        /**
         * Checks the network status by making an HTTP request to Google.
         */
        function checkNetworkStatus() {
            let timeoutReached = false;
            loadingText.textContent = 'Checking network status...';

            const timeout = setTimeout(() => {
                timeoutReached = true;
                window.updateUI({ step: 'google', status: 'failure' });
            }, 10000); // Timeout after 10 seconds

            fetch('/api/pingUrlByPrimaryChannel', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ path: 'https://www.google.com/generate_204', timeout: 8000 })
            })
            .then(response => response.json())
            .then(result => {
                if (timeoutReached) return;
                window.updateUI({ step: 'google', status: result.success ? 'success' : 'failure' });
            })
            .catch(() => {
                if (timeoutReached) return;
                window.updateUI({ step: 'google', status: 'failure' });
            })
            .finally(() => clearTimeout(timeout));
        }

        /**
         * Checks the tunnel status by making requests to multiple URLs.
         */
        function checkTunnelStatus() {
            setIcon(tunnelCheckItem, 'spinner');
            loadingText.textContent = 'Checking boost tunnel...';

            const urls = [
                'https://claude.ai/new',
                'https://www.freepik.com/',
                'https://discord.com/channels/@me',
                'https://elements.envato.com/'
            ];

            const requests = urls.map(url =>
                fetch('/api/pingUrlByPrimaryChannel', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ path: url, timeout: 8000 })
                })
                .then(response => response.json())
                .then(result => ({ url, status: result.success ? 'success' : 'failure' }))
                .catch(err => ({url, status: 'failure'}))
            );

            Promise.all(requests)
                .then(results => {
                    const failures = results.filter(r => r.status === 'failure');
                    failedUrls = failures.map(r => r.url);

                    if (failures.length === 0) {
                        window.updateUI({ step: 'tunnel', status: 'success' });
                    } else if (failures.length === urls.length) {
                        window.updateUI({ step: 'tunnel', status: 'total_failure' });
                    } else {
                        window.updateUI({ step: 'tunnel', status: 'partial_failure' });
                    }
                });
        }

        /**
         * Starts the entire automatic check process.
         */
        function startAutomaticCheck() {
            initializeUI();
            checkNetworkStatus();
        }

        // Start the process when the page loads.
        window.onload = startAutomaticCheck;
    </script>

</body>
</html>
