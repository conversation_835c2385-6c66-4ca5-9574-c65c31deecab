<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>请确认</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
    }
    .container {
      display: flex;
      gap: 16px;
      padding: 36px 24px;
    }
    .title {
      font-size: 14px;
      margin-bottom: 8px;
    }
    .desc {
      min-height: 70px;
      font-size: 12px;
      color: #999;
      margin-bottom: 16px;
    }
    .svg-wrap > svg {
      width: 48px;
      height: 48px;
    }
    #btn-row {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      justify-content: flex-end;
      padding: 16px 24px;
      display: flex;
      gap: 8px;
      align-content: flex-end;
    }
    #btn-row > button {
      display: none;
      line-height: 1.5715;
      position: relative;
      font-weight: 400;
      white-space: nowrap;
      text-align: center;
      background-image: none;
      border: 1px solid transparent;
      box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      touch-action: manipulation;
      height: 32px;
      padding: 4px 15px;
      font-size: 14px;
      border-radius: 3px;
      color: #404040;
      border-color: #d9d9d9;
      background: #fff;
      outline: 0;
    }
    #btn-row > button.visible {
      display: inline-block;
    }
    #btn-row > button.primary {
      color: #fff;
      border-color: #0F7CF4;
      background: #0F7CF4;
      text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);
      box-shadow: 0 2px 0 rgb(0 0 0 / 5%);
    }
    #btn-row > button.success {
      color: white !important;
      background-color: #52c41a !important;
      border: none !important;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon-wrap">
      <div class="svg-wrap">
        <svg t="1720695898610" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1451" width="48" height="48"><path d="M512 0c282.752 0 512 229.248 512 512s-229.248 512-512 512S0 794.752 0 512 229.248 0 512 0z m32 426.666667h-64a10.709333 10.709333 0 0 0-10.368 8.234666L469.333333 437.333333v320c0 5.034667 3.541333 9.258667 8.234667 10.368l2.432 0.298667h64a10.709333 10.709333 0 0 0 10.368-8.234667L554.666667 757.333333v-320a10.709333 10.709333 0 0 0-8.234667-10.368L544 426.666667zM554.666667 256h-85.333334v85.333333h85.333334V256z" p-id="1452" fill="#3b78f4"></path></svg>
      </div>
    </div>
    <div>
      <div class="title"></div>
      <div class="desc"></div>
    </div>
    <div id="btn-row">
      <button id="extraBtn1"></button>
      <button id="extraBtn2"></button>
      <button id="okBtn"></button>
      <button id="cancelBtn"></button>
    </div>
  </div>
  <script>
    const urlObj = new URL(location.href);
    const sp = urlObj.searchParams;
    document.querySelector('.title').innerText = sp.get('title') || '请确认是否继续';
    document.querySelector('.desc').innerText = sp.get('description') || '';
    if (sp.get('extraBtn1')) {
      document.querySelector('#extraBtn1').classList.add('visible');
      document.querySelector('#extraBtn1').innerText = sp.get('extraBtn1');
      if (sp.get('extraBtn1Type')) {
        document.querySelector('#extraBtn1').classList.add(sp.get('extraBtn1Type'));
      }
    }
    if (sp.get('extraBtn2')) {
      document.querySelector('#extraBtn2').classList.add('visible');
      document.querySelector('#extraBtn2').innerText = sp.get('extraBtn2');
      if (sp.get('extraBtn2Type')) {
        document.querySelector('#extraBtn2').classList.add(sp.get('extraBtn2Type'));
      }
    }
    if (sp.get('okBtn')) {
      document.querySelector('#okBtn').classList.add('visible');
      document.querySelector('#okBtn').innerText = sp.get('okBtn');
      if (sp.get('okBtnType')) {
        document.querySelector('#okBtn').classList.add(sp.get('okBtnType'));
      }
    }
    if (sp.get('cancelBtn')) {
      document.querySelector('#cancelBtn').classList.add('visible');
      document.querySelector('#cancelBtn').innerText = sp.get('cancelBtn');
      if (sp.get('cancelBtnType')) {
        document.querySelector('#cancelBtn').classList.add(sp.get('cancelBtnType'));
      }
    }
    document.querySelector('#btn-row').addEventListener('click', (evt) => {
      if (evt.target.tagName !== 'BUTTON') {
        return;
      }
      console.log(evt.target.id);
      window.ipcRenderer.send('btn-click', { key: evt.target.id });
    });
  </script>
</body>
</html>
