# Electron 本地客户端

## 目录结构说明
- 客户端开发在 `src` 目录，最终会经过 webpack 编译输出 `main.js`, `preload.js`；
- 浏览器插件开发关注 `extensions` 目录，浏览器插件逻辑一般比较简单，所以没有配置 webpack 编译；

```
├── assets                  // 外部程序、数据库文件等跟主程序逻辑关系不大的文件（编译时清空）
├── build                   // 编译时需要用到的资源文件
├── electron-builder.json   // 编译配置文件
├── extensions              // 浏览器扩展程序
├── main.js                 // 主进程入口文件（webpack output）
├── package.json            // npm 配置文件
├── preload.js              // 渲染进程 preload 文件（webpack output）
├── src                     // 源码，最终会编译为 main.js, preload.js
```

## 注意事项
1. 由于 `src` 下的文件经过 webpack 编译后输出到 `electron` 根目录下，所以在用到 `path.resolve()`, `path.join()` 这类方法来指定路径时，要基于根目录来定位。
