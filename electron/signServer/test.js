var child_process = require('child_process');
var path = require('path');
const filePath = 'C:/Users/<USER>/Downloads/DaMai_1.0.3_setup.exe';

child_process.spawnSync(
  'curl.exe',
  [
    '-F',
    'exe=@' + filePath, //生成的exe文件的绝对路径
    'http://127.0.0.1:3000/sign', //签名服务
    '-k',
    '-f',
    '-o',
    `${filePath}-1`, //签名完之后就下载，下载之后就覆盖
  ],
  {
    cwd: path.dirname(filePath),
    stdio: 'inherit', //上传下载过程同步到当前控制台
  },
);
