const express = require('express');
const child_process = require('child_process');
//引入multer模块
var multer = require('multer');
var path = require('path');
var fs = require('fs');
//设置上传的目录，
var upload = multer({
  dest: path.join(__dirname, 'temp'),
});
const app = express();
const port = process.env.SIGN_PORT || 3000;

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// 以下时间服务器可用
// http://time.certum.pl
// http://sha256timestamp.ws.symantec.com/sha256/
// http://rfc3161timestamp.globalsign.com/advanced
const timstampServers = [
  'http://rfc3161timestamp.globalsign.com/advanced',
  'http://time.certum.pl',
  'http://sha256timestamp.ws.symantec.com/sha256/',
  'http://timestamp.comodoca.com/rfc3161',
  'http://tsa.wosign.com/timestamp',
  'http://tsa.starfieldtech.com',
  'http://timestamp.globalsign.com/scripts/timstamp.dll',
  'http://timestamp.digicert.com',
  'http://timestamp.verisign.com/scripts/timestamp.dll',
  'http://timestamp.entrust.net/TSS/AuthenticodeTS',
];
let sleepTime = 1;

const doSign = (req, res, targetPath, timstampServer) => {
  let error = null;
  console.log(new Date());
  console.log(req.file);
  console.log(req.body);
  // signtool sign /v /fd sha256 /sha1 5554dbe50f2f4283f40068f640adca63d7814938 /tr http://rfc3161timestamp.globalsign.com/advanced /td sha256 all_files/*.*
  const cmd = 'C:/GlobalsignEV代码签名/CMD下sha256签名/signtool';
  const params = [
    'sign',
    '/v',
    '/fd',
    'sha256',
    '/sha1',
    process.env.TOKEN_FINGERPRINT,
    '/tr',
    timstampServer,
    '/td',
    'sha256',
    targetPath,
  ];
  console.log(cmd, JSON.stringify(params));
  const spawn = child_process.spawnSync(cmd, params);
  if (spawn.error) {
    console.error(spawn.error);
    error = spawn.error;
  } else {
    const out = spawn.stdout.toString();
    console.log('签名完成', out);
    if (out.indexOf('Error') > -1) {
      error = out;
    } else {
      try {
        // 检查签名是否成功
        let rs = out.match(/Number of files successfully Signed: (\d+)/);
        if (rs && rs[1] > 0) {
          res.download(targetPath, req.file.originalname);
        } else {
          error = '签名成功数量为0：\n' + out;
        }
      } catch (e) {
        error = '签名成功数量为0：\n' + out;
      }
    }
  }
  setTimeout(() => {
    try {
      // 如果文件存在则删除
      fs.rmSync(targetPath);
    } catch (e) {}
  }, 10 * 60 * 1000);
  return error;
};

app.get('/', (req, res) => {
  res.send('Hello World!');
});

app.post('/sign', upload.single('exe'), async function (req, res, next) {
  const targetPath = `${req.file.path}.exe`;
  fs.renameSync(req.file.path, targetPath);
  let i = 0;
  let error;
  // 自动重试
  while (i < timstampServers.length) {
    if (i > 0) {
      console.error(`${sleepTime}s后开始第${i}次重试`);
      await sleep(sleepTime * 1000); // 休眠
    }
    error = doSign(req, res, targetPath, timstampServers[i]);
    i++;
    if (!error) {
      break;
    }
  }

  if (error) {
    res.status(500).send(`签名失败：${error}`);
  }
});

app.listen(port, () => {
  console.log(`Example app listening at http://localhost:${port}`);
});
