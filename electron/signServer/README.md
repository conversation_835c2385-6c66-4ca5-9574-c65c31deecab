## 沃通证书远程签名

启动：

```
yarn
set TOKEN_FINGERPRINT=xxxxx

cd signing
node main.js
```

## 解决每次签名都需要输入密码的问题

1、服务器要安装 safenet 10.8-x64-10.8-R2.msi 并启用单点登录，参考当前目录下的“safenet 启用单点登录.png” 2、不要使用 mstsc 远程桌面连接到服务器，建议使用 Todesk 之类的工具，并确保断开连接时不要自动锁屏，参考当前目录下的“Todesk.png”

## 测试

检查服务是否启动：

```
curl http://targetHost:3000/
```

输出"Hello World!"证明服务已经启动

签名测试：

```
curl --noproxy "*" -F exe=@/tmp/aa.exe http://**************:3000/sign -k -f -o /tmp/bb.exe1
```
