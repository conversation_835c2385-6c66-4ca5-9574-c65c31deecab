diff --git a/node_modules/appium-adb/build/lib/tools/apk-utils.js b/node_modules/appium-adb/build/lib/tools/apk-utils.js
index a66e56a..77e989b 100644
--- a/node_modules/appium-adb/build/lib/tools/apk-utils.js
+++ b/node_modules/appium-adb/build/lib/tools/apk-utils.js
@@ -78,6 +78,9 @@ apkUtilsMethods.isAppInstalled = async function isAppInstalled(pkg, opts = {}) {
             }
         }
         isInstalled = new RegExp(`^package:${lodash_1.default.escapeRegExp(pkg)}$`, 'm').test(stdout);
+        if(!isInstalled) {
+          isInstalled = new RegExp(`^PackageName:${lodash_1.default.escapeRegExp(pkg)};$`, 'm').test(stdout);
+        }
     }
     logger_js_1.default.debug(`'${pkg}' is${!isInstalled ? ' not' : ''} installed`);
     return isInstalled;
diff --git a/node_modules/appium-adb/lib/tools/apk-utils.js b/node_modules/appium-adb/lib/tools/apk-utils.js
index 600e2a0..0e2bf5f 100644
--- a/node_modules/appium-adb/lib/tools/apk-utils.js
+++ b/node_modules/appium-adb/lib/tools/apk-utils.js
@@ -80,6 +80,9 @@ apkUtilsMethods.isAppInstalled = async function isAppInstalled (pkg, opts = {})
       }
     }
     isInstalled = new RegExp(`^package:${_.escapeRegExp(pkg)}$`, 'm').test(stdout);
+    if(!isInstalled) {
+      isInstalled = new RegExp(`^PackageName:${_.escapeRegExp(pkg)};$`, 'm').test(stdout);
+    }
   }
   log.debug(`'${pkg}' is${!isInstalled ? ' not' : ''} installed`);
   return isInstalled;
