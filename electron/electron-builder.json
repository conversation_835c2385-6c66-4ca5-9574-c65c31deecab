{"appId": "huayoung.com.desktop", "artifactName": "HuaYoung_${env.PLATFORM}_${version}_setup.${ext}", "productName": "花漾客户端", "copyright": "Copyright © 2025 深圳市云上悦动科技有限公司", "directories": {"output": "electron-dist", "app": "electron", "buildResources": "electron/build"}, "files": ["**/*", "!src/**/*", "!readme.md", "!tsconfig.json", "!signServer/**/*", "!build_en/**/*", "!scrcpy/**/*", "!electron-builder-en.json", "!afterMacSign.js", "!afterPackHook.js", "!autoSignChromeMacApp.js", "!customSign.js", "!customSign.test.js", "!**/node_modules/**/{LICENSE,LICENSE.md,LICENSE-MIT,CHANGELOG.md,CONTRIBUTING.md,GOVERNANCE.md,README.md,README,readme.md,readme,History.md,SECURITY.md}", "!**/node_modules/**/{test,__tests__,tests,powered-test,example,examples,.github,.husky}", "!**/node_modules/**/{*.ts,*.d.ts,*.d.ts.map,*.mjs.map,*.cjs.map,*.js.map}", "!**/node_modules/.bin"], "extraResources": ["dist/**/*", "browserPage/**/*", "extensions-dist/**/*", "extra/**/*", {"from": "electron/extra", "to": "extra"}], "asar": false, "removePackageScripts": false, "win": {"target": ["nsis"], "sign": "./electron/customSign.js", "signingHashAlgorithms": ["sha256"], "requestedExecutionLevel": "highestAvailable", "extraFiles": [{"from": "electron/build/readme.txt", "to": "."}]}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "花漾客户端"}, "mac": {"extraFiles": [{"from": "electron/build/readme.txt", "to": "Resources/readme.txt"}], "entitlements": "electron/build/entitlements.mac.plist", "hardenedRuntime": true, "extendInfo": {"CFBundleURLSchemes": ["huay<PERSON><PERSON>"], "CFBundleURLTypes": ["huay<PERSON><PERSON>"], "NSPrincipalClass": "AtomApplication", "NSMicrophoneUsageDescription": "请允许本程序访问您的麦克风", "NSCameraUsageDescription": "请允许本程序访问您的摄像头"}}, "dmg": {}, "afterAllArtifactBuild": "electron/beforePackHook.js", "afterPack": "electron/afterPackHook.js", "afterSign": "electron/afterMacSign.js", "linux": {"target": ["AppImage", "deb"], "extraFiles": [{"from": "electron/build/readme.txt", "to": "."}]}, "deb": {"depends": ["libnotify4", "libxtst6", "libnss3"], "compression": "gz"}, "publish": [{"provider": "generic", "url": "https://dev.thinkoncloud.cn/downloads/"}]}