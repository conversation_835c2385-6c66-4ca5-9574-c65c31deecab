import { hy_actions } from '@e/helper/index';

import { exec } from 'child_process';
import path from 'path';
import { Buffer } from 'buffer';

export class WinUtil {
  /**
   * 对一个窗口截屏
   * @param title 窗口标题
   * @param saveTo 保存文件的绝对路径
   */
  static async captureWindow(title: string, saveTo: string) {
    const exePath = path.join(__dirname, '../extra', 'scrcpy', 'hyutil.exe');
    return await new Promise((resolve, reject) => {
      const json = JSON.stringify({
        name: hy_actions.ACTION_WIN_CAPTURE,
        title,
        save_to: saveTo,
      });
      // 拼接命令和参数为字符串
      const command = `"${exePath}" ${Buffer.from(json).toString('base64')}`;

      // 执行命令
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(`Error: ${stderr || error?.message}`);
        } else {
          let ret = JSON.parse(stdout);
          if (ret.success) {
            resolve(ret);
          }
          reject(ret.message);
        }
      });
    });
  }
}
