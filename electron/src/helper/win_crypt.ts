import { exec } from 'child_process';

import { hy_actions } from '@e/helper/index';
import path from 'path';
import { <PERSON><PERSON><PERSON> } from 'buffer';

export class Win32crypt {
  static async encryptData(data: Buffer): Promise<string> {
    const exePath = path.join(__dirname, '../extra', 'scrcpy', 'hyutil.exe');
    const resp: any = await new Promise((resolve, reject) => {
      const json = JSON.stringify({
        name: hy_actions.ACTION_CRYPT_ENCRYPT,
        key: data.toString('base64'),
      });
      // 拼接命令和参数为字符串
      const command = `"${exePath}" ${Buffer.from(json).toString('base64')}`;

      // 执行命令
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(`Error: ${stderr || error?.message}`);
        } else {
          let ret = JSON.parse(stdout);
          if (ret.success) {
            resolve(ret);
          }
          reject(ret.message);
        }
      });
    });
    return resp.encrypt_key;
  }

  static async decryptData(encryptedData: Buffer): Promise<string> {
    const exePath = path.join(__dirname, '../extra', 'scrcpy', 'hyutil.exe');
    const resp: any = await new Promise((resolve, reject) => {
      const json = JSON.stringify({
        name: hy_actions.ACTION_CRYPT_DECRYPT,
        encrypt_key: encryptedData.toString('base64'),
      });
      // 拼接命令和参数为字符串
      const command = `"${exePath}" ${Buffer.from(json).toString('base64')}`;

      // 执行命令
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(`Error: ${stderr || error?.message}`);
        } else {
          let ret = JSON.parse(stdout);
          if (ret.success) {
            resolve(ret);
          }
          reject(ret.message);
        }
      });
    });
    return resp.key;
  }
}
