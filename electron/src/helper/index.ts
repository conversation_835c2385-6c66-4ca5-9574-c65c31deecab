import { GroupController } from '@e/helper/group_control';
import { spawn } from 'child_process';
import EventEmitter from 'events';
import logger from '@e/services/logger';
import { waitSeconds } from '@e/rpa/utils';

const helper_stated_marker = 'Helper started';
const magic_string = 'hy_rpa_helper';

export const hy_actions = {
  ACTION_PING: 'ping',
  ACTION_EXIT: 'exit',
  ACTION_ECHO: 'echo',
  ACTION_VERSION: 'version',

  //rpa操作
  ACTION_ROBOT_CLICK: 'robot/click',
  ACTION_ROBOT_CLICK_ELEMENT: 'robot/click_element',

  //群控相关
  ACTION_GC_START: 'gc/start',
  ACTION_GC_STOP: 'gc/stop',
  ACTION_GC_PAUSE: 'gc/pause',
  ACTION_GC_RESUME: 'gc/resume',
  ACTION_GC_DETAIL: 'gc/detail',
  ACTION_GC_SYNC_TABS: 'gc/sync_tabs',
  ACTION_GC_CG_COLOR: 'gc/cg_color',
  EVENT_GC_MASTER_CLOSED: 'gc/event/masterClosed',
  EVENT_GC_WIN_SIZE_CHG: 'gc/event/winSizeChange',
  EVENT_GC_DETAIL_UPDATE: 'gc/event/detailUpdate',
  EVENT_GC_PAUSED_STATE_UPDATE: 'gc/event/pausedStateUpdate',

  //win32crypt 相关
  ACTION_CRYPT_DECRYPT: 'crypt/decrypt',
  ACTION_CRYPT_ENCRYPT: 'crypt/encrypt',

  //一些工具命令
  ACTION_WIN_BRING_TO_FRONT: 'win/bringToFront',
  ACTION_WIN_RESTORE: 'win/restore',
  ACTION_WIN_MINIMIZING: 'win/minimizing',
  ACTION_WIN_CAPTURE: 'win/capture',
  ACTION_WIN_SEND_KEYS: 'win/sendKeys', //批量往窗口发送文字（如果当前有焦点的不是输入框则无意义）
};

export interface IHelper {
  version: number;

  start: () => Promise<any>;
  destroy: () => Promise<any>;

  echo: (text: string) => ActionResponse;

  send: (name: string) => ActionResponse;
  sendAction: (action: any) => ActionResponse;

  listen: (eventName: string, callback: (data: any) => void) => void;

  unlisten: (eventName: string, callback: (data: any) => void) => void;

  enableDebug: () => void;
}

export interface GCHelper extends IHelper {
  /**
   * 群控
   */
  gc: GroupController;

  /**
   * 设置窗口大小改变回调函数
   * 当参与群控的任何一个会话窗口的大小发生变化时，都会调用该回调函数
   * @param callback
   */
  set windowResizeCallback(callback: (sessionId: number) => void);
}

export class ActionResponse {
  id: number;
  callback?: ActionCallback;

  constructor(id: number, callback?: ActionCallback) {
    this.id = id;
    this.callback = callback;
  }

  /**
   * 获取结果。如果success==false会有异常
   */
  async get(): Promise<any> {
    return this.callback?.get();
  }

  /**
   * 有些指令只关心是否success，使用该函数避免写try-catch
   */
  async success(): Promise<boolean> {
    try {
      await this.get();
      return true;
    } catch (e) {
      return false;
    }
  }

  listen(listener: (data?: any) => any) {
    this.callback?.listen(listener);
    return this;
  }
}

export function createGCHelper(execFile: string): GCHelper {
  return new GCHelperImpl(execFile);
}

//=================  private =================

class ActionCallback {
  id: number;
  private readonly promise: Promise<any>;

  private listener?: (data?: any) => {};

  resolve!: (data?: any) => any;
  reject!: (data?: any) => any;

  constructor(id: number) {
    this.id = id;
    this.promise = new Promise<any>((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
  }

  resp() {
    return new ActionResponse(this.id, this);
  }

  listen(listener: (data?: any) => any) {
    this.listener = listener;
    return this;
  }

  onData(data: any) {
    try {
      this.listener ? this.listener(data) : void data;
    } catch (ignore) {}
  }

  async get() {
    return await this.promise;
  }
}

class Reject_ActionResponse extends ActionResponse {
  e: any;
  constructor(id: number, e: string) {
    super(id);
    this.e = e;
  }
  async wait() {
    return Promise.reject(this.e);
  }
}

let action_index = 1;
function nextActionIndex() {
  let ret = action_index++;
  if (ret > 99999999) {
    action_index = 1;
  }
  return ret;
}

class IHelperImpl implements IHelper {
  type: string;
  execFile: string;
  exeVersion = 0;

  proc?: any;
  heatKick?: any;
  lastPing: number = 0;
  usable: boolean = false;

  start_callback?: ActionCallback;

  callbacks!: Map<number, ActionCallback>;

  emitter: EventEmitter;

  constructor(execFile: string, type: string) {
    this.emitter = new EventEmitter();
    this.emitter.setMaxListeners(9999);

    this.execFile = execFile;
    this.type = type;
  }

  get version(): number {
    return this.exeVersion;
  }

  async start() {
    await this.start_callback?.get();
    if (this.proc && this.usable) {
      return;
    }
    clearInterval(this.heatKick);
    await this.init();
  }

  async destroy() {
    this.send(hy_actions.ACTION_EXIT);
    clearInterval(this.heatKick);
    this.usable = false;
    this.proc?.kill();
  }

  echo(text: string): ActionResponse {
    return this.sendAction({
      name: hy_actions.ACTION_ECHO,
      text,
    });
  }

  listen(eventName: string, callback: (data: any) => void) {
    this.emitter.addListener(eventName, callback);
  }

  unlisten(eventName: string, callback: (data: any) => void) {
    this.emitter.removeListener(eventName, callback);
  }

  enableDebug() {
    this.sendRawStr('enable_debug_print').then(() => {});
  }

  send(name: string): ActionResponse {
    return this.sendAction({
      name,
    });
  }

  sendAction(action: any): ActionResponse {
    if (!action.name) {
      throw 'action.name未定义';
    }
    action.id = nextActionIndex();
    if (this.usable) {
      try {
        let callback: ActionCallback = new ActionCallback(action.id);
        let str = JSON.stringify(action);
        this.sendRawStr(str)
          .then(() => void 0)
          .catch((e) => {
            try {
              callback.reject({ success: false, message: '服务异常：' + (e.message || String(e)) });
            } catch (ignore) {}
          });

        this.callbacks?.set(action.id, callback);
        return callback.resp();
      } catch (e: any) {
        return new Reject_ActionResponse(action.id, e);
      }
    } else {
      return new Reject_ActionResponse(action.id, '服务未就绪');
    }
  }

  protected async sendRawStr(str: string) {
    let count = 0;
    while (true) {
      try {
        this.proc.stdin.write(str + '\n');
        break;
      } catch (e) {
        if (count++ < 3) {
          logger.error('send message to helper error, ' + str, e);
          await waitSeconds(2);
        } else {
          throw e;
        }
      }
    }
  }

  private onLog(action: any) {
    if (action.text && action.text.indexOf(helper_stated_marker) != -1) {
      this.start_callback?.resolve();
    }
    if (action.name == 'error') {
      console.error('HyHelper: ' + action.text);
    } else {
      console.log('HyHelper: ' + action.text);
    }
  }

  protected async onStdData(buf: any) {
    // let lines = iconv.decode(buf, 'utf-8')
    let lines = String(buf);
    if (lines) {
      lines = lines.trim();
      const strs = lines.split('\n');
      for (let str of strs) {
        str = (str || '').trim();
        if (!str) {
          continue;
        }
        try {
          if (str.startsWith("b'")) {
            str = str.slice(2, -1);
          }
          let resp = JSON.parse(str);
          switch (resp.name) {
            case 'log':
            case 'error':
              this.onLog(resp);
              break;
            default:
              if (resp.id && this.callbacks?.has(resp.id)) {
                let callback = this.callbacks.get(resp.id)!;
                if (resp.done) {
                  this.callbacks.delete(resp.id);
                  if (false === resp.success) {
                    callback.reject(resp.message);
                  } else {
                    callback.resolve(resp);
                  }
                } else {
                  callback.onData(resp);
                }
              } else {
                this.emitter.emit(resp.name, resp);
              }
              break;
          }
        } catch (e) {
          console.warn(str);
        }
      }
    }
  }

  protected async heartBeat() {
    try {
      process.kill(this.proc.pid, 0);
    } catch (e) {
      //做重连
      await this.destroy();
      await this.init();
      return;
    }
    this.send(hy_actions.ACTION_PING)
      .get()
      .then((resp) => {
        let time = parseInt(resp.data);
        time = time * 1000; //python里时间精确到秒
        this.lastPing = time;
      });
  }

  protected async init() {
    this.callbacks = new Map();
    this.proc = spawn(this.execFile, [magic_string]);
    this.proc.stdout?.on('data', this.onStdData.bind(this));
    this.proc.stdout?.on('error', (e: Error) => {
      logger.error(`[HYHelper] ${this.type} helper stdout error`, e);
    });
    this.proc.stdin?.on('error', (e: Error) => {
      logger.error(`[HYHelper] ${this.type} helper stdin error`, e);
    });
    this.proc.on('error', (e: Error) => {
      logger.error(`[HYHelper] ${this.type} helper process error`, e);
    });
    this.start_callback = new ActionCallback(-1);
    this.usable = true;
    let startChecker = setTimeout(() => {
      this.start_callback?.reject('启动超时');
    }, 100000);
    await this.start_callback.get();
    clearTimeout(startChecker);
    this.start_callback = undefined;
    this.exeVersion = (await this.send(hy_actions.ACTION_VERSION).get()).version;
    this.heatKick = setInterval(() => this.heartBeat(), 5000);
  }
}

class GCHelperImpl extends IHelperImpl implements GCHelper {
  gc: GroupController;
  constructor(execFile: string) {
    super(execFile, 'gc');

    this.gc = new GroupController(this);
  }

  set windowResizeCallback(callback: (sessionId: number) => void) {
    this.gc.windowResizeCallback = callback;
  }
}
