import {BaseDeviceDescriptor} from './BaseDeviceDescriptor';

export default interface AndroidDeviceDescriptor extends BaseDeviceDescriptor {
    'ro.build.version.release': string;
    'ro.build.version.sdk': string;
    'ro.product.cpu.abi': string;
    'ro.product.manufacturer': string;
    'ro.product.marketname': string;
    'ro.product.model': string;
    'wifi.interface': string;
    'ro.product.vndk.version': string;
    screenWidth: number;
    screenHeight: number;
}
