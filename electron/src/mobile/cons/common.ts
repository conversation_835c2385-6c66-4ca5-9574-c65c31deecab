
export type MobilePlatform = 'Android' | 'IOS';

export interface MobileDto {
  code: string;
  connectType: ConnectType;
  name: string;
  platform: MobilePlatform;
  mode: string;
  androidVersion?: string;
  screenWidth?: number;
  screenHeight?: number;
}

export interface IOSDto  extends MobileDto {
  wdaBoundId?: string;
}

export type ConnectType = 'USB' | 'WIFI' | 'ARMCLOUD' | 'Baidu' | 'QCloud';
