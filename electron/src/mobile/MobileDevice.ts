import EventEmitter from "events";
import {ConnectType, MobileDto} from "@e/mobile/cons/common";
import request from "@e/services/request";
import logger from "@e/services/logger";
import {JpegPreviewService} from "@e/mobile/previewer";
import {JpegConsumer} from "@e/mobile/ios/consts";

export interface SnapshotProvider {

  snapshot:(scalingFactor: number)=>Promise<Buffer>;

}

export abstract class MobileDevice extends EventEmitter implements SnapshotProvider {

  udid: string;
  readonly connectType: ConnectType = 'USB';
  protected connected: boolean = false;

  previewService: JpegPreviewService;

  constructor(udid: string, connectType: ConnectType) {
    super();
    this.udid = udid;
    this.connectType = connectType;
    this.previewService = new JpegPreviewService(this);
  }

  public isConnected(): boolean {
    return this.connected;
  }

  /**
   * 声明当前的adb client正在使用，因为云手机长时间不使用需要断开连接
   * 当有人使用scrcpy视频流程或者有流程正在自动执行的时候会定时调用该函数
   */
  public kickInUse() {
    //默认什么也不做
  }

  public abstract toMobileDto(): Promise<MobileDto>;

  async reportState(status?: 'ONLINE' | 'OFFLINE') {
    status = status || (this.connected ? 'ONLINE' : 'OFFLINE');
    return request(`/api/shop/app_mobile/updateStatus`, {
      method: 'PUT',
      params: {
        code: this.udid,
        status,
      },
      forceHttp: true,
    });
  }

  public abstract textExtraction(teamId: number, translate?: boolean, lang?: string, mode?: string): Promise<string>;

  protected async translateTextExtraction(teamId: number, lang: string, mode: string, textNodes: any[]) {
    if(textNodes && textNodes.length > 0) {
      let prompt =
        '你现在是一个专业的翻译，我已经将手机界面上的文本处理成了一个json数组，数组的每个对象都有一个text字段，它可能是英语或其它任何语言。\n';
      prompt += `你需要输出一个字符串数组，数组的每一项都是这个对象text属性值翻译成"${lang}"的值，如果text本身就是"${lang}"请保持原样复制到输出的数组。\n`;
      prompt +=
        '另外有一个bounds属性是这段文本在屏幕上的位置，这意味着各段文本之间可能会存在某些联系，你翻译的时候需要考虑这些上下文联系。\n';
      prompt +=
        '注意注意你只需要输出翻译处理过后的字符串数组(不需要格式化)，不要markdown，也不要有其它任何文字，请保证输出后的数组与原数组长度一样且一一对应。\n\n';
      prompt += JSON.stringify(textNodes);
      let result = await request(`/api/shop/app_mobile/mobileAiChat`, {
        method: 'POST',
        teamId,
        data: {
          provider: 'openai',
          mode: 'gpt-4o-mini',
          prompts: [prompt],
        },
      });
      if (/```javascript([\s\S]*)```/m.test(result) || /```json([\s\S]*)```/m.test(result)) {
        result = RegExp.$1;
      }
      try {
        let translatedNodes = JSON.parse(result);
        for (let i = 0; i < translatedNodes.length; i++) {
          if (translatedNodes[i]) {
            textNodes[i].translation = translatedNodes[i];
          }
        }
      } catch (e) {
        logger.error(`[APP] 翻译结果解析失败`, result);
      }
    }
  }

  abstract snapshot(scalingFactor: number): Promise<Buffer>;

  private previewRef = 0;
  killPreviewTimer: any = 0;
  async allocJpegPreviewServer(clientId: number, consumer: JpegConsumer): Promise<any> {
    clearTimeout(this.killPreviewTimer);
    this.previewRef++;
    await this.previewService.start();
    this.previewService.subscribeJpeg(clientId, consumer);
  }

  async releaseJpegPreviewServer(clientId: number) {
    this.previewRef--;
    this.previewService.unSubscribeJpeg(clientId);
    if(this.previewRef == 0) {
      this.killPreviewTimer = setTimeout(()=>{
        this.previewService.stop();
      }, 5 * 1000);
    }
  }

}
