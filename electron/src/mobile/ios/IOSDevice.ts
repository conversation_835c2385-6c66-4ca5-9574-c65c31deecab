import {MobileDevice} from "@e/mobile/MobileDevice";
import {ConnectType, IOSDto} from "@e/mobile/cons/common";
import {IOSDriver} from "@e/mobile/ios/driver";
import {executeIOS} from "@e/mobile/ios/utils";
import path from "path";
import {MJpegServer} from "@e/mobile/ios/service/mjpeg";
import {extractIOSTextNodes} from "@e/mobile/android/util";
import logger from "@e/services/logger";
import {JpegConsumer} from "@e/mobile/ios/consts";
import request from "@e/services/request";
import Downloader from "nodejs-file-downloader";
import {getHyDb} from "@e/components/db";

const current_wda_version = '1.0';

export class IOSDevice extends MobileDevice {

  driver: IOSDriver;
  version!: string;
  name!: string;
  mJpegServer: MJpegServer;

  constructor(udid: string, connectType: ConnectType) {
    super(udid, connectType);
    this.driver = new IOSDriver(udid);
    this.mJpegServer = new MJpegServer(this.driver);
    this.driver.on('wda-ready', ()=>{
      this.updateGeoLocationOnConnect();
    });
  }

  /**
   * 如果依赖wda，在使用driver的时候必须用 connectDriver() 来获取
   */
  async connectDriver() {
    if(!this.driver.wda) {
      //需要重新创建，先检查一下wda版本是不是需要重新安装
      await this.tryInstallWDA();
    }
    await this.driver.create();
    this.kickInUse();
    return this.driver;
  }

  lastKickTime = 0;
  killTimer: any = 0;
  public kickInUse() {
    if(this.connected && Date.now() - this.lastKickTime > 10 * 1000) {
      this.lastKickTime = Date.now();
      clearTimeout(this.killTimer);
      this.killTimer = setTimeout(()=> this.disconnectIOS(), 5 * 60 * 1000);
    }
  }

  /**
   * 检查是不安装了WDA
   */
  async isWDAInstalled(): Promise<boolean> {
    let bound = await this.driver.getInstalledWDA();
    return !!bound;
  }

  /**
   * 尝试安装WDA，设备的udid必须添加到开发者列表
   * 打包 WDA.ipa 方法，在 WebDriverAgent 目录下执行命令：
   * xcodebuild build-for-testing -allowProvisioningDeviceRegistration -allowProvisioningUpdates -scheme WebDriverAgentRunner -sdk iphoneos -configuration Release -derivedDataPath /Users/<USER>/Downloads/wda/
   * cd /Users/<USER>/Downloads/wda/Build/Products/Release-iphoneos
   * mkdir Payload && mv *.app Payload
   * zip -r WDA.ipa Payload
   * cp WDA.ipa /Users/<USER>/Downloads/ios/wda.ipa
   */
  async tryInstallWDA() {
    let bound = await this.driver.getInstalledWDA();
    if(!bound || (bound.version < current_wda_version)) {
      let wdaUrl = await request(`/api/shop/mobile/getWDAUrl`, {
        method: 'GET',
        params: { udid: this.udid },
      });
      if(!wdaUrl) {
        throw '请将udid告知客服提交Apple白名单';
      }
      let extraDir = path.resolve(__dirname, '../extra');
      //下载最新的WDA文件
      await new Downloader({
        url: wdaUrl,
        fileName: 'WDA.ipa',
        cloneFiles: false,
        directory: extraDir,
        timeout: 5 * 60 * 1000,
        maxAttempts: 3,
      }).download();
      let ret = await executeIOS(this.udid, ['install', `--path=${path.resolve(extraDir, 'WDA.ipa')}`], '[WDA安装]');
      if(!(await this.driver.getInstalledWDA())) {
        return ret;
      }
    }
    return 'success';
  }

  lastReportTime = 0;
  setState(state: string) {
    let needReport = false;
    if(Date.now() - this.lastReportTime > 5 * 60 * 1000) { // 每5分钟强行汇报一次
      needReport = true;
    }
    if(state === 'device') {
      if(!this.connected) {
        this.connected = true;
        this.emit('online');
        needReport = true;
      }
    } else if(state === 'offline') {
      if(this.connected) {
        this.connected = false;
        this.driver.close();
        this.mJpegRef = 0;
        this.mJpegServer.stop();
        this.previewService.stop();
        this.emit('offline');
        needReport = true;
      }
    }
    if(needReport) {
      this.lastReportTime = Date.now();
      this.reportState();
    }
  }

  async runIosCommand(script: string[]): Promise<string> {
    await this.connectDriver();
    if(script.length == 3 && 'setlocation' == script[0]) {
      //设置时区，需要记下来
      await this.saveGelLocation(script);
      return 'success';
    }
    return await executeIOS(this.udid, script);
  }

  private async saveGelLocation(script: string[]) {
    if(!await this.isDevelopMode()) {
      throw '当前手机未开启开发者模式，无法模拟位置';
    }
    let lat = '', lon = '';
    for(let param  of script) {
      if(param?.indexOf('--lat=') != -1) {
        lat = param.split('=')[1];
      }
      if(param?.indexOf('--lon=') != -1) {
        lon = param.split('=')[1];
      }
    }
    let hyDb = getHyDb();
    if(lat && lon && hyDb) {
      try {
        await this.driver.setSimulatedLocation(lat, lon);
        const stmt = getHyDb()?.conn.prepare(
          `SELECT * FROM ios_geo_location WHERE udid=?`,
        );
        const row = stmt?.get(this.udid) as any;
        if (row?.id > 0) {
          const updateStmt = getHyDb()?.conn.prepare(
            `UPDATE ios_geo_location SET lat=?, lon=? WHERE udid=?`,
          );
          updateStmt?.run(lat, lon, this.udid);
        } else {
          const insertStmt = getHyDb()?.conn.prepare(
            `INSERT INTO ios_geo_location (udid, lat, lon) VALUES (?, ?, ?)`,
          );
          insertStmt?.run(this.udid, lat, lon);
        }
        return true;
      } catch (e) {
        logger.error('[APP] update ios geo location failed', e);
        return false;
      }
    }
  }

  private async updateGeoLocationOnConnect() {
    let hyDb = getHyDb();
    if(hyDb) {
      try {
        const stmt = getHyDb()?.conn.prepare(
          `SELECT * FROM ios_geo_location WHERE udid=?`,
        );
        const row = stmt?.get(this.udid) as any;
        if (row?.id > 0) {
          await this.driver.setSimulatedLocation(row.lat, row.lon);
        }
        return true;
      } catch (e: any) {
        logger.error('[APP] update ios geo location failed', e);
        return false;
      }
    }
  }

  public async toMobileDto(): Promise<IOSDto> {
    let result: any = {
      code: this.udid,
      connectType: 'USB',
      name: this.name,
      platform: 'IOS',
      mode: '',
      androidVersion: this.version,
      screenWidth: 0,
      screenHeight: 0,
      wdaBoundId: undefined,
    };
    if(!this.isConnected() || !(await this.isWDAInstalled())) {
      return result;
    }
    try {
      let driver = await this.connectDriver();
      let info = await driver.deviceInfo();
      let bound = await driver.getWindowSize();
      return {
        code: this.udid,
        connectType: 'USB',
        name: info.DeviceName,
        platform: 'IOS',
        mode: info.SerialNumber,
        androidVersion: info.ProductVersion,
        screenWidth: bound.width,
        screenHeight: bound.height,
        wdaBoundId: driver.wdaBoundId
      };
    } catch (e) {
      logger.error(e);
      return result;
    }
  }

  async disconnectIOS() {
    this.mJpegServer.stop();
    this.mJpegRef = 0;
    await this.driver.close();
  }

  private mJpegRef = 0; //当前有多少客户端连着
  killMjpegTimer: any = 0;
  async allocMjpegServer(clientId: number, consumer: JpegConsumer): Promise<any> {
    clearTimeout(this.killMjpegTimer);
    this.mJpegRef++;
    await this.connectDriver();
    await this.mJpegServer.start();
    this.mJpegServer.subscribeJpeg(clientId, consumer);
  }

  async releaseMjpegServer(clientId: number) {
    this.mJpegRef = Math.max(0, this.mJpegRef - 1);
    this.mJpegServer.unSubscribeJpeg(clientId);
    if(this.mJpegRef == 0) {
      this.killMjpegTimer = setTimeout(()=>{
        this.mJpegServer.stop();
      }, 5 * 1000);
    }
  }

  async allocJpegPreviewServer(clientId: number, consumer: JpegConsumer): Promise<any> {
    await this.connectDriver();
    return super.allocJpegPreviewServer(clientId, consumer);
  }

  async updateMjpegSettings(scalingFactor = 50, mjpegFps = 10, smallPreviewDuration = 5) {
    smallPreviewDuration = Math.max(smallPreviewDuration, 3);
    this.previewService.duration = smallPreviewDuration * 1000;
    this.previewService.kick();
    if(mjpegFps > 1) {
      let driver = await this.connectDriver();
      await driver.updateMjpegSettings(scalingFactor, mjpegFps);
    }
  }

  async rotateScreen() {
    let driver = await this.connectDriver();
    let orientation = await driver.getRotation();
    try {
      if (orientation === 0) {
        await driver.setRotation(270);
      } else {
        await driver.setRotation(0);
      }
    } catch (e) {
      logger.error('[APP] 设置手机屏幕旋转失败', e);
    }
    return await driver.getRotation();
  }

  async clearSimulatedLocation() {
    let driver = await this.connectDriver();
    try {
      await driver.clearSimulatedLocation();
    } catch (e) {
      logger.error('[APP] 重置地理位置失败', e);
    }
  }

  async wdaSend(method: string, path: string, data?: any) {
    let driver = await this.connectDriver();
    return await driver.wda.send(method, path, data);
  }

  public async textExtraction(teamId: number, translate = true, lang = '中文', mode = 'chatgpt'): Promise<string> {
    let xml = '';
    let textNodes = [];
    try {
      let driver = await this.connectDriver();

      let app = await driver.currentApp();
      let dumpDepth = 20;
      switch(app.bundleId) {
        case 'com.zhiliaoapp.musically':
        case 'com.ss.iphone.ugc.Aweme':
          //tk 系软件层级太深会卡死
          dumpDepth = 15;
          break;
      }
      let depth = driver.snapshotMaxDepth;
      await driver.setLayoutDepth(dumpDepth);
      xml = await driver.pageSource();
      await driver.setLayoutDepth(depth);
      textNodes = extractIOSTextNodes(xml);
      if (translate) {
        //需要翻译
        await this.translateTextExtraction(teamId, lang, mode, textNodes);
      }
      return JSON.stringify(textNodes);
    } catch (e) {
      logger.error(`[APP] 提取文本失败`, e, xml, textNodes);
      throw new Error('提取文本失败');
    }
  }

  async snapshot(scalingFactor: number): Promise<Buffer> {
    let driver = await this.connectDriver();
    let jpeg = await driver.screenshot(50);
    return jpeg;
  }

  /**
   * 是不开启了开发者模式
   */
  async isDevelopMode(): Promise<boolean> {
    let output = await executeIOS(this.udid, ['devmode', 'get']);
    return output.indexOf('Developer mode enabled: true') != -1;
  }

}
