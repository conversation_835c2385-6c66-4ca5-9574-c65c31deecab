
//mjpeg服务的图像缩放率，WDA默认是100，但流量有些大。设置成50，会影响清晰度
export const MJPEG_SCALING_FACTOR = 25;
export const MJPEG_FRAMERATE = 1;

export class Hid_Pages {
  public static PAGE_KEYBOARD = 0x07;
  public static PAGE_BUTTON = 0x0C; //手机的物理按键
}

export interface HidEvent {
  key: string;
  duration: number;
}

export interface JpegConsumer {
  isSmallPreview: ()=>boolean;
  handleJpegData: ((data: any)=>void);
  isConnected: ()=>boolean;
}

export const Hid_KB_Usages: Record<string, number> = {
  Backspace : 0x2A,
  Enter : 0x28,
  ArrowDown: 0x51,
  ArrowUp: 0x52,
  ArrowLeft : 0x50,
  ArrowRight : 0x4F,
}

export const Hid_Button_Usages: Record<string, number> = {
  home: 0x40,
  volumeUp: 0xE9,
  volumeDown: 0xEA,
  power: 0x30,
  snapshot: 0x65,
  "power+home": 0x65
}

export const XCUIElementTypes: Record<string, number> = {
  XCUIElementTypeAny: 0,
  XCUIElementTypeOther: 1,
  XCUIElementTypeApplication: 2,
  XCUIElementTypeGroup: 3,
  XCUIElementTypeWindow: 4,
  XCUIElementTypeSheet: 5,
  XCUIElementTypeDrawer: 6,
  XCUIElementTypeAlert: 7,
  XCUIElementTypeDialog: 8,
  XCUIElementTypeButton: 9,
  XCUIElementTypeRadioButton: 10,
  XCUIElementTypeRadioGroup: 11,
  XCUIElementTypeCheckBox: 12,
  XCUIElementTypeDisclosureTriangle: 13,
  XCUIElementTypePopUpButton: 14,
  XCUIElementTypeComboBox: 15,
  XCUIElementTypeMenuButton: 16,
  XCUIElementTypeToolbarButton: 17,
  XCUIElementTypePopover: 18,
  XCUIElementTypeKeyboard: 19,
  XCUIElementTypeKey: 20,
  XCUIElementTypeNavigationBar: 21,
  XCUIElementTypeTabBar: 22,
  XCUIElementTypeTabGroup: 23,
  XCUIElementTypeToolbar: 24,
  XCUIElementTypeStatusBar: 25,
  XCUIElementTypeTable: 26,
  XCUIElementTypeTableRow: 27,
  XCUIElementTypeTableColumn: 28,
  XCUIElementTypeOutline: 29,
  XCUIElementTypeOutlineRow: 30,
  XCUIElementTypeBrowser: 31,
  XCUIElementTypeCollectionView: 32,
  XCUIElementTypeSlider: 33,
  XCUIElementTypePageIndicator: 34,
  XCUIElementTypeProgressIndicator: 35,
  XCUIElementTypeActivityIndicator: 36,
  XCUIElementTypeSegmentedControl: 37,
  XCUIElementTypePicker: 38,
  XCUIElementTypePickerWheel: 39,
  XCUIElementTypeSwitch: 40,
  XCUIElementTypeToggle: 41,
  XCUIElementTypeLink: 42,
  XCUIElementTypeImage: 43,
  XCUIElementTypeIcon: 44,
  XCUIElementTypeSearchField: 45,
  XCUIElementTypeScrollView: 46,
  XCUIElementTypeScrollBar: 47,
  XCUIElementTypeStaticText: 48,
  XCUIElementTypeTextField: 49,
  XCUIElementTypeSecureTextField: 50,
  XCUIElementTypeDatePicker: 51,
  XCUIElementTypeTextView: 52,
  XCUIElementTypeMenu: 53,
  XCUIElementTypeMenuItem: 54,
  XCUIElementTypeMenuBar: 55,
  XCUIElementTypeMenuBarItem: 56,
  XCUIElementTypeMap: 57,
  XCUIElementTypeWebView: 58,
  XCUIElementTypeIncrementArrow: 59,
  XCUIElementTypeDecrementArrow: 60,
  XCUIElementTypeTimeline: 61,
  XCUIElementTypeRatingIndicator: 62,
  XCUIElementTypeValueIndicator: 63,
  XCUIElementTypeSplitGroup: 64,
  XCUIElementTypeSplitter: 65,
  XCUIElementTypeRelevanceIndicator: 66,
  XCUIElementTypeColorWell: 67,
  XCUIElementTypeHelpTag: 68,
  XCUIElementTypeMatte: 69,
  XCUIElementTypeDockItem: 70,
  XCUIElementTypeRuler: 71,
  XCUIElementTypeRulerMarker: 72,
  XCUIElementTypeGrid: 73,
  XCUIElementTypeLevelIndicator: 74,
  XCUIElementTypeCell: 75,
  XCUIElementTypeLayoutArea: 76,
  XCUIElementTypeLayoutItem: 77,
  XCUIElementTypeHandle: 78,
  XCUIElementTypeStepper: 79,
  XCUIElementTypeTab: 80,
  XCUIElementTypeTouchBar: 81,
  XCUIElementTypeStatusItem: 82,
}
