import {spawn} from "child_process";
import logger from "@e/services/logger";
import path from "path";
import {isWinPlatform} from "@e/utils/utils";

export function goIosPath() {
  const go_ios = path.resolve(__dirname, '../extra', isWinPlatform() ? 'ios' + '.exe' : 'ios');
  return go_ios;
}

export async function executeIOS(udid: string, args: string[], logTag?: string) {
  const go_ios = goIosPath()
  args = args || [];
  if(udid) {
    args = [`--udid=${udid}`].concat(args);
  }
  let output = await new Promise<string>((resolve, reject) => {
    const ios = spawn(go_ios, args, { stdio: ['ignore', 'pipe', 'pipe'] });
    let output = '';

    ios.stdout.on('data', (data) => {
      output += data.toString();
      if(logTag) {
        logger.log(logTag, `stdout: ${data.toString().replace(/\n$/, '')}`);
      }
    });

    ios.stderr.on('data', (data) => {
      if(logTag) {
        logger.error(logTag, `stderr: ${data.toString()}`);
      }
    });

    ios.on('error', (error: Error) => {
      if(logTag) {
        logger.error(logTag, `failed to spawn adb process.\n${error.stack}`);
      }
      reject(error);
    });

    ios.on('close', (code) => {
      if(logTag) {
        logger.log(logTag, `process (${args.join(' ')}) exited with code ${code}`);
      }
      resolve(output);
    });
  });
  let lines = output.split('\n');
  let stdout = '';
  for(let line of lines) {
    try {
      let obj = JSON.parse(line);
      if(obj.level) {//说明是日志
        continue;
      }
      return obj;
    } catch (ignore) {
      stdout += line + '\n';
    }
  }
  return stdout;
}

export function parseElementId(value: any) {
  return value['ELEMENT'] || value['element-6066-11e4-a52e-4f735466cecf']
}
