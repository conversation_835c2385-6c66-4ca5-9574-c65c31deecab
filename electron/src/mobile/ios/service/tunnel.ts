import {waitSeconds} from "@e/rpa/utils";
import {goIosPath} from "@e/mobile/ios/utils";
import logger from "@e/services/logger";
import {spawn} from "child_process";
import axios from "axios";

//ios 17 以上版本，需要启动tunnel服务
//ios tunnel start --userspace
export class IOSTunnelService {

  tunnelProcess: any;

  async running() {
    if(this.tunnelProcess && !this.tunnelProcess.killed) {
      try {
        let tunnel = await axios.get(`http://127.0.0.1:60105/tunnels`);
        if(Array.isArray(tunnel?.data)) {
          return true;
        }
      } catch (e) {
        return false;
      }
    }
    return false;
  };

  async start() {
    if(await this.running()) {
      return;
    }
    try {
      const go_ios = goIosPath()
      this.tunnelProcess = spawn(go_ios, ['tunnel', 'start', '--userspace', '--nojson']);
      await new Promise((resolve, reject)=>{
        let onData = (data: any) => {
          if(data?.toString().indexOf('Tunnel server started') != -1) {
            this.tunnelProcess.stdout.removeAllListeners('data');
            this.tunnelProcess.stderr.removeAllListeners('data');
            resolve();
          }
        }
        this.tunnelProcess.stdout.on('data', onData);
        this.tunnelProcess.stderr.on('data', onData);
        setTimeout(()=>{
          reject('start tunnel failed');
        }, 10000);
      });
      await waitSeconds(1, false);
      logger.info(`start tunnel success`);
    } catch (e) {
      logger.error(`start tunnel failed`, e);
    }
  }

  async stop() {
    if(this.tunnelProcess) {
      this.tunnelProcess.kill();
      this.tunnelProcess = undefined;
    }
  }

}
