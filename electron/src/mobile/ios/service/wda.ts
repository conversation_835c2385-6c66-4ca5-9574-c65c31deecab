import axios from 'axios';
import {Buffer} from "buffer";
import {TouchActions} from "@e/mobile/ios/touch";
import {waitMilliseconds} from "@e/rpa/utils";

/**
 * 实现wda协议，具体协议没找到易读的文档，可参考下面的实现
 * @see https://github.com/openatx/facebook-wda/blob/master/wda/__init__.py
 * @see https://github.com/appium/WebDriverAgent/blob/master/WebDriverAgentLib/Commands/
 */
export class WDAClient {

  url: string;
  sessionId?: string;

  constructor(url: string) {
    this.url = url;
  }

  async createSession() {
    let sessionInfo = await this.sendPost('/session', {
      capabilities: {}
    });
    this.sessionId = sessionInfo.sessionId;
  }

  async closeSession() {
    try {
      await this.sessionDelete(`/session/${this.sessionId}`, null);
    } catch (ignore: any) {}
  }

  getSessionId() {
    return this.sessionId;
  }

  async sessionGet(path: string) {
    await this.checkSession();
    try {
      return await this.sendGet(`/session/${this.sessionId}${path}`);
    } catch (e: any) {
      if('Session does not exist' == e || (e?.response?.status == 404 && 'invalid session id' == e?.response?.data?.value?.error)) {
        await this.closeSession();
        await this.createSession();
        await waitMilliseconds(100, false);
        return this.sendGet(`/session/${this.sessionId}${path}`);
      } else {
        throw e;
      }
    }
  }

  async sessionPost(path: string, data?: any) {
    await this.checkSession();
    try {
      return await this.sendPost(`/session/${this.sessionId}${path}`, data);
    } catch (e: any) {
      if('Session does not exist' == e || (e?.response?.status == 404 && 'invalid session id' == e?.response?.data?.value?.error)) {
        await this.closeSession();
        await this.createSession();
        await waitMilliseconds(100, false);
        return await this.sendPost(`/session/${this.sessionId}${path}`, data);
      } else {
        throw e;
      }
    }
  }

  private async sessionDelete(path: string, data: any) {
    await this.send('DELETE', path, data);
  }

  async getRotation() {
    let ret = await this.sendGet(`/rotation`);
    let z = ret.value.z;
    if(z == 0) {
    } else if(z == 90) {
      return 1;
    } else if(z == 180) {
      return 2;
    } else if(z == 270) {
      return 3;
    }
    return 0;
  }

  async setRotation(deg: number = 0) {
    let _deg = 0;
    if ([0, 90, 180, 270].includes(deg)) {
      _deg = deg;
    }
    await this.sendPost(`/rotation`, {
      x: 0, y: 0, z: _deg
    });
  }

  async getWindowSize(): Promise<{width: number, height: number, scale: number}> {
    let ret = await this.sendGet(`/wda/screen`);
    return {
      scale: ret.value.scale,
      width: ret.value.screenSize.width,
      height: ret.value.screenSize.height,
    };
  }

  async isLocked(): Promise<boolean> {
    let res = await this.sessionGet(`/wda/locked`);
    return res.value as boolean;
  }

  async lock() {
    await this.sessionPost(`/wda/lock`, null);
  }

  async unlock() {
    await this.sessionPost(`/wda/unlock`, null);
  }

  async pressButton(button: string) {
    if(["home", "volumeUp", "volumeDown"].includes(button)) {
      await this.sessionPost(`/wda/pressButton`, {
        name: button
      });
    } else {
      throw new Error(`unknown button: ${button}`);
    }
  }

  async performTouchActions(action: TouchActions) {
    await this.sessionPost('/actions', action);
  }

  async tap(x: number, y: number) {
    try {
      await this.sendPost('/wda/tap0', {x, y}); //新加的接口，更快速，但老版本的WDA没有
    } catch (_) {
      await this.sessionPost(`/wda/tap`, {x, y});
    }
  }

  async doubleTap(x: number, y: number) {
    await this.sessionPost(`/wda/doubleTap`, {x, y});
  }

  async sendKeys(text: string, frequency: number) {
    if(text) {
      await this.sessionPost(`/wda/keys`, {
        value: (text+'').split(''),
        frequency: frequency
      });
    }
  }

  async performIoHidEvent(page: number, usage: number, duration = 0.05) {
    await this.sessionPost(`/wda/performIoHidEvent`, {
      page: page,
      usage: usage,
      duration: duration
    });
  }

  async setClipboard(content: string, contentType: string = 'plaintext') {
    await this.sessionPost(`/wda/setPasteboard`, {
      contentType: contentType,
      content: Buffer.from(content).toString('base64')
    });
  }

  async getClipboard(contentType: string = 'plaintext') {
    let ret = await this.sessionPost(`/wda/getPasteboard`, {
      contentType: contentType
    });
    if(ret.value) {
      return Buffer.from(ret.value, 'base64').toString('utf-8');
    }
    return '';
  }

  async pageSource() {
    let ret = await this.sendGet(`/source`);
    return ret.value;
  }

  async accessibleSource(): Promise<string> {
    let ret = await this.sendGet(`/wda/accessibleSource`);
    return ret.value;
  }

  async screenshot(scalingFactor = 100) {
    scalingFactor = (scalingFactor / 100) || 1;
    let ret = await this.sendGet(`/screenshot?scalingFactor=${scalingFactor}`);
    return Buffer.from(ret.value, 'base64');
  }

  async swipe(fromX: number, fromY: number, toX: number, toY: number, duration=1) {
    if(duration <= 0) {
      duration = 1;
    }
    let touch = new TouchActions();
    touch.finger('0').press(fromX, fromY).wait(duration * 1000 * 0.9).move(toX-1, toY-1).wait(duration * 1000 * 0.1).move(toX, toY).release();
    await this.performTouchActions(touch);
    // let maxSwipeDistance = Math.max(Math.abs(toX - fromX), Math.abs(toY - fromY));
    // let velocity = maxSwipeDistance / duration * 1000;
    // // velocity 单位为像素/秒，这里做个限制，如果超过1000则滑动的过快，很容易触发fling行为
    // velocity = Math.min(velocity, 1000);
    // let data = {
    //   fromX, fromY, toX, toY, duration, velocity
    // }
    // await this.sessionPost('/wda/pressAndDragWithVelocity', data);
  }

  async swipePercent(fromX: number, fromY: number, toX: number, toY: number, duration=1) {
    if(duration <= 0) {
      duration = 1;
    }
    fromX = Math.max(0, Math.min(1, fromX));
    fromY = Math.max(0, Math.min(1, fromY));
    toX = Math.max(0, Math.min(1, toX));
    toY = Math.max(0, Math.min(1, toY));
    let bounds = await this.getWindowSize();
    fromX = fromX * bounds.width;
    fromY = fromY * bounds.height;
    toX = toX * bounds.width;
    toY = toY * bounds.height;
    await this.swipe(fromX, fromY, toX, toY, duration);
  }

  async appLaunch(bundleId: string, args: string[] =[], env: any ={}) {
    if(!bundleId || bundleId.length == 0) {
      throw 'bundleId is null';
    }
    let data: any = {
      bundleId,
    }
    if(args && args.length > 0) {
      data['arguments'] = args;
    }
    if(env && Object.keys(env).length > 0) {
      data['environment'] = env;
    }
    await this.sessionPost('/wda/apps/launch', data);
  }

  async appTerminate(bundleId: string) {
    if(!bundleId || bundleId.length == 0) {
      throw 'bundleId is null';
    }
    await this.sessionPost('/wda/apps/terminate', { bundleId })
  }

  async currentApp() {
    let ret = await this.sessionGet('/wda/activeAppInfo');
    return ret.value;
  }

  async status() {
    return this.sendGet('/status');
  }

  async appiumSettings(settings: any) {
    return this.sessionPost('/appium/settings', {
      settings: settings
    });
  }

  async send(method: string, path: string, data?: any): Promise<any> {
    let url = this.url + path;
    let body = '';
    if(data) {
      body = JSON.stringify(data);
    }
    return await new Promise((resolve, reject) => {
      axios.request({
          url: url,
          method: method || 'GET',
          data: body,
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        })
        .then((res) => {
          if(res.status == 200) {
            resolve(res.data);
          } else {
            reject(res.statusText);
          }
        })
        .catch((e) => {
          reject(e?.response?.data?.value?.message || e.message || e);
        });
    });
  }

  async sendGet(path: string): Promise<any> {
    return this.send('GET', path);
  }

  async sendPost(path: string, data: any): Promise<any> {
    return this.send('POST', path, data);
  }

  private async checkSession() {
    if(!this.sessionId) {
      await this.createSession();
    }
  }

}
