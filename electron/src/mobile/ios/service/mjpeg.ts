import {IOSDriver} from "@e/mobile/ios/driver";

import axios from "axios";
import {Writable} from "stream";
import {<PERSON><PERSON><PERSON>} from "buffer";
import {JpegConsumer} from "@e/mobile/ios/consts";

const MjpegConsumer = require('./mjpeg_reader.js');

export class MJpegServer {

  mjpegPort!: number;
  driver: IOSDriver;

  running = false;

  lastFullJpeg?: Buffer;
  mjpegPiper?: any;

  callbacks: Map<number, JpegConsumer> = new Map();

  constructor(driver: IOSDriver) {
    this.driver = driver;
  }

  subscribeJpeg(clientId: number, consumer: JpegConsumer) {
    this.callbacks.set(clientId, consumer);
    if(this.lastFullJpeg) {
      try {
        consumer.handleJpegData(this.lastFullJpeg);
      } catch (ignore: any) {}
    }
  }

  unSubscribeJpeg(clientId: number) {
    this.callbacks.delete(clientId);
  }

  async start(): Promise<any> {
    if(this.running) {
      return;
    }
    return this.pipeJpegServer();
  }

  async pipeJpegServer() {
    try {
      let that = this;
      this.running = true;
      const response = await axios.get(this.driver.mjpegUrl(), { responseType: 'stream' });
      this.mjpegPiper = response.data;
      let writable = new Writable({
        write(chunk, encoding, callback) {
          let isFull = chunk.readUInt8();
          if(isFull) {
            that.lastFullJpeg = chunk.slice(1);
          }
          for(let [clientId, consumer] of that.callbacks) {
            try {
              consumer.handleJpegData(chunk.slice(1));
            } catch (ignore: any) {}
          }
          callback();
        }
      });
      writable.on('finish', () => {
        this.onJpegEnd();
      });
      this.mjpegPiper.pipe(new MjpegConsumer()).pipe(writable);
    } catch (error: any) {
      this.onJpegEnd();
    }
  }

  reconnectTimer: any = 0;
  private async onJpegEnd() {
    if(this.running) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = setTimeout(()=>{
        this.pipeJpegServer();
      }, 1000);
    }
  }

  stop() {
    this.running = false;
    this.lastFullJpeg = undefined;
    this.callbacks.clear();
    this.mjpegPiper?.unpipe();
    this.mjpegPiper?.destroy();
    this.mjpegPiper = undefined;
  }
  logTag(): string {
    return `[ios:${this.driver.udid}]`;
  }

}
