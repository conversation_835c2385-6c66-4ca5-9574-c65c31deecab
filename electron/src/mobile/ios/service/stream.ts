/**
 * 提供类似于android scrcpy功能的服务
 */
import WebSocket from "ws";
import {IOSDevice} from "@e/mobile/ios/IOSDevice";
import logger from '@e/services/logger';
import {TouchActions} from "@e/mobile/ios/touch";
import {Hid_Button_Usages, Hid_KB_Usages, Hid_Pages, HidEvent, JpegConsumer} from "@e/mobile/ios/consts";
import {waitMilliseconds, waitPromise} from "@e/rpa/utils";
import {getAFreePort} from "@e/utils/utils";
import {AbstractStreamer} from "@e/mobile/abstract_streamer";

const ACTION_DOWN: number = 0;
const ACTION_UP: number = 1;
const ACTION_MOVE = 2;

const TYPE_CLIPBOARD = 0;
const TYPE_PUSH_RESPONSE = 101;

const TYPE_KEYCODE = 0;
const TYPE_TEXT = 1;
const TYPE_TOUCH = 2;
const TYPE_SCROLL = 3;
const TYPE_BACK_OR_SCREEN_ON = 4;
const TYPE_EXPAND_NOTIFICATION_PANEL = 5;
const TYPE_EXPAND_SETTINGS_PANEL = 6;
const TYPE_COLLAPSE_PANELS = 7;
const TYPE_GET_CLIPBOARD = 8;
const TYPE_SET_CLIPBOARD = 9;
const TYPE_SET_SCREEN_POWER_MODE = 10;
const TYPE_ROTATE_DEVICE = 11;
const TYPE_CHANGE_STREAM_PARAMETERS = 101;
const TYPE_PUSH_FILE = 102;
const TYPE_KEY_NAME = 20;

const magic_initial = Buffer.from('scrcpy_initial');
const magic_message = Buffer.from('scrcpy_message');

export class IOSStreamer extends AbstractStreamer implements JpegConsumer{
  device: IOSDevice;
  controller: Controller;

  constructor(ws: WebSocket, device: IOSDevice, smallPreview: boolean) {
    super(ws, smallPreview);
    this.device = device;
    this.controller = new Controller(ws, device);
  }

  isSmallPreview() {
    return this.smallPreview;
  }

  isConnected() {
    return this.ws.readyState <= WebSocket.OPEN;
  }

  onWSConnected() {
    if(this.ws.readyState === WebSocket.OPEN) {
      (async()=>{
        await this.prepareMjpegServer();
        //send init message
        let driver = await this.device.connectDriver();
        let bounds = await driver.getWindowSize();
        await this.sendInitialInfo(bounds);
      })();
    } else {
      this.ws.on('open', async ()=>{
        await this.prepareMjpegServer();
        //send init message
        let driver = await this.device.connectDriver();
        let bounds = await driver.getWindowSize();
        await this.sendInitialInfo(bounds);
      });
    }
    this.ws.on('message', (data: Buffer)=>{
      this.controller.handleControlMessage(data);
    });
    this.ws.on('error', ()=>{
      this.close();
    });
    this.ws.on('close', ()=>{
      this.close();
    });
  }

  private async prepareMjpegServer() {
    if(this.smallPreview) {
      await this.device.allocJpegPreviewServer(this.clientId, this);
    } else {
      await this.device.allocMjpegServer(this.clientId, this);
    }
  }

  public handleJpegData(data: Buffer) {
    this.ws.send(data);
  }

  closed = false;
  close() {
    if(!this.closed) {
      this.closed = true;
      if(this.smallPreview) {
        this.device.releaseJpegPreviewServer(this.clientId).catch(()=>{});
      } else {
        this.device.releaseMjpegServer(this.clientId).catch(()=>{});
      }
    }
  }

}

/**
 * 事件控制器
 * todo 实现参考
 * @see https://github.com/SonicCloudOrg/sonic-client-web/blob/main/src/views/RemoteEmulator/IOSRemote.vue
 * @see https://github.com/SonicCloudOrg/sonic-agent/blob/main/src/main/java/org/cloud/sonic/agent/websockets/IOSWSServer.java
 */
export class Controller {

  ws: WebSocket;
  device: IOSDevice;

  constructor(ws: WebSocket, device: IOSDevice) {
    this.ws = ws;
    this.device = device;
  }

  async handleControlMessage(data: Buffer) {
    let off = 0;
    let type = data.readUint8(off++);
    let resp: Buffer | undefined;
    switch (type) {
      case TYPE_KEY_NAME:
        await this.handleKeyName(data, off);
        break;
      case TYPE_TEXT:
        await this.handleText(data, off);
        break;
      case TYPE_GET_CLIPBOARD:
        resp = await this.handleGetClipboard(data, off);
        break;
      case TYPE_TOUCH:
        await this.handleTouch(data, off);
        break;
    }
    if(resp) {
      this.ws.send(resp);
    }
  }

  keyQueue: HidEvent[] = [];
  async handleKeyName(data: Buffer, off: number) {
    let action = data.readUint8(off); off += 1;
    let len = data.readUint8(off); off += 1;
    let key = data.toString('utf8', off, off + len); off += len;
    let duration  = data.readUint16BE(off); off += 2;
    if(action == ACTION_DOWN) {
      this.keyQueue.push({key, duration});
      this.flushKeyNames();
    }
  }
  keyFlushing = false;
  async flushKeyNames() {
    if(this.keyFlushing) {
      return;
    }
    this.keyFlushing = true;
    try {
      let driver;
      let hid = this.keyQueue.shift();
      while(hid) {
        driver = driver || (await this.device.connectDriver());
        let char = hid.key;
        if(char.length === 1) {
          while(this.keyQueue[0]?.key?.length == 1) {
            char += this.keyQueue.shift()?.key;
          }
          await driver.sendKeys(char!, 0);
        } else {
          // let duration = hid.duration || undefined;
          // if(duration) {
          //   duration = duration / 1000;
          // }
          if(Hid_KB_Usages[char]) {
            await driver.performIoHidEvent(Hid_Pages.PAGE_KEYBOARD, Hid_KB_Usages[char]);
          } else if(Hid_Button_Usages[char]) {
            await driver.performIoHidEvent(Hid_Pages.PAGE_BUTTON, Hid_Button_Usages[char]);
          }
        }

        hid = this.keyQueue.shift();
      }
    } finally {
      this.keyFlushing = false;
    }
  }

  async handleText(data: Buffer, off: number) {
    let length = data.readInt32BE(off);
    off += 4;
    if(length > 0) {
      let text = data.toString('utf8', off, off + length);
      let driver = await this.device.connectDriver();
      await driver.sendKeys(text);
    }
  }

  async handleGetClipboard(_data: Buffer, _off: number) {
    //https://appium.github.io/appium.io/docs/en/commands/device/clipboard/get-clipboard/
    //(For iOS 13+ real devices) Apple security preferences require the WebDriverAgentRunner application to be in foreground in order...

    let driver = await this.device.connectDriver();
    let text = await driver.getClipboard();

    let bytes = Buffer.from(text || '');
    let off = 0;
    let resp = Buffer.alloc(magic_message.length + 1 + 4 + bytes.length);
    off += magic_message.copy(resp, off);
    off = resp.writeUint8(TYPE_CLIPBOARD, off);
    off = resp.writeInt32BE(bytes.length, off);
    off += bytes.copy(resp, off);
    return resp;
  }

  touchEvents = [] as any[];//网页发过来的原始touch事件
  async handleTouch(data: Buffer, off: number) {
    let action = data.readUint8(off); off += 1;
    off += 4; //pointerId 占了8个字节，但只用到4个
    let pointerId = data.readInt32BE(off); off += 4;
    let x = data.readInt32BE(off); off += 4;
    let y = data.readInt32BE(off); off += 4;
    let w = data.readInt16BE(off); off += 2;
    let h = data.readInt16BE(off); off += 2;
    let pressure = data.readInt16BE(off); off += 2;
    let buttons = data.readInt32BE(off); off += 4;
    this.touchEvents.push({ action, name: pointerId, x, y, w, h, pressure, buttons, t: Date.now() });
    if(action === ACTION_UP) {
      this.packageTouchEvents();
    }
  }

  private packageTouchEvents() {
    if(this.touchQueue.length > 0) {
      //不要在队列里留太多的事件
      this.touchEvents = [];
      return;
    }
    let touchEvents = this.trimAndReduceTouchEvents(this.touchEvents);
    this.touchEvents = [];
    if(touchEvents.length > 0) {
      let touch = new TouchActions();
      let last: any = {};
      let lastMoveTime = 0;
      let x = 0, y = 0;
      for(let event of touchEvents) {
        let finger = touch.finger(event.name);
        if(lastMoveTime != 0 && event.action == ACTION_MOVE && event.t - lastMoveTime < 30) {
          continue;
        }
        if(last[finger.id]) {
          finger.wait(event.t - last[finger.id].t);
        }
        switch (event.action) {
          case ACTION_DOWN:
            finger.press(event.x, event.y);
            x = event.x;
            y = event.y;
            break;
          case ACTION_MOVE:
            lastMoveTime  = event.t;
            finger.move(event.x, event.y);
            break;
          case ACTION_UP:
            if(x != event.x || y != event.y) {
              finger.move(event.x, event.y);
            }
            finger.release();
            break;
        }
        last[finger.id] = event;
      }
      this.touchQueue.push(touch);
      this.flushTouch();
    }
  }

  touchQueue: any[] = [];
  touchFlushing = false;
  private async flushTouch() {
    if(this.touchFlushing) {
      return;
    }
    this.touchFlushing = true;
    try {
      let driver;
      let touch = this.touchQueue.shift();
      while(touch) {
        driver = driver || (await this.device.connectDriver());
        await waitPromise(driver.performTouchActions(touch), 2000);
        // console.log('touch事件已经发送...: ' + JSON.stringify(touch));
        touch = this.touchQueue.shift();
        if(touch) {
          await waitMilliseconds(100, false);
        }
      }
    } catch (e: any) {
      console.error(e);
    } finally {
      this.touchFlushing = false;
    }
  }
  //预处理事件，减少move事件个数
  private trimAndReduceTouchEvents(events: any) {
    const ACTION_DOWN = 0;
    const ACTION_UP = 1;
    const ACTION_MOVE = 2;

    const firstDownIndex = events.findIndex((e: any) => e.action === ACTION_DOWN);
    if (firstDownIndex === -1) return [];

    let lastUpIndex = -1;
    for (let i = events.length - 1; i >= 0; i--) {
      if (events[i].action === ACTION_UP) {
        lastUpIndex = i;
        break;
      }
    }

    if (lastUpIndex === -1 || lastUpIndex <= firstDownIndex) return [];

    const sliced = events.slice(firstDownIndex, lastUpIndex + 1);

    const result = [];
    let moveEvents = [];

    for (let i = 0; i < sliced.length; i++) {
      const e = sliced[i];
      if (e.action === ACTION_MOVE) {
        moveEvents.push({ event: e, index: i });
      }
    }
    result.push(sliced[0]); // ACTION_DOWN

    let reducedMiddle = [];

    const middleLimit = Math.max(0, 5 - 2); // 除去首尾后，最多还能保留几个
    if (moveEvents.length > 3) {
      // 等间距采样中间的
      const step = moveEvents.length / middleLimit;
      for (let i = 0; i < middleLimit; i++) {
        reducedMiddle.push(moveEvents[Math.floor(i * step)]);
      }
    } else {
      reducedMiddle = moveEvents;
    }

    // 合并所有保留事件
    const keepIndices = [
      ...reducedMiddle.map(m => m.index),
      sliced.length - 1, // last is ACTION_UP
    ];

    for (let i = 1; i < sliced.length - 1; i++) {
      if (keepIndices.includes(i)) {
        result.push(sliced[i]);
      }
    }

    result.push(sliced[sliced.length - 1]); // ACTION_UP

    return result;
  }

}


export class IOSStreamService {
  listenPort: number = 12025;
  wss: WebSocket.Server | null = null;
  deviceGetter: (udid: string) => IOSDevice|undefined;

  constructor(deviceGetter: (udid: string) => IOSDevice|undefined) {
    this.deviceGetter = deviceGetter;
  }

  async start() {
    this.listenPort = await getAFreePort();
    this.wss = new WebSocket.Server({ host: '127.0.0.1', port: this.listenPort });
    this.wss!.on('connection', (ws, req) => {
      if(req.url) {
        let reqUrl = new URL(`${this.getWsUrl()}${req.url}`);
        let udid = reqUrl.searchParams.get('udid') || '';
        let device = this.deviceGetter(udid);
        if(device) {
          let smallPreview = 'false' !== reqUrl.searchParams.get('smallPreview');
          new IOSStreamer(ws, device, smallPreview).onWSConnected();;
          return;
        } else {
          ws.close(1000, 'device not found');
        }
      }
      ws.close(1000, 'param error');
    });
    this.wss.on('listening', () => {
      logger.log(`ios stream service is ready on ${this.getWsUrl()}`);
    });
  }

  async stop() {
    this.wss?.close();
    this.wss = null;
  }

  getWsUrl() {
    return `ws://127.0.0.1:${this.listenPort}`
  }

}
