
export enum ActionType {
  PRESS= 'pointerDown',
  WAIT = 'pause',
  MOVE = 'pointerMove',
  RELEASE = 'pointerUp'
}

export class TouchAction {
  type: string;
  constructor(type: string) {
    this.type = type;
  }
}

export class PressAction extends TouchAction {
  constructor() {
    super(ActionType.PRESS);
  }
}

export class MoveAction extends TouchAction {
  x: number;
  y: number;
  constructor(x: number, y: number) {
    super(ActionType.MOVE);
    this.x = x;
    this.y = y;
  }
}

export class PauseAction extends TouchAction {
  duration: number;
  constructor(duration: number) {
    super(ActionType.WAIT);
    this.duration = duration;
  }
}

export class ReleaseAction extends TouchAction {
  constructor() {
    super(ActionType.RELEASE);
  }
}

export class TouchFinger {
  id: string;
  type = "pointer";
  parameters = {'pointerType': 'touch'};
  actions: TouchAction[];
  constructor(name: string) {
    this.id = 'finger-' + name;
    this.actions = []
  }

  press(x: number, y: number) {
    this.move(x, y);
    this.actions.push(new PressAction());
    return this;
  }

  wait(ms: number) {
    this.actions.push(new PauseAction(ms));
    return this;
  }

  move(x: number, y: number) {
    this.actions.push(new MoveAction(x, y));
    return this;
  }

  release() {
    this.actions.push(new ReleaseAction());
    return this;
  }

}

export class TouchActions {
  actions: TouchFinger[] = [];

  finger(name: string) {
    let finger = this.actions.find(f => f.id === 'finger-' + name);
    if(!finger) {
      finger = new TouchFinger(name);
      this.actions.push(finger);
    }
    return finger;
  }

}
