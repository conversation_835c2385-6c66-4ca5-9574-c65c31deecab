import { CloudAndroidDevice } from '@e/mobile/android/CloudAndroidDevice';
import { ConnectType } from '@e/mobile/cons/common';
import path from 'path';
import request from '@e/services/request';
import logger from '@e/services/logger';
import { portalRpcClient } from "@e/components/backendTask";
import { watchNetworkChangeRoomIds } from "@e/mobile/MobileHelperWindow";
import { regs } from "@e/utils/utils";
import random from '@e/utils/random';

export class QCloudAndroidDevice extends CloudAndroidDevice {
  constructor(padCode: string, connectType: ConnectType, state: string) {
    super(padCode, connectType, state);
  }

  async onUpdateProxyEvent(data: any) {
    let { udid, metas, proxy, teamMobileId, teamIpId } = data;
    logger.info('[MOBILE] onUpdateProxyEvent', teamMobileId, proxy, metas);
    const setProxyRet = await this.setProxy(proxy);
    for (const roomId of watchNetworkChangeRoomIds) {
      // 发送网络变更通知
      portalRpcClient.sendToChatRoom(
        roomId,
        JSON.stringify({
          action: 'mobileNetworkUpdate',
          data: {
            mobileId: teamMobileId,
            message: setProxyRet,
          },
        }),
      );
    }
    if (!data.networkEnabled) {
      return;
    }
    if((metas?.geoMode ?? 'auto') !== 'auto' && metas.geo && metas.geo.indexOf(',')) {
      let [longitude, latitude] = metas.geo.split(',');
      this.setGeo(longitude, latitude);
    }
    if((metas?.timezoneMode ?? 'auto') !== 'auto' && metas?.timezone) {
      this.setTimezone(metas.timezone);
    }
    // 根据出口 IP 自动设置地理位置与经纬度
    if ((metas?.geoMode ?? 'auto') === 'auto' || (metas?.timezoneMode ?? 'auto') === 'auto') {
      const { checkers } = await request('/api/meta/ip/checkers', {});
      let ipDetectUrl = 'https://ipv4-hk.szdamai.com/';
      let checker: any;
      if (metas?.ipDetectProvider) {
        checker = checkers.find((vo: any) => vo.provider === metas?.ipDetectProvider);
        if (checker.url) {
          ipDetectUrl = checker.url;
        }
      }
      // 获取出口 IP
      let remoteIp: any;
      let useProxy = '';
      if(proxy) {
        useProxy = `-x ${proxy}`;
      }
      const ret = await this.runShellCommandAdbKit(`curl ${useProxy} ${ipDetectUrl}`);
      try {
        const data = JSON.parse(ret);
        if (
          typeof data === 'object' &&
          checker?.dataIndex &&
          checker.dataIndex.length > 0
        ) {
          remoteIp = checker.dataIndex.reduce(
            (acc: Record<string, any>, cur: string) => acc[cur],
            data,
          );
        }
      } catch (e) {
        if (regs.ipv4.test(ret || '')) {
          remoteIp = ret;
        }
      }
      if (typeof remoteIp === 'string') {
        logger.info(`[MOBILE] 探测到手机[${teamMobileId}-${udid}]代理出口 IP：${remoteIp}`);
        const ipLocation = await request(`/api/meta/ip/myLocation?ip=${remoteIp}`);
        if ((metas?.geoMode ?? 'auto') === 'auto' && ipLocation?.longitude && ipLocation?.latitude) {
          // 经纬度
          this.setGeo(ipLocation.longitude, ipLocation.latitude);
        }
        if ((metas?.timezoneMode ?? 'auto') === 'auto' && ipLocation?.timezone) {
          // 时区
          this.setTimezone(ipLocation.timezone);
        }
      } else {
        logger.info(`[MOBILE] 探测到手机[${teamMobileId}]代理出口 IP 失败`);
      }
    }
  }

  async setProxy(proxy: string) {
    let ret = await this.runShellCommandAdbKit('gif config -a proxy.enabled=false');
    if (proxy) {
      // gif config -a proxy.enabled=true -a proxy.protocol=socks5 -a proxy.host=************* -a proxy.port=9002 -a proxy.user=test -a proxy.pass=test -a proxy.international=1
      let url = new URL(proxy);
      let command = `rm -f /data/misc/gif/proxyinfo.cfg && gif config -a proxy.enabled=true -a proxy.protocol=${url.protocol.replace(
        ':',
        '',
      )} -a proxy.host=${url.hostname} -a proxy.port=${url.port}`;
      if (url.username && url.password) {
        command += ` -a proxy.user=${url.username} -a proxy.pass=${url.password}`;
      }
      ret = await this.runShellCommandAdbKit(command);
    }
    return ret;
  }

  async setGeo(longitude: string, latitude: string) {
    let bearing = random.nextInt(0, 360);
    let altitude = random.nextInt(50, 100);
    let accuracy = random.nextInt(5, 50);
    let speed = random.nextFloat(0, 1);
    await this.runShellCommandAdbKit(`gif config -a gps.longitude=${longitude} -a gps.latitude=${latitude} -a gps.bearing=${bearing} -a gps.speed=${speed} -a gps.altitude=${altitude} -a gps.accuracy=${accuracy}`);
  }

  async setTimezone(timezone: string, language?: string) {
    await this.runShellCommandAdbKit(`gif config -a sys.timezone=${timezone}`);
    if (language) {
      await this.runShellCommandAdbKit(`gif config -a lang.language=${language}`);
    }
  }

  public async changeModel(model: string) {
    await this.prepareAdb();
    await this.installVpick();
    let modeParam = '';
    if (model) {
      modeParam = ` --model ${model}`;
    }
    let command = `vpick -r ${modeParam} && gif onekey_settings set wifi && gif onekey_settings set bt`;
    await this.runShellCommandAdbKit(command);
  }

  public async installVpick(): Promise<any> {
    await this.prepareAdb();
    let check = await this.runShellCommandAdbKit('vpick -v');
    if ((check || '').indexOf('V1.0') != -1) {
      //说明已安装
      return check;
    }
    //说明未安装
    let transfer = await this.push(
      path.join(__dirname, '../extra', 'vpick.tar.gz'),
      '/data/local/tmp/vpick.tar.gz',
    );
    await new Promise((resolve, reject) => {
      transfer.on('end', resolve);
      transfer.on('error', reject);
    });
    let output = await this.runShellCommandAdbKit(
      'cd /data/local/tmp && tar xzf /data/local/tmp/vpick.tar.gz',
    );
    output = await this.runShellCommandAdbKit('cd /data/local/tmp/vpick && ./install.sh');
    check = await this.runShellCommandAdbKit('vpick -v');
    return check;
  }
}
