import {AndroidDevice} from "@e/mobile/android/AndroidDevice";
import {Element} from "@e/mobile/android/automator/element";


export interface Selector {
  id?: string;
  clazz?: string;
  desc?: string;
  pkg?: string;
  text?: string;

  // Boolean criteria
  checked?: boolean;
  checkable?: boolean;
  clickable?: boolean;
  enabled?: boolean;
  focused?: boolean;
  focusable?: boolean;
  longClickable?: boolean;
  scrollable?: boolean;
  selected?: boolean;

  // Depth restrictions
  depth?: number;
}

class ByService {

  protected selector:Selector = {};
  protected readonly device: AndroidDevice;

  constructor(device: AndroidDevice) {
    this.device = device;
  }

  async findElement(): Promise<Element> {
    let selector = this.serialize();
    let out = await this.device.runShellCommandAdbKit(`am instrument -w -r -e debug false -e class com.android.hykeyboard.Driver#findElement -e selector ${JSON.stringify(selector)} com.android.hykeyboard/androidx.test.runner.AndroidJUnitRunner`);
    let response = this.parseOut(out);
    return response.data || {exist: false};
  }

  private serialize() {
    let json  = JSON.stringify(this.selector, (key, value) => (value === undefined ? undefined : value));
    if(json == '{}') {
      throw 'selector is empty'
    }
    return json;
  }

  private parseOut(out: string) {
    if(out) {
      for(let line of out.split('\n')) {
        if(line.indexOf('invoke_response=') != -1) {
          line = line.substring(line.indexOf('invoke_response=') + 'invoke_response='.length);
          return JSON.parse(line);
        }
      }
    }
    return {success: false, data: 'Unknown Error.'};
  }

}

export class By extends ByService {
  constructor(device: AndroidDevice) {
    super(device);
  }

  id(id: string) {
    this.selector.id = id;
    return this;
  }
  clazz(clazz: string) {
    this.selector.clazz = clazz;
    return this;
  }
  desc(desc: string) {
    this.selector.desc = desc;
    return this;
  }
  pkg(pkg: string) {
    this.selector.pkg = pkg;
    return this;
  }
  text(text: string) {
    this.selector.text = text;
    return this;
  }
  checked(checked: boolean) {
    this.selector.checked = checked;
    return this;
  }
  checkable(checkable: boolean) {
    this.selector.checkable = checkable;
    return this;
  }
  clickable(clickable: boolean) {
    this.selector.clickable = clickable;
    return this;
  }
  enabled(enabled: boolean) {
    this.selector.enabled = enabled;
    return this;
  }
  focused(focused: boolean) {
    this.selector.focused = focused;
    return this;
  }
  focusable(focusable: boolean) {
    this.selector.focusable = focusable;
    return this;
  }
  longClickable(longClickable: boolean) {
    this.selector.longClickable = longClickable;
    return this;
  }
  scrollable(scrollable: boolean) {
    this.selector.scrollable = scrollable;
    return this;
  }
  selected(selected: boolean) {
    this.selector.selected = selected;
    return this;
  }
  depth(depth: number) {
    this.selector.depth = depth;
    return this;
  }
}
