
export interface Rect {
  bottom: number;
  left: number;
  right: number;
  top: number;
}

export interface Element {
  exist: boolean;
  resourceId?: string;

  childCount?: number;

  text?: string;

  contentDescription?: string;
  clazz?: string;
  applicationPackage?: string;
  checked?: boolean;
  checkable?: boolean;
  clickable?: boolean;
  enabled?: boolean;
  focused?: boolean;
  focusable?: boolean;
  longClickable?: boolean;
  scrollable?: boolean;
  selected?: boolean;

  bounds?: Rect;
}
