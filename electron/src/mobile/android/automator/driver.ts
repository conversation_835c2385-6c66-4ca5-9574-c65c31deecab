import {By} from "@e/mobile/android/automator/by";
import {AndroidDevice} from "@e/mobile/android/AndroidDevice";
import {Element} from "@e/mobile/android/automator/element";

export class Driver {

  private readonly device: AndroidDevice;

  constructor(device: AndroidDevice) {
    this.device = device;
  }

  by() {
    return new By(this.device)
  }

  async findElement(by: By): Promise<Element> {
    return by.findElement();
  }

}
