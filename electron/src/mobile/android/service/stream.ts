import WebSocket from "ws";
import {AndroidDevice} from "@e/mobile/android/AndroidDevice";
import {getAFreePort} from "@e/utils/utils";
import logger from "@e/services/logger";
import {AbstractStreamer} from "@e/mobile/abstract_streamer";
import {JpegConsumer} from "@e/mobile/ios/consts";

export class AndroidStreamer extends AbstractStreamer implements JpegConsumer{

  device: AndroidDevice;

  constructor(ws: WebSocket, device: AndroidDevice, smallPreview: boolean) {
    super(ws, smallPreview);
    this.device = device;

  }

  isSmallPreview() {
    return this.smallPreview;
  }

  isConnected() {
    return this.ws.readyState <= WebSocket.OPEN;
  }

  onWSConnected() {
    if(this.ws.readyState === WebSocket.OPEN) {
      (async()=>{
        await this.prepareMjpegServer();
        //send init message
        // let driver = await this.device.driver();
        let size = await this.device.getScreenSize();
        let bounds = {width: size[0], height: size[1]};
        await this.sendInitialInfo(bounds);
      })();
    } else {
      this.ws.on('open', async ()=>{
        await this.prepareMjpegServer();
        //send init message
        let driver = await this.device.driver();
        let bounds = await driver.getWindowSize();
        await this.sendInitialInfo(bounds);
      });
    }
    this.ws.on('message', (data: Buffer)=>{
      //todo
    });
    this.ws.on('error', ()=>{
      this.close();
    });
    this.ws.on('close', ()=>{
      this.close();
    });
  }

  private async prepareMjpegServer() {
    if(this.smallPreview) {
      await this.device.allocJpegPreviewServer(this.clientId, this);
    } else {
      //目前不会有这种情况
    }
  }

  public handleJpegData(data: Buffer) {
    this.ws.send(data);
  }

  closed = false;
  close() {
    if(!this.closed) {
      this.closed = true;
      if(this.smallPreview) {
        this.device.releaseJpegPreviewServer(this.clientId).catch(()=>{});
      } else {
        //目前不会有这种情况
      }
    }
  }

}

/**
 * 提供每过几秒刷新一帧图像的能力
 */
export class AndroidStreamService {
  listenPort: number = 12025;
  wss: WebSocket.Server | null = null;
  deviceGetter: (udid: string) => AndroidDevice|undefined;

  constructor(deviceGetter: (udid: string) => AndroidDevice|undefined) {
    this.deviceGetter = deviceGetter;
  }

  async start() {
    this.listenPort = await getAFreePort();
    this.wss = new WebSocket.Server({ host: '127.0.0.1', port: this.listenPort });
    this.wss!.on('connection', (ws, req) => {
      if(req.url) {
        let reqUrl = new URL(`${this.getWsUrl()}${req.url}`);
        let udid = reqUrl.searchParams.get('udid') || '';
        let device = this.deviceGetter(udid);
        if(device) {
          let smallPreview = 'false' !== reqUrl.searchParams.get('smallPreview');
          new AndroidStreamer(ws, device, smallPreview).onWSConnected();
          return;
        } else {
          ws.close(1000, 'device not found');
        }
      }
      ws.close(1000, 'param error');
    });
    this.wss.on('listening', () => {
      logger.log(`android stream service is ready on ${this.getWsUrl()}`);
    });
  }

  async stop() {
    this.wss?.close();
    this.wss = null;
  }

  getWsUrl() {
    return `ws://127.0.0.1:${this.listenPort}`
  }

}
