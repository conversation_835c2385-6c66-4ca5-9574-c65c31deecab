import {XMLParser} from 'fast-xml-parser';
import axios, { AxiosProxyConfig, AxiosResponse, ResponseType } from 'axios';

/**
 * 解析bounds字符串
 * @param {string} boundsStr 格式类似 "[0,0][100,100]"
 * @returns {number[]|null} [x1, y1, x2, y2] 或 null
 */
function parseBounds(boundsStr: string): number[] | null {
  const pattern = /\[(\d+),(\d+)\]\[(\d+),(\d+)\]/;
  const match = boundsStr?.match(pattern);
  if (match) {
    return [
      parseInt(match[1]),
      parseInt(match[2]),
      parseInt(match[3]),
      parseInt(match[4])
    ];
  }
  return null;
}

export function extractTextNodes(xml: string) {
  // 配置解析器选项
  const parser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: "",
  });

  // 解析XML
  const xmlObj = parser.parse(xml);

  // 存储结果的数组
  const textNodes: any[] = [];
  // 存储已添加的bounds
  const addedBounds = new Set();
  // 用于生成索引的计数器
  let index = 1;

  /**
   * 递归处理节点
   * @param {Object} node 当前节点
   */
  function processNode(node: any) {
    // 检查节点是否有text属性且不为空
    if (node.text && node.text.trim()) {
      const bounds = parseBounds(node.bounds);
      if (bounds && bounds[2] - bounds[0] > 0 && bounds[3] - bounds[1] > 0) {
        const boundsKey = bounds.join(',');
        if (!addedBounds.has(boundsKey)) {
          textNodes.push({
            index: index++,
            text: node.text,
            bounds: bounds
          });
          addedBounds.add(boundsKey);
        }
      }
    }

    // 处理子节点
    // node 可能包含多种类型的子节点，需要找到实际的节点数组
    const children = Object.values(node).filter(value =>
      Array.isArray(value) || (typeof value === 'object' && value !== null)
    );

    children.forEach(child => {
      if (Array.isArray(child)) {
        child.forEach(item => processNode(item));
      } else {
        processNode(child);
      }
    });
  }

  // 开始处理根节点
  processNode(xmlObj.hierarchy);

  return textNodes;
}

export function extractIOSTextNodes(xml: string) {
  // 配置解析器选项
  const parser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: "",
  });

  // 解析XML
  const xmlObj = parser.parse(xml);

  // 存储结果的数组
  const textNodes: any[] = [];
  // 存储已添加的bounds
  const addedBounds = new Set();
  // 用于生成索引的计数器
  let index = 1;

  /**
   * 递归处理节点
   * @param {Object} node 当前节点
   */
  function processNode(node: any) {
    // 检查节点是否有text属性且不为空
    if (node.visible === 'true' && node.label && node.label.trim()) {
      const bounds = [~~(node.x), ~~(node.y), ~~(node.x) + ~~(node.width), ~~(node.y) + ~~(node.height)];
      if (bounds && bounds[2] - bounds[0] > 0 && bounds[3] - bounds[1] > 0) {
        const boundsKey = bounds.join(',');
        if (!addedBounds.has(boundsKey)) {
          textNodes.push({
            index: index++,
            text: node.label,
            bounds: bounds
          });
          addedBounds.add(boundsKey);
        }
      }
    }

    // 处理子节点
    // node 可能包含多种类型的子节点，需要找到实际的节点数组
    const children = Object.values(node).filter(value =>
      Array.isArray(value) || (typeof value === 'object' && value !== null)
    );

    children.forEach(child => {
      if (Array.isArray(child)) {
        child.forEach(item => processNode(item));
      } else {
        processNode(child);
      }
    });
  }

  // 开始处理根节点
  processNode(xmlObj.XCUIElementTypeApplication);

  return textNodes;
}

export function layoutToMarkdown(xml: string) {
  const xmlObj = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: "",
  }).parse(xml);
  let texts: string[] = [];

  function processNode(node: any) {
    let text = node.text;
    let contentDesc = node['content-desc']
    if(text || contentDesc) {
      texts.push(`${text || ''} ${contentDesc || ''}`);
    }
    Object.values(node).filter(value =>
      Array.isArray(value) || (typeof value === 'object' && value !== null)
    ).forEach(child => {
      if (Array.isArray(child)) {
        child.forEach(item => processNode(item));
      } else {
        processNode(child);
      }
    });
  }

  processNode(xmlObj.hierarchy);

  return texts.join('\n');
}

/**
 * 判断dump的layout是不是空的
 * @param xmlObj
 */
export function checkIsEmptyLayout(hierarchy: any) {
  let children: any[] = Object.values(hierarchy).filter(value => Array.isArray(value))[0] as any[] || [];
  if(children.length > 0) {
    let root = children[0];
    children = Object.values(root).filter(value => Array.isArray(value))[0] as any[] || [];
    if(children.length > 0) {
      return false;
    }
  }
  return true;
}

/**
 * 调用花漾的服务检测可交互区域
 * @param screenshot
 */
async function screenInteractiveDetection(screenshot: string): Promise<any[]> {
  //从服务器转一道会耗费非常多时间，暂先直接从客户端调用
  const url = 'http://build.thinkoncloud.cn:16888/captcha';
  const ocrUsername = 'huayoung20250418';
  const ocrPassword = 'f1bf61c579d7e1d51acf40c3c1678178';
  const codeType = 'InteractiveDetection';
  let regions = await new Promise((resolve, reject)=>{
    axios.request({
        url: url,
        method: 'POST',
        responseType: 'json',
        headers: {"Content-Type": "application/json"},
        data: {
          username: ocrUsername,
          password: ocrPassword,
          codeType, image: screenshot
        },
      })
      .then((res) => {
        let data = res.data;
        if(data.success) {
          resolve(JSON.parse(data.text));
        } else {
          reject(new Error(data.message));
        }
      })
      .catch((e) => {
        if (e.response) {
          let resp = e.response;
          e = new Error(
            JSON.stringify({
              status: resp.status,
              statusText: resp.statusText,
              message: resp.data?.message,
            }),
          );
        }
        reject(e);
      });
  });
  return regions as any[];
}

const empty_xml_for_debug = `
<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy rotation="0">
  <node index="0" text="" resource-id="" class="" package="com.tencent.mm" content-desc="" checkable="false" checked="false" clickable="false" enabled="false" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][0,0]" />
</hierarchy>
`;
export async function extractClickableNodes(xml: string, screenshot?: string) {
  // xml = empty_xml_for_debug; // just for debug
  // 配置解析器选项
  const parser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: "",
    preserveOrder: true,
  });

  // 解析XML
  const xmlObj = parser.parse(xml);

  // 存储结果的数组
  const clickableNodes: any[] = [];
  // 存储已添加的bounds
  const addedBounds = new Set();

  // 开始处理根节点
  let hierarchy = xmlObj.filter((n: any) => n.hierarchy)[0];
  if(checkIsEmptyLayout(hierarchy) && screenshot) {
    let regions = await screenInteractiveDetection(screenshot);
    for(let i = 0; i < regions.length; i++) {
      clickableNodes.push({
        bounds: regions[i],
        forceKeep: false,
        text: '',
        desc: '',
        'class': 'unknown',
        xpath: 'omni-parser'
      });
    }
  } else {
    hierarchy.xpath = '/hierarchy';
    processNode(hierarchy);
  }

  /**
   * 检查两个区域是否有重叠
   */
  function isCross(bounds1: number[], bounds2: number[]) {
    const [x1, y1, x2, y2] = bounds1;
    const [a1, b1, a2, b2] = bounds2;

    // 判断是否有交集
    const isOverlapping = !(x2 <= a1 || a2 <= x1 || y2 <= b1 || b2 <= y1);

    // 判断是否完全包含
    const isContained =
      (x1 >= a1 && y1 >= b1 && x2 <= a2 && y2 <= b2) || // bounds1 被 bounds2 完全包含
      (a1 >= x1 && b1 >= y1 && a2 <= x2 && b2 <= y2);   // bounds2 被 bounds1 完全包含

    return isOverlapping && !isContained;
  }

  /**
   * 检查新bounds是否被任何已有bounds包含，并返回包含它的节点索引列表
   */
  function findContainingNodes(newBounds: number[], nodes: any[]) {
    const containingIndices: any[] = [];
    let isContained = false;

    nodes.forEach((node, i) => {
      if (node.forceKeep) {
        return;
      }

      const existingBounds = node.bounds;
      if (existingBounds[0] <= newBounds[0] &&
        existingBounds[1] <= newBounds[1] &&
        existingBounds[2] >= newBounds[2] &&
        existingBounds[3] >= newBounds[3]) {
        let area = (newBounds[2] - newBounds[0]) * (newBounds[3] - newBounds[1]);
        let existingArea = (existingBounds[2] - existingBounds[0]) * (existingBounds[3] - existingBounds[1]);
        if(area / existingArea > 0.8) {
          isContained = true;
          containingIndices.push(i);
        }
      }
    });

    return { isContained, containingIndices };
  }

  /**
   * 找出所有与目标区域重叠且在其上层的节点
   */
  function findOverlappingNodes(targetBounds: number[], nodes: any[], startIndex: number) {
    const overlappingIndices = [];
    for (let i = startIndex + 1; i < nodes.length; i++) {
      // 如果上层节点是编辑框，跳过重叠检查
      if (nodes[i].editor) {
        continue;
      }
      if (isCross(targetBounds, nodes[i].bounds)) {
        overlappingIndices.push(i);
      }
    }
    return overlappingIndices;
  }

  /**
   * 递归处理节点
   */
  function processNode(node: any, nodePath: any[] = []) {
    //预处理node
    const children: any[] = Object.values(node).filter(value => Array.isArray(value))[0] as any[] || [];
    node = {children, xpath: node.xpath, brotherIndex: node.brotherIndex || 0, ...(node[':@'] || {})}

    // 检查节点是否可点击或是输入框
    const isClickable = node.clickable === 'true';
    const isCheckable = node.checkable === 'true';
    const isEditText = (node.class === 'android.widget.EditText' || node.class === 'android.widget.TextView');
    // const isEditText = (node.class === 'android.widget.EditText');

    if (isClickable || isCheckable || isEditText) {
      const bounds = parseBounds(node.bounds);
      if (bounds) {
        let width = bounds[2] - bounds[0], height = bounds[3] - bounds[1];
        if(width > 0 && width > 0 && !(width > 500 && height > 500)) {
          const boundsKey = bounds.join(',');
          if (!addedBounds.has(boundsKey)) {
            const { isContained, containingIndices } = findContainingNodes(bounds, clickableNodes);

            if (isContained) {
              for (const i of containingIndices.sort((a, b) => b - a)) {
                const removedNode = clickableNodes.splice(i, 1)[0];
                addedBounds.delete(removedNode.bounds.join(','));
              }
            }

            clickableNodes.push({
              bounds,
              forceKeep: isEditText,
              text: `${node.text || ''}`,
              desc: `${node['content-desc'] || ''}`,
              'class': node['class'],
              xpath: node.xpath
            });
            addedBounds.add(boundsKey);
          }
        }
      }
    }

    for(let i = 0; i < children.length; i++) {
      let child: any = children[i];
      child.brotherIndex = i;
      child.xpath = node.xpath + `/*[${i + 1}]`;
      processNode(child, [...nodePath, node]);
    }
  }

  // 移除被覆盖的区域
  // let i = 0;
  // while (i < clickableNodes.length) {
  //   if (clickableNodes[i].forceKeep) {
  //     i++;
  //     continue;
  //   }
  //
  //   const overlapping = findOverlappingNodes(clickableNodes[i].bounds, clickableNodes, i);
  //   if (overlapping.length > 0) {
  //     // 如果当前节点被其他节点覆盖，移除当前节点
  //     clickableNodes.splice(i, 1);
  //   } else {
  //     i++;
  //   }
  // }

  // 添加索引
  clickableNodes.forEach((node, i) => {
    node.index = i + 1;
  });

  return clickableNodes;
}
