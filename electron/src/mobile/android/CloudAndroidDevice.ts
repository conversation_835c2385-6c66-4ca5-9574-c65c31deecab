import {AdbClient} from "./adb/AdbClient";
import {AndroidDevice} from "./AndroidDevice";
import {createPipeTunnel, FrontendSocksTunnel} from "@e/utils/tunnels/direct/frontend";
import request from "@e/services/request";
import logger from "@e/services/logger";
import {ConnectType, MobileDto} from "@e/mobile/cons/common";
import path from "path";

export class CloudAndroidDevice extends AndroidDevice {

  padCode: string;
  mobileDto?: MobileDto;
  frontendTunnel?: FrontendSocksTunnel;

  constructor(padCode: string, connectType: ConnectType, state: string) {
    super(padCode, connectType, state);
    this.padCode = padCode;
    this.setState(state);
  }

  public async getCloudAdbAddress(): Promise<any> {
    const connectInfo = await request(`/api/shop/app_mobile/toggleArmCloudAdb`, {
      method: 'PUT',
      params: {padCode: this.padCode, enable: true}
    });
    return connectInfo;
  }

  public async prepareAdb() {
    if(!this.connected) {
      if(!this.mobileDto) {
        this.mobileDto = await request('/api/shop/mobile/getCloudProperties', {
          params: {padCode: this.padCode}
        });
      }
      this.connected = true;
      const connectInfo = await this.getCloudAdbAddress();
      let {adbAddress, adbFrontends} = connectInfo;
      let [remoteAddr, remotePort] = adbAddress!.split(':');
      this.frontendTunnel?.close();
      this.frontendTunnel = createPipeTunnel(adbFrontends, remoteAddr, ~~remotePort);
      if(this.frontendTunnel) {
        const tunnelInfo = await this.frontendTunnel.open();
        this.udid = `${tunnelInfo.host}:${tunnelInfo.port}`;
      }
      await this.client.runAdbCommand(undefined, 'connect', this.udid);
    }
    this.kickInUse();
  }

  async disconnectAdb() {
    if(this.connected) {
      this.connected = false;
      this.client.runAdbCommand(undefined, 'disconnect', this.udid);
      this.frontendTunnel?.close();
      this.frontendTunnel = undefined;
      logger.info(`[${this.padCode}, ${this.udid}] 长时间未使用，主动断开连接`);
    }
  }

  public async killServer(pid: number): Promise<void> {
    await super.killServer(pid);
  }

  async getClient(): Promise<AdbClient> {
    await this.prepareAdb();
    return super.getClient();
  }
  public async driver(): Promise<WebdriverIO.Browser> {
    await this.prepareAdb();
    return super.driver();
  }

  lastKickTime = 0;
  killTimer = 0;
  public kickInUse() {
    if(this.connected && Date.now() - this.lastKickTime > 10 * 1000) {
      this.lastKickTime = Date.now();
      clearTimeout(this.killTimer);
      // @ts-ignore
      this.killTimer = setTimeout(()=> this.disconnectAdb(), 5 * 60 * 1000);
    }
  }

  setState(state: string) {
    if (state === 'device') {
      this.connected = true;
      this.properties = undefined;
    } else {
      this.connected = false;
    }
    this.emitUpdate();
  }

  public async toMobileDto(): Promise<MobileDto> {
    return this.mobileDto || {
      code: this.padCode,
      connectType: this.connectType,
      name: this.padCode,
      platform: 'Android',
      mode: this.padCode,
      androidVersion: '',
      screenWidth: 0,
      screenHeight: 0,
    };
  }

}
