import os from 'os';
import fs from 'fs';
import path from 'path';
import Client from '@dead50f7/adbkit/lib/adb/client';
import {ClientOptions} from '@dead50f7/adbkit/lib/ClientOptions';
import Downloader from 'nodejs-file-downloader';
import {isLinuxPlatform, isMacPlatform, resolveUrl} from '@e/utils/utils';
import db, {getDataSavePath} from '@e/components/db';
import _ from 'lodash';
import logger from '../../../services/logger';
import {app} from 'electron';
import extract from 'extract-zip';
import {spawn} from "child_process";
import Adb from "@dead50f7/adbkit";

export const ADB_SERVICE_PORT = 5037;

export class AdbClient extends Client {
  constructor(opts: ClientOptions) {
    super(opts);
  }

  /**
   * 文档见 https://github.com/openstf/adbkit
   */
  static createClient(): AdbClient {
    const opts: ClientOptions = {
      bin: getAdbExecutePath(),
      host: '127.0.0.1',
      port: ADB_SERVICE_PORT,
    };
    return new AdbClient(opts);
  }

  public async runAdbCommand(udid: undefined|string, shell: string = 'shell', command: string | string[], logTag: undefined|string = undefined, stdListener: { stdout?: (text: string) => void; stderr?: (text: string) => void } = {}): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const cmd = getAdbExecutePath();
      const args: any[] = [];
      if(udid) {
        args.push('-s', `${udid}`);
      }
      if(Array.isArray(command)) {
        args.push(shell, ...command);
      } else {
        args.push(shell, command);
      }
      const adb = spawn(cmd, args, { stdio: ['ignore', 'pipe', 'pipe'] });
      let output = '';

      adb.stdout.on('data', (data) => {
        output += data.toString();
        if(logTag) {
          logger.log(logTag, `stdout: ${data.toString().replace(/\n$/, '')}`);
        }
        if (stdListener.stdout) {
          stdListener.stdout(data.toString());
        }
      });

      adb.stderr.on('data', (data) => {
        if(logTag) {
          logger.error(logTag, `stderr: ${data}`);
        }
        if (stdListener.stderr) {
          stdListener.stderr(data.toString());
        }
      });

      adb.on('error', (error: Error) => {
        if(logTag) {
          logger.error(logTag, `failed to spawn adb process.\n${error.stack}`);
        }
        reject(error);
      });

      adb.on('close', (code) => {
        if(logTag) {
          logger.log(logTag, `adb process (${args.join(' ')}) exited with code ${code}`);
        }
        resolve(output);
      });
    });
  }

  public async runShellCommandAdbKit(udid: string, command: string): Promise<string> {
    return this.shell(udid, command)
      .then(Adb.util.readAll)
      .then((output: Buffer) => output.toString().trim());
  }
}

function adbName(toolName = 'adb') {
  const platform = os.platform();
  switch (platform) {
    // @ts-ignore
    case 'mas':
    case 'darwin':
    case 'freebsd':
    case 'openbsd':
    case 'linux':
      return toolName;
    case 'win32':
      return `${toolName}.exe`;
    default:
      throw new Error('adb builds are not available on platform: ' + platform);
  }
}

// let env_adb_path: string | undefined = undefined;
// exec('adb version', (err: any, stdout: string, stderr: string) => {
//   if (err) {
//     return;
//   }
//   if (stdout.includes('Android Debug Bridge')) {
//     env_adb_path = adbName();
//   }
// });

export async function downloadAdb() {
  try {
    let fileName = 'platform-tools-windows.zip';
    if (isMacPlatform()) {
      fileName = 'platform-tools-darwin.zip';
    } else if (isLinuxPlatform()) {
      fileName = 'platform-tools-linux.zip';
    }
    const targetFileName = 'platform-tools.zip';
    const directory = path.resolve(getDataSavePath('appData'), app.getName());
    await new Downloader({
      url: resolveUrl(db.getDlUrl(), `/downloads/platform-tools/${fileName}`),
      fileName: targetFileName,
      cloneFiles: false,
      directory: directory,
      timeout: 5 * 60 * 1000,
      maxAttempts: 3,
      onProgress: _.throttle(
        (percentage: string, chunk: object, remainingSize: number) => {
          console.log(
            `downloading ${targetFileName} percentage: ${percentage}, remainingSize: ${remainingSize}`,
          );
        },
        500,
        { trailing: true },
      ),
      onError(e: Error) {
        logger.error(`[APP] download ${fileName} failed`, e);
      },
    }).download();
    if (fs.existsSync(path.join(directory, targetFileName))) {
      await extract(path.join(directory, targetFileName), { dir: directory });
    }
  } catch (e: any) {
    logger.error(`[APP] download platform-tools failed`, e);
  }
}

export function getAdbRootPath() {
  const sdk_root = path.resolve(getDataSavePath('appData'), app.getName(), 'platform-tools');
  return sdk_root;
}

/**
 * 获取adb执行路径
 */
export function getAdbExecutePath() {
  // if (env_adb_path) {
  //   return env_adb_path;
  // }
  const adb_path = path.resolve(getAdbRootPath(), adbName());
  const aapt2_path = path.resolve(getAdbRootPath(), adbName('aapt2'));
  if (fs.existsSync(adb_path)) {
    // win/mac 旧版本的 adb 没有 aapt2，需要重新下载
    if (!isLinuxPlatform() && !fs.existsSync(aapt2_path)) {
      throw new Error('aapt2 not found');
    }
    return adb_path;
  }
  throw new Error('adb not found');
}
