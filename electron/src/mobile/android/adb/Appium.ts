import {getAdbRootPath} from '@e/mobile/android/adb/AdbClient';
import path from 'path';

import {ChildProcess, fork} from 'child_process';
import {getAFreePort} from '@e/utils/utils';
import {appiumLogger} from '@e/services/logger';
import {waitSeconds} from "@e/rpa/utils";

export class AppiumService {
  appiumPort = 0;

  private static instance: AppiumService;

  private appiumProcess?: ChildProcess;

  private constructor() {}

  static getInstance() {
    if (!AppiumService.instance) {
      AppiumService.instance = new AppiumService();
    }
    return AppiumService.instance;
  }

  async startServer() {
    if (!this.appiumProcess || !this.appiumProcess.pid) {
      this.appiumProcess?.kill(9);
      let newPort= await getAFreePort();
      if(newPort == this.appiumPort) {
        newPort = await getAFreePort();
      }
      this.appiumPort = newPort;
      let appiumHome = __dirname;
      if (process.env.NODE_ENV === 'development') {
        appiumHome = path.resolve(__dirname, '../');
      }
      const env = Object.create(process.env);
      env.ANDROID_SDK_ROOT = getAdbRootPath();
      env.APPIUM_HOME = appiumHome;
      appiumLogger.info(`use ${process.execPath} to start appium.`);
      await waitSeconds(1, false);
      this.appiumProcess = fork(
        path.resolve(appiumHome, 'node_modules/appium/index.js'),
        ['--log-no-colors', '--address', '127.0.0.1', '--port', `${this.appiumPort}`],
        {
          env: env,
          stdio: 'pipe',
        },
      );
      this.appiumProcess.on('close', (code: any) => {
        this.appiumPort = 0;
        this.appiumProcess = undefined;
      });
      const result = await new Promise((resolve, reject) => {
        this.appiumProcess?.on('error', (error: any) => {
          this.appiumPort = 0;
          resolve(false);
        });
        this.appiumProcess?.stdout?.on('data', (data: any) => {
          let message = data?.toString();
          appiumLogger.log(message);
          if (message && message.indexOf('interface listener started on') != -1) {
            // this.appiumProcess?.stdout?.removeAllListeners('data');
            // this.appiumProcess?.stderr?.removeAllListeners('data');
            resolve(true);
          }
        });
        this.appiumProcess?.stderr?.on('data', (data: any) => {
          let message = data?.toString();
          appiumLogger.log(message);
          if (message && message.indexOf('interface listener started on') != -1) {
            // this.appiumProcess?.stdout?.removeAllListeners('data');
            // this.appiumProcess?.stderr?.removeAllListeners('data');
            resolve(true);
          }
        });
        setTimeout(() => {
          resolve(false);
        }, 60000);
      });
      if (!result) {
        this.appiumProcess = undefined;
        this.appiumPort = 0;
      }
    }
    if (!this.appiumProcess) {
      throw '启动Appium失败';
    }
  }

  async stopServer() {
    if (this.appiumProcess) {
      this.appiumProcess.kill();
      this.appiumProcess = undefined;
      this.appiumPort = 0;
    }
  }
}
