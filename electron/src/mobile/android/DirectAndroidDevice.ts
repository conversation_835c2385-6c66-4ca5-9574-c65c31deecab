import {AndroidDevice} from "./AndroidDevice";
import {MobileDto} from "@e/mobile/cons/common";
import Timeout = NodeJS.Timeout;
import AndroidDeviceDescriptor from "@e/mobile/cons/AndroidDeviceDescriptor";


export class DirectAndroidDevice extends AndroidDevice {

  private updateTimeoutId?: Timeout;
  private updateTimeout = AndroidDevice.INITIAL_UPDATE_TIMEOUT;
  public readonly descriptor: AndroidDeviceDescriptor;

  constructor(udid: string, state: string) {
    super(udid, /:\d+$/.test(udid) ? 'WIFI' : 'USB', state);
    this.descriptor = {
      udid,
      state,
      'wifi.interface': '',
      'ro.build.version.release': '',
      'ro.build.version.sdk': '',
      'ro.product.manufacturer': '',
      'ro.product.marketname': '',
      'ro.product.vndk.version': '',
      'ro.product.model': '',
      'ro.product.cpu.abi': '',
      screenWidth: 0,
      screenHeight: 0,
    };
    this.setState(state);
    this.killOldProcessOnStart();
  }

  setState(state: string) {
    let oldConnected = this.connected;
    if (state === 'device') {
      this.connected = true;
      this.properties = undefined;
    } else {
      this.connected = false;
    }
    if (this.connected != oldConnected) {
      if (!this.connected) {
        this.emit('offline');
      }
      this.reportState();
    }
    this.emitUpdate();
    this.fetchDeviceInfo();
  }

  private scheduleInfoUpdate(): void {
    if (this.updateTimeoutId) {
      return;
    }
    this.updateTimeoutId = setTimeout(() => {
      this.fetchDeviceInfo();
    }, this.updateTimeout);
  }

  protected fetchDeviceInfo(): void {
    if (this.connected) {
      const propsPromise = this.getProperties().then((props) => {
        if (!props) {
          return false;
        }
        let changed = false;
        for (let key of Object.keys(this.descriptor)) {
          //@ts-ignore
          if (typeof props[key] != 'undefined' && props[key] !== this.descriptor[key]) {
            changed = true;
            //@ts-ignore
            (this.descriptor[key] as any) = props[key];
          }
        }
        if (changed) {
          this.emitUpdate();
        }
        return true;
      });
      let pidPromise: Promise<number | undefined>;
      if (this.spawnServer) {
        pidPromise = this.startServer();
      } else {
        pidPromise = this.getServerPid();
      }
      const serverPromise = pidPromise.then(() => {
        return !(this.scrcpyPid === -1 && this.spawnServer);
      });
      const screenSizePromise = this.getScreenSize().then((sc) => {
        this.descriptor['screenWidth'] = sc[0];
        this.descriptor['screenHeight'] = sc[1];
        return sc && sc.length == 2;
      });
      Promise.all([propsPromise, serverPromise, screenSizePromise])
        .then((results) => {
          this.updateTimeoutId = undefined;
          const failedCount = results.filter((result) => !result).length;
          if (!failedCount) {
            this.updateTimeout = AndroidDevice.INITIAL_UPDATE_TIMEOUT;
          } else {
            this.scheduleInfoUpdate();
          }
        })
        .catch(() => {
          this.updateTimeoutId = undefined;
          this.scheduleInfoUpdate();
        });
    } else {
      this.updateTimeout = AndroidDevice.INITIAL_UPDATE_TIMEOUT;
      this.updateTimeoutId = undefined;
      this.emitUpdate();
    }
  }

  public async toMobileDto(): Promise<MobileDto> {
    let name = this.descriptor['ro.product.marketname'] || this.descriptor['ro.product.model'];
    return {
      code: this.udid,
      connectType: this.connectType,
      name: name,
      platform: 'Android',
      mode: this.descriptor['ro.product.model'],
      androidVersion: this.descriptor['ro.product.vndk.version'],
      screenWidth: this.descriptor['screenWidth'],
      screenHeight: this.descriptor['screenHeight'],
    };
  }

}
