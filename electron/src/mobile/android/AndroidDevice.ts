import {MobileDevice} from '../MobileDevice';
import {ADB_SERVICE_PORT, AdbClient} from '@e/mobile/android/adb/AdbClient';
import PushTransfer from '@dead50f7/adbkit/lib/adb/sync/pushtransfer';
import {AppiumService} from '@e/mobile/android/adb/Appium';
import logger from '@e/services/logger';
import {remote} from 'webdriverio';
import {ConnectType} from '@e/mobile/cons/common';
import {ScrcpyServer} from '@e/mobile/android/scrcpy/ScrcpyServer';
import {SERVER_PORT} from '@e/mobile/android/scrcpy/Constants';
import {Forward} from '@dead50f7/adbkit/lib/Forward';
import {waitSeconds} from '@e/rpa/utils';
import {getAFreePort} from '@e/utils/utils';
import WebSocket from 'ws';
import path from 'path';
import {extractTextNodes} from '@e/mobile/android/util';
import {Driver} from '@e/mobile/android/automator/driver';
import _ from "lodash";
import axios from "axios";
import {URL} from "url";
import fs from "fs-extra";
import Timeout = NodeJS.Timeout;

export interface AppActivity {
  packageName: string;
  activity: string;
}

enum PID_DETECTION {
  UNKNOWN,
  PIDOF,
  GREP_PS,
  GREP_PS_A,
  LS_PROC,
}

const android_ffmpeg_download_url: string = 'https://dl.szdamai.com/downloads/ffmpeg/android/ffmpeg';

export abstract class AndroidDevice extends MobileDevice {
  static readonly INITIAL_UPDATE_TIMEOUT = 1500;

  protected properties?: Record<string, string>;

  protected spawnServer = true;
  protected scrcpyPid: number = -1;
  private pidDetectionVariant: PID_DETECTION = PID_DETECTION.UNKNOWN;
  private throttleTimeoutId?: Timeout;
  private lastEmit = 0;

  public readonly TAG: string;

  protected client: AdbClient;

  protected constructor(udid: string, connectType: ConnectType, state: string) {
    super(udid, connectType);
    this.TAG = `[${this.udid}]`;
    this.client = AdbClient.createClient();
  }

  public async prepareAdb() {}

  public async disconnectAdb() {}

  async getClient(): Promise<AdbClient> {
    this.kickInUse();
    return this.client;
  }

  public isDirect(): boolean {
    return this.connectType === 'USB' || this.connectType === 'WIFI';
  }

  abstract setState(state: string): any;

  public async uiautomator(): Promise<Driver> {
    return new Driver(this);
  }

  public async driver(): Promise<WebdriverIO.Browser> {
    this.kickInUse();
    await AppiumService.getInstance().startServer();
    let systemPort = await getAFreePort();
    const capabilities = {
      platformName: 'Android',
      'appium:automationName': 'UiAutomator2',
      'appium:udid': this.udid,
      'appium:noReset': true, // 不复位数据
      'appium:unicodeKeyboard': false, // 隐藏键盘
      'appium:newCommandTimeout': 5 * 60,
      'appium:adbPort': ADB_SERVICE_PORT,
      'appium:systemPort': systemPort,
    };
    const wdOpts = {
      hostname: '127.0.0.1',
      port: AppiumService.getInstance().appiumPort,
      logLevel: 'error',
      capabilities,
    };
    logger.log(
      this.TAG,
      `trying to connect appium with port: ${AppiumService.getInstance().appiumPort}`,
    );
    // @ts-ignore
    const driver = await remote(wdOpts);
    logger.log(this.TAG, `connected appium with port: ${AppiumService.getInstance().appiumPort}`);
    await driver.updateSettings({
      waitForIdleTimeout: 1,
    });
    return driver;
  }

  async isHYKeyboardInstalled() {
    const client = await this.getClient();
    return client.isInstalled(this.udid, 'com.android.hykeyboard');
  }

  async getHYKeyboardVersion() {
    const client = await this.getClient();
    let output = await this.runShellCommandAdbKit('dumpsys package com.android.hykeyboard');
    if (output && /versionCode=(\d+)/.test(output)) {
      return parseInt(RegExp.$1);
    }
  }

  //花漾键盘的最新版本
  readonly latest_version = 2002;
  public async installHyKeyboard(force = false, enable = true): Promise<any> {
    const client = await this.getClient();
    let forceInstall = force;
    let runnerOut = await this.runShellCommandAdbKit(
      'pm list instrumentation | grep com.android.hykeyboard',
    );
    if (!runnerOut || runnerOut.indexOf('androidx.test.runner.AndroidJUnitRunner') == -1) {
      forceInstall = true;
    }

    let current = '';
    if (!forceInstall) {
      current = await this.getCurrentIME();
      if (current.indexOf('com.android.hykeyboard') != -1) {
        return current;
      }
    }

    let installed = await client.isInstalled(this.udid, 'com.android.hykeyboard');
    if(installed) {
      let version = await this.getHYKeyboardVersion();
      if(!version || version < this.latest_version) {
        forceInstall = true;
      }
    }
    if (forceInstall || !installed) {
      const apkPath = path.join(__dirname, '../extra', 'scrcpy', 'HYKeyboard.apk');
      await client.install(this.udid, apkPath);
    }
    installed = await client.isInstalled(this.udid, 'com.android.hykeyboard');
    if (installed) {
      for (let i = 0; i < 5; i++) {
        await waitSeconds(1, false);
        if(enable) {
          await client.runAdbCommand(this.udid, 'shell', 'ime enable com.android.hykeyboard/.HyIME');
          await client.runAdbCommand(this.udid, 'shell', 'ime set com.android.hykeyboard/.HyIME');
        }
        await client.runAdbCommand(
          this.udid,
          'shell',
          'pm grant com.android.hykeyboard android.permission.WRITE_CONTACTS',
        );
        await client.runAdbCommand(
          this.udid,
          'shell',
          'pm grant com.android.hykeyboard android.permission.READ_CONTACTS',
        );
        await client.runAdbCommand(
          this.udid,
          'shell',
          'appops set com.android.hykeyboard WRITE_CONTACTS allow',
        );
        current = await this.getCurrentIME();
        if (current.indexOf('com.android.hykeyboard') != -1) {
          return current;
        }
      }
    }
    return current;
  }

  public async getClipboardByHYKeyboard() {
    let keyboardVersion = await this.getHYKeyboardVersion();
    if(!keyboardVersion || keyboardVersion < 2002) { //2002以上版本支持获取剪切板
      await this.installHyKeyboard(true, false);
    }
    let output = await this.runShellCommandAdbKit(`am instrument -w -r -e debug false -e class com.android.hykeyboard.Tools#getClipboard com.android.hykeyboard/androidx.test.runner.AndroidJUnitRunner`);
    let response = this.parseHyOutput(output);
    if(response.success) {
      return response.data;
    }
    return undefined;
  }

  public async installAppium(): Promise<any> {
    const driver = await this.driver();
    driver?.deleteSession({ shutdownDriver: false }).catch(() => {});
  }

  public async textExtraction(teamId: number, translate = true, lang = '中文', mode = 'chatgpt'): Promise<string> {
    let xml = '';
    let textNodes = [];
    try {
      xml = await this.dumpLayout();
      textNodes = extractTextNodes(xml);
      if (translate) {
        //需要翻译
        await this.translateTextExtraction(teamId, lang, mode, textNodes);
      }
      return JSON.stringify(textNodes);
    } catch (e) {
      logger.error(`[APP] 提取文本失败`, e, xml, textNodes);
      throw new Error('提取文本失败');
    }
  }

  /**
   * 启动一个应用
   * @param packageName
   * @param activity 如果不为空表示启动指定的activity
   */
  public async startPackage(packageName: string, activity?: string): Promise<string> {
    let command = '';
    if (!activity) {
      let brief = await this.runShellCommandAdbKit('cmd package resolve-activity --brief ' + packageName);
      let lines = _.filter(brief.split('\n'), (line) => line.indexOf(packageName) != -1 && line.indexOf('/') != -1);
      if(lines.length > 0) {
        let line = lines[0].trim();
        activity = line.substring(line.indexOf('/') + 1);
      }
      if(!activity) {
        let info = await this.runShellCommandAdbKit(`dumpsys package ${packageName}`);
        let lines = info.split('\n');
        let row = 0;
        for (; row < lines.length; row++) {
          let line = lines[row];
          if (line.indexOf('android.intent.action.MAIN') != -1) {
            break;
          }
        }
        let activityReg = new RegExp(`${packageName}\/([^\\s]+)`);
        for (; row < lines.length; row++) {
          let line = lines[row];
          if (activityReg.test(line)) {
            activity = RegExp.$1;
            break;
          }
        }
      }
      if (!activity) {
        //找不到启动器，使用 monkey 的方式启动应用，但是这种方式会导致关闭方向锁定
        //adb shell monkey -p ${packageName} -c android.intent.category.LAUNCHER 1
        command = `monkey -p ${packageName} -c android.intent.category.LAUNCHER 1`;
        return this.runShellCommandAdbKit(command);
      }
    }
    //adb shell am start com.tencent.mm/.ui.LauncherUI
    command = `am start ${packageName}/${activity}`;
    return this.runShellCommandAdbKit(command);
  }

  /**
   * 获取当前正打开的应用的包名
   */
  public async getCurrentPackage(): Promise<AppActivity | undefined> {
    let info = await this.runShellCommandAdbKit('dumpsys window');
    let app: any;
    if (info) {
      let match = this.matchShellResult(/mCurrentFocus=Window{.*? (\S+?)\/(\S+?)}/, info);
      if (match) {
        app = {
          packageName: match[1],
          activity: match[2],
        };
      }
      if (app) {
        info = await this.runShellCommandAdbKit('dumpsys package ' + app.packageName);
        match = this.matchShellResult(/versionName=([^\s]+)/, info);
        if (match) {
          app.version = match[1];
        }
      }
    }
    return app;
  }

  protected matchShellResult(regex: RegExp, info: string) {
    let strs = info.split('\n');
    for (let str of strs) {
      const match = str?.match(regex);
      if (match) {
        return match;
      }
    }
    return undefined;
  }

  /**
   * 往手机push一个文件
   * @param contents contents When String, treated as a local file path and forwarded to sync.pushFile(). Otherwise, treated as a Stream and forwarded to sync.pushStream().
   * @param path
   */
  public async push(contents: string, path: string): Promise<PushTransfer> {
    const client = await this.getClient();
    return client.push(this.udid, contents, path);
  }

  /**
   * 将文件推送到手机
   * @param inputPathOrUrl http链接或者是本机的文件
   * @param path
   */
  public async pushFileWithProgress(inputPathOrUrl: string, remoteDir: string, onProgress?: (props: { status?: string; message?: string }) => void): Promise<string> {
    const client = await this.getClient();
    let stream, totalSize: number, name: string;

    if(/https?:\/\//.test(inputPathOrUrl)) {
      // 直接通过 shell curl 方式下载
      name = new URL(inputPathOrUrl).pathname.split('/').pop() || 'uploaded_file';
      // android 存储路径
      let remoteFile = `${remoteDir}/${name}`;
      let command = `curl -L -# -o ${remoteFile} ${inputPathOrUrl}`;
      // await client.runShellCommandAdbKit(this.udid, command);
      await client.runAdbCommand(this.udid, 'shell', command, undefined, {
        stderr: (text = '') => {
          // 检测 text 是否包含进度信息 xx.x%
          const percentMatch = text.match(/(\d+(\.\d+)?)%$/);
          if (percentMatch) {
            const percent = parseFloat(percentMatch[1]);
            onProgress?.({
              status: 'loading',
              message: `${Math.min(99.9, percent)}%`,
            });
          }
        }
      });
      onProgress?.({
        status: 'success',
        message: `上传成功`,
      });
      return remoteFile;
    } else {
      // 本地文件
      stream = fs.createReadStream(inputPathOrUrl);
      totalSize = fs.statSync(inputPathOrUrl).size;
      name = path.basename(inputPathOrUrl);
      // android 存储路径
      let remoteFile = `${remoteDir}/${name}`;
      let transfer = await client.push(this.udid, stream, remoteFile);
      let percent = 0;
      let lastTrigger = Date.now();
      return new Promise((resolve, reject) => {
        transfer.on('progress', (event) => {
          let newPercent = 0;
          if(totalSize == 0) {
            newPercent = Math.floor(100 * event.bytesTransferred / (event.bytesTransferred + 1024));
          } else {
            newPercent = Math.floor(100 * event.bytesTransferred / totalSize);
          }
          if(newPercent != percent) {
            if(Date.now() - lastTrigger > 1000 || newPercent == 100) {
              lastTrigger = Date.now();
              onProgress?.({
                status: 'loading',
                message: `${Math.min(95, newPercent)}%`,
              });
            }
          }
          percent = newPercent;
        });

        transfer.on('end', () => {
          onProgress?.({
            status: 'success',
            message: `上传成功`,
          });
          resolve(remoteFile);
        });
        transfer.on('error', (err) => {
          onProgress?.({
            status: 'failed',
            message: `上传失败`,
          });
          reject(err.message);
        });
      });
    }
  }

  /**
   * @param localPath 本地文件保存路径
   * @param path 手机文件路径
   */
  public async pull(localPath: string, path: string) {
    const client = await this.getClient();
    return client.runAdbCommand(this.udid, 'pull', [path, localPath]);
  }

  public async dumpLayout() {
    //下面的代码在rpa环境中一定会失败
    let ok = await this.runShellCommandAdbKit(
      'am instrument -w -r -e debug false -e class com.android.hykeyboard.DumpUI#dump com.android.hykeyboard/androidx.test.runner.AndroidJUnitRunner',
    );
    if (ok.includes('OK (1 test)')) {
      let xml = await this.runShellCommandAdbKit(
        `cat /sdcard/Android/data/com.android.hykeyboard/cache/window_dump.xml`,
      );
      return xml;
    } else {
      let driver = await this.driver();
      return await driver.getPageSource();
    }
  }

  public async runShellCommandAdbKit(command: string): Promise<string> {
    const client = await this.getClient();
    return await client.runShellCommandAdbKit(this.udid, command);
  }

  public async runCommandAdbKit(shell: string, command: string): Promise<string> {
    const client = await this.getClient();
    return await client.runAdbCommand(this.udid, shell, command);
  }

  public parseHyOutput(out: string) {
    if(out) {
      for(let line of out.split('\n')) {
        if(line.indexOf('invoke_response=') != -1) {
          line = line.substring(line.indexOf('invoke_response=') + 'invoke_response='.length);
          return JSON.parse(line);
        }
      }
    }
    return {success: false, data: 'Unknown Error.'};
  }

  protected async executedWithoutError(command: string): Promise<boolean> {
    return this.runShellCommandAdbKit(command)
      .then((output) => {
        const err = parseInt(output, 10);
        return err === 0;
      })
      .catch(() => {
        return false;
      });
  }

  protected async killOldProcessOnStart() {
    const pids = await ScrcpyServer.getServerPid(this);
    if (Array.isArray(pids) && pids.length) {
      logger.error(this.TAG, `killing previous scrcpy process ${pids.join(' ')}`);
      for (let pid of pids) {
        await this.killProcess(pid);
      }
    }
    for (const packageName of [
      'io.appium.settings',
      'io.appium.uiautomator2.server',
      'io.appium.uiautomator2.server.test',
    ]) {
      await this.runShellCommandAdbKit(`am force-stop ${packageName}`);
    }
  }

  /**
   * 转发 scrcpy-service 端口，可重复调用
   */
  private forwardPromise?: Promise<number>;
  async forwardScrcpy(): Promise<number> {
    const client = await this.getClient();
    if (this.forwardPromise) {
      try {
        await this.forwardPromise;
      } catch (ignore: any) {}
    }
    this.forwardPromise = new Promise(async (resolve, reject) => {
      try {
        await this.startServer();
        let remote = `tcp:${SERVER_PORT}`;
        const forwards = await client.listForwards(this.udid);
        const forward = forwards.find((item: Forward) => {
          return (
            item.remote === remote && item.local.startsWith('tcp:') && item.serial === this.udid
          );
        });
        if (forward) {
          const { local } = forward;
          let port = parseInt(local.split('tcp:')[1], 10);
          if (await this.isForwardOk(port)) {
            resolve(port);
            logger.log(this.TAG, `reuse scrcpy forward port: ${port}`);
            return;
          } else {
            logger.log(this.TAG, `test scrcpy port failed, restarting scrcpy server`);
            for (let count = 0; count < 3; count++) {
              try {
                await this.killServer(0);
                await this.startServer();
                break;
              } catch (ignore: any) {
                await waitSeconds(1, false);
              }
            }
          }
        }
        const port = await getAFreePort();
        const local = `tcp:${port}`;
        await client.forward(this.udid, local, remote);
        resolve(port);
      } catch (error: any) {
        reject(error);
      }
    });
    try {
      const port = await this.forwardPromise;
      return port;
    } finally {
      this.forwardPromise = undefined;
    }
  }

  isForwardOk(port: number) {
    return new Promise((resolve) => {
      const ws = new WebSocket(`ws://127.0.0.1:${port}`);
      ws.on('message', (data) => {
        ws.close();
        resolve(true);
      });
      ws.on('error', (err) => {
        resolve(false);
      });
      setTimeout(() => resolve(false), 5000);
    });
  }

  public async getPidOf(processName: string): Promise<number[] | undefined> {
    if (!this.connected) {
      return;
    }
    if (this.pidDetectionVariant === PID_DETECTION.UNKNOWN) {
      this.pidDetectionVariant = await this.findDetectionVariant();
    }
    switch (this.pidDetectionVariant) {
      case PID_DETECTION.PIDOF:
        return this.pidOf(processName);
      case PID_DETECTION.GREP_PS:
        return this.grepPs(processName);
      case PID_DETECTION.GREP_PS_A:
        return this.grepPs_A(processName);
      default:
        return this.listProc(processName);
    }
  }

  private async pidOf(processName: string): Promise<number[]> {
    return this.runShellCommandAdbKit(`pidof ${processName}`)
      .then((output) => {
        return output
          .split(' ')
          .map((pid) => parseInt(pid, 10))
          .filter((num) => !isNaN(num));
      })
      .catch(() => {
        return [];
      });
  }

  private async hasPs(): Promise<boolean> {
    return this.executedWithoutError('ps | grep init 2>&1 >/dev/null; echo $?');
  }

  private async hasPs_A(): Promise<boolean> {
    return this.executedWithoutError('ps -A | grep init 2>&1 >/dev/null; echo $?');
  }

  private filterPsOutput(processName: string, output: string): number[] {
    const list: number[] = [];
    const processes = output.split('\n');
    processes.map((line) => {
      const cols = line
        .trim()
        .split(' ')
        .filter((item) => item.length);
      if (cols[cols.length - 1] === processName) {
        const pid = parseInt(cols[1], 10);
        if (!isNaN(pid)) {
          list.push(pid);
        }
      }
    });
    return list;
  }

  private async grepPs_A(processName: string): Promise<number[]> {
    return this.runShellCommandAdbKit(`ps -A | grep ${processName}`)
      .then((output) => {
        return this.filterPsOutput(processName, output);
      })
      .catch(() => {
        return [];
      });
  }

  private async hasPidOf(): Promise<boolean> {
    const ok = await this.executedWithoutError('which pidof 2>&1 >/dev/null && echo $?');
    if (!ok) {
      return false;
    }
    return this.runShellCommandAdbKit('echo $PPID; pidof init')
      .then((output) => {
        const pids = output.split('\n').filter((a) => a.length);
        if (pids.length < 2) {
          return false;
        }
        const parentPid = pids[0].replace('\r', '');
        const list = pids[1].split(' ');
        if (list.includes(parentPid)) {
          return false;
        }
        return list.includes('1');
      })
      .catch(() => {
        return false;
      });
  }

  private async grepPs(processName: string): Promise<number[]> {
    return this.runShellCommandAdbKit(`ps | grep ${processName}`)
      .then((output) => {
        return this.filterPsOutput(processName, output);
      })
      .catch(() => {
        return [];
      });
  }

  private async listProc(processName: string): Promise<number[]> {
    const find = `find /proc -maxdepth 2 -name cmdline  2>/dev/null`;
    const lines = await this.runShellCommandAdbKit(
      `for L in \`${find}\`; do grep -sae '^${processName}' $L 2>&1 >/dev/null && echo $L; done`,
    );
    const re = /\/proc\/([0-9]+)\/cmdline/;
    const list: number[] = [];
    lines.split('\n').map((line) => {
      const trim = line.trim();
      const m = trim.match(re);
      if (m) {
        list.push(parseInt(m[1], 10));
      }
    });
    return list;
  }

  private async findDetectionVariant(): Promise<PID_DETECTION> {
    if (await this.hasPidOf()) {
      return PID_DETECTION.PIDOF;
    }
    if (await this.hasPs_A()) {
      return PID_DETECTION.GREP_PS_A;
    }
    if (await this.hasPs()) {
      return PID_DETECTION.GREP_PS;
    }
    return PID_DETECTION.LS_PROC;
  }

  protected emitUpdate(setUpdateTime = true): void {
    const THROTTLE = 300;
    const now = Date.now();
    const time = now - this.lastEmit;
    if (time > THROTTLE) {
      this.lastEmit = now;
      return;
    }
    if (!this.throttleTimeoutId) {
      this.throttleTimeoutId = setTimeout(() => {
        delete this.throttleTimeoutId;
        this.emitUpdate(false);
      }, THROTTLE - time);
    }
  }

  public async killServer(pid: number): Promise<void> {
    this.spawnServer = false;
    const realPid = await this.getServerPid();
    if (typeof realPid !== 'number') {
      return;
    }
    if (realPid !== pid && pid !== 0) {
      logger.info(
        this.TAG,
        `Requested to kill server with PID ${pid}. Real server PID is ${realPid}.`,
      );
    }
    try {
      const output = await this.killProcess(realPid);
      if (output) {
        logger.log(this.TAG, `kill server: "${output}"`);
      }
      this.scrcpyPid = -1;
      this.emitUpdate();
    } catch (error: any) {
      logger.info(this.TAG, `Error: ${error.message}`);
      throw error;
    }
  }

  public async startServer(): Promise<number | undefined> {
    this.spawnServer = true;
    const pid = await this.getServerPid();
    if (typeof pid === 'number') {
      return pid;
    }
    try {
      const output = await ScrcpyServer.run(this);
      if (output) {
        logger.log(this.TAG, `start scrcpy server success: "${output}"`);
      }
      return this.getServerPid();
    } catch (error: any) {
      logger.error(this.TAG, `start scrcpy server Error: ${error.message}`);
      throw error;
    }
  }

  public killProcess(pid: number): Promise<string> {
    const command = `kill ${pid}`;
    return this.runShellCommandAdbKit(command);
  }

  protected async getServerPid(): Promise<undefined | number> {
    const pids = await ScrcpyServer.getServerPid(this);
    let pid;
    if (!Array.isArray(pids) || !pids.length) {
      pid = -1;
    } else {
      pid = pids[0];
    }
    if (this.scrcpyPid !== pid) {
      this.scrcpyPid = pid;
      this.emitUpdate();
    }
    if (pid !== -1) {
      return pid;
    } else {
      return;
    }
  }

  public async getCurrentIME(): Promise<string> {
    const client = await this.getClient();
    const ime =
      (await client.runAdbCommand(
        this.udid,
        'shell',
        'settings get secure default_input_method',
      )) || '';
    return ime.trim();
  }

  public async getProperties(): Promise<Record<string, string> | undefined> {
    const client = await this.getClient();
    if (this.properties) {
      return this.properties;
    }
    if (!this.connected) {
      return;
    }
    this.properties = await client.getProperties(this.udid);
    return this.properties;
  }

  public async getScreenSize() {
    if (!this.connected) {
      return [];
    }
    return this.runShellCommandAdbKit('wm size').then((output) => {
      const re = /(\d+)x(\d+)/;
      const m = output.match(re);
      if (m) {
        return [parseInt(m[1], 10), parseInt(m[2], 10)];
      }
      return [];
    });
  }

  async snapshot(scalingFactor: number): Promise<Buffer> {
    const client = await this.getClient();

    let ffmpeg = await this.checkFFMpeg();
    if(ffmpeg) {
      //使用ffmpeg方式截屏
      let scale = 150 / scalingFactor;
      let output = await this.runShellCommandAdbKit(`rm -f /sdcard/screen.png && rm -f /sdcard/screen.jpg && screencap -p /sdcard/screen.png && /data/local/tmp/ffmpeg -loglevel info -y -i /sdcard/screen.png -vf scale=iw/${scale}:ih/${scale} -q:v 5 /sdcard/screen.jpg`);
      if(output.includes('/sdcard/screen.jpg')) {//说明转成功了
        const transfer = await client.pull(this.udid, '/sdcard/screen.jpg');
        let buffer: Buffer = await new Promise((resolve, reject) => {
          const chunks: any[] = [];
          transfer.on('data', (chunk) => chunks.push(chunk));
          transfer.on('end', () => resolve(Buffer.concat(chunks)));
          transfer.on('error', reject);
        });
        return buffer;
      }
    }

    //如果使用ffmpeg截屏失败
    let buffer: Buffer = await new Promise(async (resolve, reject)=>{
      try {
        const connection = await client.screencap(this.udid);
        const chunks: any[] = [];

        connection.on('data', chunk => chunks.push(chunk));
        connection.on('end', async () => {
          //todo 参考 scalingFactor
          const pngBuffer: Buffer = Buffer.concat(chunks);
          resolve(pngBuffer);
        });
      } catch (err) {
        reject(err);
      }
    });

    // let temp = path.resolve(app.getPath('temp'), `mobile-${random.nextString(8)}.png`);
    // let adbRet = await this.runShellCommandAdbKit('screencap -p /sdcard/screenshot.png');
    // adbRet = await this.pull(temp, '/sdcard/screenshot.png');
    // adbRet = await this.runShellCommandAdbKit('rm /sdcard/screenshot.png');
    // fs.unlink(temp, () => {});
    return buffer;
  }

  private async checkFFMpeg() {
    let output = await this.runShellCommandAdbKit('ls /data/local/tmp');
    if(output.includes('ffmpeg')) {
      return true;
    }
    this.installFFMpeg().catch(()=>{});
    return false;
  }

  private install_ffmpeg_promise?: Promise<boolean>;
  private async installFFMpeg() {
    let client = await this.getClient();
    if(this.install_ffmpeg_promise) {
      return this.install_ffmpeg_promise;
    }
    this.install_ffmpeg_promise = new Promise(async (resolve, reject)=>{
      const response = await axios.get(android_ffmpeg_download_url, { responseType: 'stream' });
      let transfer = await client.push(this.udid, response.data, '/data/local/tmp/ffmpeg');
      transfer.on('end', async ()=>{
        await waitSeconds(1);
        let output = await this.runShellCommandAdbKit('chmod +x /data/local/tmp/ffmpeg');
        resolve(true);
      });
      transfer.on('error', reject);
    });
    try {
      await this.install_ffmpeg_promise;
    } finally {
      delete this.install_ffmpeg_promise;
    }
  }

}
