import WebSocket from "ws";
import {Buffer} from "buffer";

const DEVICE_NAME_FIELD_LENGTH = 64;
const magic_initial = Buffer.from('scrcpy_initial');
const magic_message = Buffer.from('scrcpy_message');

let client_id_seed = 1;

export class AbstractStreamer {

  protected clientId: number;
  ws: WebSocket;

  smallPreview: boolean;

  constructor(ws: WebSocket, smallPreview: boolean) {
    this.clientId = client_id_seed++;
    this.ws = ws;
    this.smallPreview = smallPreview;
  }

  async sendInitialInfo(bounds: {width: number, height: number}) {
    // let rotation = await driver.getRotation();
    let rotation = 1;
    let displayId = 0;
    let encoderName = 'mjpeg';
    let encoderNameBuf = Buffer.from(encoderName);
    let deviceNameBuf = Buffer.alloc(DEVICE_NAME_FIELD_LENGTH);
    Buffer.from('Android').copy(deviceNameBuf, 0);

    let off = 0;
    let length = magic_initial.length + DEVICE_NAME_FIELD_LENGTH + 4 + 24 + 4 + 4 + 25 + 4 + 35 + encoderNameBuf.length + 4 + 4 + encoderNameBuf.length + 4;
    let buffer = Buffer.alloc(length);
    //magic_initial mark
    off += magic_initial.copy(buffer, off);
    //DEVICE_NAME_FIELD_LENGTH 64
    off += deviceNameBuf.copy(buffer, off);
    //displaysCount 4
    off = buffer.writeInt32BE(1, off);
    // displayInfo 24
    off = buffer.writeInt32BE(displayId, off); //displayID
    off = buffer.writeInt32BE(bounds.width, off); //width
    off = buffer.writeInt32BE(bounds.height, off); //height
    off = buffer.writeInt32BE(rotation, off); //rotation
    off += 8; //layerStack and flags
    //connectionCount 4
    off = buffer.writeInt32BE(1, off); //不知道有什么用，写死1
    //screenInfoBytes 4
    off = buffer.writeInt32BE(25, off);
    //screenInfo 25
    off = buffer.writeInt32BE(0, off); //left
    off = buffer.writeInt32BE(0, off); //top
    off = buffer.writeInt32BE(bounds.width, off); //right
    off = buffer.writeInt32BE(bounds.height, off); //bottom
    off = buffer.writeInt32BE(bounds.width, off); //width
    off = buffer.writeInt32BE(bounds.height, off); //height
    off = buffer.writeUint8(rotation, off); //rotation
    //videoSettingBytes 4
    off = buffer.writeInt32BE(35 + encoderNameBuf.length, off);
    //videoSettings 35 + encoderNameBuf.length
    off = buffer.writeInt32BE(0, off); //bitrate
    off = buffer.writeInt32BE(10, off); //maxFps
    off = buffer.writeInt8(0, off); //iFrameInterval
    off = buffer.writeInt16BE(bounds.width, off);
    off = buffer.writeInt16BE(bounds.height, off);
    off = buffer.writeInt16BE(0, off); //left
    off = buffer.writeInt16BE(0, off); //top
    off = buffer.writeInt16BE(bounds.width, off); //right
    off = buffer.writeInt16BE(bounds.height, off); //bottom
    off = buffer.writeInt8(0, off); //sendFrameMeta
    off = buffer.writeInt8(0, off); //lockedVideoOrientation
    off = buffer.writeInt32BE(displayId, off); //displayId
    off = buffer.writeInt32BE(0, off); //codecOptionsBytes.length
    off = buffer.writeInt32BE(encoderNameBuf.length, off); //encoderNameBytes.length
    off += encoderNameBuf.copy(buffer, off); //encoderNameBytes
    //encodersCount 4
    off = buffer.writeInt32BE(1, off);
    //encoderName 4 + encoderNameBuf.length
    off = buffer.writeInt32BE(encoderNameBuf.length, off); //encoderNameBytes.length
    off += encoderNameBuf.copy(buffer, off); //encoderNameBytes
    //clientId 4
    off = buffer.writeInt32BE(this.clientId, off);

    this.ws.send(buffer);
  }


}
