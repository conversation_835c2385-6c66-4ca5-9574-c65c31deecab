import { ipc<PERSON>ender<PERSON> } from 'electron';
import EventEmitter from 'events';
import {ConnectType, MobilePlatform} from '@e/mobile/cons/common';

interface Protocol {
  version: number;
  supportDirectWebSocket: boolean;
  keyboardVersion: number;
}

type StreamType = 'webrtc' | 'direct_websocket';

interface RoomConfig {
  teamId: number;
  mobileId: number;
  connectType: ConnectType;
  platform: MobilePlatform;
  parentMobileId: number; //如果是联营的手机
  udid: string;
  roomId: string;
  turnURI: string;
  turnUsername: string;
  turnPassword: string;
  watcher: any;
  //是不小屏预览方式打开
  smallPreview: boolean;
}

interface BeforeExecuteEvent {
  id: number; //itemId
  taskId: number;
  mobileId: number;
  parentMobileId?: number;
  udid: string;
  preview: boolean;
  flowName: string;
  taskName: string;
  executeTime?: Date;
}

interface AfterExecuteEvent {
  id: number; //itemId
  mobileId: number;
  parentMobileId?: number;
  udid: string;
}

/**
 * 必须指定一个
 */
class MobileVideoRoom extends EventEmitter {
  config: RoomConfig;
  pc?: RTCPeerConnection;
  dataChannel?: RTCDataChannel;
  wsUrl!: string;
  ws?: WebSocket;

  hasReceiveAnswer = false;
  candidateQueue: any[] = [];

  protocol: Protocol = { version: 1, supportDirectWebSocket: true, keyboardVersion: 114 };
  streamType: StreamType = 'webrtc';
  streamTypePromise: Promise<string>;
  streamTypePromiseResolve!: (value: string) => void;

  constructor(config: RoomConfig) {
    super();
    this.config = config;
    this.streamTypePromise = new Promise((resolve, reject) => {
      this.streamTypePromiseResolve = resolve;
      setTimeout(() => {
        resolve(this.streamType);
      }, 3000);
    });
    this.init().then(() => {});
  }

  async init() {
    try {
      //加入房间
      await this.joinRoom();
    } catch (e: any) {
      console.error('init mobile video room error:', e);
    }
  }

  async initDirectWebsocket() {
    this.wsUrl = await ipcRenderer.invoke('mobile-forward-scrcpy', {
      udid: this.config.udid,
      platform: this.config.platform,
      connectType: this.config.connectType,
      smallPreview: this.config.smallPreview
    });
    this.sendSignaling({
      action: 'direct-websocket-ready',
      wsUrl: this.wsUrl,
    });
  }

  async initWebrtc() {
    if (!this.pc) {
      const iceConfig = {
        iceServers: [
          {
            urls: this.config.turnURI,
            username: this.config.turnUsername,
            credential: this.config.turnPassword,
          },
          {
            urls: 'stun:stun.l.google.com:19302',
          },
        ],
      };
      this.pc = new RTCPeerConnection(iceConfig);
      this.pc.onicecandidate = async (event) => {
        if (event.candidate) {
          await this.sendSignaling({ action: 'signaling', candidate: event.candidate });
        }
      };
      this.pc.onnegotiationneeded = async () => {
        const offer = await this.pc!.createOffer();
        try {
          await this.pc!.setLocalDescription(offer);
        } catch (e) {
          console.error(e);
        }
        await this.sendSignaling({ action: 'signaling-offer', sdp: this.pc!.localDescription });
      };
      this.pc.oniceconnectionstatechange = async () => {
        let iceConnectionState = this.pc!.iceConnectionState;
        if (
          iceConnectionState === 'failed' ||
          iceConnectionState === 'disconnected' ||
          iceConnectionState === 'closed'
        ) {
          await this.sendSignaling({ action: 'webrtc-disconnected', msg: 'Webrtc连接已断开' });
          this.onRoomError();
        }
      };
      this.dataChannel = this.pc.createDataChannel('mobile-datachannel');
      this.dataChannel.onopen = async () => {
        if(this.config.connectType === 'QCloud' && !this.config.smallPreview) {
          //腾讯去大屏时走的是腾讯自己的推流方案，不需要建立websocket连接
          //do nothing
        } else {
          await this.pipeWebrtcScrcpy();
        }
      };
      this.dataChannel.onmessage = async (event: any) => {
        if (typeof event.data === 'string') {
          //ignore
        } else {
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(event.data);
          }
        }
      };
      this.dataChannel.onerror = async (ev) => {
        console.error(ev);
      };
      this.dataChannel.onclose = async (ev) => {
        await this.sendSignaling({ action: 'webrtc-disconnected', msg: 'Webrtc连接已断开' });
        this.onRoomError();
      };
    }
  }

  private concatUint8Arrays(arrays: Uint8Array[]) {
    // 计算所有数组的总长度
    let totalLength = arrays.reduce((acc, value) => acc + value.length, 0);

    // 创建一个新的 Uint8Array，用来存储所有数据
    let result = new Uint8Array(totalLength);

    // 使用 set 方法将每个数组的数据依次复制到结果数组中
    let offset = 0;
    arrays.forEach((array) => {
      result.set(array, offset);
      offset += array.length;
    });

    return result;
  }

  async pipeWebrtcScrcpy() {
    try {
      this.wsUrl = await ipcRenderer.invoke('mobile-forward-scrcpy', {
        udid: this.config.udid,
        platform: this.config.platform,
        connectType: this.config.connectType,
        smallPreview: this.config.smallPreview
      });
      this.ws = new WebSocket(this.wsUrl);
      this.ws.onopen = async () => {
        console.log(
          `[${this.config.udid} / ${this.config.roomId}]`,
          'scrcpy ws service connected with ' + this.wsUrl,
        );
      };
      this.ws.onmessage = async (event) => {
        if (this.dataChannel?.readyState !== 'open') return;
        if (event.data instanceof Blob) {
          const buf = await event.data.arrayBuffer();
          const max_frame_length = 200000;
          if (buf.byteLength > max_frame_length) {
            //只有 scrcpy_partial 消息才可能oversize
            console.log('oversized package ' + buf.byteLength);
            let exactly = true;
            let count = Math.floor(buf.byteLength / max_frame_length);
            if (max_frame_length * count < buf.byteLength) {
              count++;
              exactly = false;
            }
            let offset = 0;
            for (let i = 0; i < count; i++) {
              let mark = new TextEncoder().encode('scrcpy_partial00');
              mark[14] = i == 0 ? 1 : 0;
              mark[15] = i == count - 1 ? 1 : 0;
              let sliceSize = max_frame_length;
              if (i == count - 1 && !exactly) {
                sliceSize = buf.byteLength % max_frame_length;
              }
              let slice = new Uint8Array(buf, offset, sliceSize);
              offset += sliceSize;
              let data = this.concatUint8Arrays([mark, slice]);
              this.dataChannel?.send(data.buffer);
            }
            return;
          }
          this.dataChannel?.send(buf);
        } else {
          this.dataChannel?.send(event.data);
        }
      };
      this.ws.onerror = async (e) => {
        console.log(`[${this.config.udid} / ${this.config.roomId}]`, 'scrcpy ws service error', e);
      };
      this.ws.onclose = async (e) => {
        console.log(
          `[${this.config.udid} / ${this.config.roomId}]`,
          'scrcpy ws service disconnected',
        );
        await this.sendSignaling({ action: 'scrcpy-disconnected', msg: e.reason || String(e) });
        this.onRoomError();
      };
    } catch (e: any) {
      await this.sendSignaling({
        action: 'init-scrcpy-failed',
        msg: e.message || e.msg || String(e),
      });
    }
  }

  async sendSignaling(data: any) {
    let str = JSON.stringify(data);
    await ipcRenderer.invoke('mobile-send-to-chat-room', {
      roomId: this.config.roomId,
      data: str,
    });
  }

  async onSignaling(data: any) {
    switch (data.action) {
      case 'signaling':
        if (!this.hasReceiveAnswer) {
          this.candidateQueue.push(data.candidate);
          break;
        }
        await this.pc!.addIceCandidate(data.candidate);
        break;
      case 'signaling-answer':
        this.hasReceiveAnswer = true;
        await this.pc!.setRemoteDescription(new RTCSessionDescription(data.sdp));
        // 处理队列中的消息
        while (this.candidateQueue.length > 0) {
          await this.pc!.addIceCandidate(this.candidateQueue.shift()!);
        }
        break;
      case 'trigger-mobile-watchers-change-event':
        mobileHelperContent?.emitWatcher(this.config.parentMobileId || this.config.mobileId);
        break;
      case 'stream-type-announce':
        this.streamType = data.streamType;
        this.streamTypePromiseResolve(this.streamType);
        break;
      case 'hy.scrcpy.command':
        data.udid = this.config.udid;
        data.platform = this.config.platform;
        data.teamId = this.config.teamId;
        data.roomId = this.config.roomId;
        let response = await ipcRenderer.invoke('hy.scrcpy.command', data);
        data.response = response;
        await this.sendSignaling(data);
        break;
    }
  }

  async joinRoom() {
    console.log('join room => ' + this.config.roomId);
    await ipcRenderer.invoke('mobile-join-room', { roomId: this.config.roomId });
  }

  private roomReady = false;
  onUserJoined(currentUser: string, data: any) {
    const userCount = data.userCount;
    const otherSide = currentUser !== data.fromUser;
    if (userCount > 1) {
      this.roomReady = true;
      this.sendSignaling({
        action: 'protocol-announce',
        protocol: this.protocol,
      }).then(() => {
        this.streamTypePromise.then(() => {
          switch (this.streamType) {
            case 'webrtc':
              this.initWebrtc();
              break;
            case 'direct_websocket':
              this.initDirectWebsocket();
              break;
          }
        });
      });
    }
  }
  onUserLeaved(currentUser: string, data: any) {
    const otherSide = currentUser !== data.fromUser;
    if (otherSide && this.roomReady) {
      //该房间曾经大于1人，现在人数为1，说明对面下线了
      this.pc?.close();
      if (this.streamType !== 'webrtc') {
        this.onRoomError();
      }
    }
  }

  async onChatRoomMessage(message: string) {
    let data = JSON.parse(message);
    await this.onSignaling(data);
  }

  _leaveTimeout: any;
  onRoomError() {
    try {
      this.pc?.close();
    } catch (e) {}
    try {
      this.ws?.close();
    } catch (e) {}
    clearTimeout(this._leaveTimeout);
    this._leaveTimeout = setTimeout(() => {
      mobileHelperContent?.leaveRoom(this.config.roomId, this.config);
    }, 1000);
  }
}

class MobileHelperPreloadContent {
  rooms: Map<string, MobileVideoRoom> = new Map();
  taskItems: Map<number, any> = new Map(); //记录有哪些流程在跑
  private emitWatcherTimerMap: Record<number, any> = {};

  mobileKickTimer: any;

  constructor() {}

  async init() {
    this.mobileKickTimer = setInterval(() => {
      let udids = [...this.rooms.values()].map((room) => room.config.udid);
      udids = [...new Set(udids)];
      if (udids.length > 0) {
        ipcRenderer
          .invoke('mobile-kickInUse', { udids })
          .then(() => {})
          .catch(() => {});
        console.log(new Date() + ' kick mobile in use done.');
      }
    }, 60 * 1000);
  }

  async close() {}

  emitWatcher(mobileId: number) {
    clearTimeout(this.emitWatcherTimerMap[mobileId]);
    this.emitWatcherTimerMap[mobileId] = setTimeout(() => {
      let mobileRooms = [];
      let roomWatchers = [];
      let addedWatchers: { teamId: number; id: number }[] = [];
      //添加观看者
      for (let room of this.rooms.values()) {
        if (room.config.mobileId == mobileId || room.config.parentMobileId == mobileId) {
          mobileRooms.push(room);
          // 不重复添加同一团队下的同一用户
          if (
            !addedWatchers.some(
              (w) => w.id == room.config.watcher.id && w.teamId == room.config.watcher.teamId,
            )
          ) {
            addedWatchers.push({
              teamId: room.config.watcher.teamId,
              id: room.config.watcher.id,
            });
            roomWatchers.push(room.config.watcher);
          }
        }
      }
      //添加rpa
      for (let taskItem of this.taskItems.values()) {
        if (taskItem.parentMobileId == mobileId || taskItem.mobileId == mobileId) {
          roomWatchers.push(taskItem);
          //只添加一个就够了
          break;
        }
      }
      console.log('emitWatcher', this.taskItems, roomWatchers);
      for (let room of mobileRooms) {
        room.sendSignaling({
          action: 'mobile-watchers-change',
          mobileId: mobileId,
          watchers: roomWatchers,
        });
      }
      // console.log(JSON.stringify(roomWatchers));
    }, 300);
  }

  async handleMobileInitiateVideoRoom(data: any) {
    let config = data as RoomConfig;
    let room = new MobileVideoRoom(config);
    this.rooms.set(config.roomId, room);
    this.emitWatcher(config.parentMobileId || config.mobileId);
  }

  getRoom(roomId: string) {
    return this.rooms.get(roomId);
  }

  leaveRoom(roomId: string, config: RoomConfig) {
    let room = this.rooms.get(roomId);
    if (room) {
      try {
        room.pc?.close();
      } catch (e) {}
      ipcRenderer.invoke('mobile-leave-room', { roomId });
      this.rooms.delete(roomId);
      this.emitWatcher(config.parentMobileId || config.mobileId);
    }
  }

  beforeTaskItemExecute(item: BeforeExecuteEvent) {
    this.taskItems.set(item.id, item);
    console.log('beforeTaskItemExecute', this.taskItems);
    this.emitWatcher(item.parentMobileId || item.mobileId);
  }

  afterTaskItemExecute(item: AfterExecuteEvent) {
    this.taskItems.delete(item.id);
    console.log('afterTaskItemExecute', this.taskItems);
    this.emitWatcher(item.parentMobileId || item.mobileId);
  }
}

let mobileHelperContent: MobileHelperPreloadContent = new MobileHelperPreloadContent();
ipcRenderer.on('mobile-initiate-video-room', (evt, data) => {
  mobileHelperContent.handleMobileInitiateVideoRoom(data).catch((ignore) => {});
});
ipcRenderer.on('mobile-room-users-change', (evt, currentUid, data) => {
  const roomId = data.roomId;
  let room = mobileHelperContent.getRoom(roomId);
  if (room) {
    const isLeave = data.event == 'leave';
    if (isLeave) {
      room.onUserLeaved(currentUid, data);
    } else {
      room.onUserJoined(currentUid, data);
    }
  }
});
ipcRenderer.on('mobile-room-message', async (evt, roomId, message) => {
  let room = mobileHelperContent.getRoom(roomId);
  if (room) {
    await room.onChatRoomMessage(message);
  }
});
ipcRenderer.on('before-rpa-item-execute', (evt, data) => {
  mobileHelperContent.beforeTaskItemExecute(data as BeforeExecuteEvent);
});
ipcRenderer.on('after-rpa-item-execute', (evt, data) => {
  mobileHelperContent.afterTaskItemExecute(data as AfterExecuteEvent);
});

window.onload = async function () {
  console.debug('mobile helper window loaded!');
  document.title = '花漾手机管理Helper窗口';
  await mobileHelperContent.init();
};

window.onunload = async function () {
  await mobileHelperContent?.close();
};
