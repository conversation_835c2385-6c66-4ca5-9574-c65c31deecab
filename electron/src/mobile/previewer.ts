import {<PERSON><PERSON><PERSON>} from "buffer";
import {JpegConsumer} from "@e/mobile/ios/consts";
import <PERSON><PERSON> from "jimp";
import {SnapshotProvider} from "@e/mobile/MobileDevice";

export class JpegPreviewService {
  snapshoter: SnapshotProvider;

  running = false;

  lastFullJpeg?: Buffer;

  callbacks: Map<number, JpegConsumer> = new Map();

  previewInterval?: any;

  duration = 5000;

  constructor(snapshoter: SnapshotProvider) {
    this.snapshoter = snapshoter;
  }

  subscribeJpeg(clientId: number, consumer: JpegConsumer) {
    this.callbacks.set(clientId, consumer);
    if(this.lastFullJpeg) {
      try {
        consumer.handleJpegData(this.lastFullJpeg);
      } catch (ignore: any) {}
    } else {
      this.takeSnapshot();
    }
  }

  unSubscribeJpeg(clientId: number) {
    this.callbacks.delete(clientId);
  }

  async start(): Promise<any> {
    if(this.running) {
      return;
    }
    this.running = true;
    this.kick();
  }

  kick() {
    if(this.running) {
      clearInterval(this.previewInterval);
      this.previewInterval = setInterval(async () => {
        await this.takeSnapshot();
      }, this.duration);
    }
  }

  async takeSnapshot() {
    for(let key  of this.callbacks.keys()) {
      let consumer = this.callbacks.get(key);
      if(!consumer || !consumer.isConnected()) {
        this.callbacks.delete(key);
      }
    }
    if(this.callbacks.size > 0) {
      let jpeg = await this.snapshoter.snapshot(50);
      let jimpImg = await Jimp.read(jpeg);
      jimpImg.resize(jimpImg.getWidth() / 2 , jimpImg.getHeight() / 2);
      jpeg = await jimpImg.getBufferAsync(Jimp.MIME_PNG);

      let buffer = Buffer.alloc(8 + 2 + 4 * 5);
      let off = 0;
      buffer.writeInt32BE(jimpImg.getWidth(), off); off += 4;
      buffer.writeInt32BE(jimpImg.getHeight(), off); off += 4;
      buffer.writeInt16BE(1, off); off += 2; //tiles count
      buffer.writeInt32BE(0, off); off += 4; //x
      buffer.writeInt32BE(0, off); off += 4; //y
      buffer.writeInt32BE(jimpImg.getWidth(), off); off += 4;
      buffer.writeInt32BE(jimpImg.getHeight(), off); off += 4;
      buffer.writeInt32BE(jpeg.length, off); off += 4;

      this.lastFullJpeg = Buffer.concat([buffer, jpeg]);
      for(let [clientId, consumer] of this.callbacks) {
        try {
          consumer.handleJpegData(this.lastFullJpeg);
        } catch (ignore: any) {}
      }
    }
  }

  stop() {
    this.running = false;
    this.lastFullJpeg = undefined;
    this.callbacks.clear();
    clearInterval(this.previewInterval);
  }

}
