import { net } from 'electron';
import FormData from 'form-data';
import _ from 'lodash';

import db from '../components/db';
import appConfig from '../configs/app';
import logger from './logger';
import { waitSeconds } from '@e/rpa/utils';
import { portalRpcClient } from '@e/components/backendTask';

const isDebug = appConfig.DEBUG;
export const filterPattern =
  /ajax-event|heartbeat|getCookies|setCookies|getHistories|uploadHistories|getUserBookmarks|overrideUserBookmarks|meta\/dwl|exchange\/rate|grantedFunctions|autofill\.json|\/transit\/groups|\/transit\/list|app_mobile|chat\/completions/;

interface RequestAgentProps {
  teamId?: number;
  userId?: number;
  token?: string;
}

class NetworkError extends Error {
  constructor(message: string, public code: string) {
    super(message);
  }
}

export function rawRequest(url: string, options?: { [propName: string]: any }): Promise<any> {
  const {
    data,
    parseJson = true,
    method = 'get',
    params = {},
    exposeHeaders = false,
    timeout = 0,
    headers,
    ...restOpts
  } = options || {};
  return new Promise((resolve, reject) => {
    let resData = Buffer.from([]);
    const queryStr = new URLSearchParams({
      ...params,
    }).toString();
    const _url = url.includes('?') ? url : `${url}?${queryStr}`;
    const req = net.request({
      url: _url,
      method,
      ...restOpts,
    });
    if (_.size(headers) > 0) {
      _.forEach(headers, (v, k) => {
        req.setHeader(k, v);
      });
    }
    req.on('response', (res) => {
      res.on('data', (chunk) => {
        resData = Buffer.concat([resData, chunk]);
      });
      res.on('end', () => {
        if (!parseJson) {
          if (exposeHeaders) {
            resolve({
              responseBody: resData.toString(),
              responseHeaders: res.headers,
              httpCode: res.statusCode,
            });
          } else {
            resolve(resData.toString());
          }
          return;
        }
        // 服务端响应 json
        try {
          if (exposeHeaders) {
            resolve({
              responseBody: JSON.parse(resData.toString()),
              responseHeaders: res.headers,
              httpCode: res.statusCode,
            });
          } else {
            resolve(JSON.parse(resData.toString()));
          }
        } catch (e) {
          reject(new Error(`响应内容无法JSON序列化(path: ${_url})：${resData.toString()}`));
        }
      });
      res.on('error', (err: Error) => {
        logger.error(`[API] res error(${_url})`, err);
        reject(err);
      });
    });
    req.on('error', (err: Error) => {
      logger.error(`[API] req error(${_url})：${err.message}`);
      reject(err);
    });
    if (data) {
      req.end(JSON.stringify(data), 'utf8');
    } else {
      req.end();
    }
    if (timeout) {
      setTimeout(() => {
        req.abort();
        reject(new Error(`请求超时(${timeout}ms)`));
      }, timeout);
    }
  });
}

/**
 * 通过 net 模块，对 http 请求的封装
 * @param path
 * @param options
 */
export default async function request(
  path: string,
  options?: { [propName: string]: any },
): Promise<any> {
  const {
    teamId,
    userId,
    token, //sscToken
    mstToken, //mobile share token
    data,
    parseJson = true,
    method = 'get',
    params = {},
    timeout = 60 * 1000,
    headers,
    forceHttp = false, //强制使用http而不是websocket
    ...restOpts
  } = options || {};
  const _data =
    typeof data != 'undefined'
      ? data
      : ['post', 'put', 'patch'].includes(method.toLowerCase())
      ? {}
      : undefined;
  const isFormData = _data instanceof FormData;
  if (!forceHttp && !isFormData && portalRpcClient.isConnected()) {
    let httpMethod = (method || 'get').toUpperCase();
    switch (httpMethod) {
      case 'GET':
      case 'POST':
      case 'PUT':
      case 'DELETE':
        let url = new URL(`http://127.0.0.1${path}`);
        //多发了一次请求，检查当前mvc调用是否可通过websocket发送。
        //todo 1.缓存mvcOption 2.找找有没有不通过发请求的方式来判断
        let mvcOption = await portalRpcClient.mvcOption(url.pathname, httpMethod);
        if (mvcOption?.suitable) {
          options = options || {};
          options.params = options.params || {};
          options.params = { ...Object.fromEntries(url.searchParams), ...options.params };
          return portalRpcClient.invokeMvc(url.pathname, options);
        }
    }
  }
  let url = resolveApiUrl(path, { params });
  return new Promise((resolve, reject) => {
    let resData = Buffer.from([]);
    const req = net.request({
      url,
      method,
      ...restOpts,
    });
    if (_.size(headers) > 0) {
      _.forEach(headers, (v, k) => {
        req.setHeader(k, v);
      });
    }
    req.on('response', (res) => {
      // 处理 set-cookie 响应头
      const cookiesMap: any = {};
      db.getCookies().forEach((s) => {
        const [k, v] = s.split('=');
        cookiesMap[k] = v;
      });
      const headers = res.headers;
      if (headers) {
        const cookieStr = headers['set-cookie'] || headers['Set-Cookie'];
        if (Object.prototype.toString.call(cookieStr) === '[object Array]') {
          // @ts-ignore
          cookieStr.forEach((str) => {
            const [key = '', value = ''] = str.slice(0, str.indexOf(';')).split('=');
            if (key) {
              cookiesMap[key] = value;
            }
          });
          const cookies = _.map(cookiesMap, (v, k) => `${k}=${v}`);
          console.info(`[APP] set cookies(${url})`, cookies.join(';'));
          db.getDb().set('cookies', cookies).write();
        }
      }
      res.on('data', (chunk) => {
        if (!filterPattern.test(url)) {
          isDebug && logger.info(`[API] response data: ${url}`, chunk.toString());
        }
        resData = Buffer.concat([resData, chunk]);
      });
      res.on('end', () => {
        if (!parseJson) {
          resolve(resData.toString());
          return;
        }
        // 服务端响应 json
        try {
          const res = JSON.parse(resData.toString());
          if (res.success) {
            resolve(res.data);
          } else {
            logger.info(`[API] response end: ${url}`, JSON.stringify(res, null, ' '));
            const err: any = new Error(res.message);
            err.code = res.code;
            reject(err);
          }
        } catch (e) {
          reject(new Error(`响应内容无法JSON序列化(path: ${path})：${resData.toString()}`));
        }
      });
      res.on('error', (err: Error) => {
        logger.error(`[API] res error(${path})`, err);
        reject(err);
      });
    });
    req.on('error', (err: Error) => {
      logger.error(`[API] req error(${path})：${err.message}`);
      reject(new NetworkError(err.message, 'request_error'));
    });
    if (isFormData) {
      req.setHeader(
        'Content-Type',
        `multipart/form-data; boundary=${(data as FormData).getBoundary()}`,
      );
    } else {
      req.setHeader('Content-Type', 'application/json');
    }
    req.setHeader('Cookie', db.getCookies().join(';') ?? '');
    if (teamId) {
      req.setHeader('x-dm-team-id', teamId);
    }
    if (userId) {
      req.setHeader('x-dm-user-id', userId);
    }
    if (token) {
      req.setHeader('x-ssc-token', token);
    } else if (mstToken) {
      req.setHeader('x-mst-token', mstToken);
    } else {
      // 设置 jwt
      req.setHeader('Authorization', db.getJwt() ?? '');
    }
    const runtimeJwt = db.getRuntimeJwt();
    if (runtimeJwt) {
      req.setHeader('x-dm-runtime-jwt', runtimeJwt);
    }
    if (typeof _data != 'undefined') {
      if (isFormData) {
        req.write((_data as FormData).getBuffer());
        req.end();
      } else {
        req.end(JSON.stringify(_data), 'utf8');
      }
    } else {
      req.end();
    }
    if (timeout) {
      setTimeout(() => {
        req.abort();
        reject(new Error(`请求超时(${timeout}ms)`));
      }, timeout);
    }
    if (!filterPattern.test(url)) {
      isDebug &&
        logger.info(
          `[API] request url: ${url}\n request options: ${JSON.stringify(
            _.omit(options, ['formData']),
            null,
            ' ',
          )}`,
        );
    }
  });
}

export class RequestAgent {
  private props: RequestAgentProps;

  constructor(props: RequestAgentProps = {}) {
    this.props = props;
  }

  request(path: string, options?: { [propName: string]: any }): Promise<any> {
    return request(path, {
      ...options,
      ...this.props,
    });
  }

  async requestWithRetry(path: string, options?: { [propName: string]: any }): Promise<any> {
    let retryTimes = options?.retryTimes || 10;
    if (options) {
      delete options.retryTimes;
    }
    while (retryTimes > 0) {
      retryTimes--;
      try {
        let ret = await this.request(path, options);
        return ret;
      } catch (e) {
        //网络异常     或者      重试次数剩余0 就将异常抛出
        if (!(e instanceof NetworkError) || retryTimes < 0) {
          throw e;
        }
        logger.error(`[API] request ${path} error, retrying(${retryTimes})... `, e);
        await waitSeconds(1);
      }
    }
  }

  getProps() {
    return this.props;
  }
}

export function resolveApiUrl(path: string, options?: { [propName: string]: any }) {
  const { params = {} } = options || {};
  let url = path;
  if (!/^https?:\/\//.test(path)) {
    const API_URL = new URL(db.getApiUrl());
    const p: any = {};
    const qIdx = path.indexOf('?');
    const sp = new URLSearchParams(qIdx !== -1 ? path.substring(qIdx + 1) : '');
    sp.forEach((value, key) => {
      p[key] = value;
    });
    const queryStr = new URLSearchParams({
      ...p,
      ...params,
    }).toString();
    const urlObj = new URL(path, API_URL);
    url = `${urlObj.origin}${urlObj.pathname}${queryStr ? `?${queryStr}` : ''}`;
  }
  return url;
}
