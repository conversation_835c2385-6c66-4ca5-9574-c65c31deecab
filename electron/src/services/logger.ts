import log, { ElectronLog } from 'electron-log';
import path from 'path';
import _ from 'lodash';
import OSS from 'ali-oss';
import archiver from 'archiver';
import fs from 'fs-extra';

import { getDynamicPath } from '../configs/app';
import { guid } from '@e/utils/utils';
import { dispatchMsg } from '@e/utils/ipc';
import db from '@e/components/db';
import request from '@e/services/request';

type Logger = {
  reportError: (error: any, opt?: { context?: string; noNotify?: boolean }) => void;
  uploadLogFile: (logId: string, callback?: () => void) => void;
} & ElectronLog;

const { LOG_DIR } = getDynamicPath();
export const rpaLogger = log.create('RPA');
export const appiumLogger = log.create('Appium');
export const aiAgentLogger = log.create('AIAgent');
export const wdaLogger = log.create('WDA');
if (process.env.NODE_ENV !== 'development') {
  log.transports.console.level = false;
  rpaLogger.transports.console.level = false;
}
if (log.transports.ipc) {
  log.transports.ipc.level = false;
}
if (rpaLogger.transports.ipc) {
  rpaLogger.transports.ipc.level = false;
}
log.transports.file.resolvePath = () => path.join(LOG_DIR, 'main.log');
rpaLogger.transports.file.resolvePath = () => path.join(LOG_DIR, 'rpa.log');
appiumLogger.transports.file.resolvePath = () => path.join(LOG_DIR, 'appium.log');
aiAgentLogger.transports.file.resolvePath = () => path.join(LOG_DIR, 'aiAgent.log');
wdaLogger.transports.file.resolvePath = () => path.join(LOG_DIR, 'WDA.log');
log.transports.file.maxSize = 10 * 1024 * 1024;
rpaLogger.transports.file.maxSize = 10 * 1024 * 1024;
appiumLogger.transports.file.maxSize = 10 * 1024 * 1024;
aiAgentLogger.transports.file.maxSize = 10 * 1024 * 1024;
wdaLogger.transports.file.maxSize = 10 * 1024 * 1024;
Object.assign(console, log.functions);
log.catchErrors({
  showDialog: false,
  onError: (error) => {
    log.error('[APP] catchErrors', error);
    return false;
  },
});

// @ts-ignore
const logger: Logger = log;

async function getOssSignature(logId: string) {
  const clientId = db.getDb().get('uuid');
  const signature = await request(`/api/logs/getClientLogSignature`, {
    params: {
      clientUuid: clientId,
      logUuid: logId,
    },
  });
  return signature;
}

const uploadLogFile = _.debounce(async (logId: string, callback?: () => void) => {
  const signature = await getOssSignature(logId);
  try {
    const ossClient = new OSS({
      region: signature.region,
      accessKeyId: signature.accessKeyId,
      accessKeySecret: signature.accessKeySecret,
      bucket: signature.bucketName,
      stsToken: signature.securityToken,
      secure: true,
      refreshSTSTokenInterval: 30 * 60 * 1000,
      refreshSTSToken: async () => {
        const info = await getOssSignature(logId);
        return {
          accessKeyId: info.accessKeyId,
          accessKeySecret: info.accessKeySecret,
          stsToken: info.securityToken,
        };
      },
    });
    const { DATA_DIR } = getDynamicPath();
    const archive = archiver('zip', { zlib: { level: 9 } });
    const zipFilePath = path.join(DATA_DIR, 'log.zip');
    const output = fs.createWriteStream(zipFilePath);
    await new Promise((resolve, reject) => {
      output.on('close', () => {
        resolve(true);
      });
      archive
        .directory(LOG_DIR, false)
        .on('error', function (err) {
          reject(err);
        })
        .pipe(output);
      archive.finalize();
    });
    const res = await ossClient.put(signature.url, zipFilePath, {});
    fs.remove(zipFilePath);
    // logger.info('[APP] upload log file success', res);
    callback?.();
  } catch (e) {
    logger.error('[APP] report log failed', e);
  }
}, 1000);

/**
 * 汇报异常
 */
logger.reportError = (error: any, opt: { context?: string; noNotify?: boolean } = {}) => {
  const { context = 'APP', noNotify = false } = opt;
  // 生成 uuid
  const clientId = db.getDb().get('uuid');
  const uuid = `${context}-${db.getDeviceIdFromCookies()}`;
  logger.error(`Reporting error(${uuid})`, error);
  const date = new Date().getFullYear() + String(new Date().getMonth() + 1).padStart(2, '0');
  if (!noNotify) {
    // 发送事件
    const { code, ...restProps } = error;
    dispatchMsg('app-error-report', {
      uuid: `${date}-${clientId}-${uuid}`,
      errorCode: code,
      ...restProps,
      error,
    });
  }
  // 上传文件
  uploadLogFile(uuid);
  return error;
};

logger.uploadLogFile = uploadLogFile;

export function setLevel(level: log.LogLevel) {
  log.info(`[APP] log level is "${level}"`);
  log.transports.file.level = level;
}

export default logger;
