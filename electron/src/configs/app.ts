import path from 'path';

import db from '../components/db';

export function getDynamicPath() {
  const { dataDir } = db.getDb().get('sysPres').value();
  return {
    DATA_DIR: dataDir,
    LOG_DIR: path.resolve(dataDir, 'logs'),
    BROWSER_USER_DATA_DIR_PATH: path.resolve(dataDir, 'shop-data'),
    EXTENSIONS_DIR: path.resolve(dataDir, 'extensions'),
  };
}

export default {
  isOEM: process.env.OEM_NAME !== 'default',
  DEBUG: process.env.NODE_ENV === 'development' || db.getDb().get('DEBUG').value(),
  BROWSER_DEBUG: db.getDb().get('BROWSER_DEBUG').value(),
  MIN_WIDTH: 400, // 窗口最小宽度
  MIN_HEIGHT: 300, // 窗口最小高度
  DEFAULT_WIDTH: 1360, // 窗口默认宽度
  DEFAULT_HEIGHT: 834 + 40, // 窗口默认高度
  MAX_WIDTH: 1650,
  MAX_HEIGHT: 1050,
  BROWSER_USER_DATA_DIR_PREFIX: 'shop-',
  BROWSER_VERSION: Number(process.env.BUNDLE_KERNEL_VERSION),
  BROWSER_BUILD_NUMBER: Number(process.env.BUNDLE_KERNEL_BUILD_NUMBER),
};
