export namespace IPC {
  export enum Event {
    // 打开登录窗口
    OPEN_LOGIN_WINDOW = 'open-login-window',
    // 打开首页
    OPEN_HOME_WINDOW = 'open-home-window',
    // 打开店铺
    OPEN_SHOP_WINDOW = 'open-shop-window',
    // jwt更新
    JWT_UPDATE = 'jwt-update',
    // 聚焦浏览器
    FOCUS_SHOP_BROWSER = 'focus-shop-browser',
    // 设置API_URL
    SET_API_URL = 'set-api-url',
    // 获取门户 endpoint
    GET_PORTAL_URL = 'get-portal-url',
    // 获取 API endpoint
    GET_API_URL = 'get-api-url',
    // 打开外部连接（在系统浏览器中打开）
    OPEN_EXTERNAL_URL = 'open-external-url',
    CREATE_BLOCK_ELEMENT_RULE = 'create-block-element-rule',
    //
  }

  export type OPEN_HOME_WINDOW_VO = {
    url: string;
  };

  export type OPEN_SHOP_WINDOW_VO = {
    shopId: number;
  };

  export type JWT_UPDATE_VO = {
    jwt?: string;
  };
}

/**
 * 传给录像窗口的录像信息
 */
export interface RecordInfo {
  /** 所属会话id */
  sessionId: number;
  /** 要录像窗口标题 */
  windowName: string;
  /** 录像缓存目录 */
  cachePath: string;
  wsUrl?: string;
  mimeType?: string;
  /** 帧率默认值10 */
  fps?: number;
  /** videoBitsPerSecond，默认值2000000 */
  bps?: number;
  sessionStartTime?: number;
  userId?: number;
}

export interface RecordSlice {
  id: number;
  //所属团队
  teamId: number;
  //所属店铺
  shopId: number;
  //所属店铺会话
  sessionId: number;
  //序号
  index: number;
  //文件存储的bucket
  bucketId: number;
  //文件保存路径
  filePath: string;
  mimeType?: string;
  //该片段是否可用
  valid: boolean;
  //该片段开始时间
  startTime?: any;
  //该片段结束时间
  endTime?: any;
  //size
  size?: number;
  //片段结束类型
  endType?: any;
  //片段结束原因，按需求，暂停录像需要输入理由
  endReason?: string;
}

/**
 * 打开店铺时需要的店铺信息
 */
export interface ShopInfo extends API.ShopDetailVo {}

/**
 * 数据库结构
 */
export interface DbScheme {
  account: IPC.JWT_UPDATE_VO;
  cookies: string[];
  [p: string]: any;
}
