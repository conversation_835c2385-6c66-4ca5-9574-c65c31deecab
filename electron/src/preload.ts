import { contextBridge, ipc<PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron';
import process from 'process';

const isDebug = true;
let showCustomWindowTitleBar = process.argv.includes('--show-custom-window-title-bar');
let hideWindowTitleBarHelpBtn = process.argv.includes('--hide-window-title-bar-help-button');
let hideWindowTitleBarPinBtn = process.argv.includes('--hide-window-title-bar-pin-button');

contextBridge.exposeInMainWorld('isElectronEnv', true);
contextBridge.exposeInMainWorld('showCustomWindowTitleBar', showCustomWindowTitleBar);
contextBridge.exposeInMainWorld('hideWindowTitleBarHelpBtn', hideWindowTitleBarHelpBtn);
contextBridge.exposeInMainWorld('hideWindowTitleBarPinBtn', hideWindowTitleBarPinBtn);

contextBridge.exposeInMainWorld('ipcRenderer', {
  send: (channel: string, data: any) => {
    isDebug && console.log(`send message to main through channel: ${channel}`, data);
    ipcRenderer.send(channel, data);
  },
  sendSync: (channel: string, data: any): any => {
    isDebug && console.log(`send message to main through channel: ${channel}`, data);
    return ipcRenderer.sendSync(channel, data);
  },
  invoke: (channel: string, data: any): any => {
    isDebug && console.log(`send message to main through channel: ${channel}`, data);
    return ipcRenderer.invoke(channel, data);
  },
  on: (channel: string, func: (evt: IpcRendererEvent, data: any) => void) => {
    if (typeof func !== 'function') return;
    isDebug && console.log(`listen: ${channel}`);
    ipcRenderer.on(channel, func);
  },
  removeListener: (channel: string, func: (evt: IpcRendererEvent, data: any) => void) => {
    if (typeof func !== 'function') return;
    isDebug && console.log(`removeListener: ${channel}`);
    ipcRenderer.removeListener(channel, func);
  },
  removeAllListeners: (channel: string) => {
    isDebug && console.log(`removeAllListeners: ${channel}`);
    ipcRenderer.removeAllListeners(channel);
  },
});
