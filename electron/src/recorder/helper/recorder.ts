import {RecordInfo, RecordSlice, ShopInfo} from '../../types';
import {ipc<PERSON><PERSON><PERSON>} from 'electron';
import os from 'os';
import fs from 'fs';
import WebSocket from 'ws';
import StreamPublisher from './stream-publisher';
import {
  getRecordMimeType,
  haveLoadedMetadata,
} from './record-utils';
import {RecordEvent} from './protocol';
import OSS from 'ali-oss';
import moment from 'moment';
import {sendRequest, addAjaxEventListener, removeAjaxEventListener} from "@e/utils/helper_win_utils";

const spy_failed =
  '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';

const { recordBps, recordFps } = ipcRenderer.sendSync('get-record-config');
const default_v_bps = recordBps || 2000000;
const default_fps = recordFps || Math.min(10, os.cpus().length);
let _debug_saveTime: number = new Date().getTime();
let _debug_saveIndex: number = 0;
const __debug_saveVideoToFile = async (debugName: any, evt: any) => {
  if (new Date().getTime() - _debug_saveTime > 20000) {
    _debug_saveIndex++;
  }
  const buffer = Buffer.from(await evt.data.arrayBuffer());
  require('fs').writeFile(
    `/Users/<USER>/Downloads/${debugName}.${_debug_saveIndex}.mp4`,
    buffer,
    { flag: 'a' },
    (err: any) => err && console.error(err),
  );
};

/**
 * 代表着一个录像实例，对应着一个会话
 */
export default class Recorder {
  shopInfo: ShopInfo;
  recordInfo: RecordInfo;
  requestProps: Record<string, any>;

  errorMsg?: string;

  browserWinId?: string;
  shopStream?: any;
  mediaRecorder?: any;

  currentSlice?: RecordSlice;
  ossClient!: OSS;

  recordStatus: string = 'Pause';
  recordStarting: boolean = false;
  pauseTimer: any = 0;
  pauseRemainSec: number = 0;
  pauseRemainSecTimer: any = 0;
  startTime: number = 0;
  duration: number = 0; // 录像时长，单位s

  ajaxHds: string[] = [];

  ws?: WebSocket;

  publisher?: StreamPublisher;
  // 录像上传队列
  uploadQueue: Blob[];
  // 是否上传的是第一个片段
  firstPart: boolean;
  private uploading: boolean;
  watchingUsers: any[];
  // 主动触发停止操作
  manualStopFlag: boolean;
  hasAutoStart: boolean;

  spyInterval?: any;
  spyIndex: number = 1;
  spyDir?: string;
  spyDuration?: number;

  constructor({
    shopInfo,
    recordInfo,
    requestProps,
  }: {
    shopInfo: ShopInfo;
    recordInfo: RecordInfo;
    requestProps: Record<string, any>;
  }) {
    this.shopInfo = shopInfo;
    this.recordInfo = recordInfo;
    this.requestProps = requestProps;
    this.recordInfo.mimeType = getRecordMimeType(recordInfo.mimeType || 'undefined'); //'video/webm;codecs=H264'
    this.recordInfo.bps = recordInfo.bps || default_v_bps;
    this.recordInfo.fps = recordInfo.fps || default_fps;
    this.uploadQueue = [];
    this.firstPart = true;
    this.uploading = false;
    this.watchingUsers = [];
    this.manualStopFlag = false;
    this.hasAutoStart = true;
    console.debug(this.shopInfo, this.recordInfo);
    this.initEvents().then(() => {});
    this._prepareOssClient();
  }

  async initEvents() {
    let hd = await addAjaxEventListener('takeSessionSnapshot', this.recordInfo.sessionId);
    this.ajaxHds.push(hd);
    hd = await addAjaxEventListener('session-monitor-created', this.recordInfo.sessionId);
    this.ajaxHds.push(hd);
    hd = await addAjaxEventListener('session-monitor-closed', this.recordInfo.sessionId);
    this.ajaxHds.push(hd);
    hd = await addAjaxEventListener('session-monitor-signaling', this.recordInfo.sessionId);
    this.ajaxHds.push(hd);
    hd = await addAjaxEventListener('takeASmile', this.recordInfo.sessionId);
    this.ajaxHds.push(hd);
  }

  async connectWebsocket(ws: WebSocket) {
    this.ws?.close(); //关闭之前的连接
    this.ws = ws;
    this.ws.on('message', (data) => {
      this._handleWebsocketMessage(data);
    });
    this.ws.send('ready');
  }

  private async initStream() {
    if (this.shopStream) {
      return;
    }
    //有websocket连接请求过来说明店铺浏览器窗口已经打开成功，尝试获取窗口
    await this._attackShopWindow();
    if (!this.browserWinId) {
      this.errorMsg = '找不到录像窗口';
      return;
    }
    this.shopStream = await navigator.mediaDevices.getUserMedia({
      audio: false,
      video: {
        // @ts-ignore
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: this.browserWinId,
          maxFrameRate: this.recordInfo.fps,
        },
      },
    });
    if (!this.shopStream) {
      this.errorMsg = '无法获取窗口视频流';
      return;
    }
  }

  private async initPublisher() {
    await this.initStream();
    if (this.publisher) {
      if (!this.publisher.shopStream) {
        this.publisher.shopStream = this.shopStream;
        await this.publisher.init();
      }
      return;
    }
    this.publisher = new StreamPublisher({
      shopStream: this.shopStream,
      shopInfo: this.shopInfo,
      recordInfo: this.recordInfo,
    });
    await this.publisher.init();
  }

  async startRecord() {
    try {
      if (this.recordStarting) {
        return this.recordInfo.mimeType;
      }
      let accessStatus = ipcRenderer.sendSync('getMediaAccessStatus');
      if ('granted' != accessStatus) {
        await this.initStream(); //触发授权窗口
        this.sendDispatchEvent({
          event: 'recordStatus',
          data: { status: 'error', message: '未获得屏幕录制权限，录像失败' },
        });
        return;
      }
      await this.initStream();
      this.recordStarting = true;
      this.startTime = new Date().getTime();
      if (this.currentSlice) {
        await this.stopRecord();
      }
      console.info(`start recording（sessionId: ${this.recordInfo.sessionId}）`);
      clearTimeout(this.pauseTimer);
      clearInterval(this.pauseRemainSecTimer);
      let data = `mimeType=${encodeURIComponent(this.recordInfo.mimeType!)}`;
      this.currentSlice = await sendRequest(
        `/api/record/session/${this.recordInfo.sessionId}/slice?${data}`,
        {
          method: 'put',
          ...this.requestProps,
          teamId: this.shopInfo.teamId,
          userId: this.recordInfo.userId,
        },
      );
      setTimeout(() => {
        this.heartbeatSlice();
      }, 2000);
      this.sliceHeartbeatTimeout = setInterval(() => {
        this.heartbeatSlice();
      }, 60 * 1000);
      await this._recordStream(async (evt: BlobEvent) => {
        try {
          if (this.currentSlice && evt.data.size > 0) {
            if (1 + 1 == 3) {
              await __debug_saveVideoToFile(this.recordInfo.sessionId, evt);
            }
            this.uploadQueue.push(evt.data);
            this.upload();
          }
        } catch (err) {
          console.error('on DataAvailable callback error', err);
          if (this.recordStatus != 'started') {
            await this.stopRecord('ClientError', '' + err);
          } else {
            //正在录像过程中的错误，重新开始录像
            await this.restartRecord('ClientError', '' + err);
          }
        }
      });
      this.recordStatus = 'started';
      this.sendDispatchEvent({ event: 'recordStatus', data: { status: 'started' } });
    } catch (e: any) {
      console.error(`startRecord failed（sessionId: ${this.recordInfo.sessionId}）：${e.message}`);
    } finally {
      this.recordStarting = false;
    }
    return this.recordInfo.mimeType;
  }

  async upload() {
    if (!this.ossClient || this.uploading) return;
    let meta = undefined;
    const sliceLen = Math.min(10, this.uploadQueue.length);
    const bufPkg = [];
    // 合并 blob
    for (let i = 0; i < sliceLen; i++) {
      bufPkg.push(Buffer.from(await this.uploadQueue[i].arrayBuffer()));
    }
    if (bufPkg.length === 0) return;
    this.uploading = true;
    const buffer = Buffer.concat(bufPkg);
    // console.log('upload start', sliceLen, buffer.length);
    if (this.firstPart) {
      this.firstPart = false;
      meta = {
        'head-size': '' + buffer.length,
      };
    }
    try {
      await this.ossClientAppend(buffer, meta, this.uploadQueue[0].type);
      // console.log('upload done', sliceLen, buffer.length);
      this.uploadQueue = this.uploadQueue.slice(sliceLen);
      if (this.uploadQueue.length > 0) {
        await this.upload();
      }
    } catch (e: any) {
      console.error(`Upload video failed（sessionId: ${this.recordInfo.sessionId}）：${e.message}`);
    }
    this.uploading = false;
  }

  async ossClientAppend(buffer: Buffer, meta: any, type = 'video/mp4') {
    if (!this.currentSlice) return;

    let response;
    try {
      response = await this.ossClient.append(this.currentSlice.filePath, buffer, {
        mime: type,
        position: '' + (this.currentSlice.size || 0),
        meta,
      });
    } catch (err: any) {
      if (err.name === 'PositionNotEqualToLengthError') {
        if (!this.currentSlice) return;
        //@ts-ignore
        const meta = await this.ossClient.getObjectMeta(this.currentSlice.filePath);
        if (meta.res.headers['content-length']) {
          this.currentSlice.size = parseInt(meta.res.headers['content-length']);
        }
        response = await this.ossClient.append(this.currentSlice.filePath, buffer, {
          mime: type,
          position: '' + this.currentSlice.size,
          meta,
        });
      } else {
        throw err;
      }
    }
    if (!this.currentSlice) return;
    this.currentSlice.size = ~~response.nextAppendPosition;
    //slice总共录制大小。 因为不能突破oss appendObject最大5G限制
    if (this.currentSlice.size! > 4.8 * 1024 * 1024 * 1024) {
      this.recordStatus = 'Overrun';
      this.sendDispatchEvent({ event: 'recordStatus', data: { status: 'Overrun' } });
      await this.restartRecord('Overrun', '');
    }
  }

  sliceHeartbeatTimeout: any;
  async heartbeatSlice() {
    if (!this.currentSlice) return;
    let data = `size=${this.currentSlice?.size || 0}`;
    console.debug(
      `sessionId=${this.currentSlice.sessionId} sliceSize updated to ${this.currentSlice.size}`,
    );
    await sendRequest(`/api/record/slice/${this.currentSlice.id}/heartbeat?${data}`, {
      method: 'put',
      ...this.requestProps,
      teamId: this.shopInfo.teamId,
      userId: this.recordInfo.userId,
    });
  }

  async restartRecord(endType: string, endReason: string) {
    await this.stopRecord(endType, endReason);
    await this.startRecord();
  }

  errorImageUploaded = false;
  async startSpy(spyDir: string, spyDuration: number = 5) {
    if (this.spyInterval || !spyDir) {
      return;
    }
    this.spyDir = spyDir;
    this.spyDuration = spyDuration > 0 ? spyDuration : 5;
    let processing = false;
    const date = moment().format('YYYYMMDD');
    this.spyInterval = setInterval(async () => {
      if (processing) {
        return;
      }
      processing = true;
      try {
        let accessStatus = ipcRenderer.sendSync('getMediaAccessStatus');
        if ('granted' != accessStatus) {
          if (this.errorImageUploaded) {
            return;
          }
          const buffer = Buffer.from(spy_failed, 'base64');
          await this.ossClient.put(
            `${this.spyDir}${date}-${this.recordInfo.sessionId}.scr.0.${this.spyIndex}.png`,
            buffer,
            { mime: 'image/png' },
          );
          this.errorImageUploaded = true;
          return;
        }
        let capture = await ipcRenderer.sendSync('capture-desktop');
        if (capture.success) {
          let previewPngs = capture.previewPngs;
          for (let i = 0; i < previewPngs.length; i++) {
            let previewPng = previewPngs[i];
            await this.ossClient.put(
              `${this.spyDir}${date}-${this.recordInfo.sessionId}.scr.${i}.${this.spyIndex}.png`,
              previewPng,
              { mime: 'image/png' },
            );
          }
          for (let i = 0; i < previewPngs.length; i++) {
            fs.unlink(previewPngs[i], (err) => {});
          }
        }
      } catch (e: any) {
        if (e) {
          let message = e.stack || e.message || e.toString();
          ipcRenderer.sendSync('ipc-log-to-main', message);
        }
        console.error('take smile error, crying...');
        console.error(e);
      } finally {
        this.spyIndex++;
        processing = false;

        //单个会话最多截300张图片
        if(this.spyIndex >= 300) {
          clearInterval(this.spyInterval);
        }
      }
    }, this.spyDuration * 1000);
  }

  async stopRecord(endType: string = 'Pause', endReason: string = 'Pause', timeout: number = 0) {
    this.recordStatus = endType;
    if (this.startTime !== 0) {
      this.duration = this.duration + (new Date().getTime() - this.startTime) / 1000;
    }
    this.startTime = 0;
    clearTimeout(this.pauseTimer);
    clearInterval(this.pauseRemainSecTimer);
    clearInterval(this.sliceHeartbeatTimeout);
    console.info(`stop recording（sessionId: ${this.recordInfo.sessionId}）`);
    if (this.currentSlice) {
      let data = `size=${
        this.currentSlice.size || 0
      }&endType=${endType}&endReason=${encodeURIComponent(endReason)}`;
      await sendRequest(`/api/record/slice/${this.currentSlice.id}/end?${data}`, {
        method: 'put',
        ...this.requestProps,
        teamId: this.shopInfo.teamId,
        userId: this.recordInfo.userId,
      });
      this.currentSlice = undefined;
    }
    if (this.mediaRecorder && this.mediaRecorder.state == 'recording') {
      this.manualStopFlag = true;
      this.mediaRecorder.stop();
      this.mediaRecorder = null;
    }
    this.sendDispatchEvent({ event: 'recordStatus', data: { status: endType } });
    if (endType === 'Pause' && timeout > 0) {
      this.pauseRemainSec = timeout;
      this.pauseRemainSecTimer = setInterval(() => {
        if (this.pauseRemainSec > 0) {
          this.pauseRemainSec--;
          this.sendDispatchEvent({ event: 'pauseRemain', data: { sec: this.pauseRemainSec } });
        } else {
          clearInterval(this.pauseRemainSecTimer);
        }
      }, 1000);
      this.pauseTimer = setTimeout(() => {
        this.startRecord();
      }, timeout * 1000);
    }
  }

  async close() {
    try {
      if (this.spyInterval) {
        clearInterval(this.spyInterval);
      }
      clearInterval(this.sliceHeartbeatTimeout);
      await this.stopRecord('Natural');
      this.ws?.close(1000, 'session closed');
      for (let hd of this.ajaxHds) {
        removeAjaxEventListener(hd).catch((ignore) => {});
      }
      await this.publisher?.close();
      this.publisher = undefined;
    } catch (err) {
      console.error(err);
    }
  }

  //private functions
  async _handleWebsocketMessage(wsData: WebSocket.Data) {
    if (typeof wsData === 'string') {
      try {
        let evt: RecordEvent = JSON.parse(wsData);
        let data = evt.data;
        let returnData = null;
        switch (evt.name) {
          case 'startRecord':
            returnData = await this.startRecord();
            break;
          case 'stopRecord':
            returnData = await this.stopRecord(data.endType, data.endReason, data.timeout);
            break;
          case 'getCurrentSlice':
            returnData = this._getCurrentSlice();
            break;
          case 'sendDonkeyRequest':
            returnData = await sendRequest(data.path, { ...this.requestProps, ...data.options });
            break;
          case 'getRecordDuration':
            if (this.startTime === 0) {
              returnData = this.duration;
            } else {
              returnData = this.duration + (new Date().getTime() - this.startTime) / 1000;
            }
            break;
          case 'getRecordStatus':
            returnData = this.recordStatus;
            break;
          case 'getWatchingUsers':
            returnData = this.watchingUsers;
            break;
          case 'heartbeat':
            if (!this.hasAutoStart) {
              this.startRecord();
              this.hasAutoStart = true;
            }
            break;
        }
        evt = { name: evt.name + '-cb', data: returnData };
        this.ws?.send(JSON.stringify(evt));
      } catch (e) {
        console.error('_handleWebsocketMessage Exception', e);
      }
    }
  }

  sendDispatchEvent(data: any) {
    let evt: RecordEvent = { name: 'send-dispatch', data: data };
    return this.ws?.send(JSON.stringify(evt));
  }

  async onAjaxEvent(name: string, data: any) {
    console.info(`receive ajax event: ${name}（sessionId: ${this.recordInfo.sessionId}）`);
    console.debug(data);
    switch (name) {
      case 'takeSessionSnapshot':
        if (!this.ossClient) return;
        let pngPath = data;
        let buffer = null;
        let capture = ipcRenderer.sendSync('capture-browser-window', {
          browserTitle: this.recordInfo!.windowName,
          thumbnailSize: { width: 800, height: 800 },
        });
        if (capture.success) {
          let response = await this.ossClient.put(pngPath, capture.previewPng, {
            mime: 'image/png',
          });
          // console.log(response);
          sendRequest(`/api/shop/session/${this.recordInfo.sessionId}/snapshot_ok`, {
            method: 'put',
            ...this.requestProps,
            teamId: this.shopInfo.teamId,
            userId: this.recordInfo.userId,
          });
          fs.unlink(capture.previewPng, () => {});
        } else {
          sendRequest(`/api/shop/session/${this.recordInfo.sessionId}/snapshot_ok`, {
            method: 'put',
            ...this.requestProps,
            teamId: this.shopInfo.teamId,
            userId: this.recordInfo.userId,
            params: {
              error: '截屏失败，请确保已经授予了屏幕录制权限且窗口没有被最小化',
            },
          });
        }
        break;

      case 'takeASmile': //获取截屏以便了解用户使用状态
        if (typeof data === 'string') {
          await this.startSpy(data);
        } else {
          await this.startSpy(data.dir, data.duration);
        }
        break;

      case 'session-monitor-created':
        await this.initPublisher();
        this.publisher?.onWatcherRequest(data);
        this._loadWatchingUsers();
        break;

      case 'session-monitor-signaling':
        this.publisher?.onSignalingRequest(data);
        break;

      case 'session-monitor-closed':
        this.publisher?.onWatcherClosed(data);
        this._loadWatchingUsers();
        break;
    }
  }

  async _prepareOssClient() {
    if (!this.ossClient) {
      let signature = await this._refreshPostSignature();
      // @ts-ignore
      this.ossClient = new OSS({
        region: signature.region,
        accessKeyId: signature.accessKeyId,
        accessKeySecret: signature.accessKeySecret,
        bucket: signature.bucketName,
        stsToken: signature.securityToken,
        secure: true,
        refreshSTSToken: async () => {
          const info = await this._refreshPostSignature();
          return {
            accessKeyId: info.accessKeyId,
            accessKeySecret: info.accessKeySecret,
            stsToken: info.securityToken,
          };
        },
        refreshSTSTokenInterval: 30 * 60 * 1000, //30min刷新sts token
      });
    }
  }

  async _loadWatchingUsers() {
    const userVos = await sendRequest(
      `/api/shop/session/${this.recordInfo.sessionId}/findWatchingUsers`,
      {
        ...this.requestProps,
        teamId: this.shopInfo.teamId,
        userId: this.recordInfo.userId,
      },
    );
    console.debug('_loadWatchingUsers', userVos);
    if (userVos) {
      this.watchingUsers = userVos;
    }
  }

  async _refreshPostSignature() {
    const signature = await sendRequest(
      `/api/record/session/${this.recordInfo.sessionId}/signature`,
      { ...this.requestProps, teamId: this.shopInfo.teamId, userId: this.recordInfo.userId },
    );
    console.debug(signature);
    return signature;
  }

  async _attackShopWindow() {
    if (!this.browserWinId) {
      let bw = ipcRenderer.sendSync('get-shop-browserWinId', {
        browserTitle: this.recordInfo!.windowName,
      });
      if (bw.success) {
        this.browserWinId = bw.browserWinId;
      } else {
        console.error(`获取窗口 ${this.recordInfo!.windowName} 失败`);
      }
    }
  }

  async _recordStream(onDataAvailable: any) {
    const options = {
      mimeType: this.recordInfo.mimeType,
      videoBitsPerSecond: this.recordInfo.bps,
      frameRate: this.recordInfo.fps,
    };
    await haveLoadedMetadata(this.shopStream);
    // @ts-ignore
    this.mediaRecorder = new MediaRecorder(this.shopStream, options);
    this.mediaRecorder.ondataavailable = onDataAvailable;
    this.mediaRecorder.onstop = () => {
      if (!this.manualStopFlag) {
        // 直接断开 VPS 会导致录屏的 stop 事件直接触发
        this.hasAutoStart = false;
        this.stopRecord('ForceStop', 'ForceStop');
      }
      this.manualStopFlag = true;
    };
    try {
      this.mediaRecorder.start(1000); //1秒汇报一次数据
    } catch (e: any) {
      console.error(
        `start recorder failed（sessionId: ${this.recordInfo.sessionId}）, error: ${e.message}`,
      );
      // fix VPS 重连后需要重现获取窗口信息，start 方法会抛出异常
      this.browserWinId = undefined;
      this.shopStream = undefined;
      await this.initStream();
      this._recordStream(onDataAvailable);
    }
  }

  _getCurrentSlice() {
    return this.currentSlice;
  }
}
