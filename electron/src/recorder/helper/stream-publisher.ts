import {RecordInfo, ShopInfo} from '../../types';
import {haveLoadedMetadata} from './record-utils';
import {sendRequest} from "@e/utils/helper_win_utils";

const io = require('socket.io-client');

const SIGNALING_OFFER = 'SIGNALING_OFFER';
const SIGNALING_ANSWER = 'SIGNALING_ANSWER';
const SIGNALING_CANDIDATE = 'SIGNALING_CANDIDATE';
const SIGNALING_WATCH_MODE = 'SIGNALING_WATCH_MODE';
const SIGNALING_REQUEST_IMAGE = 'SIGNALING_REQUEST_IMAGE';
const SIGNALING_WATCH_ERROR = 'SIGNALING_WATCH_ERROR';

interface MonitorInfo {
  teamId: number;
  sessionId: number;
  monitorId: number;

  //webrtc相关
  turn: string;
  username: string;
  password: string;
  signaling: string;
  roomName: string;
}

interface SignalingRequest {
  monitorId: number;
  type: string;
  signaling: string;
}

/**
 * 录像直播器
 */
export default class StreamPublisher {
  shopStream: any;
  shopInfo: ShopInfo;
  recordInfo: RecordInfo;

  watchers: Map<number, Watcher>;

  previewVideo: any;
  publishHandler?: any;
  // imageCapture: any;

  constructor({
    shopStream,
    shopInfo,
    recordInfo,
  }: {
    shopStream: any;
    shopInfo: ShopInfo;
    recordInfo: RecordInfo;
  }) {
    this.shopStream = shopStream;
    this.shopInfo = shopInfo;
    this.recordInfo = recordInfo;
    this.watchers = new Map();
  }

  async init() {
    if (this.shopStream) {
      this.previewVideo = await haveLoadedMetadata(this.shopStream, true);
      this.previewVideo.muted = false;
      this.previewVideo.play();
    }
    // const videoTrack = this.shopStream.getVideoTracks()[0];
    // @ts-ignore
    // this.imageCapture = new ImageCapture(videoTrack);
  }

  async onWatcherRequest(data: MonitorInfo) {
    if (!this.watchers.has(data.monitorId)) {
      const watcher = new Watcher({
        shopStream: this.shopStream,
        shopInfo: this.shopInfo,
        monitor: data,
        publisher: this,
        recordInfo: this.recordInfo,
        // imageCapture: this.imageCapture,
        previewVideo: this.previewVideo,
      });
      this.watchers.set(data.monitorId, watcher);
      await watcher.init();
    } // else 不允许重复，直接忽略
  }

  async onWatcherClosed(monitorId: number) {
    if (this.watchers.has(monitorId)) {
      await this.watchers.get(monitorId)?.close();
      this.watchers.delete(monitorId);
    }
  }

  async onSignalingRequest(data: SignalingRequest) {
    if (this.watchers.has(data.monitorId)) {
      const watcher = this.watchers.get(data.monitorId);
      await watcher?.onSignalingRequest(data);
    }
  }

  async _onDataAvailable(evt: any) {
    if (evt.data.size > 0) {
    }
  }

  async pauseWatchSession() {
    clearTimeout(this.publishHandler);
  }

  //关闭
  async close() {
    for (let watcher of this.watchers.values()) {
      watcher.close();
    }
    await this.pauseWatchSession();
    clearTimeout(this.publishHandler);
  }
}

class Watcher {
  shopStream: any;
  shopInfo: ShopInfo;
  monitor: MonitorInfo;
  publisher: StreamPublisher;
  recordInfo: RecordInfo;

  pc?: RTCPeerConnection;
  datachannel?: RTCDataChannel;

  trcSender?: RTCRtpSender;
  hasReceiveAnswer: boolean;
  candidateQueue: string[];

  heartbeatInterval?: any;

  ioClient: any;

  currentMode: string = '';
  // imageCapture: any;
  previewVideo: any;

  constructor({
    shopStream,
    shopInfo,
    monitor,
    publisher,
    recordInfo,
    // imageCapture,
    previewVideo,
  }: {
    shopStream: any;
    shopInfo: ShopInfo;
    monitor: MonitorInfo;
    publisher: StreamPublisher;
    recordInfo: RecordInfo;
    // imageCapture: any;
    previewVideo: any;
  }) {
    this.shopStream = shopStream;
    this.shopInfo = shopInfo;
    this.monitor = monitor;
    this.publisher = publisher;
    this.recordInfo = recordInfo;
    this.hasReceiveAnswer = false;
    this.candidateQueue = [];
    // this.imageCapture = imageCapture;
    this.previewVideo = previewVideo;
  }

  async init() {
    this.ioClient = io.connect(this.monitor.signaling, { transports: ['websocket'] });
    await new Promise((resolve, reject) => {
      this.ioClient.on('connect', () => {
        this.ioClient.emit('subscribe-room', { room: '' + this.monitor.roomName });
        resolve(true);
      });
    });
    this.ioClient.on('message', (message: any) => {
      let { from, data } = message;
      if (from != this.ioClient.id) {
        this.onSignalingRequest(data);
      }
    });

    const iceConfig = {
      iceServers: [
        {
          urls: this.monitor.turn,
          username: this.monitor.username,
          credential: this.monitor.password,
        },
      ],
    };
    try {
      // @ts-ignore
      this.pc = new RTCPeerConnection(iceConfig);
      this.pc.onnegotiationneeded = this._onnegotiationneeded.bind(this);
      this.pc.onicecandidate = this._onicecandidate.bind(this);
      this.pc.oniceconnectionstatechange = this._oniceconnectionstatechange.bind(this);
      this.datachannel = this.pc.createDataChannel('shop-datachannel');
      this.datachannel.onopen = () => {
        this.datachannel?.send(JSON.stringify({ action: 'ping' }));
        if (this.needCaptureStream) {
          this.captureShopStream();
        }
      };
      this.datachannel.onmessage = this.onDataChannelMessage;
      console.info(`Init RTCPeerConnection success（sessionId: ${this.recordInfo.sessionId}）`);
    } catch (e: any) {
      console.error(
        `Init RTCPeerConnection（sessionId: ${this.recordInfo.sessionId}）, error: ${e.message}`,
      );
    }

    this.heartbeatInterval = setInterval(() => {
      this._sendHeartBeat();
    }, 10000);
  }

  _sendHeartBeat() {
    sendRequest(
      `/api/shop/session/monitor/${this.monitor.monitorId}/heartbeatMonitor?traffic=0`, //todo 统计流量
      { method: 'PUT', teamId: this.shopInfo.teamId, userId: this.recordInfo.userId },
    );
  }

  sendRTCEvent(msg: any) {
    this.ioClient.emit('publish-message', msg);
    // return sendRequest(`/api/shop/monitor/signaling`, {
    //   method: 'PUT',
    //   teamId: this.shopInfo.teamId,
    //   data: msg,
    // });
  }

  private async onDataChannelMessage(event: MessageEvent) {
    //todo
  }

  private async handleWatchModeChange(data: SignalingRequest) {
    if (this.currentMode != data.signaling) {
      if (!this.shopStream) {
        this.sendRTCEvent({
          type: SIGNALING_WATCH_ERROR,
          monitorId: this.monitor.monitorId,
          signaling: '对方未开启屏幕录制权限，查看失败',
        });
        return;
      }
      if (this.currentMode == 'video') {
        //清理
        if (this.trcSender) {
          this.pc!.removeTrack(this.trcSender);
        }
      } else if (this.currentMode == 'image') {
        //清理
      }
      this.currentMode = data.signaling;
      console.info(
        `Change watch mode to ${this.currentMode}（sessionId: ${this.recordInfo.sessionId}）`,
      );
      if (this.currentMode == 'video') {
        const videoTrack = this.shopStream.getVideoTracks()[0];
        try {
          this.trcSender = this.pc!.addTrack(videoTrack!, this.shopStream);
        } catch (e) {
          console.error(e);
        }
      } else if (this.currentMode == 'image') {
        // const videoTrack = this.shopStream.getVideoTracks()[0];
        // try {
        //   this.trcSender = this.pc!.addTrack(videoTrack!, this.shopStream);
        // } catch (e) {
        //   console.error(e);
        // }
        await this.captureShopStream();
      }
    }
  }

  capturing = false;
  needCaptureStream = false;
  private async captureShopStream() {
    if (this.currentMode == 'image') {
      if (this.capturing) {
        return;
      }
      if (this.datachannel?.readyState != 'open') {
        this.needCaptureStream = true;
        return;
      }
      try {
        this.capturing = true;
        // let bmp = await this.imageCapture.grabFrame();
        // console.log(bmp.width, bmp.height);
        const canvas = document.createElement('canvas');
        // resize it to the size of our ImageBitmap
        // canvas.width = bmp.width;
        // canvas.height = bmp.height;
        canvas.width = this.previewVideo.videoWidth;
        canvas.height = this.previewVideo.videoHeight;
        // get a bitmaprenderer context
        // const ctx = canvas.getContext('bitmaprenderer');
        // ctx!.transferFromImageBitmap(bmp);
        const ctx = canvas.getContext('2d');
        ctx!.drawImage(
          this.previewVideo,
          0,
          0,
          this.previewVideo.videoWidth,
          this.previewVideo.videoHeight,
        );
        let png = canvas.toDataURL('image/webp');
        let mark = 'buffer:';
        this.datachannel!.send(JSON.stringify({ action: 'buffer_start', type: 'string' }));
        for (let i = 0; i < png.length; i += 10 * 1000 - mark.length) {
          let pack = mark + png.substring(i, Math.min(i + 10 * 1000 - mark.length, png.length));
          this.datachannel!.send(pack);
        }
        this.datachannel!.send(JSON.stringify({ action: 'buffer_end', type: 'string' }));
      } finally {
        this.capturing = false;
      }
    }
  }

  async onSignalingRequest(data: SignalingRequest) {
    switch (data.type) {
      case SIGNALING_OFFER:
        //never
        break;
      case SIGNALING_ANSWER:
        await this.handleReceiveAnswer(data);
        break;
      case SIGNALING_CANDIDATE:
        await this.handleReceiveCandidate(data);
        break;
      case SIGNALING_WATCH_MODE:
        await this.handleWatchModeChange(data);
        break;
      case SIGNALING_REQUEST_IMAGE:
        await this.captureShopStream();
        break;
    }
  }

  async _onnegotiationneeded() {
    const offer = await this.pc!.createOffer();
    try {
      await this.pc!.setLocalDescription(offer);
    } catch (e) {
      console.error(e);
    }
    this.sendRTCEvent({
      type: SIGNALING_OFFER,
      monitorId: this.monitor.monitorId,
      signaling: JSON.stringify(this.pc!.localDescription),
    });
  }

  async _onicecandidate(evt: any) {
    if (evt.candidate) {
      this.sendRTCEvent({
        type: SIGNALING_CANDIDATE,
        monitorId: this.monitor.monitorId,
        signaling: JSON.stringify(evt.candidate),
      });
    }
  }

  async handleReceiveAnswer(data: SignalingRequest) {
    this.hasReceiveAnswer = true;
    const remoteDescription = new RTCSessionDescription(JSON.parse(data.signaling));
    try {
      await this.pc!.setRemoteDescription(remoteDescription);
      // 处理队列中的消息
      while (this.candidateQueue.length > 0) {
        await this.pc!.addIceCandidate(JSON.parse(this.candidateQueue.shift()!));
      }
    } catch (e) {
      console.error(e);
    }
  }

  async handleReceiveCandidate(data: SignalingRequest) {
    try {
      if (!this.hasReceiveAnswer) {
        this.candidateQueue.push(data.signaling);
        return;
      }
      await this.pc!.addIceCandidate(JSON.parse(data.signaling));
    } catch (e) {
      console.error(e);
    }
  }

  _oniceconnectionstatechange() {
    console.info(
      `pc.iceConnectionState change to ${this.pc!.iceConnectionState}（sessionId: ${
        this.recordInfo.sessionId
      }）`,
    );
    if (this.pc!.iceConnectionState === 'connected') {
      this._sendHeartBeat();
    }
    if (
      this.pc!.iceConnectionState === 'failed' ||
      this.pc!.iceConnectionState === 'disconnected' ||
      this.pc!.iceConnectionState === 'closed'
    ) {
      sendRequest(
        `/api/shop/session/monitor/${this.monitor.monitorId}/closed?traffic=0`, //todo 统计流量
        { method: 'PUT', teamId: this.shopInfo.teamId, userId: this.recordInfo.userId },
      );

      this.close();
      this.publisher.watchers.delete(this.monitor.monitorId);
    }
  }

  async close() {
    clearInterval(this.heartbeatInterval);
    try {
      this.ioClient?.disconnect();
    } catch (e) {}
    try {
      this.pc?.close();
    } catch (e) {
      console.error(e);
    }
  }
}
