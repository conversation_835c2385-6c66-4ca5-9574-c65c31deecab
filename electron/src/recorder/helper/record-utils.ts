export const haveLoadedMetadata = (stream: any, keep=false) => {
  let preview = document.createElement('video');
  preview.srcObject = stream;
  preview.muted = false;
  return new Promise(
    (resolve) =>
      (preview.onloadedmetadata = () => {
        if(!keep) {
          preview.srcObject = null;
        }
        resolve(preview);
      }),
  );
};

export const getRecordMimeType = (favoriteType: string) => {
  let all = [
    favoriteType,
    //vp8
    'video/webm; codecs="vp8"',
    //vp9
    'video/webm; codecs="vp9"',
    //h264 codecs
    'video/x-matroska;codecs=H264',
    'video/x-matroska;codecs=avc1',
    'video/x-matroska; codecs="avc1.64001e"',
    'video/x-matroska; codecs="avc1.4d001e"',
  ];
  for (let mimeType of all) {
    // @ts-ignore
    if (mimeType && MediaRecorder.isTypeSupported(mimeType)) {
      return mimeType;
    }
  }
  //一个都没找到？
  return 'video/webm; codecs="vp8"';
};

export const printTimeRanges = (t: TimeRanges) => {
  if (t.length) {
    return `[${t.start(0)}, ${t.end(0)}]`;
  }
  return '';
};
