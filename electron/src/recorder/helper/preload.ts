import {ipc<PERSON><PERSON><PERSON>} from 'electron';
import WebSocket from 'ws';
import {RecordInfo, ShopInfo} from '../../types';
import Recorder from './recorder';

const querystring = require('querystring');

//该文件代码跑在 electron 的录像窗口里

/**
 * 录像控制器
 */
class RecordController {
  sessions: Map<number, Recorder>;

  wss: WebSocket.Server | null = null;

  constructor() {
    this.sessions = new Map();
  }

  async init(host: string = '127.0.0.1', port: number) {
    this.wss = new WebSocket.Server({ host, port });
    console.debug(this.wss);
    this.wss.on('connection', (ws, req) => {
      let reg = /sessionId=(\d+)/;
      if (req.url && reg.test(req.url)) {
        let sessionId = parseInt(RegExp.$1);
        if (this.sessions.has(sessionId)) {
          this.sessions.get(sessionId)!.connectWebsocket(ws);
          return;
        }
      }
      ws.close(1000, 'param error');
    });
  }

  async onSessionOpen(
    shopInfo: ShopInfo,
    recordInfo: RecordInfo,
    requestProps: Record<string, any>,
  ) {
    let recorder = new Recorder({ shopInfo, recordInfo, requestProps });
    this.sessions.set(recordInfo.sessionId, recorder);
    renderSessionList();
  }

  async onSessionClosed(sessionId: number) {
    if (this.sessions.has(sessionId)) {
      let recorder = this.sessions.get(sessionId);
      recorder?.close();
      this.sessions.delete(sessionId);
    }
    renderSessionList();
  }

  async onAjaxEventCB(evt: any) {
    let sessionId = evt.resId;
    if (this.sessions.has(sessionId)) {
      let recorder = this.sessions.get(sessionId);
      recorder?.onAjaxEvent(evt.name, evt.data);
    }
  }
}

const renderSessionList = () => {
  let sessionListDiv = document.getElementById('session_list');
  if(!sessionListDiv) {
    sessionListDiv = document.createElement('div');
    sessionListDiv.id = 'session_list';
    document.body.appendChild(sessionListDiv);
  }
  sessionListDiv!.innerHTML = '';
  for (let recorder of recordController.sessions.values()) {
    let htmlString = `<span>${recorder.shopInfo.name}</span> <input type="button" sessionId="${recorder.recordInfo.sessionId}" value="监视" />`;
    let div = document.createElement('div');
    div.innerHTML = htmlString.trim();
    sessionListDiv!.appendChild(div);
  }
};

let recordController: RecordController;
ipcRenderer.on(
  'shopSessionOpen',
  (evt, shopInfo: ShopInfo, recordInfo: RecordInfo, requestProps: Record<string, any>) => {
    recordController.onSessionOpen(shopInfo, recordInfo, requestProps);
  },
);
ipcRenderer.on('shopSessionClose', (evt, sessionId) => {
  recordController.onSessionClosed(sessionId);
});
ipcRenderer.on('onAjaxEvent-cb', (evt, ajaxEvt) => {
  recordController.onAjaxEventCB(ajaxEvt);
});

console.debug(window.location.href);

window.onload = async function () {
  console.debug('record window loaded!');
  document.title = '花漾录像管理Helper窗口';
  let query = querystring.parse(global.location.search);
  let data = JSON.parse(query['?data']);
  recordController = new RecordController();
  await recordController.init(data.host, data.port);
};
