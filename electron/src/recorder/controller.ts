import { BrowserWindow } from 'electron';
import path from 'path';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import { getAFreePort } from '@e/utils/utils';
import appConfig from '@e/configs/app';

export class RecordControllerOwner {
  recorderWindow: BrowserWindow | undefined;
  host: string = '127.0.0.1';
  port: number = 0;

  constructor() {
    if (appConfig.isOEM) return;
    this.recorderWindow = new BrowserWindow({
      show: false,
      width: 1024,
      height: 768,
      maximizable: false,
      fullscreenable: false,
      webPreferences: {
        // devTools: false,
        devTools: true,
        preload: path.join(__dirname, 'helper/recorder_helper.js'),
        nodeIntegration: true,
        // enableRemoteModule: true,
        webSecurity: false,
        backgroundThrottling: false,
      },
    });
    this.recorderWindow.webContents.on('console-message', (evt, level, message) => {
      if ([1, 3].includes(level)) {
        logger.info(`[RECORDER] ${message}`);
      }
    });
    this.init().then(() => {});
    // try {
    //   this.recorderWindow.webContents.openDevTools();
    // } catch (ignore) {}
  }

  async init() {
    if (appConfig.isOEM) return;
    this.port = await getAFreePort();
    let queryData = JSON.stringify({ host: this.host, port: this.port });
    await this.recorderWindow?.loadFile(path.join(__dirname, './helper/index.html'), {
      query: { data: queryData },
    });
  }

  getWSUrl() {
    return `ws://${this.host}:${this.port}`;
  }

  async onSessionOpen(shopInfo: any, recordInfo: any, requestAgent: RequestAgent) {
    try {
      if (appConfig.isOEM) return;
      if (this.recorderWindow?.webContents.isDestroyed()) return;
      await this.recorderWindow?.webContents.send(
        'shopSessionOpen',
        shopInfo,
        recordInfo,
        requestAgent.getProps(),
      );
    } catch (e) {
      logger.verbose('[APP] recorderWindow send shopSessionOpen failed', e);
    }
  }

  async onSessionClose(sessionId: number) {
    try {
      if (appConfig.isOEM) return;
      if (this.recorderWindow?.webContents.isDestroyed()) return;
      await this.recorderWindow?.webContents.send('shopSessionClose', sessionId);
    } catch (e) {
      //
    }
  }

  async close() {
    try {
      this.recorderWindow?.close(); //fixme 立即close会导致录像可能不完整
    } catch (e) {
      logger.error('[APP] close recorderWindow failed', e);
    }
  }
}
