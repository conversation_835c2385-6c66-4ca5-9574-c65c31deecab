import os from 'os';
import fs from 'fs';

/**
 * 获取浏览器执行路径
 * @param browserCode
 */
export default (browserCode: 'chrome' | 'edge' | 'kuajingvs' | string) => {
  let path = '';
  const osPlatform = os.platform();
  if (osPlatform === 'win32') {
    if (browserCode === 'chrome') {
      const suffix = '\\Google\\Chrome\\Application\\chrome.exe';
      const prefixes = [
        process.env.LOCALAPPDATA,
        process.env.PROGRAMFILES,
        process.env.ProgramW6432,
        process.env['PROGRAMFILES(X86)'],
      ];
      for (let i = 0; i < prefixes.length; i++) {
        const exe = prefixes[i] + suffix;
        if (fs.existsSync(exe)) {
          path = exe;
          break;
        }
      }
    } else if (browserCode === 'edge') {
      const suffix = '\\Microsoft\\Edge\\Application\\msedge.exe';
      const prefixes = [
        process.env.LOCALAPPDATA,
        process.env.PROGRAMFILES,
        process.env.ProgramW6432,
        process.env['PROGRAMFILES(X86)'],
      ];
      for (let i = 0; i < prefixes.length; i++) {
        const exe = prefixes[i] + suffix;
        if (fs.existsSync(exe)) {
          path = exe;
          break;
        }
      }
    }
  } else if (osPlatform === 'darwin') {
    if (browserCode === 'chrome') {
      path = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
    } else if (browserCode === 'edge') {
      path = '/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge';
    } else if (browserCode === 'kuajingvs') {
      path = '/Applications/KuaJingVS Browser.app/Contents/MacOS/KuaJingVS Browser ';
    }
    if (!fs.existsSync(path)) {
      path = '';
    }
  } else if (osPlatform === 'linux') {
    if (browserCode === 'chrome') {
      path = '/opt/google/chrome/chrome';
    } else if (browserCode === 'edge') {
      path = '/opt/microsoft/msedge/msedge';
    }
    if (!fs.existsSync(path)) {
      path = '';
    }
  }
  return path;
};
