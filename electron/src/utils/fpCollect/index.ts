import puppeteer from 'donkey-puppeteer-core';
import fs from 'fs';
import path from 'path';
import os from 'os';

import browserExeFetcher from './browserExeFetcher';
import logger from '../../services/logger';
import { getHostName } from '@e/utils/utils';
import i18n from '@e/utils/i18n';

export async function getFingerprint(browserCode: string) {
  const exePath = browserExeFetcher(browserCode);
  if (!exePath) {
    throw new Error(i18n.t('未在当前电脑上寻找到{{browserCode}}浏览器的安装路径', { browserCode }));
  }
  try {
    logger.info('[APP] getFingerprint', exePath);
    const browser = await puppeteer.launch({
      executablePath: exePath,
      headless: true,
      dumpio: true,
      ignoreDefaultArgs: ['--mute-audio'],
      // userDataDir: getBrowserUserDataDir(browserCode),
    });
    const page = await browser.newPage();
    let scriptContent = '(async function(){';
    scriptContent += fs.readFileSync(path.resolve(__dirname, 'scripts', 'fp.min.js'));
    scriptContent += fs.readFileSync(path.resolve(__dirname, 'scripts', 'fp-fetch.js'));
    scriptContent += `})()`;
    try {
      const res: any = await page.evaluate(scriptContent);
      res.userAgent = res.userAgent.replace('HeadlessChrome', 'Chrome');
      res.computerName = await getHostName();
      res.mem = Math.round(os.totalmem() / Math.pow(1024, 3));
      browser.close();
      return res;
    } catch (e) {
      logger.error('[BROWSER] evaluate script error', e);
      browser.close();
      throw e;
    }
  } catch (e) {
    logger.error('[BROWSER] launch browser error', e);
    throw new Error(i18n.t('唤醒浏览器失败'));
  }
}

export default {
  getFingerprint,
};
