import { BrowserWindow, screen } from 'electron';
import { DEFAULT_WIN_OPTS, loadURL, resolveUrl } from '@e/utils/utils';
import logger from '@e/services/logger';
import db from '@e/components/db';
import { getMainWindow } from '@e/utils/window';

export const popupTaskWins: Map<number, BrowserWindow> = new Map();

export async function popupRpaTaskWin(teamId?: number | string, taskId?: number) {
  if (!teamId || !taskId) return;
  if (popupTaskWins.has(taskId)) {
    popupTaskWins.get(taskId)?.show();
    return;
  }
  const portalUrl = db.getPortalUrl();
  const win = new BrowserWindow({
    ...DEFAULT_WIN_OPTS,
    width: getMainWindow()?.getBounds().width ?? DEFAULT_WIN_OPTS.width,
    height: getMainWindow()?.getBounds().height ?? DEFAULT_WIN_OPTS.height,
    minWidth: 800,
    minHeight: 600,
  });
  win.on('closed', () => {
    popupTaskWins.delete(taskId);
  });
  try {
    await loadURL(
      win,
      resolveUrl(portalUrl, `/team/${teamId}/rpa/popup/task/${taskId}?autoClose=1`),
    );
    popupTaskWins.set(taskId, win);
  } catch (e) {
    logger.error(e);
  }
}
