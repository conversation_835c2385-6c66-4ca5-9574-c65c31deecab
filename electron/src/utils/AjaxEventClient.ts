import request from '../services/request';
import random from './random';
import logger from '@e/services/logger';
import _ from 'lodash';
import db from '@e/components/db';
import { guid } from '@e/utils/utils';
import { portalRpcClient } from '@e/components/backendTask';
import appConfig from "@e/configs/app";

interface Listener {
  hd: string;
  once: boolean;
  name: string;
  resId: any;
  fun: (evt: any) => void;
}

export class AjaxEventClient {
  listeners: Listener[];
  queryTimer: any = 0;
  pageId: string;
  lastQueryTime: number = 0;
  extraHeaders?: Record<string, any>;
  queryInterval = 5012;

  _handleAjaxPush: any;
  _onWsReady: any;

  constructor(props?: { extraHeaders?: Record<string, any> }) {
    this.pageId = random.nextString(6);
    this.listeners = [];
    this.extraHeaders = props?.extraHeaders;
    this._query();
    this._handleAjaxPush = this.handleAjaxPush.bind(this);
    this._onWsReady = this.onWsReady.bind(this);
    portalRpcClient.addListener('on-ajax-push', this._handleAjaxPush);
    portalRpcClient.transport.addListener('connected', this._onWsReady);
    logger.info(`[APP] ajaxEventClient started (pageId: ${this.pageId})`);
    if (this.extraHeaders) {
      logger.info(`[APP] ajaxEventClient extraHeaders`, JSON.stringify(this.extraHeaders));
    }
  }

  listenUploadLogs() {
    portalRpcClient.removeAllListeners('debug.trigger.upload_logs'); //避免重复监听
    // 监听上传客户端日志事件
    portalRpcClient.onAppEmit('debug.trigger.upload_logs', () => {
      const clientUuid = db.getDb().get('uuid').value();
      logger.verbose('[APP] receive report logs event');
      // 上传客户端日志
      logger.uploadLogFile(`FETCH-${guid().toLocaleUpperCase()}`, () => {
        // rpaExecutor 上传完日志后，通知服务端，标记为已完成
        if (db.isRpaExecutor()) {
          request(`/api/logs/markFinished?clientUuid=${clientUuid}`, {
            headers: this.extraHeaders,
          });
        }
      });
    });
  }

  /**
   * 监听resId相关的一个事件
   * @param name
   * @param resId
   * @param fun
   * @param once 是否只监听一次，默认false
   * @return 一个字符串，取消监听要用到
   */
  async on(name: string, resId: any, fun: (evt: any) => void, once: boolean = false) {
    if (_.findIndex(this.listeners, (lsn) => lsn.name === name && lsn.resId === resId) === -1) {
      // 同一个事件同一个资源不重复发请求
      let data = `eventName=${name}&resId=${resId}&pageId=${this.pageId}`;
      await request(`/api/ajax-event/listener?${data}`, {
        method: 'put',
        headers: this.extraHeaders,
      });
    }
    let hd = random.nextString(12);
    let listener: Listener = { hd, once, fun, resId, name };
    this.listeners.push(listener);
    return hd;
  }

  async tryReListenerEvents() {
    if (this.listeners.length > 0) {
      logger.info('事件监听已经丢失，尝试重新监听');
      logger.log(
        `客户端监听列表: ${JSON.stringify(
          _.map(this.listeners, (listener) => _.pick(listener, ['name', 'resId'])),
        )}`,
      );
      let serverEvents = null;
      for (let i = 0; i < this.listeners.length; i++) {
        let listener = this.listeners[i];
        let data = `eventName=${listener.name}&resId=${listener.resId}&pageId=${this.pageId}`;
        if(appConfig.DEBUG && i === (this.listeners.length - 1)) {
          data += 'needDetail=true';
        }
        try {
          serverEvents = await request(`/api/ajax-event/listener?${data}`, {
            method: 'put',
            headers: this.extraHeaders,
          });
        } catch (e: any) {
          logger.error(`重新监听事件失败: ${data}`, e);
        }
      }
      logger.log(`重新监听后服务端监听列表: ${JSON.stringify(serverEvents)}`);
    }
  }

  /**
   * 一次性监听resId相关的一个事件
   * @param name
   * @param resId
   * @param fun
   * @see #on
   * @return 一个字符串，取消监听要用到
   */
  async once(name: string, resId: any, fun: (evt: any) => void) {
    return await this.on(name, resId, fun, true);
  }

  /**
   * 取消监听一个事件
   * @param hd on 或 once 返回的handler id
   */
  async un(hd: string) {
    for (let i = 0; i < this.listeners.length; i++) {
      const listener = this.listeners[i];
      if (listener.hd === hd) {
        this.listeners.splice(i, 1);
        await this._unRequest(listener.name, listener.resId);
        break;
      }
    }
  }

  async close() {
    clearTimeout(this.queryTimer);
    portalRpcClient.removeListener('on-ajax-push', this._handleAjaxPush);
    portalRpcClient.transport.removeListener('connected', this._onWsReady);
    for (const listener of this.listeners) {
      this.un(listener.hd);
    }
    this.listeners = [];
    logger.info(`[APP] ajaxEventClient stopped (pageId: ${this.pageId})`);
  }

  reset() {
    const newPageId = random.nextString(6);
    logger.info(`[APP] reset ajaxEventClient (${this.pageId} -> ${newPageId})`);
    this.queryInterval = 5012;
    this.pageId = newPageId;
    this.tryReListenerEvents();
    clearTimeout(this.queryTimer);
    this._query();
  }

  async _query() {
    this.queryTimer = setTimeout(async () => {
      if (this.listeners.length > 0) {
        try {
          const now = Date.now();
          if (now - this.lastQueryTime < 500) return;
          this.lastQueryTime = now;
          let events = await request('/api/ajax-event/query?pageId=' + this.pageId, {
            method: 'get',
            headers: this.extraHeaders,
          });
          if (events && events.length > 0) {
            this.queryInterval = 5012;
            await this.handleEvents(events);
          }
        } catch (e: any) {
          if (e == 'PageIdNotExist' || e.message == 'PageIdNotExist') {
            logger.info(`[EVT] pageId 不存在(${this.pageId})，尝试重新监听事件`);
            this.tryReListenerEvents();
          }
        } finally {
          this._query();
        }
      } else {
        this._query();
      }
    }, this.queryInterval);
  }

  private async handleEvents(events: any[]) {
    for (let evt of events) {
      for (let i = 0; i < this.listeners.length; i++) {
        let listener = this.listeners[i];
        if (listener.name == evt.name && listener.resId == evt.resId) {
          logger.verbose(`[APP] Ajax Event receive: ${JSON.stringify(evt)}`);
          this._invoke(listener, evt.data).then((r) => {});
          if (listener.once) {
            this.listeners.splice(i, 1);
            i--;
          }
        }
      }
    }
  }

  async handleAjaxPush(events: any[]) {
    // console.log('handleAjaxPush', events);
    this.queryInterval = 30000;
    if (events && events.length > 0) {
      events = _.filter(events, (evt) => evt.name != 'empty-push-notify');
      if (events && events.length > 0) {
        await this.handleEvents(events);
      }
    }
  }

  async onWsReady() {
    setTimeout(async () => {
      await request(`/api/ajax-event/ws_ready?pageId=${this.pageId}`, {
        method: 'put',
        headers: this.extraHeaders,
      });
    }, 1000);
  }

  async _unRequest(name: string, resId: string) {
    let data = `eventName=${name}&resId=${resId}&pageId=${this.pageId}`;
    await request(`/api/ajax-event/un-listener?${data}`, {
      method: 'put',
      headers: this.extraHeaders,
    });
  }

  async _invoke(listener: Listener, data: any) {
    try {
      listener.fun.call(null, data);
    } catch (e) {}
  }
}
