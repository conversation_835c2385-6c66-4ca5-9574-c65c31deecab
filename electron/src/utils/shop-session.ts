import { rawRequest, RequestAgent } from '@e/services/request';
import logger from '@e/services/logger';
import { hyDecryptVo } from '@e/utils/crypto';
import _ from 'lodash';
import { TunnelRouter } from '@e/utils/tunnelRouter';
import { getAjaxEventClientIns } from '@e/components/backendTask';

interface Props {
  shopId: number;
  teamId: number;
  openapiTaskId?: number;
  rpaFlowId?: number;
  rpaTaskId?: number;
  ghost?: boolean;
  requestAgent?: RequestAgent;
}

export default class ShopSession {
  private props: Props;
  requestAgent: RequestAgent;
  shopInfo?: API.ShopDetailVo;
  sessionTokenVo?: API.SessionTokenVo;
  sessionId?: number;
  tunnelRouter?: TunnelRouter;
  primaryChannelTokenVo?: API.SessionChannelTokenVo;
  transitList?: API.TransitWithLocationVo[];
  constructor(props: Props) {
    this.props = props;
    this.requestAgent = props.requestAgent || new RequestAgent();
  }

  async init() {
    await this.getShopInfo();
  }

  async getShopInfo() {
    this.shopInfo = await this.requestAgent.request(`/api/shop/${this.props.shopId}`, {
      teamId: this.props.teamId,
    });
    return this.shopInfo!;
  }

  async openSession() {
    const { teamId, shopId, rpaFlowId, rpaTaskId, openapiTaskId, ghost = false } = this.props;

    const primaryChannel = this.shopInfo?.channels?.find((channel) => channel.primary);
    // IP池需要检测是否有可用IP
    let openSessionRequest = undefined;
    if (primaryChannel?.ippId) {
      logger.info('[IPP] start to request ip');
      const allocIppIpResult = await this.requestAgent.request(
        `/api/ipp/pool/${primaryChannel.ippId}/allocIppIp`,
        {
          teamId,
        },
      );
      if (allocIppIpResult.ippIpId) {
        openSessionRequest = {
          channelIppIpMap: {
            [String(primaryChannel!.id)]: allocIppIpResult.ippIpId,
          },
        };
      } else if (allocIppIpResult.produceIpSpecVo) {
        const { url, method, headers } = allocIppIpResult.produceIpSpecVo;
        logger.info(`[IPP] request ip, url: ${url}, headers: ${headers}`);
        try {
          const { responseBody, responseHeaders, httpCode } = await rawRequest(url, {
            method,
            headers,
            parseJson: false,
            exposeHeaders: true,
          });
          logger.info(
            `[IPP] request ip response: code => ${httpCode}, body => ${responseBody}, headers => ${responseHeaders}`,
          );
          // 获取IP池ipId
          const ippIpId = await this.requestAgent.request(
            `/api/ipp/pool/${primaryChannel.ippId}/parseIppIp`,
            {
              method: 'post',
              data: {
                produceIpSpecVo: allocIppIpResult.produceIpSpecVo,
                responseBody,
                responseHeaders,
                httpCode,
              },
            },
          );
          openSessionRequest = {
            channelIppIpMap: {
              [String(primaryChannel!.id)]: ippIpId,
            },
          };
        } catch (err: any) {
          throw new Error(`从IP服务商获取IP失败，错误信息：${err.message}`);
        }
      }
    }
    logger.info(`[BROWSER] create session (shopId: ${shopId})`);
    // 打开分身会话，获取 token
    this.sessionTokenVo = await this.requestAgent.request(`/api/shop/${shopId}/openSession`, {
      teamId,
      method: 'post',
      data: {
        rpaFlowId: rpaFlowId || '',
        rpaTaskId: rpaTaskId || '',
        openapiTaskId: openapiTaskId || '',
        ghost,
        ...openSessionRequest,
      },
    });
    this.sessionTokenVo!.channelTokens?.forEach((channelToken) => {
      if (channelToken.proxyConfig) {
        channelToken.proxyConfig = hyDecryptVo(channelToken.proxyConfig);
      }
    });
    this.sessionId = this.sessionTokenVo!.sessionId;
    if (primaryChannel) {
      this.primaryChannelTokenVo = _.find(this.sessionTokenVo!.channelTokens, {
        channelId: primaryChannel!.id,
      });
    }
  }

  async closeSession() {
    if (!this.sessionId) return;
    await this.requestAgent.request(`/api/shop/session/${this.sessionId}/closed`, {
      teamId: this.props.teamId,
      method: 'put',
    });
  }

  async initTunnelRouter() {
    // 中转列表
    this.transitList = await this.requestAgent.request('/api/transit/list', {
      teamId: this.props.teamId,
      params: { includeIpEndpoints: true },
    });
    this.tunnelRouter = new TunnelRouter({
      shopInfo: this.shopInfo!,
      sessionTokenVo: this.sessionTokenVo!,
      transitList: this.transitList!,
      requestAgent: this.requestAgent,
      ajaxEventClient: getAjaxEventClientIns(),
    });
    try {
      await this.tunnelRouter.init();
    } catch (e) {
      await this.closeSession();
      throw e;
    }
  }

  reportOpenApiTaskException(message?: string) {
    if (!this.props.openapiTaskId) return;
    this.requestAgent.request(`/api/open/task/${this.props.openapiTaskId}`, {
      teamId: this.props.teamId,
      method: 'put',
      params: {
        status: 'Fail',
        remarks: message,
        done: true,
      },
    });
  }

  sessionOpenReport() {
    this.requestAgent.request(`/api/shop/session/${this.sessionId}/onOpen`, {
      method: 'PUT',
      teamId: this.props.teamId,
      data: {
        success: true,
        remoteProxyType: this.tunnelRouter!.getDefaultChannel()?.getProxyInfo().type,
        remoteProxyPort: this.tunnelRouter!.getDefaultChannel()?.getProxyInfo().port,
      },
    });
  }
}
