function getNotABrandLabel(version: number) {
  return [
    'Not A(Brand',
    'Not(A:Brand',
    'Not:A-Brand',
    'Not-A.Brand',
    'Not.A/Brand',
    'Not/A)Brand',
    'Not)A;Brand',
    'Not;A=Brand',
    'Not=A?Brand',
    'Not?A_Brand',
    'Not_A Brand',
  ][version % 11];
}

function getBrandVersion(version: number) {
  return ['8', '99', '24'][version % 3];
}

function getBrandFullVersion(version: number) {
  let brandVersion = getBrandVersion(version);
  return `${brandVersion}.0.0.0`;
}

function getBrandsOrder(version: number) {
  return [
    ['brand', 'chromium', 'chrome'],
    ['brand', 'chrome', 'chromium'],
    ['chromium', 'brand', 'chrome'],
    ['chrome', 'brand', 'chromium'],
    ['chromium', 'chrome', 'brand'],
    ['chrome', 'chromium', 'brand']
  ][version % 6];
}

export function getBrowserBrands(version: number) {
  let notABrand = getNotABrandLabel(version);
  let brandVersion = getBrandVersion(version);
  let brandFullVersion = getBrandFullVersion(version);
  let brandsOrder = getBrandsOrder(version);
  return {
    notABrand,
    brandVersion,
    brandFullVersion,
    brandsOrder,
  };
}
