import crypto from 'crypto';
import _ from 'lodash';
import { <PERSON>uff<PERSON> } from 'buffer';
import logger from '@e/services/logger';

const ALGORITHM = 'aes-256-ecb';
const PREFIX = '{aes.salt}';
const iterationCount = 7;

function xorStrings(input: string, key: string) {
  let result = '';
  for (let i = 0; i < input.length; i++) {
    const xorChar = String.fromCharCode(input.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    result += xorChar;
  }
  return result;
}

function buildKey() {
  return xorStrings(
    Buffer.from([
      40, 31, 45, 46, 30, 28, 35, 70, 44, 54, 60, 54, 54, 36, 24, 7, 8, 49, 0, 30, 42, 39, 42, 31,
      4, 61, 39, 91, 38, 46, 95, 61,
    ]).toString('utf8'),
    'nothingtodo',
  );
}
const password = buildKey(); // 'FpYFwrD2CRSXYPpnfVtqNHDppUN5AZ0Y';

function getKeyFromPassword(_password: string, salt: string) {
  // Generate the key using PBKDF2 with HMAC SHA-256
  return crypto.pbkdf2Sync(_password, Buffer.from(salt, 'hex'), iterationCount, 32, 'sha256');
}

// 加密
export function hyEncrypt(data: string) {
  try {
    const salt = crypto.randomBytes(16).toString('hex');
    const key = getKeyFromPassword(password, salt);
    const cipher = crypto.createCipheriv(ALGORITHM, key, null);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return `${PREFIX}${salt}.${encrypted}`;
  } catch (e: any) {
    logger.error(`hyEncrypt error`, e.message);
    return data;
  }
}

// 解密
export function hyDecrypt(encryptedPair: string = '') {
  if (!encryptedPair.startsWith(PREFIX)) {
    return encryptedPair;
  }
  try {
    const [, str] = encryptedPair.split(PREFIX);
    const [salt, encrypted] = str.split('.');
    const key = getKeyFromPassword(password, salt);
    const decipher = crypto.createDecipheriv(ALGORITHM, key, null);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (e: any) {
    logger.error(`hyDecrypt error - ${encryptedPair}:`, e.message);
    return encryptedPair;
  }
}

// 对一个对象的所有属性进行加密
export function hyEncryptVo(data: Record<string, any>): any {
  if (!_.isPlainObject(data)) return data;
  const result: Record<string, any> = {
    ...data,
  };
  Object.keys(data).forEach((key) => {
    if (['host', 'username', 'password', 'sshKey'].includes(key) && typeof data[key] === 'string') {
      result[key] = hyEncrypt(data[key]);
    }
  });
  return result;
}

// 对一个对象的所有属性进行解密
export function hyDecryptVo(data: Record<string, any>): any {
  if (!_.isPlainObject(data)) return data;
  const result: Record<string, any> = {
    ...data,
  };
  Object.keys(data).forEach((key) => {
    if (['host', 'username', 'password', 'sshKey'].includes(key) && typeof data[key] === 'string') {
      result[key] = hyDecrypt(data[key]);
    }
  });
  return result;
}
