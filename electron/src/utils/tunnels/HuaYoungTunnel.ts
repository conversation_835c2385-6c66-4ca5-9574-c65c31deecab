import net, { Server, Socket } from 'net';
import WebSocket, { Data } from 'ws';
import crypto from 'crypto';
import electron, { powerMonitor } from 'electron';
import { setInterval, setTimeout, clearInterval, clearTimeout } from 'timers';

import logger from '@e/services/logger';
import db from '@e/components/db';
import { getHostname, ITunnel } from '@e/utils/tunnels/index';
import { HuaYoungTunnelProps } from '@e/utils/tunnels/ChannelTunnel';
import { hyDecrypt } from '@e/utils/crypto';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { PROBE_TIME_OUT } from '@e/utils/proxy_util';

const CMD_Bytes = 4; // 指令
const LEN_Bytes = 4; // 内容长度
const CONNECTION_ID_Bytes = 12; // 一个连接的 uuid

// 协议族
const PROTOCOL_FAMILY = 0x00030000;
// 管道心跳
const Heartbeat = 0;
const HeartBeatAck = 1;

function wrapCmd(cmd: number) {
  return PROTOCOL_FAMILY + cmd;
}

// 准备创建
const PrepareDynamicForwardPipe = wrapCmd(1);
// 准备完成
const PrepareDynamicForwardPipeAck = wrapCmd(2);
// 发起连接
const CreateDynamicForwardLink = wrapCmd(3);
// 连接成功
const CreateDynamicForwardLinkAck = wrapCmd(4);
// 传输数据
const DynamicForwardLinkData = wrapCmd(7);
// 链路关闭，通知，没有ACK，在控制链路传播，用于清理资源
const DynamicForwardLinkClosed = wrapCmd(8);
// 管道关闭（会话结束）
const CloseDynamicForwardPipe = wrapCmd(9);
// token认证失败
const ClientAuthFail = wrapCmd(10);

const TUNNEL_VERBOSE = db.getDb().get('TUNNEL_VERBOSE').value();
function loggerVerbose(...args: any) {
  if (TUNNEL_VERBOSE) {
    logger.verbose(...args);
  }
}

/**
 * 与IPGo或者中转连接的通道，走的是自有协议
 */
export class HuaYoungTunnel extends ITunnel {
  props: HuaYoungTunnelProps;
  channelSessionId: number;
  token: string;
  endpoints?: API.TunnelEndpointVo[];
  endpointUrl?: string;
  socks5Port: number;
  server?: Server;
  websocket?: WebSocket;
  isTunnelReady: boolean;
  isWebsocketConnecting = false;
  websocketInitTimer?: any;

  // 管道心跳
  tunnelHeartbeatTimer?: any;
  tunnelHeartbeatStamp: number;
  // PrepareDynamicForwardPipeAck Error 后强制重连
  forceReconnect: boolean;
  // 检查管道心跳
  checkTunnelHeartbeatTimer?: any;
  pingTimer: any;
  // 重连尝试次数和最大重连次数
  private reconnectAttempts: number = 0;
  private readonly maxReconnectAttempts: number = 5;
  // 重试间隔相关参数
  private readonly baseRetryDelay: number = 3 * 1000; // 基础重试间隔3秒
  private readonly maxRetryDelay: number = 60 * 1000; // 最大重试间隔60秒
  // 心跳检查时间戳
  private lastHeartbeatCheck: number = Date.now();
  // 期望的心跳检查间隔
  private expectedHeartbeatInterval: number = 3 * 1000;
  connContainer: {
    [uuid: string]: {
      data: Buffer;
      socket: Socket;
      host: string;
      port: number;
    };
  };
  domainRecordFlag: Record<string, boolean>;
  private transitPing: Record<string, number>;
  private forceReconnectCount: number;
  currentTransitId?: number;
  private wsInfoList: {
    ws: WebSocket;
    direct: boolean;
    endpoint: string;
    transitId: number;
    ping: number;
    connected: boolean;
    trafficPrice: number | null;
  }[];
  private closed: boolean;
  private id: string;
  // 电源事件监听器引用
  private suspendListener?: () => void;
  private resumeListener?: () => void;

  constructor(props: HuaYoungTunnelProps) {
    super({
      sessionTokenVo: props.sessionTokenVo,
      sessionChannelTokenVo: props.sessionChannelTokenVo,
      teamId: props.teamId,
      requestAgent: props.requestAgent,
    });
    this.props = props;
    const { channelSessionId, endpoints } = this.props.sessionChannelTokenVo;
    this.channelSessionId = channelSessionId!;
    this.token = this.props.token;
    this.endpoints = endpoints?.filter(
      (vo) => vo.tunnelType === 'transit' || vo.tunnelType === 'jump' || vo.pipeType === 'Tunnel',
    );
    // 让操作系统分配随机的可用端口
    this.socks5Port = 0;
    this.isTunnelReady = false;
    this.tunnelHeartbeatStamp = new Date().getTime();
    this.forceReconnect = false;
    this.connContainer = {};
    this.checkTunnelHeartbeatTimer = 0;
    this.pingTimer = 0;
    this.transitPing = {};
    this.domainRecordFlag = {};
    this.forceReconnectCount = 0;
    this.wsInfoList = [];
    this.closed = false;
    this.id = guid();
    logger.info(`[TUNNEL] new HuaYoungTunnel instance id => ${this.id}`);
    const { endpoint = '' } = db.getDb().get('sysPres').value();
    // 根据系统配置决定是否用指定的 endpoint
    if (endpoint) {
      this.endpoints = [endpoint];
    }

    // 监听系统电源事件
    try {
      this.suspendListener = () => {
        logger.info('[TUNNEL] System suspending, clearing heartbeat timer');
        if (this.checkTunnelHeartbeatTimer) {
          clearTimeout(this.checkTunnelHeartbeatTimer);
          this.checkTunnelHeartbeatTimer = null;
        }
      };

      this.resumeListener = () => {
        logger.info('[TUNNEL] System resuming, restarting heartbeat timer');
        this._startCheckTunnelHeartbeatTimer();
      };

      powerMonitor.on('suspend', this.suspendListener);
      powerMonitor.on('resume', this.resumeListener);
    } catch (e) {
      logger.warn('[TUNNEL] Failed to setup power monitor listeners:', e);
    }
  }

  pacRule(): string {
    return `SOCKS5 127.0.0.1:${this.socks5Port}`;
  }

  async init(props?: { defaultTransitId?: number; rejectOnWebsocketInitFailed?: boolean }) {
    if (typeof props?.defaultTransitId !== undefined) {
      const endpoint = this.endpoints?.find(
        (endpoint) => endpoint.nodeId === props?.defaultTransitId,
      );
      if (endpoint?.endpoint) {
        this.endpointUrl = endpoint.endpoint;
        this.currentTransitId = endpoint.nodeId;
      }
    }
    return new Promise(async (resolve, reject) => {
      try {
        if (this.endpoints?.filter((ep) => !!ep.endpoint)?.length! > 0) {
          try {
            await this._initWebsocket();
          } catch (e) {
            logger.error(`[TUNNEL] 初始化 Websocket 连接失败`, e);
            if (props?.rejectOnWebsocketInitFailed) {
              reject(e);
            }
          }
        }
        const socks5Server = net.createServer((socket) => {
          socket.once('data', (data) => {
            if (!data || data[0] !== 0x05)
              return socket.destroy(new Error(`${data[0]} is not supported`));
            socket.write(Buffer.from([5, 0]), (err) => {
              if (err) {
                socket.destroy();
              }
              socket.once('data', (data) => {
                if (data.length < 7 || data[1] !== 0x01) return socket.destroy(); // 只支持 CONNECT
                try {
                  const ATYP = data[3]; // 目标服务器地址类型
                  if (ATYP !== 1 && ATYP !== 3) {
                    return socket.destroy();
                  }
                  let remoteAddr;
                  let remotePort = data.slice(data.length - 2).readUInt16BE(0); //最后两位为端口值
                  let copyBuf = Buffer.allocUnsafe(data.length);
                  data.copy(copyBuf);
                  if (ATYP === 1) {
                    // 0x01 IP V4地址
                    remoteAddr = getHostname(data.slice(4, 8));
                    this._connectWithWs(remoteAddr, remotePort, copyBuf, socket);
                  } else {
                    //0x03 域名地址
                    // @ts-ignore
                    let len = parseInt(data[4], 10);
                    remoteAddr = data.slice(5, 5 + len).toString('utf8');
                    this._connectWithWs(remoteAddr, remotePort, copyBuf, socket);
                  }
                } catch (e) {
                  logger.error('[TUNNEL] socket trans to websocket Error: ', e);
                }
              });
            });
          });

          //监听错误
          socket.on('error', (err: any) => {
            socket.destroyed || socket.destroy();
            if (err.code !== 'ECONNRESET') {
              logger.error('[TUNNEL] socket Error: ', err.message);
            }
          });

          socket.on('close', () => {
            socket.destroyed || socket.destroy();
          });
        });

        socks5Server.listen(0, this.listenHost, () => {
          const address = socks5Server.address();
          if (address && typeof address === 'object') {
            this.socks5Port = address.port;
            logger.info(
              `[TUNNEL] [transit:${this.currentTransitId}] SOCKS server listening on port ${this.socks5Port}`,
            );
            resolve(this.socks5Port);
          } else {
            reject(new Error('[TUNNEL] parse server address failed'));
          }
        });

        this.server = socks5Server;
      } catch (e) {
        reject(e);
      }
    }).then(() => {
      if (!this.closed && this.endpoints?.length! > 0) {
        this._startCheckTunnelHeartbeatTimer();
      }
    });
  }

  _startCheckTunnelHeartbeatTimer() {
    // 清除之前的定时器
    if (this.checkTunnelHeartbeatTimer) {
      clearTimeout(this.checkTunnelHeartbeatTimer);
      this.checkTunnelHeartbeatTimer = null;
    }

    // 计算当前重试延迟时间（指数退避）
    const currentDelay = Math.min(
      this.baseRetryDelay * Math.pow(2, this.reconnectAttempts),
      this.maxRetryDelay,
    );

    if (this.closed) {
      return;
    }

    // 更新期望间隔
    this.expectedHeartbeatInterval = currentDelay;

    this.checkTunnelHeartbeatTimer = setTimeout(async () => {
      const now = Date.now();
      // 添加基于时间戳的检查，确保不会因为系统休眠等原因导致检查过于频繁
      if (now - this.lastHeartbeatCheck >= this.expectedHeartbeatInterval * 0.8) {
        this.lastHeartbeatCheck = now;

        if (!electron.net.isOnline()) {
          loggerVerbose('[TUNNEL] Network is offline');
          // 继续调度下一次检查
          this._startCheckTunnelHeartbeatTimer();
          return;
        }

        // PrepareDynamicForwardPipeAck Error 后强制重连，管道心跳超时，重连
        if (
          this.forceReconnect ||
          (!this.isWebsocketConnecting &&
            this.tunnelHeartbeatStamp < new Date().getTime() - 15 * 1000)
        ) {
          // 检查是否已达到最大重连尝试次数
          if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger.error(
              `[TUNNEL] [transit:${this.currentTransitId}] Max reconnect attempts reached (${this.maxReconnectAttempts}), stopping reconnection attempts`,
            );
            // 可以在这里添加通知上层应用连接失败的逻辑
            return;
          }

          if (this.forceReconnect) {
            this.forceReconnectCount++;
          } else {
            logger.info(
              `[TUNNEL] [transit:${this.currentTransitId}] timeout，try to reconnect (${Math.round(
                (new Date().getTime() - this.tunnelHeartbeatStamp) / 1000,
              )}s)`,
            );
          }

          try {
            // 增加重连尝试计数
            this.reconnectAttempts++;

            if (this.forceReconnectCount >= 2) {
              this.forceReconnect = false;
              this.forceReconnectCount = 0;
              // 重置重连尝试计数，因为这是切换中转节点而不是简单重连
              this.reconnectAttempts = 0;
              logger.info(
                `[TUNNEL] [transit:${this.currentTransitId}] autoSwitchTransit (HuaYoungTunnel instance id: ${this.id} attempt ${this.reconnectAttempts})`,
              );
              this.props.autoSwitchTransit();
            } else {
              logger.info(
                `[TUNNEL] [transit:${this.currentTransitId}] force reconnect (HuaYoungTunnel instance id: ${this.id} attempt ${this.reconnectAttempts})`,
              );
              this._clearSockets();
              this._closeWebsocket();
              await this._initWebsocket();
              // 重置重连尝试计数，因为连接成功了
              this.reconnectAttempts = 0;
            }
          } catch (e) {
            logger.error(
              `[TUNNEL] [transit:${this.currentTransitId}] reconnect failed (HuaYoungTunnel instance id: ${this.id} attempt ${this.reconnectAttempts}):`,
              e,
            );
            // 错误已经记录，继续下一次重连尝试
          } finally {
            // 重新调度下一次检查
            this._startCheckTunnelHeartbeatTimer();
          }
        } else {
          // 如果没有触发重连条件，重置重连尝试计数
          if (this.reconnectAttempts > 0) {
            this.reconnectAttempts = 0;
          }
          // 继续调度下一次检查
          this._startCheckTunnelHeartbeatTimer();
        }
      } else {
        // 时间间隔太短，重新调度但不执行检查逻辑
        this._startCheckTunnelHeartbeatTimer();
      }
    }, currentDelay);
  }

  getEndpointUrl() {
    return this.endpointUrl;
  }

  getCurrentEndpointVo() {
    return this.endpoints?.find((vo) => vo.nodeId === this.currentTransitId);
  }

  getTransitPing() {
    return this.transitPing;
  }

  getEndpointUrlToConnect(endpointVo?: API.TunnelEndpointVo) {
    return endpointVo?.endpoint;
  }

  updateToken(token: string) {
    this.token = token;
  }

  updateEndpoints(endpoints: API.TunnelEndpointVo[]) {
    this.endpoints = endpoints;
  }

  async switchTransit(transitId: number, manual = true, revertOnException = true) {
    if (this.currentTransitId === transitId) return;
    let endpoint;
    endpoint = this.endpoints?.find((endpoint) => endpoint.nodeId === transitId);
    this.forceReconnectCount = 0;
    // 重置重连尝试计数
    this.reconnectAttempts = 0;
    const newEndpointUrl = this.getEndpointUrlToConnect(endpoint);
    if (newEndpointUrl) {
      let lastEndpoint = this.endpoints?.find(
        (endpoint) => endpoint.nodeId === this.currentTransitId,
      );
      this.currentTransitId = endpoint!.nodeId;
      try {
        if (this.endpointUrl) {
          logger.info(
            `[TUNNEL] switch (manual: ${manual}) transit from ${this.endpointUrl} to ${newEndpointUrl}, (transitId: ${this.currentTransitId}, channelSessionId: ${this.channelSessionId}, official: ${this.props.sessionChannelTokenVo.official})`,
          );
          this.endpointUrl = newEndpointUrl;
          this._clearSockets();
          this._closeWebsocket();
          // 清除心跳检查定时器
          if (this.checkTunnelHeartbeatTimer) {
            clearTimeout(this.checkTunnelHeartbeatTimer);
            this.checkTunnelHeartbeatTimer = null;
          }
          await this._initWebsocket();
          this._startCheckTunnelHeartbeatTimer();
        } else {
          this.endpointUrl = newEndpointUrl;
        }
      } catch (e: any) {
        if (revertOnException) {
          this.endpointUrl = newEndpointUrl;
          logger.error(
            `[TUNNEL] switch transit(transitId: ${this.currentTransitId}) failed`,
            e.message,
          );
          if (lastEndpoint?.nodeId && transitId !== lastEndpoint.nodeId) {
            await this.switchTransit(lastEndpoint.nodeId, false, false);
          }
        }
        throw e;
      }
    }
  }

  _wrapEndpointUrl(endpoint?: string) {
    return endpoint ? new URL('transitSrv', endpoint).href : '';
  }

  /**
   * 清理 socket 连接
   */
  _clearSockets() {
    for (const connId in this.connContainer) {
      const socket = this.connContainer[connId].socket;
      socket.destroyed || socket.destroy();
      delete this.connContainer[connId];
    }
  }

  _getConnByConnId(connId: string) {
    if (this.connContainer[connId]) {
      return this.connContainer[connId];
    }
    return null;
  }

  async _prepareWebsocket() {
    return new Promise<WebSocket>(async (resolve, reject) => {
      let endpointUrl = this.endpointUrl;
      logger.info(
        `[TUNNEL] connecting transit ${endpointUrl}, (transitId: ${this.currentTransitId}, channelSessionId: ${this.channelSessionId}, official: ${this.props.sessionChannelTokenVo.official})`,
      );
      const ws = new WebSocket(this._wrapEndpointUrl(endpointUrl), [], {
        headers: {
          Authorization: hyDecrypt(this.token),
        },
        rejectUnauthorized: false,
      });
      ws.on('open', () => {
        logger.info(
          `[TUNNEL] connected transit ${endpointUrl}, (transitId: ${this.currentTransitId}, channelSessionId: ${this.channelSessionId}, official: ${this.props.sessionChannelTokenVo.official})`,
        );
        logger.info(
          `[TUNNEL] use transit ${endpointUrl}, (transitId: ${this.currentTransitId}, channelSessionId: ${this.channelSessionId}, official: ${this.props.sessionChannelTokenVo.official})`,
        );
        resolve(ws);
      });
      ws.on('close', () => {
        logger.info(
          `[TUNNEL] websocket closed, (transitId: ${this.currentTransitId}, channelSessionId: ${this.channelSessionId}, official: ${this.props.sessionChannelTokenVo.official})`,
        );
      });
      ws.on('error', (e) => {
        logger.info(
          `[TUNNEL] connect has timeout or closed ${endpointUrl}, (transitId: ${this.currentTransitId}, channelSessionId: ${this.channelSessionId}, official: ${this.props.sessionChannelTokenVo.official})`,
          e,
        );
        reject(
          new Error(
            `Websocket 连接失败（endpoint:${endpointUrl}, transitId: ${this.currentTransitId}, channelSessionId: ${this.channelSessionId}, official: ${this.props.sessionChannelTokenVo.official}）`,
          ),
        );
      });
    });
  }

  /**
   * 初始化 websocket 管道
   */
  async _initWebsocket() {
    return new Promise(async (resolve, reject) => {
      // 15s 如果websocket还未就绪，关闭连接
      const initTimer = (this.websocketInitTimer = setTimeout(() => {
        logger.error(`[TUNNEL] [transit:${this.currentTransitId}] Websocket 连接超时`);
        reject(new Error('Websocket connect timeout'));
        this._closeWebsocket();
        this.isWebsocketConnecting = false;
      }, 15 * 1000));

      try {
        this.forceReconnect = false;
        this.isWebsocketConnecting = true;
        this.websocket = await this._prepareWebsocket();
        if (this.closed) {
          clearTimeout(initTimer);
          this.isWebsocketConnecting = false;
          this._closeWebsocket();
          reject(new Error(`HuaYoungTunnel instance (${this.id}) has closed`));
          return;
        }
        this.websocket?.send(socksBuf2WsPacket(PrepareDynamicForwardPipe));
        clearInterval(this.tunnelHeartbeatTimer);
        this.tunnelHeartbeatTimer = setInterval(() => {
          // console.log('send heartbeat');
          this._sendWebsocketCmd(socksBuf2WsPacket(Heartbeat), false);
        }, 2000);

        this.websocket.on('message', (wsData) => {
          // console.log('websocket message', wsData);
          this.tunnelHeartbeatStamp = new Date().getTime();
          // @ts-ignore
          const family = parseInt(wsData[1], 10);
          // @ts-ignore
          const cmdValue = parseInt(wsData[3], 10);
          const cmd = family === 0x03 ? wrapCmd(cmdValue) : cmdValue;
          if (cmd === Heartbeat) {
            // 心跳
            // console.log('Heartbeat!!!', new Date().getTime());
            this._sendWebsocketCmd(socksBuf2WsPacket(HeartBeatAck), false);
          } else if (cmd === HeartBeatAck) {
            // console.log('HeartBeatAck!!!', new Date().getTime());
          } else if (cmd === PrepareDynamicForwardPipeAck) {
            clearTimeout(initTimer);
            resolve(this.websocket);
            let body = { success: false, error: '' };
            try {
              body = JSON.parse(wsBuf2SocksPacket(wsData, true).toString());
            } catch (e) {}
            if (!body.success) {
              this.forceReconnect = true;
              logger.error(
                `[TUNNEL] [transit:${this.currentTransitId}] PrepareDynamicForwardPipeAck Error: ${body.error}`,
              );
              return;
            }
            // 管道准备就绪
            this.isTunnelReady = true;
            logger.info(
              `[TUNNEL] [transit:${this.currentTransitId}] PrepareDynamicForwardPipeAck Success`,
            );
          } else {
            // 处理连接相关的逻辑
            if (cmd === CreateDynamicForwardLinkAck) {
              // console.log('websocket msg => CreateDynamicForwardLinkAck');
              const connId = JSON.parse(wsData.slice(CMD_Bytes + LEN_Bytes).toString()).pipelineId;
              loggerVerbose(`[TUNNEL] CreateDynamicForwardLink Response, pipelineId: ${connId}`);
              const conn = this._getConnByConnId(connId);
              if (!conn) return;
              let body = { success: false, error: '' };
              try {
                body = JSON.parse(wsBuf2SocksPacket(wsData, true).toString());
              } catch (e) {
                logger.error(
                  `[TUNNEL] packet parse error, pipelineId: ${connId}`,
                  wsData.toString(),
                );
              }
              if (!body.success) {
                logger.error(
                  `[TUNNEL] [transit:${this.currentTransitId}] CreateDynamicForwardLinkAck Failed, pipelineId: ${connId}, ${conn.host}:${conn.port} Error: ${body.error}`,
                );
                conn.socket.destroyed || conn.socket.destroy();
                return;
              }
              // logger.info('@@@ connected', conn.host);
              this.logForLinkConnect(`${conn.host}:${conn.port}`);
              // 连接就绪
              if (conn.socket.writable) {
                conn.data[1] = 0x00;
                conn.socket.write(conn.data);
                conn.socket.on('data', (d) => {
                  loggerVerbose(
                    `[TUNNEL] DynamicForwardLinkData(${d.length}) Request, pipelineId: ${connId}`,
                  );
                  this._sendWebsocketCmd(socksBuf2WsPacket(DynamicForwardLinkData, connId, d));
                });
              }
            } else if (cmd === DynamicForwardLinkData) {
              // 连接数据
              const connId = wsData
                .slice(CMD_Bytes + LEN_Bytes, CMD_Bytes + LEN_Bytes + CONNECTION_ID_Bytes)
                .toString();
              const socksPacket = wsBuf2SocksPacket(wsData);
              loggerVerbose(
                `[TUNNEL] DynamicForwardLinkData(${socksPacket.length}) Response, pipelineId: ${connId}`,
              );
              const conn = this._getConnByConnId(connId);
              if (!conn) return;
              conn.socket.writable && conn.socket.write(socksPacket);
            } else if (cmd === DynamicForwardLinkClosed) {
              const connId = JSON.parse(wsData.slice(CMD_Bytes + LEN_Bytes).toString()).pipelineId;
              const conn = this._getConnByConnId(connId);
              if (!conn) return;
              // logger.info('@@@ close(transit)', conn.host);
              conn.socket.end();
            } else if (cmd === ClientAuthFail) {
              this.close();
            }
          }
        });
        this.websocket.on('close', (code) => {
          logger.info(
            `[TUNNEL] [transit:${this.currentTransitId}] websocket closed（code: ${code}, channelSessionId: ${this.channelSessionId}）`,
          );
          if (code === 1000 || code === 1006) {
            // 正常关闭，尝试重连
            this.forceReconnect = true;
          }
          clearTimeout(initTimer);
          reject(new Error('Websocket closed'));
        });
        this.websocket.on('error', (err) => {
          logger.error(`[TUNNEL] [transit:${this.currentTransitId}] websocket error`, err);
          reject(err);
        });
      } catch (e) {
        this.forceReconnect = true;
        clearTimeout(initTimer);
        reject(e);
      } finally {
        this.isWebsocketConnecting = false;
      }
    });
  }

  _sendWebsocketCmd(buffer: Buffer, validateReadyStatus = true, msg?: string) {
    if (this.websocket?.readyState !== 1) return;
    if (this.isTunnelReady || !validateReadyStatus) {
      try {
        this.websocket?.send(buffer, (err) => {
          if (err) {
            logger.error('[TUNNEL] websocket send Error: ', err.name, err.message);
          }
        });
      } catch (e) {
        logger.error('[TUNNEL] websocket send Error: ', e);
      }
    }
  }

  _closeWebsocket() {
    logger.info(`[TUNNEL] _closeWebsocket（channelSessionId: ${this.channelSessionId}）`);
    this.isTunnelReady = false;
    clearTimeout(this.websocketInitTimer);
    this._sendWebsocketCmd(socksBuf2WsPacket(CloseDynamicForwardPipe), true);
    this.websocket?.removeAllListeners();
    this.websocket?.close(4321);
  }

  _connectWithWs(host: string, port: number, data: Buffer, sock: Socket) {
    const uuid = guid();
    // websocket 连接未就绪
    if (!this.isTunnelReady) {
      // 网络不可达
      data[1] = 0x03;
      sock.writable && sock.write(data);
      sock.destroy();
      return;
    }
    // 缓存
    this.connContainer[uuid] = {
      data,
      socket: sock,
      host,
      port,
    };
    const pipelineTargetVo = JSON.stringify({
      pipelineId: uuid,
      targetAddress: {
        host,
        port,
      },
    });
    loggerVerbose(`[TUNNEL] CreateDynamicForwardLink Request, pipelineId: ${uuid}`);
    this._sendWebsocketCmd(
      socksBuf2WsPacket(CreateDynamicForwardLink, '', Buffer.from(pipelineTargetVo, 'utf8')),
    );
    sock.on('close', () => {
      loggerVerbose(`[TUNNEL] DynamicForwardLinkClosed Request, pipelineId: ${uuid}`);
      const pipelineClosedVo = JSON.stringify({
        pipelineId: uuid,
      });
      // logger.info('@@@ close(browser)', host);
      this._sendWebsocketCmd(
        socksBuf2WsPacket(DynamicForwardLinkClosed, '', Buffer.from(pipelineClosedVo, 'utf8')),
      );
      delete this.connContainer[uuid];
    });
  }

  /**
   * 获取浏览器 socks5 代理连接
   */
  getSocks5Url() {
    return `127.0.0.1:${this.socks5Port}`;
  }

  async close() {
    this.closed = true;
    logger.info(`[TUNNEL] closing (sessionId: ${this.channelSessionId})`);
    logger.info(`[TUNNEL] destroy HuaYoungTunnel instance id => ${this.id}`);
    // 清除所有定时器
    if (this.tunnelHeartbeatTimer) {
      clearInterval(this.tunnelHeartbeatTimer);
      this.tunnelHeartbeatTimer = null;
    }
    if (this.checkTunnelHeartbeatTimer) {
      clearTimeout(this.checkTunnelHeartbeatTimer);
      this.checkTunnelHeartbeatTimer = null;
    }
    if (this.pingTimer) {
      clearTimeout(this.pingTimer);
      this.pingTimer = null;
    }
    // 注销电源事件监听器
    try {
      if (this.suspendListener) {
        powerMonitor.removeListener('suspend', this.suspendListener);
        this.suspendListener = undefined;
      }
      if (this.resumeListener) {
        powerMonitor.removeListener('resume', this.resumeListener);
        this.resumeListener = undefined;
      }
    } catch (e) {
      logger.warn('[TUNNEL] Failed to remove power monitor listeners:', e);
    }
    // 重置重连尝试计数
    this.reconnectAttempts = 0;
    // 清理 socket 连接
    this._clearSockets();
    // 关闭 websocket 连接
    this._closeWebsocket();
    // 关闭 socks5 服务
    this.server && this.server.close();
    this.domainRecordFlag = {};
  }

  request(url: string, timeout?: number) {
    const agent = new SocksProxyAgent(
      {
        hostname: this.listenHost,
        port: this.socks5Port,
        type: 5,
      },
      {
        timeout: timeout ?? PROBE_TIME_OUT * 1000,
      },
    );
    return super.request(url, agent, timeout);
  }

  authInfo(): {
    password: string;
    port: number;
    proxyType: string;
    host: string;
    username: string;
  } {
    return {
      proxyType: 'socks5',
      host: this.listenHost,
      port: this.socks5Port,
      username: '',
      password: '',
    };
  }
}

function socksBuf2WsPacket(cmd: number, uuid: string = '', skData: Buffer = Buffer.from([])) {
  const cmdBuf = Buffer.allocUnsafe(CMD_Bytes);
  cmdBuf.writeUInt32BE(cmd);
  const lenBuf = Buffer.allocUnsafe(LEN_Bytes);
  lenBuf.writeUInt32BE(skData.length + uuid.length);
  if (uuid) {
    const connIdBuf = Buffer.from(uuid.substr(0, CONNECTION_ID_Bytes), 'utf-8');
    return Buffer.concat([cmdBuf, lenBuf, connIdBuf, skData]);
  }
  return Buffer.concat([cmdBuf, lenBuf, skData]);
}

function wsBuf2SocksPacket(wsData: Data, noConnId = false) {
  if (noConnId) {
    return wsData.slice(CMD_Bytes + LEN_Bytes) as Buffer;
  }
  return wsData.slice(CMD_Bytes + LEN_Bytes + CONNECTION_ID_Bytes) as Buffer;
}

function guid() {
  return crypto.randomBytes(CONNECTION_ID_Bytes / 2).toString('hex');
}
