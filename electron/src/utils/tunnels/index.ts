import { regs } from '@e/utils/utils';
import { RequestAgent } from '@e/services/request';
import axios from 'axios';
import { PROBE_TIME_OUT } from '@e/utils/proxy_util';
import https from 'https';
import http from 'http';

interface ITunnelProps {
  teamId?: number;
  sessionTokenVo?: API.SessionTokenVo;
  sessionChannelTokenVo: API.SessionChannelTokenVo;
  requestAgent?: RequestAgent;
}

/**
 * 一条通道
 */
export class ITunnel {
  domainRecordFlag: Record<string, boolean>;
  private remoteIp?: string;
  baseProps: ITunnelProps;
  needLogForLinkConnect?: boolean;
  listenHost = '127.0.0.1';

  constructor(props: ITunnelProps) {
    this.baseProps = props;
    const { sessionChannelTokenVo, teamId, requestAgent } = this.baseProps;
    if (sessionChannelTokenVo && teamId) {
      if (!sessionChannelTokenVo.official) {
        this.remoteIp = sessionChannelTokenVo.targetIp?.remoteIp;
        requestAgent?.request(`/api/ipdd/ipPurenessCheck`, { teamId }).then((data: any) => {
          this.needLogForLinkConnect = data;
        });
      }
    }
    if (props.sessionTokenVo?.sessionProxyHost) {
      this.listenHost = props.sessionTokenVo.sessionProxyHost;
    }
    this.domainRecordFlag = {};
  }
  /**
   * 初始化通道
   */
  async init(): Promise<any> {
    //do nothing
  }

  updateRemoteIp(newIpv4?: string) {
    const { sessionChannelTokenVo, teamId, requestAgent } = this.baseProps;
    if (teamId && newIpv4 && regs.ipv4.test(newIpv4)) {
      this.remoteIp = newIpv4;
      if (
        !sessionChannelTokenVo?.official &&
        sessionChannelTokenVo.targetIp?.dynamic &&
        sessionChannelTokenVo.targetIp?.ipId
      ) {
        // 动态IP，更新出口IP的值
        requestAgent?.request(`/api/ip/${sessionChannelTokenVo.targetIp?.ipId}/ip?ip=${newIpv4}`, {
          method: 'PUT',
          teamId,
        });
      }
    }
  }

  /**
   * 记录TCP连接
   */
  logForLinkConnect(domain: string) {
    const { sessionChannelTokenVo, teamId, requestAgent } = this.baseProps;
    if (!sessionChannelTokenVo || !this.remoteIp || !domain || !this.needLogForLinkConnect) return;
    const recordKey = `${this.remoteIp}_${domain}`;
    if (this.domainRecordFlag[recordKey]) return;
    this.domainRecordFlag[recordKey] = true;

    if (teamId && this.remoteIp && requestAgent) {
      requestAgent.request(`/api/ipdd/record?ip=${this.remoteIp}&domain=${domain}`, {
        method: 'POST',
        teamId,
      });
    }
  }

  /**
   * 获取该条通道的 pac 规则，如 SOCKS5 127.0.0.1:1080 或 HTTPS 127.0.0.1:3030
   */
  pacRule(): string {
    return '';
  }

  getProxyInfo() {
    const res = { type: '', port: 0 };
    const rule = this.pacRule();
    const [, type, port] = /^(\w+)\s[\w.]+:(\d+)$/.exec(rule) ?? [];
    if (type && port) {
      res.type = type === 'SOCKS5' ? 'socks5' : 'http';
      res.port = Number(port);
    }
    return res;
  }

  getCurrentEndpointVo(): API.TunnelEndpointVo | undefined {
    return undefined;
  }

  request(url: string, agent?: any, timeout?: number): Promise<any> {
    return new Promise((resolve, reject) => {
      axios
        .request({
          url,
          method: 'get',
          httpAgent: agent,
          httpsAgent: agent,
          timeout: timeout ?? PROBE_TIME_OUT * 1000,
          adapter: (config) => {
            return new Promise((resolve, reject) => {
              const url = new URL(config.url!);
              const protocol = url.protocol === 'https:' ? https : http;
              const options = {
                hostname: url.hostname,
                port: url.port || (protocol === https ? 443 : 80),
                path: url.pathname + url.search,
                method: (config.method ?? 'GET').toUpperCase(),
                headers: config.headers,
                agent,
                timeout: timeout ?? PROBE_TIME_OUT * 1000,
              };

              let connectStart = 0,
                connectEnd = 0,
                responseStart = 0,
                downloadEnd = 0;

              const req = protocol.request(options, (res) => {
                res.once('readable', () => {
                  // console.log(`@@@readable`);
                  responseStart = Date.now();
                });

                let dataLength = 0;
                // const chunks: any[] = [];
                res.on('data', (chunk) => {
                  dataLength += chunk.length;
                  // chunks.push(chunk);
                  // console.log(`@@@data => ${chunk.length}`);
                });
                res.on('end', () => {
                  console.log(`[PROBE] request end - ${res.statusCode}`);
                  if (res.statusCode !== 200) {
                    reject(new Error(res.statusMessage || ''));
                  } else {
                    downloadEnd = Date.now();
                    // const buffer = Buffer.concat(chunks);
                    // const data = buffer.toString('utf8');
                    // console.log('@@@data', data);
                    resolve({
                      dataLength,
                      timing: {
                        // 返回计时数据
                        connect: responseStart - connectStart,
                        download: downloadEnd - connectStart,
                      },
                    });
                  }
                });
              });

              req.on('socket', (socket) => {
                socket.once('connect', () => {
                  // console.log(`@@@connect`);
                  connectEnd = Date.now();
                });
                connectStart = Date.now();
              });

              req.on('error', reject);
              req.end(config.data);
            });
          },
        })
        .then((res) => {
          resolve(res);
        })
        .catch((e) => {
          reject(e);
        });
    });
  }

  authInfo() {
    return {
      proxyType: '',
      host: this.listenHost,
      port: 0,
      username: '',
      password: '',
    };
  }

  /**
   * 关闭通道
   */
  async close(): Promise<any> {}
}

export function getHostname(buf: Buffer) {
  let hostName = '';
  if (buf.length === 4) {
    for (let i = 0; i < buf.length; i++) {
      // @ts-ignore
      hostName += parseInt(buf[i], 10);
      if (i !== 3) hostName += '.';
    }
  } else if (buf.length == 16) {
    for (let i = 0; i < 16; i += 2) {
      let part = buf
        .slice(i, i + 2)
        .readUInt16BE(0)
        .toString(16);
      hostName += part;
      if (i != 14) hostName += ':';
    }
  }
  return hostName;
}
