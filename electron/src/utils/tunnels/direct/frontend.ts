import logger from '@e/services/logger';
import { getAFreePort, isPortOpen, isWinPlatform } from '@e/utils/utils';
import path from 'path';
import { ChildProcess, exec, spawn } from 'child_process';
import { waitSeconds } from '@e/rpa/utils';
import axios, { AxiosResponse } from 'axios';
import { SocksProxyAgent } from 'socks-proxy-agent';
import fs from 'fs-extra';
import os from 'os';
import db from '@e/components/db';
import { readSystemProxy } from '@e/utils/proxy_util';

export interface FrontConfig {
  frontProxy: string;
  //真实的代理地址
  remoteAddr?: string;
  remotePort?: number;

  backendProxy?: string;
}

type FrontendTunnelMode = 'PIPE' | 'SOCKS';

export async function getLocalFrontendProxyUrl() {
  const sysPres = db.getSysPres();
  let proxyUrl = '';
  if (sysPres.localFrontendProxyEnabled) {
    // 开启了本地前置代理
    if (sysPres.localFrontendProxyMode === 'system') {
      const res = await readSystemProxy();
      if (res.success && res.proxy) {
        proxyUrl = `${res.proxy.proxyType}://${res.proxy.host}:${res.proxy.port}`;
      }
    } else if (sysPres.localFrontendProxyUrl) {
      proxyUrl = sysPres.localFrontendProxyUrl;
    }
  }
  return proxyUrl;
}

export function createPipeTunnel(frontProxy: string, remoteAddr: string, remotePort: number) {
  if (!!frontProxy) {
    if (!remoteAddr || !remotePort) {
      throw 'PIPE模式必须指定远程地址和端口';
    }
    return new FrontendSocksTunnel('PIPE', {
      frontProxy,
      remoteAddr,
      remotePort,
    });
  }
  return undefined;
}

export function createSocksTunnel(frontProxy: string, backendProxy?: string) {
  if (!!frontProxy) {
    return new FrontendSocksTunnel('SOCKS', {
      frontProxy,
      backendProxy,
    });
  }
  return undefined;
}

/**
 * 给代理ip加一个前置代理
 */

/**
 * 直接代理ip的前置socks代理
 */
export class FrontendSocksTunnel {
  mode: FrontendTunnelMode;
  listenPort: number = 0;
  config: FrontConfig;

  ndriverProc?: ChildProcess;

  constructor(mode: FrontendTunnelMode, config: FrontConfig) {
    this.mode = mode;
    this.config = config;
  }

  async open(
    listenPort?: number,
  ): Promise<{ mode: FrontendTunnelMode; host: string; port: number }> {
    if (!this.ndriverProc) {
      this.listenPort = listenPort || (await getAFreePort());
      await new Promise(async (resolve, reject) => {
        let scheme = 'tcp';
        if (this.mode === 'SOCKS') {
          scheme = 'socks5';
        }
        let listen = `${scheme}://127.0.0.1:${this.listenPort}`;
        let command = `-listen ${listen} -forward ${this.config.frontProxy}`;
        switch (this.mode) {
          case 'PIPE':
            command += ` -remote tcp://${this.config.remoteAddr}:${this.config.remotePort}`;
            break;
          case 'SOCKS':
            if (this.config.backendProxy) {
              command += ` -remote ${this.config.backendProxy}`;
            }
            break;
        }
        if (process.env.NODE_ENV === 'development') {
          //打印详细日志
          command += ' -verbose';
        }
        const ndriver = 'ndriver';
        const execFile = path.join(
          __dirname,
          '../extra',
          isWinPlatform() ? ndriver + '.exe' : ndriver,
        );
        if (!fs.existsSync(execFile)) {
          reject(new Error('NDriver 文件不存在，请检查是否被误删'));
        }
        this.ndriverProc = spawn(execFile);
        function onNDriverPrint(buf: any) {
          let message = String(buf);
          logger.info(`[NDriver]`, message);
        }
        this.ndriverProc.stdout?.on('data', onNDriverPrint);
        this.ndriverProc.stderr?.on('data', onNDriverPrint);
        this.ndriverProc.stdout?.on('error', (e: Error) => {
          logger.error(`[NDriver] stdout error`, e);
        });
        this.ndriverProc.stdin?.on('error', (e: Error) => {
          logger.error(`[NDriver] stdin error`, e);
        });
        this.ndriverProc.on('error', (e: Error) => {
          logger.error(`[NDriver] process error`, e);
          reject(e);
        });
        this.ndriverProc.once('spawn', () => {
          this.ndriverProc?.stdin?.write(`${command} \n`);
        });
        for (let i = 0; i < 10; i++) {
          await waitSeconds(1, false);
          if (await isPortOpen('127.0.0.1', this.listenPort)) {
            resolve(true);
            break;
          }
        }
        reject(new Error(`NDriver 监听端口（${this.listenPort}）超时`));
      });
    }
    return {
      mode: this.mode,
      host: '127.0.0.1',
      port: this.listenPort,
    };
  }

  /**
   * 当为SOCKS模式时，支持请求多个链接来测试前置代理的速度
   * @param urls
   * @param timeout
   */
  async ping(
    urls = ['https://www.google.com/', 'https://ipinfo.io/ip'],
    timeout = 10 * 1000,
  ): Promise<number> {
    if (this.mode === 'PIPE') {
      throw '只有SOCKS模式才支持ping';
    }
    await this.open();
    let startTime = Date.now();
    let endTime = await new Promise<number>(async (resolve, reject) => {
      const agent = new SocksProxyAgent(`socks5h://127.0.0.1:${this.listenPort}`);
      const promiseArr = urls.map((url) => {
        return new Promise((rsv, rjc) => {
          axios
            .request({
              url,
              httpAgent: agent,
              httpsAgent: agent,
              timeout,
            })
            .then((res) => {
              rsv(res.data!);
            })
            .catch((e) => {
              rjc(e);
            });
        });
      });
      Promise.any(promiseArr)
        .then(() => {
          resolve(Date.now());
        })
        .catch(({ errors }) => {
          reject(errors[0]);
        });
    });
    return endTime - startTime;
  }
  async request(url: string, timeout = 10 * 1000): Promise<AxiosResponse> {
    await this.open();
    return new Promise<AxiosResponse>((resolve, reject) => {
      const agent = new SocksProxyAgent(`socks5h://127.0.0.1:${this.listenPort}`);
      axios
        .request({
          url,
          httpAgent: agent,
          httpsAgent: agent,
          timeout,
        })
        .then((res) => {
          resolve(res.data!);
        })
        .catch((e) => {
          reject(e);
        });
    });
  }

  async close(): Promise<any> {
    let pid = this.ndriverProc?.pid;
    this.ndriverProc?.kill();
    this.ndriverProc = undefined;
    if (pid && 'win32' == os.platform()) {
      //只有windows出现过杀不掉的情况 /T 杀指定进程和其子进程 /F 强制
      //https://learn.microsoft.com/zh-tw/windows-server/administration/windows-commands/taskkill
      exec(`taskkill /T /F /PID ${pid}`, (e) => {
        //ignore
      });
    }
    this.listenPort = 0;
  }
}
