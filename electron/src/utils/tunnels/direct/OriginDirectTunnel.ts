import { TunnelProps } from '@e/utils/tunnels/ChannelTunnel';
import { RequestAgent } from '@e/services/request';
import { OriginalSocksTunnel } from '@e/utils/tunnels/direct/socks';
import { OriginalSshTunnel } from '@e/utils/tunnels/direct/ssh';
import { OriginalHttpTunnel } from '@e/utils/tunnels/direct/http';

export { OriginDirectTunnel } from '@e/utils/tunnels/direct/base';
export { OriginalSocksTunnel } from '@e/utils/tunnels/direct/socks';
export { OriginalSshTunnel } from '@e/utils/tunnels/direct/ssh';
export { OriginalHttpTunnel } from '@e/utils/tunnels/direct/http';

export function createOriginDirectTunnel(
  socksConfig: API.ProxyConfig,
  teamId: number,
  sessionTokenVo: API.SessionTokenVo,
  sessionChannelTokenVo: API.SessionChannelTokenVo,
  requestAgent: RequestAgent,
) {
  if (socksConfig) {
    if (socksConfig.proxyType == 'socks5') {
      return new OriginalSocksTunnel(
        sessionTokenVo,
        { ...sessionChannelTokenVo, teamId },
        socksConfig,
        requestAgent,
      );
    } else if (socksConfig.proxyType === 'ssh') {
      return new OriginalSshTunnel(
        sessionTokenVo,
        { ...sessionChannelTokenVo, teamId },
        socksConfig,
        requestAgent,
      );
    } else {
      return new OriginalHttpTunnel(
        sessionTokenVo,
        { ...sessionChannelTokenVo, teamId },
        socksConfig,
        requestAgent,
      );
    }
  } else {
    throw 'origin direct not supported.';
  }
}

export function createLanProxyTunnel(props: TunnelProps) {
  const { sessionTokenVo } = props;
  let proxyConfig = props.sessionChannelTokenVo.proxyConfig!;
  if (proxyConfig) {
    if (proxyConfig.proxyType == 'socks5') {
      return new OriginalSocksTunnel(
        sessionTokenVo,
        { ...props.sessionChannelTokenVo, teamId: props.teamId },
        proxyConfig,
        props.requestAgent,
      );
    } else if (proxyConfig.proxyType === 'ssh') {
      return new OriginalSshTunnel(
        sessionTokenVo,
        { ...props.sessionChannelTokenVo, teamId: props.teamId },
        proxyConfig,
        props.requestAgent,
      );
    } else {
      return new OriginalHttpTunnel(
        sessionTokenVo,
        { ...props.sessionChannelTokenVo, teamId: props.teamId },
        proxyConfig,
        props.requestAgent,
      );
    }
  } else {
    throw 'lan proxy config not found.';
  }
}
