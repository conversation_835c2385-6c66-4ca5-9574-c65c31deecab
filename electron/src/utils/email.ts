import {
  createSocksTunnel,
  FrontendSocksTunnel,
  getLocalFrontendProxyUrl,
} from '@e/utils/tunnels/direct/frontend';
import { getAppRawProxy, parseProxyUrl, readSystemProxy } from '@e/utils/proxy_util';
import logger from '@e/services/logger';
import * as socks from 'socks';

const nodemailer = require('nodemailer');

export async function checkEmailServer(config: {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  proxy_mode: string;
  proxyRules?: string;
}) {
  let proxyUrl: string | undefined = undefined;
  let proxyMode = config.proxy_mode || 'app';
  let sysProxy = undefined;
  let frontendProxy: FrontendSocksTunnel | undefined = undefined;
  switch (proxyMode) {
    case 'none':
      break;
    case 'app':
      sysProxy = await getAppRawProxy();
      break;
    case 'localFrontendProxy':
      try {
        const localFrontProxyUrl = await getLocalFrontendProxyUrl();
        frontendProxy = createSocksTunnel(localFrontProxyUrl)!;
        await frontendProxy.open();
        sysProxy = {
          proxyType: 'socks5',
          host: '127.0.0.1',
          port: frontendProxy.listenPort,
        };
      } catch (e) {
        logger.error(`创建前置代理失败：${e}`);
      }
      break;
    case 'fixed_servers':
      sysProxy = parseProxyUrl(config.proxyRules ?? '');
      break;
    case 'system':
      let ret = await readSystemProxy();
      if (ret && ret.success) {
        sysProxy = ret.proxy;
      }
      break;
  }
  if (sysProxy) {
    proxyUrl = `${sysProxy.proxyType}://${sysProxy.host}:${sysProxy.port}`;
  }
  let transporter = nodemailer.createTransport({
    ...config,
    port: Number(String(config.port).replace(/\D/g, '')),
    proxy: proxyUrl,
  });
  if (sysProxy?.proxyType === 'socks5') {
    transporter.set('proxy_socks_module', socks);
  }
  try {
    await transporter.verify();
    return { success: true };
  } catch (error: any) {
    return { success: false, message: error.message };
  }
}
