import fs from 'fs-extra';
import path from 'path';
import Downloader from 'nodejs-file-downloader';
import _ from 'lodash';
import os from 'os';
import { exec } from 'child_process';

import db from '@e/components/db';

type ProgressItem = {
  downloadedSize: number;
  percentage: number;
  totalSize: number;
};

function getFFmpegExeName() {
  const platform = os.platform();
  switch (platform) {
    case 'win32':
      return 'ffmpeg.exe';
    default:
      return 'ffmpeg';
  }
}

function getFFmpegDownloadUrl() {
  const platform = os.platform();
  const DOWNLOAD_URL = `${db.getDlUrl()}/downloads/ffmpeg`;
  switch (platform) {
    case 'win32':
      return `${DOWNLOAD_URL}/win/ffmpeg.exe`;
    case 'darwin':
      return `${DOWNLOAD_URL}/mac/ffmpeg`;
    default:
      return `${DOWNLOAD_URL}/linux/ffmpeg`;
  }
}

const cache_dir = '.cache';
let ffmpegDownloadState = false;
let downloadFFmpegPromise: any = null;

type Tasks = {
  [sessionId: string]: string[];
};
type Progress = {
  [index: string]: ProgressItem[];
};

/**
 * 改成支持批量下载
 * @todo
 */
export default class RecordsDownloader {
  basePath: string;
  cachePath: string;
  ffmpegExePath: string;
  downloadProgress = {} as Progress;
  ended = false;
  task: {
    taskId: number;
    map: Tasks;
  };
  creatingMap: {
    [sessionId: string]: boolean;
  } = {};
  reportProgressTimer: any;
  reportProgress: () => void;

  constructor(
    basePath: string,
    task: {
      taskId: number;
      map: Tasks;
    },
    onProgress: (progress: number) => void,
  ) {
    this.basePath = basePath;
    this.task = task;
    const { dataDir } = db.getDb().get('sysPres').value();
    this.ffmpegExePath = path.join(dataDir, getFFmpegExeName());
    ffmpegDownloadState = fs.existsSync(this.ffmpegExePath);
    this.cachePath = path.join(this.basePath, cache_dir);
    if (!fs.existsSync(this.basePath)) {
      fs.mkdirSync(this.basePath, { recursive: true });
    }
    if (!fs.existsSync(this.cachePath)) {
      fs.mkdirSync(this.cachePath, { recursive: true });
    }

    Object.keys(this.task.map).forEach((sessionId) => {
      this.downloadProgress[sessionId] = [];
      this.creatingMap[sessionId] = false;
      this.downloadSessionRecord(sessionId);
    });
    const _fn = () => {
      clearTimeout(this.reportProgressTimer);
      const progress = this.calcTotalProgress();
      if (this.ended) {
        return false;
      }
      onProgress(progress);
      this.ended = progress === 100;
      if (this.ended) {
        return false;
      }
      this.reportProgressTimer = setTimeout(() => {
        _fn();
      }, 1000);
    };
    this.reportProgress = _fn;
    this.reportProgress();
  }

  /**
   * 会话的录像是否已经下载了
   * @param sessionId
   */
  private getTargetStat(sessionId: string) {
    const mp4Path = path.join(this.basePath, sessionId + '.mp4');
    const exists = fs.existsSync(mp4Path);
    if (exists) {
      const { size } = fs.statSync(mp4Path);
      return size;
    }
    return undefined;
  }

  private removeCacheDir(sessionId: string) {
    fs.removeSync(path.join(this.cachePath, String(sessionId)));
  }

  /**
   * 下载单个会话录像
   * @param sessionId
   */
  private downloadSessionRecord(sessionId: string) {
    const downloaded = this.getTargetStat(sessionId);
    if (downloaded) {
      return;
    }
    this.removeCacheDir(sessionId);
    const urls = this.task.map[sessionId];
    // 请求所有slice
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      const download = new Downloader({
        url,
        directory: path.join(this.cachePath, String(sessionId)),
        fileName: `${i}.mp4`,
        maxAttempts: 10,
        onResponse: (res) => {
          this.downloadProgress[sessionId][i] = {
            downloadedSize: 0,
            percentage: 0,
            totalSize: Number(res.headers['content-length'] ?? '0'),
          };
        },
        onProgress: (percentage: string, chunk: object, remaningSize: number) => {
          this.downloadProgress[sessionId][i].percentage = Number(percentage);
          this.downloadProgress[sessionId][i].downloadedSize =
            this.downloadProgress[sessionId][i].totalSize - remaningSize;
          if (parseInt(percentage) === 100) {
            this.checkDownloadProgress(sessionId);
          }
        },
        onError: (e: Error) => {
          console.error('download record slice error', e);
        },
      });
      download.download();
    }
  }

  /**
   * 检查进度，如果所有 slice 都下载完了，就开始合并
   */
  private checkDownloadProgress(sessionId: string) {
    const done = !_.some(this.downloadProgress[sessionId], (item) => item.percentage < 100);
    if (done && !this.creatingMap[sessionId]) {
      this.creatingMap[sessionId] = true;
      // 合并视频
      try {
        setTimeout(() => {
          this.concatFile(sessionId);
        }, 1000);
      } catch (e) {
        this.creatingMap[sessionId] = false;
        // 清理临时目录
        this.removeCacheDir(sessionId);
        throw e;
      }
    }
  }

  private downloadFFmpeg() {
    return new Promise((resolve) => {
      try {
        const { dataDir } = db.getDb().get('sysPres').value();
        const download = new Downloader({
          url: getFFmpegDownloadUrl(),
          directory: dataDir,
          fileName: getFFmpegExeName(),
          maxAttempts: 10,
        });
        download.download().then(() => {
          ffmpegDownloadState = true;
          fs.chmod(this.ffmpegExePath, 0o755, (err) => {
            if (err) throw err;
            resolve(true);
          });
        });
      } catch (e) {
        setTimeout(() => {
          this.downloadFFmpeg();
        }, 5000);
      }
    });
  }

  private concatFile(sessionId: string) {
    if (!ffmpegDownloadState) {
      if (!downloadFFmpegPromise) {
        downloadFFmpegPromise = this.downloadFFmpeg();
      }
      downloadFFmpegPromise.then(() => {
        this.doConcatFile(sessionId);
      });
    } else {
      this.doConcatFile(sessionId);
    }
  }

  private doConcatFile(sessionId: string) {
    const dir = path.join(this.cachePath, String(sessionId));
    const files = fs.readdirSync(dir);
    const filePathList = [];
    for (const filePath of files) {
      filePathList.push(path.join(dir, filePath));
    }
    const destFilePath = path.join(this.basePath, `${sessionId}.webm`);
    const concatTxtPath = path.join(this.cachePath, String(sessionId), 'concat.txt');
    //生成 concat.txt 文件
    let txt = '';
    for (let mp4 of filePathList) {
      txt += `file ${mp4.replace(/\\/g, '\\\\').replace(/\s/g, '\\ ')}\n`;
    }
    fs.writeFileSync(concatTxtPath, txt);
    let command = `"${this.ffmpegExePath}" -safe 0 -y -f concat -i "${concatTxtPath}" -c copy "${destFilePath}"`;
    exec(command, (error, stdout, stderr) => {
      this.creatingMap[sessionId] = false;
      // 清理临时目录
      this.removeCacheDir(sessionId);
      if (error) {
        console.error(error);
      }
      // 合并完成以后，触发一次进度上报
      this.reportProgress();
    });
  }

  /**
   * 获取某个录像的下载进度。
   * 如果录像未下载也未点下载，返回0。如果已经下载过返回100。正在下载的返回真实进度
   * @param sessionId
   */
  private getDownloadProgress(sessionId: string) {
    const downloaded = this.getTargetStat(sessionId);
    if (downloaded) {
      return {
        totalSize: downloaded,
        downloadedSize: downloaded,
      };
    }
    //未下载，从正在下载列表里找
    const totalSize = _.sumBy(this.downloadProgress[sessionId], (item) => item.totalSize);
    const downloadedSize = this.creatingMap[sessionId]
      ? 0.95 * totalSize
      : _.sumBy(this.downloadProgress[sessionId], (item) => item.downloadedSize);
    return {
      totalSize,
      downloadedSize,
    };
  }
  private calcTotalProgress() {
    const totalSum = _.sumBy(Object.keys(this.task.map), (sessionId) => {
      return this.getDownloadProgress(sessionId).totalSize;
    });
    const downloadedSum = _.sumBy(Object.keys(this.task.map), (sessionId) => {
      return this.getDownloadProgress(sessionId).downloadedSize;
    });
    return Math.min(100, (downloadedSum * 100) / (totalSum || 100));
  }
}
