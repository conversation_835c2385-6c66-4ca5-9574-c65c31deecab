import {
  <PERSON><PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>rowserWindowConstructorOptions,
  dialog,
  Event,
  screen,
  shell,
} from 'electron';
import _ from 'lodash';
import fs from 'fs-extra';
import path from 'path';
import { IPC } from '../types';
import db, { getHyDb } from '../components/db';
import LoginDatabase from '../components/loginDatabase';
import Extensions from '../components/extensions';
import { rawRequest, RequestAgent } from '../services/request';
import appConfig, { getDynamicPath } from '../configs/app';
import { Chromium } from './chromium';
import { TunnelRouter } from './tunnelRouter';
import WebServer from './webServer';
import FingerprintConfig, { FingerprintDetail, IP } from './fingerprint';
import * as ipc from './ipc';
import { dispatchMsg } from './ipc';
import logger from '@e/services/logger';
import { MessageDispatcher } from '@e/components/messageDispatcher';
import HistorySync from '@e/components/history';
import BookmarksSync from '@e/components/bookmarks';
import CookiesSync from '@e/components/cookies';
import Preferences from '@e/components/preferences';
import {
  pAny,
  probeByTransit,
  ProbeResult,
  processProxyData,
  proxyProbe,
  ProxyProps,
  readSystemProxy,
} from '@e/utils/proxy_util';
import {
  checkAppCompatibility,
  cleanLocalShopData,
  DEFAULT_WIN_OPTS,
  isWinPlatform,
  loadURL,
  resolveUrl,
  useSystemDefaultTitleBar,
} from './utils';
import { destroyAllPreviewWins } from '@e/utils/ipc/disk';
import {
  backendTaskIns,
  baseBackendTaskIns,
  getAjaxEventClientIns,
  getRecorderController,
} from '@e/components/backendTask';
import LocalStorageSync from '@e/components/localStorage';
import IndexedDBSync from '@e/components/indexedDB';
import { ActionBlockWriteModal } from '@e/typings';
import i18n from '@e/utils/i18n';
import { getTrayMenu } from '@e/components/init';
import { AjaxEventClient } from '@e/utils/AjaxEventClient';
import KernelManage from '@e/components/kernelManage';
import { popupMobileWins } from '@e/utils/ipc/mobile';
import { popupTaskWins } from '@e/utils/popupTaskWindow';
import ShopSession from '@e/utils/shop-session';

type OpenShopProgressVo = {
  status: string;
  // FAIL 或 SUCCESS 时用到
  msg?: string;
  // PENDING 时用到，用来表示进度取值范围 0 ~ 100
  percent?: number;
  errCode?: string;
};

type IpProxyConfig = Omit<ProxyProps, 'requestAgent'>;

const { MIN_WIDTH, MIN_HEIGHT, DEFAULT_WIDTH, DEFAULT_HEIGHT } = appConfig;
let loginWin: BrowserWindow | null = null;
let mainWin: BrowserWindow | null = null;
let lastMainWinUrl: string | undefined;
let lastOpenUrlTime = 0;
let systemPrefWin: BrowserWindow | null = null;
let aboutRuntimeWin: BrowserWindow | null = null;
let sessionIds: {
  [teamId: number | string]: number[];
} = {};
let hyBrowsers: Record<number, HYBrowser | null> = {};
let shopBrowsers: Record<number, Chromium | null> = {};
let rpaBrowsers: Record<number, Chromium | null> = {};
let taskItemBrowsers: Record<number, Chromium | null> = {};
let sessionBrowsers: Record<number, Chromium | null> = {};
export const shopBrowsersLock: Record<number | string, boolean> = {};

/**
 * 覆盖 Request Headers
 * @param session
 */
export function reWriteRequestHeaders(
  session: Electron.Session,
  getOtherHeaders?: () => Record<string, string>,
) {
  session.webRequest.onBeforeSendHeaders((details, callback) => {
    // 给 User-Agent 添加构建版本号
    let uaKey = 'user-agent';
    if (details.requestHeaders['User-Agent']) {
      uaKey = 'User-Agent';
    }
    details.requestHeaders[uaKey] = details.requestHeaders[uaKey]?.replace(
      /(Electron\/[\d.]*)/,
      `$1 BuildNo/${process.env.BUILD_NUMBER ?? '0'}`,
    );
    // if (details.requestHeaders.Cookie) {
    //   appConfig.DEBUG && console.log('[API] Header Cookie', details.requestHeaders.Cookie);
    // }
    let otherHeaders = {};
    if (getOtherHeaders) {
      otherHeaders = getOtherHeaders();
    }
    callback({ requestHeaders: { ...details.requestHeaders, ...otherHeaders } });
  });
}

function detectionUrlNavigate(mainWindow: BrowserWindow) {
  // 监听特殊路由
  const doFilter = (evt: Event, url: string) => {
    const urlTemp = url.replace(/^(https?:\/\/)?[^/]*/, '');
    const urls = ['/login', '/register', '/selectTeam'];
    if (urls.findIndex((str) => urlTemp && urlTemp.indexOf(str) === 0) !== -1) {
      evt.preventDefault();
      openLoginWindow(urlTemp, true);
      closeMainWindow();
    }
  };
  mainWindow.webContents.on('will-navigate', (evt, url) => {
    logger.verbose('[APP] will-navigate', url);
    doFilter(evt, url);
  });
  mainWindow.webContents.on('did-navigate-in-page', (evt, url) => {
    logger.verbose('[APP] did-navigate-in-page', url);
    doFilter(evt, url);
  });
}

// 确保窗口在屏幕内显示
export function makeWindowBoundsInScreen(
  bounds: { x?: number; y?: number; width?: number; height?: number } = {},
) {
  if (_.some([bounds.x, bounds.y, bounds.width, bounds.height], (v) => !_.isNumber(v))) {
    return {
      width: bounds.width,
      height: bounds.height,
    };
  }
  if (
    _.some(screen.getAllDisplays(), (display) => {
      const { workArea } = display;
      // 窗口在屏幕内
      if (
        bounds.x! >= workArea.x &&
        bounds.y! >= workArea.y &&
        bounds.x! + bounds.width! <= workArea.x + workArea.width &&
        bounds.y! + bounds.height! <= workArea.y + workArea.height
      ) {
        return true;
      }
      return false;
    })
  ) {
    return bounds;
  }
  return {
    width: bounds.width,
    height: bounds.height,
  };
}

export function setWindowOpenHandler(window: BrowserWindow) {
  window.webContents.setWindowOpenHandler(({ url, frameName, features }) => {
    const [x, y] = getMainWindow()?.getPosition() ?? [0, 0];
    const portalUrl = db.getPortalUrl();
    const screenSize = screen.getPrimaryDisplay().size;
    let origin = '';
    try {
      origin = new URL(url).origin;
    } catch (e) {}
    // 不在同一个域下，用外部浏览器打开
    if (origin && !portalUrl.includes(origin)) {
      shell.openExternal(url);
      return { action: 'deny' };
    }
    // 通过frameName区分窗口类型,记录窗口尺寸及位置
    // RPA editor window
    if (/\/rpa\/flow/.test(url)) {
      const {
        width = 900,
        height,
        x = 0,
        y = 0,
      } = makeWindowBoundsInScreen(db.getDb().get('rpaEditorBounds').value() ?? {});
      return {
        action: 'allow',
        overrideBrowserWindowOptions: {
          ...DEFAULT_WIN_OPTS,
          width: Math.min(screenSize.width, width),
          height: height ?? screenSize.height - 60,
          minWidth: 800,
          minHeight: 600,
          x,
          y,
        },
      };
    }
    if (/\/ipMarket/.test(url)) {
      return {
        action: 'allow',
        overrideBrowserWindowOptions: {
          ...DEFAULT_WIN_OPTS,
          width: Math.min(screenSize.width, 1440),
          height: Math.min(screenSize.height, 1150),
          minWidth: 800,
          minHeight: 600,
        },
      };
    }
    if (/\/rpaSelectorPicker/.test(url)) {
      const parentBounds = window.getBounds();
      const w = Math.min(screenSize.width, 600);
      const h = Math.min(screenSize.height, 480 + 40);
      return {
        action: 'allow',
        overrideBrowserWindowOptions: {
          ...DEFAULT_WIN_OPTS,
          width: w,
          height: h,
          resizable: false,
          x: Math.round(parentBounds.x + (parentBounds.width - w) / 2),
          y: Math.round(parentBounds.y + (parentBounds.height - h) / 2),
        },
      };
    }
    if (frameName) {
      const featuresMap: Record<string, string> = {};
      try {
        features.split(',').forEach((item) => {
          const [key, value] = item.split('=');
          featuresMap[key] = value;
        });
      } catch (e) {
        console.log(e);
      }

      let { width, height, x, y } = db.getDb().get(frameName.split('@')[0]).value() ?? {};

      const feature_width = parseInt(featuresMap.width);
      const feature_height = parseInt(featuresMap.height);
      if (!width) {
        if (feature_width && _.isNumber(feature_width)) {
          width = feature_width;
        } else {
          width = DEFAULT_WIDTH;
        }
      }
      if (!height) {
        if (feature_height && _.isNumber(feature_height)) {
          height = feature_height;
        } else {
          height = DEFAULT_HEIGHT;
        }
      }

      const overrideBrowserWindowOptions = {
        ...DEFAULT_WIN_OPTS,
        width: Math.min(screenSize.width, width),
        height: height ?? screenSize.height - 60,
        minWidth: 800,
        minHeight: 600,
        x,
        y,
      };
      return {
        action: 'allow',
        overrideBrowserWindowOptions,
      };
    }
    return {
      action: 'allow',
      overrideBrowserWindowOptions: {
        ...DEFAULT_WIN_OPTS,
        width: Math.min(screenSize.width, DEFAULT_WIDTH),
        height: Math.min(screenSize.height - 60, DEFAULT_HEIGHT),
        minWidth: MIN_WIDTH,
        minHeight: MIN_HEIGHT,
        x: x + 22,
        y: y + 22,
      },
    };
  });
}

const confirmWindowRecord: Record<string, BrowserWindow | null> = {};
export function showConfirmWindow(props: {
  key: string;
  width?: number;
  height?: number;
  title?: string;
  description?: string;
  data?: Record<string, any>;
  extraBtn1?: string;
  extraBtn1Type?: string;
  extraBtn2?: string;
  extraBtn2Type?: string;
  okBtn?: string;
  okBtnType?: string;
  cancelBtn?: string;
  cancelBtnType?: string;
  onBtnClick?: (key: string) => void;
}) {
  const { key, width = 500, height = 200, onBtnClick, data, ...restProps } = props;
  if (confirmWindowRecord[key]) return confirmWindowRecord[key];
  let btnClicked = false;
  let confirmWin: BrowserWindow | null = new BrowserWindow({
    ...DEFAULT_WIN_OPTS,
    width,
    height,
    center: true,
    titleBarStyle: 'default',
    titleBarOverlay: false,
    resizable: false,
    maximizable: false,
    minimizable: false,
    fullscreenable: false,
    webPreferences: {
      ...DEFAULT_WIN_OPTS.webPreferences,
      additionalArguments: [],
    },
  });
  confirmWindowRecord[key] = confirmWin;
  confirmWin.webContents.ipc.handle('get-confirm-window-data', () => {
    return props.data;
  });
  if (data) {
    loadURL(confirmWin, '/modal');
  } else {
    confirmWin.webContents.loadFile(path.resolve(__dirname, 'html', 'confirm.html'), {
      query: {
        ...restProps,
      },
    });
  }
  confirmWin.on('closed', () => {
    if (key) {
      confirmWindowRecord[key] = null;
    }
    if (!btnClicked) {
      onBtnClick?.('cancelBtn');
    }
    confirmWindowRecord[key] = null;
  });
  confirmWin.webContents.ipc.on('btn-click', (evt, payload) => {
    btnClicked = true;
    confirmWin?.destroy();
    onBtnClick?.(payload.key);
  });
  return confirmWin;
}

async function createMainWindow(url = '') {
  if (mainWin) {
    try {
      await loadURL(mainWin, url);
      bringWindowFront(mainWin);
    } catch (e) {
      logger.error('[APP] load url failed', e);
    }
    return;
  }

  const portalUrl = db.getPortalUrl();
  const preferences = db.getPreferences();
  const screenSize = screen.getPrimaryDisplay().size;
  const options: BrowserWindowConstructorOptions = {
    ...DEFAULT_WIN_OPTS,
    width: Math.min(screenSize.width, DEFAULT_WIDTH),
    height: Math.min(screenSize.height - 60, DEFAULT_HEIGHT),
    minWidth: MIN_WIDTH,
    minHeight: MIN_HEIGHT,
    ...preferences,
  };
  const menu = getTrayMenu();
  if (menu.getMenuItemById('systemPref')) {
    menu.getMenuItemById('systemPref')!.visible = true;
  }
  if (useSystemDefaultTitleBar) {
    if (menu?.items.find((item) => item.id === 'pin')?.checked) {
      options.alwaysOnTop = true;
    }
  }
  mainWin = new BrowserWindow(options);
  // @ts-ignore
  mainWin._winType = 'main';
  backendTaskIns.start();
  detectionUrlNavigate(mainWin);

  mainWin.on('resized', () => {
    // 窗口移动最大化最小化不算resize,
    // 因为设置了useContentSize，所以这里也取 contentBounds的rect
    const rect = mainWin?.getContentBounds();
    if (rect && rect.width && rect.height) {
      db.getDb()
        .set('preferences', {
          width: rect.width,
          height: rect.height,
        })
        .write();
    }
  });
  mainWin.on('close', (e) => {
    const sysPres = db.getSysPres();
    if (!sysPres.exitOnClose) {
      e.preventDefault();
      mainWin?.hide();
      return false;
    }
    return true;
  });
  mainWin.on('closed', async () => {
    logger.info('[APP] 关闭主窗口');
    destroyAllPreviewWins();
    const menu = getTrayMenu();
    if (menu.getMenuItemById('systemPref')) {
      menu.getMenuItemById('systemPref')!.visible = false;
    }
    Object.values(runTaskModalWinRecord).forEach((win) => win?.destroy());
    Object.values(confirmWindowRecord).forEach((win) => win?.destroy());
    popupMobileWins.forEach((win) => win.destroy());
    popupTaskWins.forEach((win) => win.destroy());
    windowSyncToolboxWin?.destroy();
    mainWin = null;
    const chromiumMap = getChromiums();
    _.forEach(chromiumMap, (chromium) => {
      if (chromium) {
        chromium.close();
      }
    });
    await waitAllChromiumClosed();
    if (!getMainWindow()) {
      backendTaskIns.stop();
    }
  });
  mainWin.webContents.on('did-create-window', (window, details) => {
    const { url, frameName } = details;
    if (/\/rpa\/flow/.test(url)) {
      const regRes = /\/rpa\/flow\/(\d+)/.exec(url);
      if (!regRes) return;
      const flowId = Number(regRes[1]);
      // @ts-ignore
      window._winType = 'rpaEditor';
      // @ts-ignore
      window._flowId = flowId;
      window.on('close', (e) => {
        e.preventDefault();
        window.show();
        window.webContents.on('ipc-message', (evt, channel, data) => {
          if (channel === 'close-window') {
            window.destroy();
            if (data.closeFlowBrowser) {
              const rpaFlowBrowser = getChromium({ rpaFlowId: flowId });
              if (rpaFlowBrowser && rpaFlowBrowser.rpaPreview) {
                rpaFlowBrowser.close();
              }
            }
          }
        });
        // send message to render process
        window.webContents.send('window-closing-evt');
      });
      window.on('resized', () => {
        const bounds = window.getBounds();
        db.getDb().set('rpaEditorBounds', bounds).write();
      });
      window.on('moved', () => {
        const bounds = window.getBounds();
        db.getDb().set('rpaEditorBounds', bounds).write();
      });
    } else if (frameName) {
      // @ts-ignore
      // 通过frameName区分窗口类型,记录窗口尺寸及位置
      window.on('resized', () => {
        const bounds = window.getBounds();
        db.getDb().set(frameName.split('@')[0], bounds).write();
      });
      window.on('moved', () => {
        const bounds = window.getBounds();
        db.getDb().set(frameName.split('@')[0], bounds).write();
      });
    }
  });
  for (let loadCount = 0; loadCount < 5; loadCount++) {
    try {
      await loadURL(mainWin, url);
      break;
    } catch (e) {
      logger.error('[APP] main window load url failed', e);
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
  }
}

/**
 * 打开首页
 * @param url
 */
export const openMainWindow = (url = '') => {
  if (url === lastMainWinUrl && new Date().getTime() - lastOpenUrlTime < 2000) {
    return;
  }
  logger.info('[APP] 打开主窗口，url：', url);
  createMainWindow(url);
  lastMainWinUrl = url;
  lastOpenUrlTime = new Date().getTime();
};

interface HYBrowserProps {
  params: {
    shopId: number;
    teamId: number;
    uuid?: string;
    actionBlockWriteModal?: ActionBlockWriteModal; // actionBlockWriteModal 为 ActionBlock 的写模式，可取值 None, Shop, Team
    rpaFlowId?: number;
    rpaTaskId?: number;
    rpaTaskItemId?: number;
    openapiTaskId?: number;
    forceRecord?: boolean;
    rpaPreview?: boolean;
    showMouseTrack?: boolean;
    windowSize?: string;
    windowPosition?: string;
    loadVideo?: null | boolean;
    loadImage?: null | boolean;
    imageForbiddenSize?: number;
    headless?: boolean;
    remoteDebugPort?: number;
    url?: string;
    browserSwitches?: string;
    fingerprintId?: number;
    requestAgent?: RequestAgent;
  };
  callback?: (shopId: number, uuid: string, vo: OpenShopProgressVo) => void;
}

type HYBrowserStatus = 'pending' | 'success' | 'fail' | 'sync' | 'closed' | 'downloadingKernel';

class HYBrowser {
  props: HYBrowserProps;
  status: HYBrowserStatus = 'pending';
  chromiumIns?: Chromium;
  private startTimer: any = 0;
  cookiesSync: CookiesSync | null = null;
  loginDatabase: LoginDatabase | null = null;
  historySync: HistorySync | null = null;
  bookmarksSync: BookmarksSync | null = null;
  localStorageSync: LocalStorageSync | null = null;
  indexedDBSync: IndexedDBSync | null = null;
  extensionSync: Extensions | null = null;
  constructor(props: HYBrowserProps) {
    this.props = props;
  }

  reportStatus(detailInfo: {
    status: HYBrowserStatus;
    percent?: number;
    msg?: string;
    errCode?: string;
    kernelVersion?: string;
    unzipping?: boolean;
  }) {
    try {
      this.status = detailInfo.status;
      if (this.props.callback) {
        this.props.callback(this.props.params.shopId, this.props.params.uuid ?? '', detailInfo);
      }
    } catch (e) {
      logger.error('[BROWSER] reportStatus error', e);
    }
  }

  async start(): Promise<Chromium | undefined> {
    const { params, callback = () => {} } = this.props;
    const {
      shopId,
      teamId,
      uuid = '',
      actionBlockWriteModal = 'None',
      rpaFlowId,
      rpaTaskId,
      rpaTaskItemId,
      openapiTaskId,
      forceRecord = false,
      rpaPreview = false,
      showMouseTrack = false,
      windowSize = '',
      windowPosition = '',
      loadVideo = null,
      loadImage = null,
      imageForbiddenSize = 50,
      headless = false,
      remoteDebugPort,
      url,
      browserSwitches = '',
      fingerprintId,
      requestAgent = new RequestAgent(),
    } = params;
    const setProgress = (percent: number) => {
      this.reportStatus({
        status: 'pending',
        percent: Math.min(100, Math.round(percent + 5 * Math.random())),
      });
    };
    setProgress(15);
    const shopSession = new ShopSession(params);
    const shopBrowsersLockKey = db.isRpaExecutor() ? `${shopId}-${rpaTaskId}` : shopId;
    // 当前客户端是否已经打开了该分身的会话
    if (
      !db.isRpaExecutor() &&
      (shopBrowsersLock[shopBrowsersLockKey] || getChromium({ shopId })?.getBrowser())
    ) {
      logger.info(
        `[BROWSER] browser has opened（shopId:${shopBrowsersLockKey}, taskId:${
          rpaTaskId || ''
        }）, shopBrowsersLock: ${shopBrowsersLock[shopBrowsersLockKey]}, instance: ${!!getChromium({
          shopId,
        })}`,
      );
      const message = i18n.t('当前客户端已经打开了该分身的浏览器');
      this.reportStatus({ status: 'fail', msg: message });
      shopSession.reportOpenApiTaskException(message);
      const error = new Error(message);
      // @ts-ignore
      error.code = 1001;
      throw error;
    }
    shopBrowsersLock[shopBrowsersLockKey] = true;
    let sessionCloseListener: string | undefined;
    let ajaxEventClient = getAjaxEventClientIns();
    let tmpAjaxEventClient: AjaxEventClient | undefined;
    // 通过快捷方式打开的分身，需要使用快捷方式的token来监听 ajax event
    if (requestAgent.getProps().token) {
      ajaxEventClient = new AjaxEventClient({
        extraHeaders: {
          'x-ssc-token': requestAgent.getProps().token,
        },
      });
      tmpAjaxEventClient = ajaxEventClient;
    }
    if (!getRecorderController()) {
      baseBackendTaskIns.startRecorderController();
    }
    try {
      // 检查客户端与系统版本的兼容性
      checkAppCompatibility();
      await shopSession.init();
      let shopInfo = shopSession.shopInfo!;
      // 安全策略设置优先、任务设置次之
      if (
        rpaFlowId &&
        shopInfo.securityPolicyEnabled &&
        shopInfo.recordPolicy === 'Chosen' &&
        forceRecord
      ) {
        shopInfo.recordPolicy = 'Forced';
      }
      // 资源加载策略
      if (!shopInfo.resourcePolicyVo) {
        shopInfo.resourcePolicyVo = {};
      }
      if (loadVideo !== null) {
        shopInfo.resourcePolicyVo.video = loadVideo;
      }
      if (loadImage !== null) {
        shopInfo.resourcePolicyVo.image = loadImage;
        shopInfo.imageForbiddenSize = (imageForbiddenSize || 50) * 1024;
      }
      let accountLoginUrl =
        shopInfo.platform?.name === 'Other' ? '' : shopInfo.platform?.loginUrl || '';
      let paymentPlatformInfo: API.ShopPlatformVo = {};
      let mailPlatformInfo: API.ShopPlatformVo = {};
      setProgress(30);
      // 获取代理IP信息
      let ipDetail: API.TeamIpVo | undefined;
      const primaryChannel = shopInfo.channels?.find((channel) => channel.primary);
      if (primaryChannel) {
        if (primaryChannel.ipId && primaryChannel.ip) {
          ipDetail = primaryChannel.ip;
        }
        // ipDetail = shopInfo.channels[0].ip;
        // ipDetail = await requestAgent.request(`/api/ip/${shopInfo.channels[0].ipId}`, { teamId });
        // if (!ipDetail) {
        //   throw new Error('无法获取分身绑定的代理IP信息');
        // }
      }
      const tempShopDataSubDir = db.isRpaExecutor() ? String(Date.now()) : '';
      const syncPropsBase = {
        teamId,
        shopId,
        shopInfo,
        shopPolicy: shopInfo.syncPolicyVo!,
        requestAgent,
        tempShopDataSubDir,
        kernelNumber: 0,
      };
      if (db.isRpaExecutor()) {
        syncPropsBase.shopPolicy.passwords = false;
        syncPropsBase.shopPolicy.history = false;
        syncPropsBase.shopPolicy.shopBookmarks = false;
        syncPropsBase.shopPolicy.userBookmarks = false;
      }
      // 每个账号设置单独 userData 目录
      const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
      const userDataDir = path.resolve(
        BROWSER_USER_DATA_DIR_PATH,
        `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${shopInfo.id}`,
        tempShopDataSubDir,
      );
      if (!fs.existsSync(userDataDir)) {
        fs.mkdirSync(userDataDir, { recursive: true });
      }
      // 获取客户端IP相关信息（时区、地理位置）
      const clientIpLocation = await requestAgent.request(`/api/meta/ip/myLocation`);
      if (!clientIpLocation.timezone) {
        // @ts-ignore
        clientIpLocation.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      }
      setProgress(40);
      await shopSession.openSession();
      await shopSession.initTunnelRouter();
      const { tunnelRouter } = shopSession;
      const sessionId = shopSession.sessionId!;
      // 监听会话关闭事件
      sessionCloseListener = await ajaxEventClient?.once(
        'shop-session-closed',
        shopSession.sessionId,
        (data) => {
          const { fromOpenapi = false } = data;
          logger.info(
            `[BROWSER] received "shop-session-closed" event(by OpenAPI: ${fromOpenapi}), start closing browser (session:${sessionId})`,
          );
          tunnelRouter?.close();
          if (!this.chromiumIns) {
            shopBrowsersLock[shopBrowsersLockKey] = false;
            return;
          }
          const releaseLockTimer = setTimeout(() => {
            shopBrowsersLock[shopBrowsersLockKey] = false;
            clearTimeout(releaseLockTimer);
          }, 5 * 1000);
          this.chromiumIns.addAfterCloseListener(() => {
            shopBrowsersLock[shopBrowsersLockKey] = false;
            clearTimeout(releaseLockTimer);
          });
          this.chromiumIns.close()?.then(() => {
            if (fromOpenapi || db.isRpaExecutor()) return;
            dialog.showMessageBox({
              type: 'error',
              title: i18n.t('会话中断'),
              message: data.reason || i18n.t('请联络您的上级或者团队管理员以获取进一步信息'),
            });
          });
        },
      );
      if (!sessionIds[teamId]) {
        sessionIds[teamId] = [];
      }
      sessionIds[teamId].push(sessionId!);
      setProgress(50);
      if (fingerprintId) {
        shopInfo.fingerprintId = fingerprintId;
      } else {
        if (shopInfo.stateless) {
          // 无状态分身打开会话时会绑定指纹实例，需要重新获取分身信息
          shopInfo = await shopSession.getShopInfo();
        }
      }
      // 拉取浏览器指纹信息
      let fingerprint: FingerprintDetail | null = null;
      if (shopInfo.fingerprintId) {
        fingerprint = await requestAgent.request(`/api/finger/detail/${shopInfo.fingerprintId}`, {
          teamId,
        });
        if (!fingerprint) {
          await shopSession.closeSession();
          const error = new Error(i18n.t('无法获取分身绑定的浏览器指纹'));
          // @ts-ignore
          error.code = 1002;
          throw error;
        }
      } else {
        await shopSession.closeSession();
        const error = new Error(i18n.t('当前分身未绑定浏览器指纹'));
        // @ts-ignore
        error.code = 1003;
        throw error;
      }
      let loadingWin: BrowserWindow | undefined;
      const kernelNumber = await KernelManage.getInstance().prepareKernelForUA(
        FingerprintConfig.privacyUserAgent(fingerprint.userAgent),
        (info) => {
          logger.info(`[BROWSER] download info: ${JSON.stringify(info)}`);
          this.reportStatus({
            status: 'downloadingKernel',
            percent: parseFloat(info.percentage),
            unzipping: info.unzipping,
          });
          if (db.isRuntimeMode() && !loadingWin) {
            loadingWin = new BrowserWindow({
              width: 500,
              height: 200,
              center: true,
            });
            loadingWin.webContents.loadFile(path.resolve(__dirname, 'html', 'loading.html'), {
              query: {
                title: i18n.t('正在为您准备浏览器运行环境'),
                description: i18n.t('这可能需要一点时间，请稍候...'),
              },
            });
          }
        },
      );
      if (loadingWin) {
        loadingWin.close();
      }
      setProgress(60);
      syncPropsBase.kernelNumber = kernelNumber!;
      this.cookiesSync = await new CookiesSync({ ...syncPropsBase }).init();
      this.loginDatabase = await new LoginDatabase({ ...syncPropsBase }).init();
      this.historySync = await new HistorySync({ ...syncPropsBase }).init();
      this.bookmarksSync = await new BookmarksSync({ ...syncPropsBase }).init();
      this.localStorageSync = await new LocalStorageSync({ ...syncPropsBase }).init();
      this.indexedDBSync = await new IndexedDBSync({ ...syncPropsBase }).init();
      const preferences = await new Preferences({ ...syncPropsBase, rpaFlowId }).init();
      const passwords: API.ShopPasswordsVo[] = this.loginDatabase.getPasswords();
      // 根据 passwords 中的 platform 获取 paymentPlatformInfo，mailPlatformInfo
      const targetAccountVo = _.find(passwords, ({ platform }) =>
        ['Shop', 'SocialMedia', 'Other'].includes(platform?.category!),
      );
      const targetPaymentVo = _.find(passwords, ({ platform }) => platform?.category === 'Payment');
      const targetMailVo = _.find(passwords, ({ platform }) => platform?.category === 'Mail');
      if (targetAccountVo && targetAccountVo.actionUrl) {
        accountLoginUrl = targetAccountVo.actionUrl;
      }
      if (targetPaymentVo) {
        paymentPlatformInfo = targetPaymentVo.platform ?? {};
        if (targetPaymentVo.actionUrl) {
          paymentPlatformInfo.loginUrl = targetPaymentVo.actionUrl;
        }
      }
      if (targetMailVo) {
        mailPlatformInfo = targetMailVo.platform ?? {};
        if (targetMailVo.actionUrl) {
          mailPlatformInfo.loginUrl = targetMailVo.actionUrl;
        }
      }
      setProgress(80);
      this.extensionSync = await new Extensions({ ...syncPropsBase }).init();
      const extensionPaths = this.extensionSync.result;
      setProgress(90);
      let wsDispatcher = new MessageDispatcher({ rpaFlowId });
      const recorderController = getRecorderController()!;
      let fpToOverride: IP | undefined = undefined;
      let ipProxyConfig: API.ProxyConfig | API.ShopLanProxyDto | undefined = undefined;
      let outboundIpToFetchLocation: string | undefined = undefined;
      if (shopInfo.lanProxy?.enabled) {
        // 使用本机直连/系统代理
        fpToOverride = {
          address: clientIpLocation.ip,
          timezone: clientIpLocation.timezone,
          locale: clientIpLocation.locale,
          longitude: clientIpLocation.longitude,
          latitude: clientIpLocation.latitude,
        };
        if (shopInfo.lanProxy.networkType !== 'UseDirect') {
          fpToOverride = {
            address: shopInfo.lanProxy.remoteIp || clientIpLocation.ip,
            timezone: shopInfo.lanProxy.timezone || clientIpLocation.timezone,
            locale: shopInfo.lanProxy.locale || clientIpLocation.locale,
            longitude: shopInfo.lanProxy.longitude || clientIpLocation.longitude,
            latitude: shopInfo.lanProxy.latitude || clientIpLocation.latitude,
          };
          if (shopInfo.lanProxy?.probeOnSession) {
            let proxyProps = shopInfo.lanProxy;
            if (shopInfo.lanProxy?.networkType === 'UseSystem') {
              proxyProps = (await readSystemProxy()).proxy;
            }
            if (proxyProps && proxyProps.host) {
              ipProxyConfig = proxyProps;
            } else {
              logger.info(`[SESSION] 未启用系统代理，无法探测出口IP`);
            }
          }
        }
      } else if (
        shopSession.primaryChannelTokenVo &&
        shopSession.primaryChannelTokenVo?.proxyConfig
      ) {
        // 第三方协议IP，不管是否为静态IP、动态IP，都需要探测出口IP
        ipProxyConfig = shopSession.primaryChannelTokenVo?.proxyConfig;
      }
      if (ipProxyConfig) {
        // @ts-ignore
        const proxyProps = processProxyData({
          ...ipProxyConfig,
          teamId: shopInfo.teamId!,
          networkType: 'UseProxy',
        });
        const proxyProbePromiseArr: Promise<ProbeResult>[] = [];
        // 客户端探测出口IP
        proxyProbePromiseArr.push(
          new Promise((resolve, reject) => {
            tunnelRouter
              ?.getRemoteIp()
              .then((ipRes) => {
                if (ipRes?.success) {
                  resolve({
                    success: true,
                    ip: ipRes.ip,
                  });
                } else {
                  reject({
                    success: false,
                  });
                }
              })
              .catch((e) => {
                reject({
                  success: false,
                });
              });
          }),
        );
        if (shopSession.primaryChannelTokenVo) {
          // 中转探测出口IP
          const transitIds = shopSession.primaryChannelTokenVo.endpoints
            ?.filter((ep) => ['transit', 'jump'].includes(ep.tunnelType!) && !!ep.nodeId)
            .map((ep) => ep.nodeId!);
          if (transitIds && transitIds.length) {
            for (const transitId of transitIds) {
              proxyProbePromiseArr.push(
                probeByTransit(
                  {
                    ...proxyProps,
                    transitId,
                    teamId,
                  },
                  requestAgent,
                ),
              );
            }
          }
        }
        // 出口IP探测结果
        const probeResult = await new Promise((resolve: (res: ProbeResult) => void) =>
          pAny(proxyProbePromiseArr, (res) => resolve(res)),
        );
        const isStaticIp = shopSession.primaryChannelTokenVo?.targetIp?.dynamic === false;
        // DAMAI-3076 静态IP的出口IP如果跟数据库不一致，以检测出来的为准
        if (probeResult.success && probeResult.ip) {
          if (
            !isStaticIp ||
            (!!shopSession.primaryChannelTokenVo?.targetIp?.remoteIp &&
              shopSession.primaryChannelTokenVo?.targetIp?.remoteIp !== probeResult.ip)
          ) {
            outboundIpToFetchLocation = probeResult.ip;
          }
          // 缓存出口IP
          cacheIpAddress(probeResult.ip, shopSession.primaryChannelTokenVo?.targetIp?.ipId);
        } else if (!isStaticIp) {
          // 查询数据库中缓存的IP
          outboundIpToFetchLocation = getIpCacheAddress(
            shopSession.primaryChannelTokenVo?.targetIp?.ipId,
          );
          if (!outboundIpToFetchLocation) {
            await shopSession.closeSession();
            const error = new Error(i18n.t('通过代理IP探测出口IP失败，请检查代理IP配置'));
            // @ts-ignore
            error.code = 1004;
            throw error;
          }
        }
      }
      if (outboundIpToFetchLocation) {
        const ipLocationVo: API.IpLocationVo = await requestAgent.request(
          `/api/meta/ip/myLocation?ip=${outboundIpToFetchLocation}`,
        );
        logger.info(`[SESSION] 获取出口IP（${outboundIpToFetchLocation}）位置信息`, ipLocationVo);
        fpToOverride = {
          address: ipLocationVo.ip,
          timezone: ipLocationVo.timezone,
          locale: ipLocationVo.locale,
          longitude: ipLocationVo.longitude,
          latitude: ipLocationVo.latitude,
        };
        //        归属地保持
        if (primaryChannel && primaryChannel.locationLevel === 'City') {
          // 归属地保持开启城市保持
          if (ipLocationVo.id !== primaryChannel.locationId) {
            /** 查询位置信息 GET /api/meta/ip/location/${param0} */
            const expect_location: API.IpLocationDto = await requestAgent.request(
              `/api/meta/ip/location/${primaryChannel.locationId}/`,
            );
            // 归属地保持城市不对应
            logger.info(
              `[SESSION] 归属地保持城市ID不匹配,存储值:${primaryChannel.locationId},实际值:${ipLocationVo.id}`,
            );
            let location_text = i18n.isCn() ? ipLocationVo.city : ipLocationVo.cityEn;
            if (!location_text) {
              location_text = i18n.isCn() ? ipLocationVo.province : ipLocationVo.provinceEn;
            }
            if (!location_text) {
              location_text = i18n.isCn() ? ipLocationVo.country : ipLocationVo.countryEn;
            }
            const e: any = new Error(
              i18n.t(
                `出口IP当前归属地［${location_text}］与期望值［${
                  i18n.isCn() ? expect_location.city : expect_location.cityEn
                }］不符`,
              ),
            );
            e.errCode = 'LOCATION_ID_NOT_MATCH';
            e.data = {
              current: ipLocationVo.id,
              expect: primaryChannel.locationId,
            };
            await shopSession.closeSession();
            throw e;
          }
        }
      }
      const fingerprintConfig = new FingerprintConfig({
        ...fingerprint,
        ip: fpToOverride
          ? fpToOverride
          : {
              address: ipDetail?.ip,
              timezone: ipDetail?.timezone,
              locale: ipDetail?.locale,
              latitude: ipDetail?.latitude,
              longitude: ipDetail?.longitude,
              forbiddenLongLatitude: ipDetail?.forbiddenLongLatitude || false,
            },
        shopInfo,
        shopName: shopInfo.name!,
        allowExtension: !shopInfo.parentShopId && !!shopInfo.allowExtension,
        sessionId: shopSession.sessionId!,
        isBlockVideo: shopInfo?.resourcePolicyVo?.video === false,
      });
      let browserLanguage = db.getBrowserLanguage();
      try {
        const teamBrowserLanguage = await requestAgent.request('/api/team/settings/language', {
          teamId: shopInfo.teamId,
        });
        if (teamBrowserLanguage) {
          browserLanguage = teamBrowserLanguage;
        }
        if (browserLanguage === 'autoIp') {
          browserLanguage = fingerprintConfig.getLangArr()[0];
        }
      } catch (e: any) {
        logger.error('[SESSION] 获取团队浏览器语言失败', e.message);
      }
      // 开启 webserver
      const webServer = new WebServer({
        sessionId: sessionId!,
        rpaTaskId,
        tunnelRouter: tunnelRouter!,
        recorderController,
        wsDispatcher,
        shopInfo,
        fingerprintDetail: fingerprint,
        fpToOverride,
        fingerprintConfig,
        ipDetail,
        accountLoginUrl,
        paymentPlatformInfo,
        mailPlatformInfo,
        clientIpLocation,
        actionBlockWriteModal,
        extensionCount: extensionPaths.length,
        transitList: shopSession.transitList!,
        requestAgent,
        rpaFlowId,
        rpaPreview,
        showMouseTrack,
        browserLanguage,
      });
      let uploadBrowserDataTimer: any = 0;
      try {
        await webServer.init();
        setProgress(95);
        logger.info(`[TUNNEL] init success (session:${sessionId})`);
        tunnelRouter?.startHeartbeat();
        // 监听流量/花瓣不足/流量超额事件
        const ajaxEventClientListener: string[] = [];
        const chromium = await new Chromium({
          wsDispatcher,
          shopInfo,
          userDataDir,
          fingerprintConfig,
          ipDetail,
          paymentPlatformInfo,
          mailPlatformInfo,
          tunnelRouter: tunnelRouter!,
          proxyPacArg: tunnelRouter!.pacArg,
          cookiesSync: this.cookiesSync,
          sessionId: sessionId!,
          recorderController,
          extensionPaths,
          firstInstallExtensionIds: this.extensionSync.firstInstallExtensionIds,
          rpaFlowId,
          rpaTaskId,
          rpaPreview,
          windowSize,
          windowPosition,
          remoteDebugPort,
          browserSwitches,
          requestAgent,
          tempShopDataSubDir,
          initUrl: url,
          browserLanguage,
          headless,
        }).init();
        if (tunnelRouter?.closed) {
          logger.info(
            `[BROWSER] 检测到会话已经结束，直接关闭浏览器 (session:${sessionId}, shopId:${shopInfo.id})`,
          );
          chromium.close();
        }
        let removeEventListenerFn = ipc.addEventListener(
          IPC.Event.FOCUS_SHOP_BROWSER,
          (evt, data) => {
            if (shopId === data.shopId) {
              chromium.bringToFront();
            }
          },
        );
        // DAMAI-3511 打开一段时间后同步一次数据
        uploadBrowserDataTimer = setTimeout(() => {
          this.uploadBrowserData(true);
        }, 6 * 60 * 1000);
        chromium.onClose(async () => {
          // 防止重复调用
          if (!shopBrowsersLock[shopBrowsersLockKey]) return;
          logger.info(`[BROWSER] closed (session:${sessionId}, shopId:${shopInfo.id})`);
          try {
            _.remove(sessionIds[teamId], (id) => id === sessionId);
            shopBrowsersLock[shopBrowsersLockKey] = false;
            this.chromiumIns = undefined;
            shopBrowsers[shopId] = null;
            sessionBrowsers[sessionId!] = null;
            clearTimeout(uploadBrowserDataTimer);
            dispatchMsg('browser.sync.list.update');
            if (getWindowSyncToolboxWin()) {
              dispatchMsg('browser.sync.list.update', {}, getWindowSyncToolboxWin()!);
            }
            if (rpaFlowId) {
              rpaBrowsers[rpaFlowId] = null;
            }
            if (rpaTaskItemId) {
              taskItemBrowsers[rpaTaskItemId] = null;
            }
            webServer.close();
            tunnelRouter && tunnelRouter.close();
            wsDispatcher.destroy();
            await preferences.restore();
            ajaxEventClientListener.forEach((hd) => {
              ajaxEventClient?.un(hd);
            });
            removeEventListenerFn();
            sessionCloseListener && ajaxEventClient?.un(sessionCloseListener);
            this.reportStatus({ status: 'sync' });
            await this.uploadBrowserData();
            // 无状态账号、云端执行及时清理 userData
            if (db.isRpaExecutor() || shopInfo.stateless) {
              if (!db.isRpaExecutor() && (shopInfo.statelessSyncPolicy ?? 0) > 0) {
                this.cookiesSync?.tryClearLocalFile();
                this.loginDatabase?.tryClearLocalFile();
                this.localStorageSync?.tryClearLocalFile();
                this.indexedDBSync?.tryClearLocalFile();
                this.historySync?.tryClearLocalFile();
                this.extensionSync?.tryClearLocalFile();
              } else {
                try {
                  await fs.rm(chromium.getUserDataDir(), {
                    recursive: true,
                    maxRetries: 6,
                    retryDelay: 10 * 1000,
                  });
                } catch (e) {
                  logger.verbose('[APP] clear userData failed', e);
                }
              }
            }
            await shopSession.closeSession();
            this.reportStatus({ status: 'closed' });
            if (tmpAjaxEventClient) {
              tmpAjaxEventClient.close();
            }
          } catch (e) {
            logger.error('[BROWSER] closed callback error', e);
          } finally {
            runTaskModalWinRecord[shopId]?.close();
          }
          logger.info(`[BROWSER] closed finished (session:${sessionId}, shopId:${shopInfo.id})`);
        });
        this.chromiumIns = chromium;
        shopBrowsers[shopId] = chromium;
        sessionBrowsers[sessionId!] = chromium;
        dispatchMsg('browser.sync.list.update');
        if (getWindowSyncToolboxWin()) {
          dispatchMsg('browser.sync.list.update', {}, getWindowSyncToolboxWin()!);
        }
        if (rpaFlowId) {
          rpaBrowsers[rpaFlowId] = chromium;
        }
        if (rpaTaskItemId) {
          taskItemBrowsers[rpaTaskItemId] = chromium;
        }
        if (!getHyBrowser(shopId)) {
          chromium.close();
        }
        await chromium.afterSpawn();
        if (this.chromiumIns) {
          this.reportStatus({ status: 'success' });
          shopSession.sessionTokenVo?.channelTokens?.forEach((shopChannelTokenVo) => {
            ajaxEventClient
              ?.on(
                'channel-session-traffic-abort',
                shopChannelTokenVo.channelSessionId,
                async () => {
                  const browser = chromium.getBrowser();
                  if (browser) {
                    const p = await browser.newPage();
                    p.goto('http://szdamai.local/creditAlert');
                  }
                },
              )
              .then((hd) => {
                ajaxEventClientListener.push(hd);
              });
          });
          ajaxEventClient
            ?.on('channel-session-traffic-strategy-abort', sessionId, () => {
              chromium?.close()?.then(() => {
                if (db.isRpaExecutor()) return;
                dialog.showMessageBox({
                  type: 'error',
                  title: i18n.t('会话中断'),
                  message: i18n.t('总流量已超额，会话被强行中断'),
                });
              });
            })
            .then((hd) => {
              ajaxEventClientListener.push(hd);
            });
          ajaxEventClient
            ?.on('shop-ip-monitor-alert', sessionId, (data) => {
              logger.info(`[SESSION] shop-ip-monitor-alert: ${JSON.stringify(data)}`);
              showConfirmWindow({
                key: `session-ip-monitor-alert-${sessionId}`,
                width: 640,
                height: 350,
                data: {
                  scope: 'shop-ip-monitor-alert',
                  iconType: 'warn',
                  ...data,
                },
                onBtnClick: (key) => {
                  if (key === 'ok') {
                    chromium?.close();
                  }
                },
              });
            })
            .then((hd) => {
              ajaxEventClientListener.push(hd);
            });
        }
        return this.chromiumIns;
      } catch (e: any) {
        shopBrowsersLock[shopBrowsersLockKey] = false;
        clearTimeout(uploadBrowserDataTimer);
        if (e.code) {
          logger.reportError(e);
        }
        await shopSession?.closeSession();
        webServer.close();
        tunnelRouter?.close();
        wsDispatcher.destroy();
        throw e;
      }
    } catch (err: any) {
      shopBrowsersLock[shopBrowsersLockKey] = false;
      sessionCloseListener && ajaxEventClient?.un(sessionCloseListener);
      logger.error('[SESSION] lifecycle', err);
      if (['当前店铺未绑定指纹模板', '当前账户未绑定指纹'].includes(err.message)) {
        err.message += `，${i18n.t('浏览器打开失败')}`;
      }
      this.reportStatus({
        status: 'fail',
        msg: err.message,
        errCode: err.code,
        kernelVersion: err.kernelVersion,
      });
      if (err.code === 'browserLaunchError') {
        err.code = 1005;
      }
      if (tmpAjaxEventClient) {
        tmpAjaxEventClient.close();
      }
      shopSession.reportOpenApiTaskException(err.message);
      shopSession.tunnelRouter?.close();
      throw err;
    }
  }

  async startWithTimeout(timeout = 5 * 60 * 1000): Promise<Chromium | undefined> {
    return new Promise(async (resolve, reject) => {
      clearTimeout(this.startTimer);
      let isTimeout = false;
      this.startTimer = setTimeout(() => {
        isTimeout = true;
        if (shopBrowsersLock[this.props.params.shopId]) {
          shopBrowsersLock[this.props.params.shopId] = false;
        }
        const error = new Error(i18n.t('打开浏览器分身超时'));
        // @ts-ignore
        error.code = 1006;
        reject(error);
      }, timeout);
      try {
        const chromium = await this.start();
        if (isTimeout) {
          await chromium?.close();
        }
        resolve(chromium);
      } catch (e) {
        reject(e);
      } finally {
        clearTimeout(this.startTimer);
      }
    });
  }

  async uploadBrowserData(tiny = false) {
    const promiseArr = [
      this.cookiesSync?.upload(true),
      this.loginDatabase?.upload(),
      this.historySync?.upload(),
      this.bookmarksSync?.upload(),
    ];
    if (!tiny) {
      promiseArr.push(this.localStorageSync?.upload());
      promiseArr.push(this.indexedDBSync?.upload());
      promiseArr.push(this.extensionSync?.uploadExtSettings());
    }
    await Promise.allSettled(promiseArr);
  }

  async stop() {
    try {
      await this.chromiumIns?.close();
      await new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
          reject(new Error(i18n.t('关闭浏览器超时')));
        }, 60 * 1000);
        const interval = setInterval(() => {
          if (this.status === 'closed') {
            clearInterval(interval);
            clearTimeout(timer);
            resolve(true);
          }
        }, 500);
      });
    } catch (e) {
      logger.error('[APP] stop hyBrowser failed', e);
      throw e;
    }
  }

  async restart(options: { isIppIp: boolean; cleanCookieAndStorage?: boolean }) {
    const { shopId, teamId, requestAgent = new RequestAgent() } = this.props.params;
    const loadingWin = new BrowserWindow({
      width: 500,
      height: 200,
      parent: getCurrentWindow() || undefined,
      center: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      frame: false,
    });
    try {
      await loadingWin.webContents.loadFile(path.resolve(__dirname, 'html', 'loading.html'), {
        query: {
          title: i18n.t('正在为您打开花漾浏览器'),
          description: `${options.isIppIp ? i18n.t('重新申请IP') : i18n.t('更换出口IP')}${i18n.t(
            '后需要重新设置浏览器的指纹信息，这可能需要一点时间，请稍候...',
          )}`,
        },
      });
      await this.stop();
      if (options?.cleanCookieAndStorage) {
        cleanLocalShopData(shopId);
        await Promise.allSettled([
          requestAgent?.request(`/api/shop/${shopId}/cleanCookies`, {
            method: 'DELETE',
            teamId,
          }),
          requestAgent?.request(`/api/shop/${shopId}/clearShopDataInOss`, {
            method: 'DELETE',
            teamId,
            params: {
              dataFile: 'IndexedDB.zip',
            },
          }),
          requestAgent?.request(`/api/shop/${shopId}/clearShopDataInOss`, {
            method: 'DELETE',
            teamId,
            params: {
              dataFile: 'Local Storage.zip',
            },
          }),
        ]);
      }
      await this.start();
    } catch (e) {
      logger.error('[APP] restart hyBrowser failed', e);
    } finally {
      loadingWin.close();
    }
  }
}

/**
 * 打开分身页面
 * @param params
 * @param callback
 */
export const openShopWindow = async function (
  params: any,
  callback: (shopId: number, uuid: string, vo: OpenShopProgressVo) => void,
) {
  const hyBrowser = new HYBrowser({ params, callback });
  try {
    hyBrowsers[params.shopId] = hyBrowser;
    if (params.timeout) {
      return await hyBrowser.startWithTimeout(params.timeout);
    }
    return await hyBrowser.startWithTimeout();
  } catch (e: any) {
    if ([1005, 1006].includes(e.code)) {
      hyBrowsers[params.shopId] = null;
    }
    throw e;
  }
};

/**
 * 打开登录窗口
 */
export async function openLoginWindow(url = '/login', cleanJwt = false) {
  if (db.isRuntimeMode()) {
    return;
  }
  // 清理用户登录态
  if (url === '/login' && cleanJwt) {
    await db.getDb().set('account', {}).write();
  }
  if (loginWin) {
    await loadURL(loginWin, url);
    return;
  }
  loginWin = new BrowserWindow({
    ...DEFAULT_WIN_OPTS,
    width: 833,
    height: 518,
    resizable: false,
    maximizable: false,
    fullscreenable: false,
    titleBarStyle: 'hiddenInset',
    titleBarOverlay: false,
    webPreferences: {
      ...DEFAULT_WIN_OPTS.webPreferences,
      additionalArguments: [],
    },
  });
  logger.info('[APP] 打开登录窗口');
  loginWin.on('closed', () => {
    if (!getMainWindow()) {
      logger.info('[APP] 关闭登录窗口并退出');
      backendTaskIns.stop();
    }
    loginWin = null;
  });
  await loadURL(loginWin, url);
}

export async function openSystemPrefWindow() {
  if (systemPrefWin) {
    systemPrefWin?.show();
    return;
  }
  systemPrefWin = new BrowserWindow({
    ...DEFAULT_WIN_OPTS,
    width: 700,
    height: 518,
    resizable: false,
    maximizable: false,
    fullscreenable: false,
    skipTaskbar: true,
    webPreferences: {
      ...DEFAULT_WIN_OPTS.webPreferences,
      additionalArguments: DEFAULT_WIN_OPTS.webPreferences?.additionalArguments?.concat([
        '--hide-window-title-bar-help-button',
        '--hide-window-title-bar-pin-button',
      ]),
    },
  });
  systemPrefWin.on('closed', () => {
    systemPrefWin?.destroy();
    systemPrefWin = null;
  });
  await loadURL(systemPrefWin, '/systemPref');
}

export function getSystemPrefWindow() {
  return systemPrefWin;
}

export async function openAboutRuntimeWindow() {
  if (aboutRuntimeWin) {
    aboutRuntimeWin?.show();
    return;
  }
  if (process.env.OEM_NAME === 'gg') {
    aboutRuntimeWin = new BrowserWindow({
      ...DEFAULT_WIN_OPTS,
      width: 560,
      height: 340,
      center: true,
      titleBarStyle: 'default',
      titleBarOverlay: false,
      resizable: false,
      maximizable: false,
      minimizable: false,
      fullscreenable: false,
      webPreferences: {
        ...DEFAULT_WIN_OPTS.webPreferences,
        additionalArguments: [],
      },
    });
  } else {
    aboutRuntimeWin = new BrowserWindow({
      ...DEFAULT_WIN_OPTS,
      width: 560,
      height: 340,
      resizable: false,
      maximizable: false,
      fullscreenable: false,
      skipTaskbar: true,
      webPreferences: {
        ...DEFAULT_WIN_OPTS.webPreferences,
        additionalArguments: DEFAULT_WIN_OPTS.webPreferences?.additionalArguments?.concat([
          '--hide-window-title-bar-help-button',
          '--hide-window-title-bar-pin-button',
        ]),
      },
    });
  }
  aboutRuntimeWin.on('closed', () => {
    aboutRuntimeWin?.destroy();
    aboutRuntimeWin = null;
  });
  if (process.env.OEM_NAME === 'gg') {
    aboutRuntimeWin.loadURL('https://www.gamsgo.com/ggbrowser.html');
  } else {
    await loadURL(aboutRuntimeWin, '/aboutRuntime');
  }
}

export function getAboutRuntimeWindow() {
  return aboutRuntimeWin;
}

const runTaskModalWinRecord: Record<number, BrowserWindow | null> = {};

export async function openRunTaskModal(props: {
  teamId: number;
  shopId: number;
  rpaFlowId: number;
  sscToken?: string;
  bizCode?: string;
}) {
  let runTaskModalWin = runTaskModalWinRecord[props.shopId];
  if (!runTaskModalWin) {
    let { width, height, x, y } = makeWindowBoundsInScreen(
      db.getDb().get('runTaskModelBounds').value() ?? {},
    );
    runTaskModalWin = new BrowserWindow({
      ...DEFAULT_WIN_OPTS,
      x,
      y,
      minWidth: 520,
      minHeight: 300,
      width: Math.max(520, width || 970),
      height: Math.max(300, height || 600),
      fullscreenable: false,
      webPreferences: {
        ...DEFAULT_WIN_OPTS.webPreferences,
        additionalArguments: DEFAULT_WIN_OPTS.webPreferences?.additionalArguments?.concat([
          '--hide-window-title-bar-help-button',
        ]),
      },
    });
    runTaskModalWinRecord[props.shopId] = runTaskModalWin;
    const savePos = () => {
      const bounds = runTaskModalWin?.getBounds();
      if (bounds) db.getDb().set('runTaskModelBounds', bounds).write();
    };
    const onClosed = () => {
      runTaskModalWin?.off('closed', onClosed);
      runTaskModalWin?.off('resized', savePos);
      runTaskModalWin?.off('moved', savePos);
      runTaskModalWin = null;
      runTaskModalWinRecord[props.shopId] = null;
    };
    runTaskModalWin.on('closed', onClosed);
    runTaskModalWin.on('resized', savePos);
    runTaskModalWin.on('moved', savePos);
  }
  runTaskModalWin.webContents.setWindowOpenHandler(({ url }) => {
    let openInExternalBrowser = false;
    let targetUrl = url;
    try {
      const urlObj = new URL(url);
      if (urlObj.searchParams.get('__openInExternalBrowser')) {
        openInExternalBrowser = true;
        urlObj.searchParams.delete('__openInExternalBrowser');
        targetUrl = urlObj.toString();
      }
    } catch (e) {}
    if (openInExternalBrowser) {
      // 外部浏览器打开
      shell.openExternal(targetUrl);
    } else {
      // 分身浏览器打开
      getChromium({ shopId: props.shopId })?.handleCmd('open-url', { url: targetUrl });
      getChromium({ shopId: props.shopId })?.bringToFront();
    }
    return { action: 'deny' };
  });
  bringWindowFront(runTaskModalWin);
  // 某些电脑窗口上面的方式还是无法使窗口置顶
  runTaskModalWin.setAlwaysOnTop(true);
  runTaskModalWin.setAlwaysOnTop(false);
  runTaskModalWin.focusOnWebView();
  for (let loadCount = 0; loadCount < 3; loadCount++) {
    try {
      await loadURL(
        runTaskModalWin,
        resolveUrl(
          db.getPortalUrl(),
          `/team/${props.teamId}/runTask/${props.rpaFlowId}?shopId=${props.shopId}&bizCode=${
            props.bizCode ?? ''
          }&sscToken=${props.sscToken ?? ''}`,
        ),
      );
      break;
    } catch (e) {
      logger.error('[APP] run task modal load url failed', e);
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
  }
}

let windowSyncToolboxWin: BrowserWindow | null;

export async function openWindowSyncToolboxModal(teamId?: number) {
  let winSize: number[] | undefined;
  if (!windowSyncToolboxWin) {
    windowSyncToolboxWin = new BrowserWindow({
      ...DEFAULT_WIN_OPTS,
      width: 580,
      height: 435,
      minWidth: 580,
      minHeight: 435,
      maximizable: false,
      minimizable: true,
      fullscreenable: false,
      webPreferences: {
        ...DEFAULT_WIN_OPTS.webPreferences,
        additionalArguments: DEFAULT_WIN_OPTS.webPreferences?.additionalArguments?.concat([
          '--hide-window-title-bar-help-button',
        ]),
      },
    });
    windowSyncToolboxWin.setOpacity(0.9);
    const onClosed = () => {
      windowSyncToolboxWin?.off('closed', onClosed);
      windowSyncToolboxWin = null;
    };
    windowSyncToolboxWin.on('closed', onClosed);
    windowSyncToolboxWin.webContents.ipc.handle('collapse-window', (evt, data) => {
      const { width = 580, height = 435 } = data;
      winSize = windowSyncToolboxWin?.getSize();
      windowSyncToolboxWin?.setMinimumSize(width, 82);
      windowSyncToolboxWin?.setSize(width, height);
      windowSyncToolboxWin?.webContents.send('window-collapsed');
    });
    windowSyncToolboxWin.webContents.ipc.handle('cancel-collapse-window', (evt) => {
      windowSyncToolboxWin?.setMinimumSize(580, 435);
      if (winSize) {
        windowSyncToolboxWin?.setSize(winSize[0], winSize[1]);
      }
      windowSyncToolboxWin?.webContents.send('window-collapsed-cancel');
    });
    await loadURL(
      windowSyncToolboxWin,
      resolveUrl(db.getPortalUrl(), `/windowSyncToolbox?teamId=${teamId}`),
    );
  } else {
    windowSyncToolboxWin.show();
    if (String(teamId) && !windowSyncToolboxWin.webContents.getURL().includes(String(teamId))) {
      await loadURL(
        windowSyncToolboxWin,
        resolveUrl(db.getPortalUrl(), `/windowSyncToolbox?teamId=${teamId}`),
      );
    }
  }
}

export function getWindowSyncToolboxWin() {
  return windowSyncToolboxWin;
}

/**
 * 关闭主窗口
 */
export function closeMainWindow() {
  try {
    mainWin?.destroy();
    mainWin = null;
    lastMainWinUrl = undefined;
  } catch (e) {
    logger.error('[APP] close window exception', e);
  }
}

/**
 * 关闭登录窗口
 */
export function closeLoginWindow() {
  try {
    loginWin?.close();
    loginWin = null;
  } catch (e) {
    logger.error('[APP] close window exception', e);
  }
}

/**
 * 获取主窗口
 */
export function getMainWindow() {
  return mainWin;
}

/**
 * 获取当前窗口
 */
export function getCurrentWindow() {
  return mainWin || loginWin;
}

export function bringWindowFront(win?: BrowserWindow | null) {
  if (!win) return;
  if (isWinPlatform()) {
    win.minimize();
  }
  win.show();
}

/**
 * 获取当前打开的会话 ids
 */
export function getSessionIds(teamId?: number | string) {
  if (teamId) {
    return sessionIds[teamId];
  }
  return _.reduce(sessionIds, (r: any[], v, k) => r.concat(v), []);
}

/**
 * 获取浏览器实例
 * @param params
 */
export function getChromium(params: {
  shopId?: number;
  rpaFlowId?: number;
  sessionId?: number;
  taskItemId?: number;
}) {
  const { shopId, rpaFlowId, taskItemId, sessionId } = params;
  if (shopId) {
    return shopBrowsers[shopId];
  }
  if (rpaFlowId) {
    return rpaBrowsers[rpaFlowId];
  }
  if (taskItemId) {
    return taskItemBrowsers[taskItemId];
  }
  if (sessionId) {
    return sessionBrowsers[sessionId];
  }
  return null;
}

export function getChromiums() {
  return sessionBrowsers;
}
export function getShopChromiums() {
  return shopBrowsers;
}

export function getHyBrowser(shopId: number) {
  return hyBrowsers[shopId];
}

export function waitAllChromiumClosed() {
  return new Promise((resolve) => {
    const chromiumMap = getChromiums();
    if (_.every(chromiumMap, (v) => v === null)) {
      resolve(true);
      return;
    }
    const timer = setInterval(() => {
      if (_.every(chromiumMap, (v) => v === null)) {
        clearInterval(timer);
        resolve(true);
      }
    }, 500);
  });
}

/**
 * 获取RPA编辑窗口
 * @param flowId
 */
export function getRpaFlowPreviewWindow(flowId: number | string) {
  const wins = BrowserWindow.getAllWindows();
  // get flow edit window
  const win = wins.find((w) => {
    const regRes = /\/rpa\/flow\/(\d+)/.exec(w.webContents.mainFrame.url);
    if (regRes && regRes[1] === `${flowId}`) return true;
  });
  return win;
}

/**
 * 获取RPA元素捕获窗口
 * @param flowId
 */
export function getRpaSelectorPickerWindow(flowId: number | string) {
  const wins = BrowserWindow.getAllWindows();
  const win = wins.find((w) => {
    const regRes = /\/rpaSelectorPicker\/(\d+)/.exec(w.webContents.mainFrame.url);
    if (regRes && regRes[1] === `${flowId}`) return true;
  });
  return win;
}

function getIpCacheAddress(ipId?: number) {
  if (!ipId) return null;
  try {
    // 获取数据库中缓存的IP地址
    const stmt = getHyDb()?.conn.prepare(`SELECT address FROM ip_address_cache WHERE ip_id=?`);
    const addressRes = stmt?.get(ipId) as any;
    return addressRes?.address;
  } catch (e) {
    logger.error('[APP] getIpCacheAddress error', e);
  }
  return null;
}

function cacheIpAddress(address: string, ipId?: number) {
  if (!ipId) return false;
  try {
    const stmt = getHyDb()?.conn.prepare(
      `SELECT count(*) as cnt FROM ip_address_cache WHERE ip_id=?`,
    );
    const row = stmt?.get(ipId) as any;
    if (row?.cnt > 0) {
      // 更新数据库中的缓存
      const updateStmt = getHyDb()?.conn.prepare(
        `UPDATE ip_address_cache SET address=? WHERE ip_id=?`,
      );
      updateStmt?.run(address, ipId);
    } else {
      // 插入数据库中的缓存
      const insertStmt = getHyDb()?.conn.prepare(
        `INSERT INTO ip_address_cache (ip_id, address) VALUES (?, ?)`,
      );
      insertStmt?.run(ipId, address);
    }
    return true;
  } catch (e) {
    logger.error('[APP] cacheIpAddress error', e);
    return false;
  }
}
