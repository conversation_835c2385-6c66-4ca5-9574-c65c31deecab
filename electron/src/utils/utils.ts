import os from 'os';
import net from 'net';
import crypto from 'crypto';
import { exec, spawn } from 'child_process';
import iconv from 'iconv-lite';
import fs from 'fs-extra';
import Papa, { ParseConfig, UnparseConfig, UnparseObject } from 'papaparse';
import logger from '@e/services/logger';
import path from 'path';
import appConfig, { getDynamicPath } from '@e/configs/app';
import { BrowserWindow, dialog } from 'electron';
import db from '@e/components/db';
import _ from 'lodash';
import i18n from '@e/utils/i18n';
import random from '../utils/random';
import { RequestAgent } from '@e/services/request';
import BrowserWindowConstructorOptions = Electron.BrowserWindowConstructorOptions;

type TimezoneInfo = {
  city: string;
  city_ascii: string;
  lat: number;
  lng: number;
  pop: number;
  country: string;
  iso2: string;
  iso3: string;
  province: string;
  timezone: string;
};

export const regs = {
  cellPhone: /^1[3-9]\d{9}$/,
  internationalCellPhone: /^1[3-9]\d{9}$/,
  ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  ipv6: /^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$ /,
  url: /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/,
  urlIgnoreProtocol: /^(http(s)?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/,
  hostname: /^([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/,
  wildHostname: /^(\*\.)?([a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,}$/,
  email: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
  port: /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
  chinese: /[\u4e00-\u9fa5]/,
};

export function guid(byteLen: number = 10) {
  return crypto.randomBytes(byteLen / 2).toString('hex');
}

/**
 * 比较两个 url 是不是在同一个域下
 * @param url1
 * @param url2
 */
export function isSameHostname(url1?: string, url2?: string) {
  if (!url1 || !url2) return false;
  try {
    const { hostname: h1 } = new URL(url1);
    const { hostname: h2 } = new URL(url2);
    return h1 === h2;
  } catch (e) {
    return false;
  }
}

export function isWinPlatform() {
  return 'win32' === os.platform();
}

export function isWin7Platform() {
  return 'win32' === os.platform() && parseInt(os.release()) < 10;
}

export function isMacPlatform() {
  return 'darwin' === os.platform();
}

export function isLinuxPlatform() {
  return ['freebsd', 'openbsd', 'linux'].includes(os.platform());
}

export function transBrowserPlatformToNodePlatform(browserPlatform: string) {
  if (browserPlatform === 'Win32') {
    return 'win32';
  }
  if (browserPlatform === 'MacIntel') {
    return 'darwin';
  }
  return 'linux';
}

export const resolveUrl = (base?: string, path = '') => {
  try {
    return new URL(path, base).href;
  } catch (e) {
    return `${base}${path}`;
  }
};

export function wildTest(wildcard: string, str: string) {
  if (/^\*\.\w+/.test(wildcard)) {
    if (wildcard.slice(2) === str) {
      return true;
    }
  }
  let w = wildcard.replace(/[.+^${}()|[\]\\]/g, '\\$&'); // regexp escape
  const re = new RegExp(`^${w.replace(/\*/g, '.*').replace(/\?/g, '.')}$`, 'i');
  return re.test(str);
}

/**
 * 将 json 转化为 vcs 字符串
 */
export function convertJson2Cvs<T>(json: T[] | UnparseObject<T>, config?: UnparseConfig) {
  return Papa.unparse(json, config);
}

/**
 * 将 cvs 转化为 json 对象
 */
export function convertCvs2Json(cvs: string, config?: ParseConfig) {
  return Papa.parse(cvs, config);
}

/**
 * TCP测速
 * @param host
 * @param port
 */
export function tcpPing(host: string, port: number) {
  const startTime = Date.now();
  let ping = 10000;
  return new Promise((resolve) => {
    const socket = net.connect({
      host,
      port,
      timeout: 10000,
    });
    socket.on('connect', () => {
      ping = Date.now() - startTime;
      resolve({ success: true, ping });
      socket.removeAllListeners();
      socket.destroy();
    });
    socket.on('timeout', () => {
      ping = Date.now() - startTime;
      resolve({ success: true, ping });
    });
    socket.on('error', () => {
      resolve({ success: false, ping });
    });
  });
}

export async function getHostName() {
  if (isWinPlatform()) {
    const hostnameProcess = spawn('cmd', ['/c', 'hostname']);
    const hostname = await new Promise((resolve) => {
      hostnameProcess.stdout.on('data', function (data) {
        const buf = iconv.decode(data, 'GB18030');
        const hostname = iconv.encode(buf, 'UTF-8').toString().trim();
        resolve(hostname);
      });
    });
    return hostname;
  }
  return os.hostname();
}

export function getShopDataPath(shopId: number) {
  const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
  return path.resolve(
    BROWSER_USER_DATA_DIR_PATH,
    `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${shopId}`,
  );
}

export function cleanLocalShopData(shopId: number, paths: string[] = [], sync = false) {
  const dir = getShopDataPath(shopId);
  if (paths.length) {
    paths.forEach((p) => {
      const fullPath = path.resolve(dir, p);
      logger.verbose('[APP] clear local data', fullPath);
      try {
        sync ? fs.removeSync(fullPath) : fs.remove(fullPath);
      } catch (e) {
        logger.verbose(e);
      }
    });
  } else {
    try {
      sync ? fs.removeSync(dir) : fs.remove(dir);
    } catch (e) {
      logger.verbose(e);
    }
  }
}

function handlePageLoadFailed(
  e: any,
  errorCode: number,
  errorDescription: string,
  validatedURL: string,
) {
  logger.error(errorCode, errorDescription, validatedURL);
  if (
    validatedURL.includes('damai-record.thinkoncloud.cn') ||
    validatedURL.includes('disk.szdamai.com') ||
    validatedURL.includes('disk.thinkoncloud.com')
  ) {
    logger.error(errorCode, `[OSS 文件预览异常]:${validatedURL}`);
  } else {
    dialog.showErrorBox(
      i18n.t('无法连接到服务器'),
      i18n.t('请检查您的网络，如果您确认网络没有问题请联系我们的客服处理。'),
    );
  }
}
export async function loadURL(win: BrowserWindow, url = '') {
  const session = win.webContents.session;
  // 客户端request拦截器，set-cookie会丢失session这个key
  // https://jira.thinkoncloud.cn/browse/DAMAI-2716
  // 注释掉，不再手动添加cookie
  // 手动向 request header 中添加 cookie
  // reWriteRequestHeaders(session, () => ({
  //   Cookie: db.getCookies().join(';'),
  // }));
  // 捕获 setCookie
  session.webRequest.onCompleted((details) => {
    if (
      details.resourceType === 'xhr' &&
      details.responseHeaders &&
      (details.responseHeaders['set-cookie'] || details.responseHeaders['Set-Cookie'])
    ) {
      const cookiesMap: Record<string, string> = {};
      db.getCookies().forEach((cookie) => {
        const [key = '', value = ''] = cookie.split('=');
        cookiesMap[key] = value;
      });
      const cookieStr =
        details.responseHeaders['set-cookie'] || details.responseHeaders['Set-Cookie'];
      cookieStr.forEach((str) => {
        const [key = '', value = ''] = str.slice(0, str.indexOf(';')).split('=');
        if (key) {
          value ? (cookiesMap[key] = value) : delete cookiesMap[key];
        }
      });
      const cookies = _.map(cookiesMap, (v, k) => `${k}=${v}`);
      appConfig.DEBUG && console.log('[APP] set cookies', cookies);
      db.getDb().set('cookies', cookies).write();
    }
  });
  if (process.env.NODE_ENV === 'development') {
    const ua = session.getUserAgent().replace(/Electron\/[\d.]+/, 'HuaYoung/99.99.99');
    win.webContents.setUserAgent(ua);
    let staticSource = process.env.STATIC_ORIGIN || 'http://localhost:8000';
    await win.loadURL(`${staticSource}${url.replace(/^(https?:\/\/)?[^/]*/, '')}`);
    // Open the DevTools.
    win.webContents.openDevTools();
  } else if (/^https?:\/\//.test(url)) {
    win.webContents.once('did-fail-load', handlePageLoadFailed);
    await win.loadURL(url);
  } else {
    // eg: /login
    const filePath = path.join(__dirname, `../dist/${url.slice(1)}/index.html`);
    try {
      await win.loadFile(filePath, { hash: url });
    } catch (e) {
      const fallbackUrl = resolveUrl(db.getPortalUrl(), url);
      logger.error(`[APP] load local file（${filePath}）failed`, e);
      logger.info(`[APP] load remote url（${fallbackUrl}）`);
      win.webContents.once('did-fail-load', handlePageLoadFailed);
      try {
        await win.loadURL(fallbackUrl);
        logger.info(`[APP] load remote url（${fallbackUrl}）finished`);
      } catch (e) {
        logger.info(`[APP] load remote url（${fallbackUrl}）failed`, e);
      }
    }
  }
  if (!db.isRuntimeMode()) {
    const domain = new URL(db.getApiUrl()).hostname;
    const cookies = await session.cookies.get({
      domain: domain !== 'localhost' ? domain.replace(/^[^.]*/, '') : domain,
    });
    const httpCookies = cookies.filter((c) => !!c.httpOnly);
    if (httpCookies.length) {
      const cookiesMap: any = {};
      db.getCookies().forEach((s) => {
        const [k, v] = s.split('=');
        cookiesMap[k] = v;
      });
      httpCookies.forEach((c) => {
        cookiesMap[c.name] = c.value;
      });
      const cookies = _.map(cookiesMap, (v, k) => `${k}=${v}`);
      db.getDb().set('cookies', cookies).write();
    }
  }
}
export type TunnelSocksConfig = API.ProxyConfig & {
  requestAgent?: RequestAgent;
};
const { DEFAULT_WIDTH, DEFAULT_HEIGHT } = appConfig;
export const useSystemDefaultTitleBar = isWin7Platform() || isLinuxPlatform();

export const DEFAULT_WIN_OPTS: BrowserWindowConstructorOptions = {
  width: DEFAULT_WIDTH,
  height: DEFAULT_HEIGHT,
  titleBarStyle: isMacPlatform() ? 'hiddenInset' : useSystemDefaultTitleBar ? 'default' : 'hidden',
  titleBarOverlay: useSystemDefaultTitleBar
    ? false
    : {
        color: '#FFFFFF',
        symbolColor: '#000000',
        height: isMacPlatform() ? 32 : undefined,
      },
  trafficLightPosition: { x: 10, y: (32 - 14) / 2 },
  maximizable: true,
  useContentSize: true,
  autoHideMenuBar: true,
  icon: ['freebsd', 'openbsd', 'linux'].includes(os.platform())
    ? path.resolve(__dirname, '../dist/logo-128.png')
    : undefined,
  webPreferences: {
    devTools: appConfig.DEBUG,
    preload: path.join(__dirname, 'preload.js'),
    webSecurity: false,
    additionalArguments: useSystemDefaultTitleBar ? [] : ['--show-custom-window-title-bar'],
    nodeIntegration: true,
  },
};

export async function getAFreePort(): Promise<number> {
  while (true) {
    let port = random.nextInt(20000, 60000);
    if (await isPortOpen('127.0.0.1', port)) {
      continue;
    }
    return port;
  }
}

export async function isPortOpen(
  host: string = '127.0.0.1',
  port: number,
  timeout: number = 2000,
): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const client = net.createConnection({ host, port }, () => {
      client.end();
      resolve(true);
    });
    client.setTimeout(timeout);
    client.on('timeout', () => {
      client.destroy();
      resolve(false);
    });
    client.on('error', (err) => {
      client.destroy();
      resolve(false);
    });
  });
}

/**
 * 计算插件id
 * @param extensionPath 插件路径，要和传给chromium的插件的路径一致
 */
export function calcExtensionId(extensionPath: string): string {
  const hash = crypto
    .createHash('sha256')
    .update(extensionPath)
    .digest()
    .slice(0, 16)
    .toString('hex');
  let id = '';
  for (let i = 0; i < hash.length; i++) {
    let val = parseInt(hash[i], 16);
    if (!isNaN(val)) {
      id += String.fromCharCode('a'.charCodeAt(0) + val);
    } else {
      id += 'a';
    }
  }
  return id;
}

/**
 * 检查客户端与系统版本的兼容性
 */
export function checkAppCompatibility() {
  if (process.env.PLATFORM === 'Win7' && parseInt(os.release()) > 6) {
    const error: any = new Error(i18n.t('版本不匹配，请重新下载适用于Win10的客户端版本'));
    error.code = 'appVersionUnMatchError';
    throw error;
  } else if (process.env.PLATFORM === 'Win10' && parseInt(os.release()) < 10) {
    const error: any = new Error(i18n.t('版本不匹配，请重新下载适用于Win7的客户端版本'));
    error.code = 'appVersionUnMatchError';
    throw error;
  }
  return true;
}

/* 判断是否是内网IP */
export function isInnerIPFn(ipAddress: string) {
  var isInnerIp = false; // 默认给定IP不是内网IP
  var ipNum = getIpNum(ipAddress);
  /**
   * 私有IP：A类  10.0.0.0    -**************
   *       B类  **********  -**************
   *       C类  *********** -***************
   *       D类   *********   -***************(环回地址)
   **/
  var aBegin = getIpNum('10.0.0.0');
  var aEnd = getIpNum('**************');
  var bBegin = getIpNum('**********');
  var bEnd = getIpNum('**************');
  var cBegin = getIpNum('***********');
  var cEnd = getIpNum('***************');
  var dBegin = getIpNum('*********');
  var dEnd = getIpNum('***************');
  isInnerIp =
    isInner(ipNum, aBegin, aEnd) ||
    isInner(ipNum, bBegin, bEnd) ||
    isInner(ipNum, cBegin, cEnd) ||
    isInner(ipNum, dBegin, dEnd);
  return isInnerIp;
}
function getIpNum(ipAddress: string) {
  /*获取IP数*/
  var ip = ipAddress.split('.');
  var a = parseInt(ip[0]);
  var b = parseInt(ip[1]);
  var c = parseInt(ip[2]);
  var d = parseInt(ip[3]);
  return a * 256 * 256 * 256 + b * 256 * 256 + c * 256 + d;
}

function isInner(userIp: number, begin: number, end: number) {
  return userIp >= begin && userIp <= end;
}

export async function runCommand(cmd: string, timeout = 60 * 1000) {
  return new Promise((resolve) => {
    exec(
      cmd,
      {
        timeout,
        windowsHide: true,
      },
      (error, stdout, stderr) => {
        let output = stdout;
        let status = 'Succeed';
        let exitCode = error?.code ?? 0;
        if (error?.signal === 'SIGTERM') {
          status = 'Timeout';
        }
        if (exitCode !== 0) {
          resolve({
            status: status ?? 'Failed',
            exitCode,
            error: stderr || error?.message,
            output,
          });
        } else {
          resolve({
            status,
            exitCode,
            error: stderr || error?.message,
            output,
          });
        }
      },
    );
  });
}
