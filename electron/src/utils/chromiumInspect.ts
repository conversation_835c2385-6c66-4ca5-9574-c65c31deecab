import { Props } from '@e/utils/chromium';
import logger from '@e/services/logger';
import { <PERSON><PERSON><PERSON>, Frame, Page } from 'donkey-puppeteer-core';
import { getRpaSelectorPickerWindow } from '@e/utils/window';
import { dispatchMsg } from '@e/utils/ipc';

export default class ChromiumInspect {
  chromium: Browser | null;
  rpaFlowId?: number;
  constructor(props: Props) {
    this.chromium = null;
    this.rpaFlowId = props.rpaFlowId;
  }

  async getActivePage() {
    try {
      const pages = (await this.chromium?.pages()) ?? [];
      const activePage = (
        await Promise.all(
          pages.map((page) =>
            page
              .evaluate(() => document.visibilityState === 'visible')
              .then((isActive) => (isActive ? page : null))
              .catch(() => null),
          ),
        )
      ).find((page) => !!page);
      return [activePage, pages[0]];
    } catch (e) {
      logger.error('[BROWSER] getActivePage error', e);
      return [null, null];
    }
  }

  async setContainer(containerElement: API.Element, containerIdx: number) {
    try {
      const pages = (await this.chromium?.pages()) ?? [];
      pages.forEach(async (page) => {
        // @ts-ignore
        if (!page) return;
        page.frames().forEach((frame) => {
          frame
            .evaluate(
              (containerElement, containerIdx) => {
                // @ts-ignore
                if (window.__hyrpa_selectorFinder) {
                  // @ts-ignore
                  window.__hyrpa_selectorFinder.stop();
                  if (containerElement) {
                    // @ts-ignore
                    window.__hyrpa_selectorFinder.setContainer(containerElement, containerIdx);
                  }
                }
              },
              containerElement,
              containerIdx,
            )
            .catch((e) => {
              logger.error('[BROWSER] setContainer error', e);
            });
        });
      });
    } catch (e) {
      logger.error('[BROWSER] inspectElement error', e);
    }
  }

  _isUrlMatched(url1: string, url2: string) {
    try {
      const u1 = new URL(url1);
      const u2 = new URL(url2);
      return u1.host === u2.host && u1.pathname === u2.pathname;
    } catch (e) {
      return false;
    }
  }

  async inspectElement(options: Record<string, any>) {
    try {
      const pages = (await this.chromium?.pages()) ?? [];
      if (options.containerElement) {
        // 定位到父元素标签页
        try {
          const [activePage, firstPage] = await this.getActivePage();
          if (activePage && !this._isUrlMatched(activePage.url(), options.containerElement.href)) {
            const searchIdx = pages.findIndex((page) =>
              this._isUrlMatched(page.url(), options.containerElement.href),
            );
            if (searchIdx !== -1) {
              pages[searchIdx].bringToFront();
            } else {
              const newPage = await this.chromium?.newPage();
              if (newPage) {
                await newPage.goto(options.containerElement.href, {
                  waitUntil: 'domcontentloaded',
                  timeout: 10 * 1000,
                });
                pages.push(newPage);
              }
            }
          }
        } catch (e) {
          logger.error('[BROWSER] locate container element page error', e);
        }
      }
      pages.forEach(async (page) => {
        // @ts-ignore
        if (!page || page?._hy_OverlayActive) return;
        page.frames().forEach((frame) => {
          frame
            .evaluate(
              (containerElement, containerIdx) => {
                // @ts-ignore
                if (window.__hyrpa_selectorFinder) {
                  // @ts-ignore
                  window.__hyrpa_selectorFinder.stop();
                  if (containerElement) {
                    // @ts-ignore
                    window.__hyrpa_selectorFinder.setContainer(containerElement, containerIdx);
                  }
                }
              },
              options.containerElement,
              options.containerIdx,
            )
            .catch((e) => {
              logger.error('[BROWSER] stop selectorFinder error', e);
            });
        });
        const conn = await page.target().createCDPSession();
        // @ts-ignore
        page._hy_OverlayActive = true;
        // @ts-ignore
        page._hy_OverlayConn = conn;
        await conn.send('Runtime.enable');
        await conn.send('DOM.enable');
        await conn.send('Overlay.enable');
        await conn.send('Overlay.setInspectMode', {
          mode: 'searchForNode',
          highlightConfig: {
            showInfo: true,
            contentColor: { r: 255, g: 0, b: 0, a: 0.3 },
            paddingColor: { r: 255, g: 0, b: 0, a: 0.3 },
          },
        });
        conn.on('Overlay.inspectNodeRequested', async (evt) => {
          const node = await conn.send('DOM.resolveNode', { backendNodeId: evt.backendNodeId });
          // 获取相似元素
          if (options.getSimilarElement) {
            const res = await conn.send('Runtime.callFunctionOn', {
              functionDeclaration: `
        (node) => {
          const res = window.__hyrpa_selectorFinder.getSimilarElement(node);
          return res;
        }`,
              returnByValue: true,
              objectId: node.object.objectId,
              arguments: [{ objectId: node.object.objectId }],
            });
            this.stopInspectElement();
            const selectorPickerWindow = getRpaSelectorPickerWindow(this.rpaFlowId!);
            selectorPickerWindow?.show();
            dispatchMsg('rpa-selector-picker-res', res.result.value ?? {}, selectorPickerWindow);
            return;
          }
          // 获取精确元素
          const { quads } = await conn.send('DOM.getContentQuads', {
            backendNodeId: evt.backendNodeId,
          });
          const quad = quads[0];
          const res = await conn.send('Runtime.callFunctionOn', {
            functionDeclaration: `
        (node) => {
          const realNode = ['::before', '::after'].includes(node.tagName) ? node.parentNode : node;
          if (!window.__hyrpa_selectorFinder) location.reload();
          window.__hyrpa_selectorFinder.elements = [realNode];
          const selector = window.__hyrpa_selectorFinder.finderSelector(realNode);
          const xpath = window.__hyrpa_selectorFinder.finderXpath(realNode);
          const containerCount = window.__hyrpa_selectorFinder.containerElements.length;
          const allContainerChildrenCount = window.__hyrpa_selectorFinder.countAllContainerChildren(selector, xpath);
          return { selector, xpath, containerCount, allContainerChildrenCount, iframeUrl: window.top === window.self ? null : realNode.ownerDocument.URL, innerText: realNode.innerText.substr(0, 10), tagName: realNode.tagName, href: window.location.href, documentTitle: document.title };
        }`,
            returnByValue: true,
            objectId: node.object.objectId,
            arguments: [{ objectId: node.object.objectId }],
          });
          const {
            selector,
            xpath,
            containerCount,
            allContainerChildrenCount,
            iframeUrl,
            innerText,
            tagName,
            href,
            documentTitle,
          } = res.result.value ?? {};
          if (!selector && !xpath) {
            return;
          }
          this.stopInspectElement();
          let tabHref = href;
          let iframeXpath = '';
          let iframeSelector = null;
          if (iframeUrl) {
            tabHref = page.url();
            // @ts-ignore
            const elementHandles = await page.$$('iframe');
            let frameHasSelector = null;
            for (let i = 0; i < elementHandles.length; i++) {
              const eh = elementHandles[i];
              iframeSelector = await (await eh.contentFrame())?.$(selector);
              if (iframeSelector) {
                frameHasSelector = eh;
                const xpath = await page.evaluate(
                  (el: any, iframeUrl) => {
                    if (el.src === iframeUrl) {
                      // @ts-ignore
                      return window.__hyrpa_selectorFinder.finderXpath(el);
                    }
                    return '';
                  },
                  eh,
                  iframeUrl,
                );
                if (xpath) {
                  iframeXpath = xpath;
                  break;
                }
              }
            }
            if (!iframeXpath && frameHasSelector) {
              iframeXpath = await page.evaluate(
                // @ts-ignore
                (el) => window.__hyrpa_selectorFinder.finderXpath(el),
                frameHasSelector,
              );
            }
          }
          const layoutMetrics = await conn.send('Page.getLayoutMetrics');
          conn.detach();
          const { pageX, pageY } = layoutMetrics.cssVisualViewport || layoutMetrics.layoutViewport;
          let width = quad[4] - quad[0];
          let height = quad[5] - quad[1];
          const ratio = Math.min(1, 400 / Math.max(width, height));
          let screenshotBase64 = '';
          try {
            screenshotBase64 = (await page.screenshot({
              encoding: 'base64',
              type: 'webp',
              clip: {
                x: pageX + quad[0],
                y: pageY + quad[1],
                width: width,
                height: height,
                scale: ratio,
              },
            })) as string;
          } catch (e) {
            logger.error('[BROWSER] screenshot error', e);
          }
          // console.log('selector', selector, xpath, iframeXpath);
          const selectorPickerWindow = getRpaSelectorPickerWindow(this.rpaFlowId!);
          selectorPickerWindow?.show();
          dispatchMsg(
            'rpa-selector-picker-res',
            {
              selector,
              xpath,
              iframe: iframeXpath,
              innerText,
              tagName,
              description: node.object.description,
              screenshot: `data:image/webp;base64,${screenshotBase64}`,
              elementCount: 1,
              resultCount: undefined,
              containerCount,
              allContainerChildrenCount,
              href: tabHref,
              documentTitle,
            },
            selectorPickerWindow,
          );
        });
      });
    } catch (e) {
      logger.error('[BROWSER] inspectElement error', e);
    }
  }

  async previewElementSelector(element: API.Element, opt: Record<string, any>) {
    try {
      const [activePage, firstPage] = await this.getActivePage();
      let page = activePage ?? firstPage;
      if (!page) return;
      if (element.href && !this._isUrlMatched(page.url(), element.href)) {
        const pages = (await this.chromium?.pages()) ?? [];
        const searchIdx = pages.findIndex((page) => this._isUrlMatched(page.url(), element.href!));
        if (searchIdx !== -1) {
          pages[searchIdx].bringToFront();
          page = pages[searchIdx];
        } else {
          const newPage = await this.chromium?.newPage();
          if (newPage) {
            await newPage.goto(element.href, {
              waitUntil: 'domcontentloaded',
              timeout: 10 * 1000,
            });
            page = newPage;
          }
        }
      }
      let frame: Page | Frame | undefined = page;
      if (element.iframe) {
        if (/^\//.test(element.iframe)) {
          frame = (await (await page.$x(element.iframe))[0]?.contentFrame()) as Frame;
        } else {
          // @ts-ignore
          frame = (await (await page.$(element.iframe))?.contentFrame()) as Frame;
        }
      }
      await frame?.evaluate(
        (element, opt) => {
          // @ts-ignore
          window.__hyrpa_selectorFinder.previewSelector(element, opt);
        },
        element,
        opt,
      );
    } catch (e) {
      logger.error('[BROWSER] previewElementSelector error', e);
    }
  }

  /**
   * 获取关联元素
   * @param element 如果是调整相似元素，不需要传 element
   * @param opt
   */
  async getNearbyElements(element: API.Element | undefined, opt: Record<string, any>) {
    try {
      const [activePage, firstPage] = await this.getActivePage();
      const page = activePage ?? firstPage;
      if (!page) return;
      let frame: Page | Frame | undefined = page;
      let errorMsg = '';
      let responseData = {};
      if (element) {
        if (element.iframe) {
          if (/^\//.test(element.iframe)) {
            frame = (await (await page.$x(element.iframe))[0]?.contentFrame()) as Frame;
          } else {
            frame = (await (await page.$(element.iframe))?.contentFrame()) as Frame;
          }
        }
        const {
          count,
          message,
          selector,
          xpath,
          innerText,
          tagName,
          href,
          documentTitle,
          fullSelector,
          needConfirmChildren,
          totalChildrenCount,
          elementCount,
          resultCount,
        } = await frame?.evaluate(
          (element, opt) => {
            // @ts-ignore
            return window.__hyrpa_selectorFinder.getNearbyElements(element, opt);
          },
          element,
          opt,
        );
        if (message) {
          errorMsg = message;
        } else if (needConfirmChildren) {
          responseData = {
            needConfirmChildren,
            totalChildrenCount,
          };
        } else if (resultCount > 0) {
          // 选择所有子元素保存为元素列表
          // @ts-ignore
          const el = await frame?.$(selector);
          let screenshotBase64 = '';
          try {
            screenshotBase64 = (await el?.screenshot({
              encoding: 'base64',
              type: 'webp',
            })) as string;
          } catch (e) {
            logger.error('[BROWSER] getNearbyElements screenshot error', e);
          }
          responseData = {
            selector,
            xpath,
            innerText,
            tagName,
            href,
            documentTitle,
            elementCount,
            resultCount,
            screenshot: `data:image/webp;base64,${screenshotBase64}`,
          };
        } else if (count > 0) {
          // @ts-ignore
          const el = await frame?.$(fullSelector ?? selector);
          let screenshotBase64 = '';
          try {
            screenshotBase64 = (await el?.screenshot({
              encoding: 'base64',
              type: 'webp',
            })) as string;
          } catch (e) {
            logger.error('[BROWSER] getNearbyElements screenshot error', e);
          }
          responseData = {
            selector,
            xpath,
            iframe: element.iframe,
            innerText,
            tagName,
            screenshot: `data:image/webp;base64,${screenshotBase64}`,
            elementCount: 1,
            resultCount: undefined,
            href,
            documentTitle,
          };
        }
      } else {
        const { message, selector, xpath, elementCount, resultCount, href, documentTitle } =
          await frame?.evaluate(
            (element, opt) => {
              // @ts-ignore
              return window.__hyrpa_selectorFinder.changeLastElementAndGetSimilarElement(
                opt.direction,
              );
            },
            element,
            opt,
          );
        if (message) {
          errorMsg = message;
        } else {
          responseData = {
            selector,
            xpath,
            elementCount,
            resultCount,
            href,
            documentTitle,
          };
        }
      }

      const selectorPickerWindow = getRpaSelectorPickerWindow(this.rpaFlowId!);
      if (!errorMsg) {
        dispatchMsg('rpa-selector-picker-res', responseData, selectorPickerWindow);
      } else {
        dispatchMsg(
          'rpa-selector-picker-error',
          {
            message: errorMsg,
          },
          selectorPickerWindow,
        );
      }
    } catch (e) {
      logger.error('[BROWSER] getNearbyElements error', e);
    }
  }

  async stopInspectElement() {
    try {
      // const [activePage, firstPage] = await this.getActivePage();
      // const page = activePage ?? firstPage;
      const pages = (await this.chromium?.pages()) ?? [];
      pages.forEach(async (page) => {
        if (!page) return;
        page.frames().forEach((frame) => {
          frame
            .evaluate(() => {
              // @ts-ignore
              if (window.__hyrpa_selectorFinder) {
                // @ts-ignore
                window.__hyrpa_selectorFinder.stop();
              }
            })
            .catch((e) => {
              logger.error('[BROWSER] stopInspectElement error', e);
            });
        });
        // @ts-ignore
        if (page._hy_OverlayConn) {
          // @ts-ignore
          await page._hy_OverlayConn.send('Overlay.disable');
        }
        // @ts-ignore
        page._hy_OverlayActive = false;
      });
    } catch (e) {
      logger.error('[BROWSER] stopInspectElement error', e);
    }
  }
}
