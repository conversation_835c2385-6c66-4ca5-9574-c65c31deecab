import { dialog, ipcMain } from 'electron';
import fs from 'fs-extra';
import i18n from '@e/utils/i18n';
import { canParse, parse, convertToHtml } from '@e/utils/bookmarksParser';
import path from 'path';
import { getDataSavePath } from '@e/components/db';

export default () => {
  ipcMain.handle('parse-bookmarks-file', async (evt, data) => {
    return new Promise((resolve) => {
      dialog
        .showOpenDialog({
          title: i18n.t('请选择书签文件'),
          filters: [{ name: 'HTML', extensions: ['html', 'htm'] }],
          properties: ['openFile'],
        })
        .then(async ({ filePaths }) => {
          const filePath = filePaths.length > 0 ? filePaths[0] : '';
          if (filePath) {
            try {
              const fileContent = await fs.readFile(filePath, 'utf-8');
              if (!canParse(fileContent)) {
                throw new Error(i18n.t('文件格式不正确'));
              }
              const json = parse(fileContent);
              resolve({
                success: true,
                filePath,
                data: json,
              });
            } catch (err: any) {
              resolve({
                success: false,
                message: err.message,
              });
            }
          } else {
            resolve({
              success: false,
              message: '',
            });
          }
        });
    });
  });

  ipcMain.handle('save-bookmarks-file', async (evt, data) => {
    const { content = [], filename } = data;
    let file = 'bookmarks.html';
    let str = convertToHtml(content);
    if (filename) {
      file = filename;
    }
    dialog
      .showSaveDialog({
        title: i18n.t('保存书签文件'),
        defaultPath: path.join(getDataSavePath('downloads'), file),
        properties: ['createDirectory'],
      })
      .then(async ({ filePath }) => {
        if (filePath) {
          // 写文件
          fs.outputFile(filePath, str);
        }
      });
  });
};
