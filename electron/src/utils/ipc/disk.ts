import { app, BrowserWindow, dialog, ipcMain } from 'electron';
import Downloader from 'nodejs-file-downloader';
import path from 'path';
import logger from '@e/services/logger';
import { DEFAULT_WIN_OPTS, loadURL } from '@e/utils/utils';
import _ from 'lodash';
import db, { getDataSavePath } from '@e/components/db';
import i18n from '@e/utils/i18n';
import fastFolderSize from 'fast-folder-size';
import checkDiskSpace from 'check-disk-space';

const PreviewWins: { [key: string]: BrowserWindow } = {};
export function destroyAllPreviewWins() {
  Object.values(PreviewWins).forEach((win) => {
    win?.destroy();
  });
}

// 60分钟缓存
const TIME = 60 * 1000 * 60;
const DiskStatCache: Record<
  string,
  {
    time: number;
    value: any;
  }
> = {};

async function getPathSpaceStat(
  path_string: string,
  force?: boolean,
): Promise<{
  path: string;
  total: number;
  disk: string;
  size: number;
  free: number;
}> {
  if (DiskStatCache[path_string] && Date.now() - DiskStatCache[path_string].time < TIME && !force) {
    return DiskStatCache[path_string].value;
  }
  const bytes = await new Promise<number>((resolve) => {
    fastFolderSize(path_string, (err, bytes) => {
      resolve(bytes || 0);
    });
  });
  const res = await checkDiskSpace(path_string);
  const stat = {
    total: res.size,
    free: res.free,
    disk: res.diskPath,
    size: bytes,
    path: path_string,
  };
  DiskStatCache[path_string] = {
    time: Date.now(),
    value: stat,
  };
  return stat;
}

export default () => {
  app.whenReady().then(() => {
    ipcMain.handle('disk-file-path-select', async (evt) => {
      const { canceled, filePaths: paths } = await dialog.showOpenDialog({
        title: i18n.t('下载文件到'),
        defaultPath: getDataSavePath('downloads'),
        properties: ['openDirectory', 'createDirectory'],
      });
      if (canceled) return '';
      logger.info('paths', paths);
      return paths && paths[0];
    });
    ipcMain.handle('disk-file-download', (evt, data) => {
      try {
        // @ts-ignore
        const { fileName, savePath, relativePath, url } = data;
        const directory_path = path.join(savePath, relativePath || '');
        const config: any = {
          url,
          directory: directory_path,
          onError(e: Error) {
            logger.error('disk-file-download', e);
            dialog.showErrorBox(i18n.t('文件下载失败'), e.message);
          },
        };
        if (typeof fileName === 'string') {
          config.fileName = fileName;
        }
        const download = new Downloader(config);
        download.download();
      } catch (e) {
        logger.error('disk-file-download', e);
      }
    });
    ipcMain.handle('disk-file-preview', (evt, { path, name, url, host }) => {
      const remainWin = PreviewWins[path];
      try {
        if (remainWin) {
          remainWin.show();
          remainWin.moveTop();
        } else {
          // @ts-ignore
          const win = new BrowserWindow(
            _.mergeWith(DEFAULT_WIN_OPTS, {
              center: true,
              width: 'auto',
              height: 'auto',
              closable: true,
              webPreferences: {
                defaultEncoding: 'utf-8',
                webgl: false,
              },
            }),
          );
          win.on('closed', () => {
            win.destroy();
            delete PreviewWins[path];
          });
          const href = `${host}?path=${path}&name=${name ?? ''}&url=${url ?? ''}`;
          // @ts-ignore
          loadURL(win, href);
          PreviewWins[path] = win;
        }
      } catch (e) {
        logger.error('disk-file-preview', e);
      }
    });
    ipcMain.handle('disk-space-of-path', async (evt, data) => {
      return await getPathSpaceStat(data.path, data.force);
    });
    ipcMain.handle('disk-space-stat', async (evt, data) => {
      const systemConfig = db.getDb().get('sysPres').value();
      const { dataDir, rpaDir, videoDir } = systemConfig;
      let danger = false;
      const values = _.uniq([dataDir, rpaDir, videoDir]);
      for (let i = 0; i < values.length; i++) {
        const _path = values[i];
        const stat = await getPathSpaceStat(_path, data?.force);
        danger = stat.free < 5 * 1024 * 1024 * 1024 || stat.free / stat.total < 0.05;
        if (danger) {
          return stat;
        }
      }
    });
  });
};
