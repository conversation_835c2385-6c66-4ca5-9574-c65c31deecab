import { app, BrowserWindow, ipcMain, screen, session, clipboard } from 'electron';
import os from 'os';
import _ from 'lodash';
import db from '@e/components/db';
import appConfig from '@e/configs/app';
import logger from '@e/services/logger';
import { getHostName } from '@e/utils/utils';
import { appUpdater } from '@e/components/updater';
import { getMainWindow, openAboutRuntimeWindow, openSystemPrefWindow } from '@e/utils/window';
import KernelManage from '@e/components/kernelManage';
import { hyDecrypt, hyDecryptVo, hyEncrypt, hyEncryptVo } from '@e/utils/crypto';
import random from '@e/utils/random';
import { localWebServerIns } from '@e/components/localWebServer';

export default () => {
  // app版本
  ipcMain.handle('get-release-app-version', (evt) => {
    return process.env.RELEASE_APP_VERSION;
  });
  // 构建版本号
  ipcMain.handle('get-app-build-number', (evt) => {
    return process.env.BUILD_NUMBER;
  });
  // 浏览器内核版本
  ipcMain.handle('get-browser-version', () => {
    return appConfig.BROWSER_VERSION;
  });
  // 客户端已安装的最新的浏览器内核版本
  ipcMain.handle('get-installed-browser-versions', () => {
    return KernelManage.getInstance().getInstalledKernelInfo();
  });
  ipcMain.handle('uninstall-browser-kernel', async (evt, data) => {
    const { buildNumber } = data;
    return await KernelManage.getInstance().uninstallKernel(buildNumber);
  });
  // APP 平台
  ipcMain.handle('get-app-platform', () => {
    return process.env.PLATFORM;
  });
  // 操作系统平台
  ipcMain.handle('get-os-platform', () => {
    return {
      platform: os.platform(),
      version: parseInt(os.release()),
    };
  });
  // APP UUID
  ipcMain.handle('get-app-uuid', () => {
    let uuid = db.getDb().get('uuid').value();
    if (!uuid) {
      uuid = random.nextString(12);
      db.getDb().set('uuid', uuid).write();
    }
    return uuid;
  });
  // hostName
  ipcMain.handle('get-host-name', async () => {
    return await getHostName();
  });
  ipcMain.handle('get-device-id', async () => {
    return db.getDeviceIdFromCookies();
  });
  // cpus
  ipcMain.handle('get-cpus', () => {
    return os.cpus().length;
  });
  // cpus
  ipcMain.handle('get-memory', () => {
    return os.totalmem();
  });
  // 获取客户端代理配置
  ipcMain.handle('get-app-proxy', () => {
    return {
      ...db.getDb().get('appProxy').value(),
      useBackupEndpoint: db.getDb().get('useBackupEndpoint').value(),
    };
  });
  // 设置客户端代理配置
  ipcMain.handle('set-app-proxy', async (evt, proxyConfig) => {
    try {
      const { useBackupEndpoint, ...restProps } = proxyConfig;
      await session.defaultSession.closeAllConnections();
      await session.defaultSession.setProxy(restProps);
      db.getDb().set('appProxy', restProps).write();
      logger.info('[IPC] set-app-proxy success', restProps);
      if (typeof useBackupEndpoint !== 'undefined') {
        const url = BrowserWindow.fromWebContents(evt.sender)?.webContents.getURL();
        let prevPortalUrl = '';
        if (url?.startsWith('http')) {
          prevPortalUrl = new URL(url).origin;
        }
        db.getDb().set('useBackupEndpoint', useBackupEndpoint).write();
        db.changeToEndpoint(useBackupEndpoint ? 2 : 1);
        if (prevPortalUrl && db.getPortalUrl() !== prevPortalUrl) {
          BrowserWindow.fromWebContents(evt.sender)?.loadURL(
            url!.replace(prevPortalUrl, db.getPortalUrl()),
          );
        } else {
          BrowserWindow.fromWebContents(evt.sender)?.reload();
        }
      }
      return true;
    } catch (e) {
      logger.error('[IPC] set-app-proxy error', e);
      return false;
    }
  });
  // 获取客户端缓存大小
  ipcMain.handle('get-app-cache-size', () => {
    return session.defaultSession.getCacheSize();
  });
  // 清除客户端缓存
  ipcMain.handle('clear-app-cache', async (evt, clearStorageDataOptions) => {
    try {
      await session.defaultSession.clearCache();
      if (clearStorageDataOptions) {
        await session.defaultSession.clearStorageData(clearStorageDataOptions);
      }
      return true;
    } catch (e) {
      logger.error('[IPC] clear-app-cache error', e);
      return false;
    }
  });
  // 获取屏幕信息
  ipcMain.handle('get-screen-info', () => {
    return {
      primaryDisplay: screen.getPrimaryDisplay(),
      allDisplays: screen.getAllDisplays(),
    };
  });
  // 检测是否有新版本
  ipcMain.handle('check-app-version', (evt, data) => {
    return appUpdater.hasNewVersion();
  });
  ipcMain.handle('get-path', (evt, name) => {
    return app.getPath(name);
  });
  ipcMain.handle('open-window', (evt, data) => {
    const { windowName } = data;
    switch (windowName) {
      case 'sysPref':
        openSystemPrefWindow();
        break;
      case 'aboutRuntime':
        openAboutRuntimeWindow();
        break;
      default:
        return 'unsupported';
    }
  });
  ipcMain.handle('show-main-window', () => {
    getMainWindow()?.show();
  });
  // 对当前窗口进行操作
  ipcMain.handle('window-action', (evt, data) => {
    const { action } = data;
    switch (action) {
      case 'minimize':
        BrowserWindow.fromWebContents(evt.sender)?.minimize();
        break;
      case 'maximize':
        BrowserWindow.fromWebContents(evt.sender)?.maximize();
        break;
      case 'unmaximize':
        BrowserWindow.fromWebContents(evt.sender)?.unmaximize();
        break;
      case 'close':
        BrowserWindow.fromWebContents(evt.sender)?.close();
        break;
      case 'pin':
        BrowserWindow.fromWebContents(evt.sender)?.setAlwaysOnTop(true);
        break;
      case 'unpin':
        BrowserWindow.fromWebContents(evt.sender)?.setAlwaysOnTop(false);
        break;
      case 'show':
        BrowserWindow.fromWebContents(evt.sender)?.show();
        break;
      case 'resize':
        const { width, height } = data;
        BrowserWindow.fromWebContents(evt.sender)?.setSize(width, height);
        break;
      case 'bring-to-front':
        BrowserWindow.fromWebContents(evt.sender)?.moveTop();
        break;
      default:
        return 'unsupported';
    }
  });
  ipcMain.handle('app-exit', (evt, data) => {
    const { quit = false } = data || {};
    if (quit) {
      app.quit();
    } else {
      app.exit(0);
    }
  });
  ipcMain.handle('get-clipboard', () => {
    return clipboard.readText();
  });
  ipcMain.handle('set-clipboard', (evt, data) => {
    clipboard.writeText(data);
  });
  // 加密
  ipcMain.handle('hy-encrypt', (evt, data) => {
    if (_.isPlainObject(data)) {
      return hyEncryptVo(data);
    } else if (_.isString(data)) {
      return hyEncrypt(data);
    }
    return data;
  });
  // 解密
  ipcMain.handle('hy-decrypt', (evt, data) => {
    if (_.isPlainObject(data)) {
      return hyDecryptVo(data);
    } else if (_.isString(data)) {
      return hyDecrypt(data);
    }
    return data;
  });
  // 解密
  ipcMain.handle('rpc-event', (evt, eventName) => {
    return localWebServerIns.getRpcEvent(eventName);
  });
};
