import { ipcMain } from 'electron';
import { appUpdater } from '@e/components/updater';

export default () => {
  ipcMain.handle('get-app-updater-info', () => {
    return appUpdater.getInfo();
  });

  ipcMain.handle('check-app-updater', () => {
    return appUpdater.check(true);
  });

  ipcMain.handle('quit-and-install-updater', () => {
    return appUpdater.quitAndInstall();
  });

  ipcMain.handle('delay-auto-install', (evt, data) => {
    return appUpdater.delayAutoInstall(data.seconds);
  });

  ipcMain.handle('has-new-version-to-install', () => {
    return appUpdater.hasNewVersionToInstall();
  });
};
