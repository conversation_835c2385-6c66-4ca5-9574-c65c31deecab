import { ipcMain } from 'electron';
import fs from 'fs-extra';
import path from 'path';

import { backendTaskIns, baseBackendTaskIns } from '@e/components/backendTask';
import { isWinPlatform, resolveUrl } from '@e/utils/utils';
import { hy_actions } from '@e/helper/index';
import { getDynamicPath } from '@e/configs/app';
import Downloader from 'nodejs-file-downloader';
import db from '@e/components/db';
import logger from '@e/services/logger';
import { dispatchMsg } from '@e/utils/ipc';
import i18n from '@e/utils/i18n';
import { getWindowSyncToolboxWin } from '@e/utils/window';

export default () => {
  if (isWinPlatform()) {
    /**
     * 检查花漾群控程序是否存在，是否需要更新
     */
    ipcMain.handle('gc/checkExeFile', async (evt, data) => {
      const { DATA_DIR } = getDynamicPath();
      const md5Path = path.join(DATA_DIR, 'GCHelper.exe.md5sum');
      const latestMd5Path = path.join(DATA_DIR, 'GCHelper.exe.md5sum.latest');
      let lastMd5 = '';
      let currentMd5 = '';
      let res = 0b00;
      if (fs.existsSync(md5Path)) {
        lastMd5 = fs.readFileSync(md5Path, 'utf-8');
      }
      try {
        await new Downloader({
          url: resolveUrl(
            process.env.NODE_ENV === 'development' ? db.getPortalUrl() : db.getDlUrl(),
            '/downloads/GCHelper.exe.md5sum',
          ),
          fileName: 'GCHelper.exe.md5sum.latest',
          cloneFiles: false,
          directory: DATA_DIR,
          timeout: 30 * 1000,
          maxAttempts: 3,
        }).download();
        currentMd5 = fs.readFileSync(latestMd5Path, 'utf-8');
      } catch (e) {
        logger.error('[APP] download GCHelper.exe.md5sum failed', e);
      }
      if (!backendTaskIns.checkGCFile()) {
        // 需要下载
        res |= 0b01;
      }
      if (lastMd5 && currentMd5 && lastMd5 !== currentMd5) {
        // 需要更新
        res |= 0b11;
      }
      return res;
    });

    /**
     * 开始群控
     * data : {master: number, follows: number[]}  master: 主控窗口id, follows: 被控窗口id数组
     */
    ipcMain.handle(hy_actions.ACTION_GC_START, async (evt, data) => {
      if (!backendTaskIns.checkGCFile()) {
        throw new Error(i18n.t('请先下载花漾群控程序'));
      }
      const gc = (await backendTaskIns.getGCHelper()).gc;
      if (gc.isProcessing()) {
        throw new Error(i18n.t('请先停止当前群控'));
      }
      let renderMasterMark = data.renderMasterMark;
      if (typeof renderMasterMark === 'undefined') {
        renderMasterMark = true;
      }
      await gc.startGroupControl(data.master, data.follows, renderMasterMark);
      gc.windowResizeCallback = (sessionId: number) => {
        dispatchMsg(hy_actions.EVENT_GC_WIN_SIZE_CHG, sessionId);
      };
      dispatchMsg(hy_actions.EVENT_GC_DETAIL_UPDATE);
      if (getWindowSyncToolboxWin()) {
        dispatchMsg(hy_actions.EVENT_GC_DETAIL_UPDATE, {}, getWindowSyncToolboxWin());
      }
    });

    /**
     * 停止群控
     */
    ipcMain.handle(hy_actions.ACTION_GC_STOP, async (evt, data) => {
      const gc = (await backendTaskIns.getGCHelper()).gc;
      await gc.stopGroupControl();
    });

    ipcMain.handle(hy_actions.ACTION_GC_PAUSE, async () => {
      const gc = (await backendTaskIns.getGCHelper()).gc;
      gc.changePauseState(true);
    });

    ipcMain.handle(hy_actions.ACTION_GC_RESUME, async () => {
      const gc = (await backendTaskIns.getGCHelper()).gc;
      gc.changePauseState(false);
    });

    /**
     * 获取群控详情
     */
    ipcMain.handle(hy_actions.ACTION_GC_DETAIL, async (evt, data) => {
      const gc = (await backendTaskIns.getGCHelper()).gc;
      return gc.detail;
    });

    /**
     * 批量往窗口发送文字（如果当前有焦点的不是输入框则无意义）
     * data: {
     *   sessions: {[sessionId]: text}
     * }
     */
    ipcMain.handle(hy_actions.ACTION_WIN_SEND_KEYS, async (evt, data) => {
      const gc = (await backendTaskIns.getGCHelper()).gc;
      await gc.sendKeys(data.sessions);
    });

    /**
     * 同步标签页
     */
    ipcMain.handle(hy_actions.ACTION_GC_SYNC_TABS, async (evt, data) => {
      const gc = (await backendTaskIns.getGCHelper()).gc;
      await gc.syncTabs();
    });

    ipcMain.handle('get-send-keys-logs', async () => {
      const gc = (await backendTaskIns.getGCHelper()).gc;
      return gc.getSendKeysLogs();
    });
  }
};
