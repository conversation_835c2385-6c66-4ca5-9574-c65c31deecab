import { ipcMain } from 'electron';
import { getChromium } from '@e/utils/window';
import * as windowUtil from '../window';

export default () => {
  ipcMain.handle('browser.open', async (evt, data) => {
    try {
      const { shopId, url, mode = 'reuse', teamId } = data;
      const browserTarget = getChromium({ shopId });
      if (browserTarget) {
        browserTarget.bringToFront();
        browserTarget.handleCmd('open-url', {
          url,
        });
        return {
          success: true,
        };
      } else {
        return await new Promise<{
          success: boolean;
          message?: string;
        }>((resolve) => {
          windowUtil.openShopWindow(
            { ...data, timeout: 5 * 60 * 1000, teamId },
            (shopId, uuid, vo) => {
              if (['success', 'fail'].includes(vo.status)) {
                resolve({
                  success: vo.status === 'success',
                  message: vo?.msg || '',
                });
              }
            },
          );
        });
      }
    } catch (e: any) {
      return {
        success: false,
        message: e?.message,
      };
    }
  });
};
