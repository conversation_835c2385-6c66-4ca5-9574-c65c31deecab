import { ipcMain } from 'electron';
import { runAiAgent, stopPreview } from '@e/rpa/task_schedule';

export default () => {
  ipcMain.handle('aiAgent/prompt', async (evt, data) => {
    const { teamId, rpaType, shopOrMobileId, prompt = '' } = data;
    return new Promise((resolve) => {
      runAiAgent(teamId, rpaType, shopOrMobileId, prompt).then((res) => {
        resolve(res);
      });
    });
  });

  ipcMain.handle('aiAgent/stopTask', async (evt, data) => {
    const { shopOrMobileId } = data;
    return new Promise((resolve) => {
      stopPreview(shopOrMobileId);
      resolve(true);
    });
  });
};
