import logger from '@e/services/logger';
import {RequestAgent} from '@e/services/request';
import {portalRpcClient} from "@e/components/backendTask";

const nodemailer = require('nodemailer');
/**
 * 邮件发送服务
 */
type EmailListenerProps = {
};
export class EmailListener {
  props: EmailListenerProps;
  requestAgent: RequestAgent | null;
  transporter: any;
  authInfo: string = '';
  constructor(props: EmailListenerProps) {
    this.props = props;
    this.requestAgent = new RequestAgent();
  }

  start() {
    // 打开会话
    portalRpcClient.onAppEmit('tk.sendEmail', async ({ teamId, id }) => {
        logger.info('[Email] receive openSession event', id);
        const res = await this.requestAgent?.request(`/api/tk/email/job/${id}`, {
          params: {
            teamId,
          },
          method: 'GET',
        });
        const { document, email, targetEmail, creator } = res;
        const { encryption, host, password, port, username, senderName } = email;
        const secure = encryption === 'SSL';
        const { title, content } = document;
        const { alias, handle } = creator;
        const _auth = `smtp${secure ? 's' : ''}://${username}:${password}@${host}:${port}`;
        if (_auth !== this.authInfo) {
          this.transporter = null;
          this.transporter = nodemailer.createTransport({
            host,
            port,
            secure,
            auth: { user: username, pass: password },
            socketTimeout: 10000, // 10秒
            connectionTimeout: 10000, // 10秒
            greetingTimeout: 10000, // 10秒
          });
        }
        this.transporter.sendMail(
          {
            from: senderName,
            to: targetEmail,
            subject: title?.replaceAll('{creator.name}', alias || handle),
            html: content?.replaceAll('{creator.name}', alias || handle),
          },
          async (err: { message: any; code: any }, info: { response: any }) => {
            const params = {
              id,
              teamId,
              status: 'Success',
              description: targetEmail + '\n' + info?.response,
            };
            if (err) {
              if (err.code == 'ETIMEDOUT') {
                params.status = 'Timeout';
              } else {
                params.status = 'Fail';
              }
              params.description = err.message;
            }
            this.requestAgent?.request(`/api/tk/email/job/${id}/result`, {
              params,
              method: 'PUT',
            });
          },
        );
      });
    logger.info('[Email] Email listener started');
    return this;
  }

  stop() {
    portalRpcClient.removeAllListeners('tk.sendEmail');
    logger.info('[Email] Email listener stopped');
    return this;
  }
}
