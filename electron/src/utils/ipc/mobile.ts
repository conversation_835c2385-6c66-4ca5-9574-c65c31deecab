import {app, BrowserWindow, ipcMain} from 'electron';
import {AndroidDeviceManager} from '@e/mobile/AndroidDeviceManager';
import {DEFAULT_WIN_OPTS, isWinPlatform, loadURL} from '@e/utils/utils';
import {dispatchMsg} from '@e/utils/ipc';
import logger from '@e/services/logger';
import {getHyDb} from '@e/components/db';
import {makeWindowBoundsInScreen} from '@e/utils/window';
import {iosDeviceManager} from "@e/components/backendTask";

export const popupMobileWins: Map<number, BrowserWindow> = new Map();

export const androidDeviceManager: AndroidDeviceManager = AndroidDeviceManager.getInstance();

function popupMobilesChange() {
  dispatchMsg('popup-mobiles-change', { phoneIds: Array.from(popupMobileWins.keys()) });
}

export async function popupMobile(phoneId: number, url: string, width?: number, height?: number) {
  if (popupMobileWins.has(phoneId)) {
    if (popupMobileWins.get(phoneId)?.webContents?.getURL() !== url) {
      popupMobileWins.get(phoneId)?.loadURL(url);
    }
    if (isWinPlatform()) {
      popupMobileWins.get(phoneId)?.minimize();
    }
    popupMobileWins.get(phoneId)?.show();
    return;
  }
  let pos: Record<string, number> = {
    width: Math.max(200, width || 320),
    height: Math.max(300, height || 640),
  };
  const cachedPos = getPopupMobilePos(phoneId);
  if (cachedPos) {
    const [_x, _y, _w, _h] = cachedPos.split(',');
    pos.x = ~~_x;
    pos.y = ~~_y;
    pos.width = Math.max(200, ~~_w);
    pos.height = Math.max(300, ~~_h);
  }
  pos = makeWindowBoundsInScreen(pos);
  const win = new BrowserWindow({
    ...DEFAULT_WIN_OPTS,
    ...pos,
    minWidth: 200,
    minHeight: 300,
    fullscreenable: false,
    webPreferences: {
      ...DEFAULT_WIN_OPTS.webPreferences,
      additionalArguments: DEFAULT_WIN_OPTS.webPreferences?.additionalArguments?.concat([
        '--hide-window-title-bar-help-button',
      ]),
    },
  });
  win.on('closed', () => {
    popupMobileWins.delete(phoneId);
    popupMobilesChange();
  });
  win.on('resized', () => {
    const bounds = win.getBounds();
    cacheMobilePos(`${bounds.x},${bounds.y},${bounds.width},${bounds.height}`, phoneId);
  });
  win.on('moved', () => {
    const bounds = win.getBounds();
    cacheMobilePos(`${bounds.x},${bounds.y},${bounds.width},${bounds.height}`, phoneId);
  });
  try {
    await loadURL(win, url);
    popupMobileWins.set(phoneId, win);
    popupMobilesChange();
  } catch (e) {
    logger.error(e);
  }
}

function getPopupMobilePos(mobileId?: number) {
  if (!mobileId) return null;
  try {
    // 获取数据库中缓存的IP地址
    const stmt = getHyDb()?.conn.prepare(`SELECT pos FROM popup_mobile_pos WHERE mobile_id=?`);
    const posRes = stmt?.get(mobileId) as any;
    return posRes?.pos;
  } catch (e) {
    logger.error('[APP] popup_mobile_pos error', e);
  }
  return null;
}

function cacheMobilePos(pos: string, mobileId?: number) {
  if (!mobileId) return false;
  try {
    const stmt = getHyDb()?.conn.prepare(
      `SELECT count(*) as cnt FROM popup_mobile_pos WHERE mobile_id=?`,
    );
    const row = stmt?.get(mobileId) as any;
    if (row?.cnt > 0) {
      // 更新数据库中的缓存
      const updateStmt = getHyDb()?.conn.prepare(
        `UPDATE popup_mobile_pos SET pos=? WHERE mobile_id=?`,
      );
      updateStmt?.run(pos, mobileId);
    } else {
      // 插入数据库中的缓存
      const insertStmt = getHyDb()?.conn.prepare(
        `INSERT INTO popup_mobile_pos (mobile_id, pos) VALUES (?, ?)`,
      );
      insertStmt?.run(mobileId, pos);
    }
    return true;
  } catch (e) {
    logger.error('[APP] cacheMobilePos error', e);
    return false;
  }
}

export default () => {
  app.whenReady().then(() => {
    /**
     * 获取当前设备连接的手机列表
     * teamId: 当前激活的团队id
     * excludeUdids: string[], 允许排除已经添加的设备(用来添加设备时只列出新设备)
     */
    ipcMain.handle('mobile-list-devices', async (evt, data) => {
      const { teamId, excludeUdids } = data;
      let devices = await androidDeviceManager.listDevices(teamId, excludeUdids);
      return devices;
    });

    /**
     * 获取当前设备连接的ios设备列表
     */
    ipcMain.handle('ios-list-devices', async (evt, data) => {
      const { teamId, excludeUdids } = data;
      let devices = await iosDeviceManager.listDevices(teamId, excludeUdids);
      return devices;
    });

    ipcMain.handle('connect-wifi-device', async (evt, data) => {
      const { host, port, pairCode, pairPort } = data;
      try {
        const mobileDto = await androidDeviceManager.connectWifiDevice(
          host,
          port,
          pairPort,
          pairCode,
        );
        return {
          success: true,
          mobileDto,
        };
      } catch (err: any) {
        return {
          success: false,
          message: err.message,
        };
      }
    });

    ipcMain.handle('get-popup-mobiles', async () => {
      return Array.from(popupMobileWins.keys());
    });

    ipcMain.handle('popup-mobile', async (evt, data) => {
      const { url, phoneId, width = 320, height = 640 } = data;
      await popupMobile(phoneId, url, width, height);
    });
  });
};
