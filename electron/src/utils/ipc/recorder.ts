import { app, desktopCapturer, ipcMain, IpcMainEvent, systemPreferences } from 'electron';
import db from '@e/components/db';
import { isLinuxPlatform, isWin7Platform } from '@e/utils/utils';
import fs from 'fs';
import path from 'path';
import { baseBackendTaskIns } from '@e/components/backendTask';
import {WinUtil} from "@e/helper/win";

export default () => {
  ipcMain.on('get-record-config', (evt) => {
    evt.returnValue = {
      recordBps: db.getDb().get('recordBps').value(),
      recordFps: db.getDb().get('recordFps').value(),
    };
  });

  ipcMain.on('getMediaAccessStatus', async (evt: IpcMainEvent) => {
    if (!isLinuxPlatform()) {
      let accessStatus = await systemPreferences.getMediaAccessStatus('screen');
      evt.returnValue = accessStatus;
    } else {
      //linux 直接返回granted
      evt.returnValue = 'granted';
    }
  });

  /**
   * 屏幕截屏
   */
  ipcMain.on('capture-desktop', async (evt, data) => {
    try {
      const screens = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: { width: 1024, height: 768 },
      });
      const tempDir = fs.mkdtempSync(path.join(app.getPath('temp'), 'hy-'));
      let previewPngs: string[] = [];
      for (let i = 0; i < screens.length; i++) {
        let screen = screens[i];
        const previewPng = path.join(tempDir, `shop_window_preview_${i}.png`);
        fs.writeFileSync(previewPng, screen.thumbnail.toPNG());
        previewPngs.push(previewPng);
      }
      evt.returnValue = {
        success: true,
        previewPngs,
      };
      setTimeout(() => {
        for (let i = 0; i < previewPngs.length; i++) {
          fs.unlink(previewPngs[i], () => {});
        }
        fs.rmdir(tempDir, () => {});
      }, 30000);
    } catch (e) {
      console.error(e);
      evt.returnValue = {
        success: false,
      };
    }
  });
  /**
   * 窗口截屏
   */
  ipcMain.on('capture-browser-window', async (evt, data) => {
    try {
      let { browserTitle, thumbnailSize } = data;
      const tempDir = fs.mkdtempSync(path.join(app.getPath('temp'), 'hy-'));
      const previewPng = path.join(tempDir, 'shop_window_preview.png');
      if (isWin7Platform()) {
        const tempDir = fs.mkdtempSync(path.join(app.getPath('temp'), 'hy-'));
        const previewPng = path.join(tempDir, 'shop_window_preview.png');
        await WinUtil.captureWindow(browserTitle, previewPng);

        if (fs.existsSync(previewPng)) {
          evt.returnValue = {
            success: true,
            previewPng,
          };
        } else {
          evt.returnValue = {
            success: false,
          };
        }
      } else {
        let shopWindow = await desktopCapturer
          .getSources({ types: ['window'], thumbnailSize })
          .then(async (sources) => {
            for (const source of sources) {
              if (source) {
                if (source.name == browserTitle) {
                  return source;
                }
              }
            }
            return undefined;
          });
        if (shopWindow) {
          fs.writeFileSync(previewPng, shopWindow.thumbnail.toPNG());
          evt.returnValue = {
            success: true,
            previewPng,
          };
        } else {
          evt.returnValue = {
            success: false,
          };
        }
      }
      setTimeout(() => {
        fs.unlink(previewPng, () => {});
        fs.rmdir(tempDir, () => {});
      }, 30000);
    } catch (e) {
      console.error(e);
      evt.returnValue = {
        success: false,
      };
    }
  });

  /**
   * 获取会话窗口id
   */
  ipcMain.on('get-shop-browserWinId', async (evt, data) => {
    try {
      let { browserTitle } = data;
      let shopWindow = await desktopCapturer
        .getSources({ types: ['window'] })
        .then(async (sources) => {
          for (const source of sources) {
            if (source) {
              if (source.name.includes(browserTitle)) {
                return source;
              }
            }
          }
          return undefined;
        });
      if (shopWindow) {
        evt.returnValue = {
          success: true,
          browserWinId: shopWindow.id,
        };
      } else {
        evt.returnValue = {
          success: false,
        };
      }
    } catch (e) {
      console.error(e);
      evt.returnValue = {
        success: false,
      };
    }
  });
};
