import { app, ipcMain, screen } from 'electron';
import _ from 'lodash';
import {
  getChromium,
  getShopChromiums,
  getWindowSyncToolboxWin,
  openWindowSyncToolboxModal,
} from '@e/utils/window';
import Downloader from 'nodejs-file-downloader';
import { dispatchMsg } from '@e/utils/ipc';
import { getDynamicPath } from '@e/configs/app';
import { isWinPlatform, resolveUrl } from '@e/utils/utils';
import db from '@e/components/db';
import { backendTaskIns } from '@e/components/backendTask';
import logger from '@e/services/logger';
import { GroupController } from '@e/helper/group_control';
import { hy_actions } from '@e/helper';
import path from 'path';
import fs from 'fs-extra';

const teamSessionsSortOrder: Record<number, number[]> = {};
const teamSessionsSortOrderBy: Record<number, { key: string; ascend: boolean }> = {};

export default () => {
  app.whenReady().then(() => {
    ipcMain.handle('browser.sync.list', async (evt, data) => {
      const chromiums = getShopChromiums();
      let list: any = [];
      if (chromiums && Object.values(chromiums).length > 0) {
        Object.values(chromiums)
          .filter((session) => {
            if (!session) return false;
            if (data?.teamId) {
              return session.shopInfo?.teamId === data.teamId;
            }
            return true;
          })
          .forEach((session) => {
            list.push({
              teamId: session?.shopInfo?.teamId,
              sessionId: session?.sessionId,
              rpaFlowId: session?.rpaFlowId,
              rpaTaskId: session?.rpaTaskId,
              rpaPreview: session?.rpaPreview,
              shop: {
                id: session?.shopInfo.id,
                name: session?.shopInfo.name,
                description: session?.shopInfo.description,
                platform: session?.shopInfo.platform,
                stateless: session?.shopInfo.stateless,
              },
              startTime: session?.recordInfo?.sessionStartTime,
            });
          });
      }
      if (data?.teamId && teamSessionsSortOrder[data.teamId]) {
        const orderArr = teamSessionsSortOrder[data.teamId];
        list = list.sort((a: any, b: any) => {
          const aOrder = orderArr.indexOf(a.sessionId) === -1 ? 999 : orderArr.indexOf(a.sessionId);
          const bOrder = orderArr.indexOf(b.sessionId) === -1 ? 999 : orderArr.indexOf(b.sessionId);
          return aOrder - bOrder;
        });
      }
      return list;
    });
  });
  ipcMain.handle('browser.sync.update.order', async (evt, data) => {
    if (data.teamId) {
      teamSessionsSortOrder[data.teamId] = data.order ?? [];
      teamSessionsSortOrderBy[data.teamId] = data.orderBy ?? { key: 'startTime', ascend: true };
      if (getWindowSyncToolboxWin()) {
        dispatchMsg(
          hy_actions.EVENT_GC_DETAIL_UPDATE,
          {
            sessionsSortOrder: teamSessionsSortOrder[data.teamId],
            sessionsSortOrderBy: teamSessionsSortOrderBy[data.teamId],
          },
          getWindowSyncToolboxWin(),
        );
      }
      dispatchMsg(hy_actions.EVENT_GC_DETAIL_UPDATE, {
        sessionsSortOrder: teamSessionsSortOrder[data.teamId],
        sessionsSortOrderBy: teamSessionsSortOrderBy[data.teamId],
      });
    }
  });
  ipcMain.handle('browser.sync.size', async (evt) => {
    return screen.getPrimaryDisplay().workAreaSize;
  });
  ipcMain.handle('browser.sync.align', async (evt, data) => {
    const chromiums = getShopChromiums();
    const { boundsMap, delay = 0, sortedShopIds } = data;
    let gc: GroupController | undefined = undefined;
    if (isWinPlatform()) {
      gc = (await backendTaskIns.getGCHelper()).gc;
      gc.skipBrowserWindowSizeCheck = true;
    }
    try {
      const shopIds = sortedShopIds || Object.keys(boundsMap);
      for (const shopId of shopIds) {
        const bounds = boundsMap[shopId];
        // @ts-ignore
        const session = chromiums[shopId];
        if (session && bounds) {
          session.bringToFront(bounds);
          await new Promise((resolve) => {
            setTimeout(() => resolve(true), delay);
          });
        }
      }
    } catch (e) {
      logger.error('[APP] browser.sync.align error', e);
      throw e;
    } finally {
      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });
      if (isWinPlatform() && gc) {
        gc.skipBrowserWindowSizeCheck = false;
      }
    }
  });
  ipcMain.handle('browser.sync.minimizeWin', async (evt, data) => {
    for (const sessionId of data) {
      const session = getChromium({ sessionId });
      if (session) {
        session.handleCmd('update-window', { state: 'minimized' });
      }
    }
  });
  ipcMain.handle('browser.sync.restoreWin', async (evt, data) => {
    for (const sessionId of data) {
      const session = getChromium({ sessionId });
      if (session) {
        session.bringToFront();
      }
    }
  });
  ipcMain.handle('browser.sync.focus', async (evt, data: any) => {
    const chromiums = getShopChromiums();
    for (const shopId of data) {
      const session = chromiums[shopId];
      if (session) {
        session.bringToFront();
      }
    }
  });
  ipcMain.handle('browser.sync.close', async (evt, data: any) => {
    const chromiums = getShopChromiums();
    for (const shopId of data) {
      const session = chromiums[shopId];
      if (session) {
        await session.close();
      }
    }
  });
  ipcMain.handle('browser.sync.download', async (evt, data: any[]) => {
    const { DATA_DIR } = getDynamicPath();
    await backendTaskIns.stopGcHelper();
    try {
      const md5Path = path.join(DATA_DIR, 'GCHelper.exe.md5sum');
      const latestMd5Path = path.join(DATA_DIR, 'GCHelper.exe.md5sum.latest');
      if (fs.existsSync(latestMd5Path)) {
        fs.move(latestMd5Path, md5Path, { overwrite: true });
      }
      await new Downloader({
        url: resolveUrl(
          process.env.NODE_ENV === 'development' ? db.getPortalUrl() : db.getDlUrl(),
          '/downloads/GCHelper.exe',
        ),
        fileName: 'GCHelper.exe',
        cloneFiles: false,
        directory: DATA_DIR,
        timeout: 5 * 60 * 1000,
        maxAttempts: 3,
        onProgress: _.throttle(
          (percentage: string, chunk: object, remainingSize: number) => {
            dispatchMsg('browser.sync.download.progress', { status: 'downloading', percentage });
          },
          500,
          { trailing: true },
        ),
        onError(e: Error) {
          dispatchMsg('browser.sync.download.progress', { status: 'error', message: e.message });
        },
      }).download();
      dispatchMsg('browser.sync.download.progress', { status: 'downloaded', percentage: '100.00' });
    } catch (e: any) {
      logger.error('[APP] download GCHelper.exe.md5sum failed', e);
      dispatchMsg('browser.sync.download.progress', { status: 'error', message: e.message });
    }
  });
  ipcMain.handle('browser.sync.open.toolbox', async (evt, data: any) => {
    openWindowSyncToolboxModal(data?.teamId);
  });
};
