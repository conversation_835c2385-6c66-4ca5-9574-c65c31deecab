// 首页登录框密码代填
import { app, ipcMain } from 'electron';
import { hyDecrypt, hyEncrypt } from '@e/utils/crypto';
import db from '@e/components/db';

type AutoFillItem = {
  username: string;
  password?: string;
  country?: string;
  passwordLength?: number;
  createTime: number;
  updateTime: number;
};
export default () => {
  app.whenReady().then(() => {
    ipcMain.handle('autofill/update', async (evt, data) => {
      const { autofill } = db.getDb().get('sysPres').value();
      if (autofill === 'none' || !data) {
        return;
      }
      const { type, username, country } = data;
      if (!username || !type) {
        return;
      }
      let password = data.password;
      if (autofill === 'username') {
        password = undefined;
      }
      const map: Record<string, AutoFillItem> = db.getDb().get(`autofill.${type}`).value() || {};
      if (!map[username]) {
        map[username] = {
          username,
          country,
          createTime: Date.now(),
          updateTime: Date.now(),
        };
        if (password) {
          // 没有密码的
          map[username].password = hyEncrypt(password);
          map[username].passwordLength = password.length;
        }
      } else {
        // 有过记录,有才更新
        if (password || country) {
          // 判断一下password,country 是否都没变化,如果都没有,则不更新
          if (password) {
            if (!map[username].password) {
              map[username].updateTime = Date.now();
              map[username].password = hyEncrypt(password);
              map[username].passwordLength = password.length;
            } else if (hyDecrypt(map[username].password) !== password) {
              map[username].updateTime = Date.now();
              map[username].password = hyEncrypt(password);
              map[username].passwordLength = password.length;
            }
          }
          if (map[username].country !== country) {
            map[username].updateTime = Date.now();
            map[username].country = country;
          }
        }
      }
      db.getDb().set(`autofill.${type}`, map).write();
    });

    ipcMain.handle('autofill/remove', async (evt, data) => {
      if (!data) {
        return;
      }
      const { type, username } = data;
      if (!type || !username) {
        return;
      }
      const map: Record<string, AutoFillItem> = db.getDb().get(`autofill.${type}`).value() || {};
      delete map[username];
      db.getDb().set(`autofill.${type}`, map).write();
    });
    ipcMain.handle('autofill/list', async (evt, data) => {
      const autofill = db.getDb().get('sysPres.autofill').value();
      const { type } = data;
      const map: Record<string, AutoFillItem> = db.getDb().get(`autofill.${type}`).value() || {};
      if (autofill === 'none') {
        return [];
      }
      return Object.values(map).map((item) => {
        if (autofill === 'username') {
          delete item.password;
          delete item.passwordLength;
        }
        return item;
      });
    });
    ipcMain.handle('autofill/decrypt', (evt, data) => {
      const { password } = data;
      if (password) {
        return hyDecrypt(password);
      }
      return undefined;
    });
    ipcMain.handle('autofill/clear', (evt, data) => {
      const autofill = db.getDb().get('sysPres.autofill').value();
      if (autofill === 'none') {
        // 清理全部
        db.getDb().set(`autofill`, {}).write();
      } else if (autofill === 'username') {
        // 清理密码
        const autofills = db.getDb().get(`autofill`).value();
        Object.keys(autofills).forEach((type) => {
          const map: Record<string, AutoFillItem> =
            db.getDb().get(`autofill.${type}`).value() || {};
          Object.values(map).forEach((item) => {
            delete item.password;
            delete item.passwordLength;
          });
          db.getDb().set(`autofill.${type}`, map).write();
        });
      }
      return;
    });
  });
};
