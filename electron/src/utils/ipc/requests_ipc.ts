import {app, ipcMain, IpcMainEvent} from "electron";
import request from "@e/services/request";
import {getAjaxEventClientIns} from "@e/components/backendTask";

export default ()=>{

  app.whenReady().then(()=>{
    ipcMain.on('send-request', async (evt: IpcMainEvent, data) => {
      try {
        let ret = await request(data.path, data.options);
        evt.returnValue = ret;
      } catch (e) {
        evt.returnValue = 'error';
      }
    });

    ipcMain.on('onAjaxEvent', async (evt: IpcMainEvent, data) => {
      let ajaxEventClient = getAjaxEventClientIns();
      let hd = await ajaxEventClient?.on(data.name, data.resId, (ajaxEvt) => {
        evt.sender.send('onAjaxEvent-cb', {
          name: data.name,
          resId: data.resId,
          data: ajaxEvt,
        });
      });
      evt.returnValue = hd;
    });
    ipcMain.on('unAjaxEvent', (evt: IpcMainEvent, hd) => {
      let ajaxEventClient = getAjaxEventClientIns();
      ajaxEventClient?.un(hd);
    });
  });

};
