import { ipcMain } from 'electron';
import { getChromium, getRpaFlowPreviewWindow, getRpaSelectorPickerWindow } from '@e/utils/window';
import {
  dispatchEvent,
  previewTask,
  runDebugCommand,
  stopExecTask,
  stopPreview,
} from '@e/rpa/task_schedule';
import { addEventListener, dispatchMsg } from '@e/utils/ipc';
import { Chromium } from '@e/utils/chromium';
import { checkEmailServer } from '@e/utils/email';
import i18n from '@e/utils/i18n';

const rpaEditingElementMap = new Map<string, any>();

export default () => {
  // 预览RPA流程
  ipcMain.handle('preview-rpa-flow', async (evt, data) => {
    const { teamId, rpaFlowId, mobileId, config, options } = data;
    let shopId: number | undefined;
    if (!mobileId) {
      const chromium = getChromium({ rpaFlowId });
      if (!chromium) {
        return {
          success: false,
          message: i18n.t('未找到浏览器实例，无法进行预览'),
        };
      }
      shopId = chromium.shopInfo.id;
    } else {
      shopId = mobileId;
    }
    try {
      await previewTask(teamId, rpaFlowId, shopId!, config, options);
      return { success: true };
    } catch (e: any) {
      if (typeof e === 'string') {
        return { success: false, message: e };
      }
      return { success: false, message: e.message, nId: e.nId };
    }
  });

  // 停止预览RPA流程
  ipcMain.handle('stop-preview-rpa-flow', (evt, data) => {
    const { rpaFlowId } = data;
    stopPreview(rpaFlowId);
  });

  // debug 指令
  ipcMain.handle('run-debug-command', async (evt, data) => {
    const { rpaFlowId, command = '', args = [] } = data;
    try {
      return await runDebugCommand(rpaFlowId, command, args);
    } catch (e: any) {
      return new Error(e?.message ?? '执行命令出错');
    }
  });

  // 停止执行 rap 任务
  ipcMain.handle('stop-exec-rpa-task', async (evt, data) => {
    const { teamId, rpaTaskId } = data;
    return await stopExecTask(teamId, rpaTaskId);
  });

  // 是否打开了流程定义编辑窗口
  ipcMain.handle('is-flow-editing', (evt, data) => {
    const { rpaFlowId } = data;
    const win = getRpaFlowPreviewWindow(rpaFlowId);
    return !!win;
  });

  // 是否打开流程定义的元素捕获窗口
  ipcMain.handle('is-rpa-selector-picker-window-opened', async (evt, data) => {
    const { flowId } = data;
    let win = getRpaSelectorPickerWindow(flowId);
    if (win) {
      win.focus();
    }
    return !!win;
  });

  // 设置当前正在编辑的元素
  ipcMain.handle('set-rpa-editing-element', async (evt, data) => {
    const { flowId, element, opt } = data;
    rpaEditingElementMap.set(flowId, { element, opt });
    let win = getRpaSelectorPickerWindow(flowId);
    if (win) {
      win.webContents.send('rpa-editing-element-change', { element, opt });
    }
  });

  // 获取当前正在编辑的元素
  ipcMain.handle('get-rpa-editing-element', async (evt, data) => {
    const { flowId } = data;
    const element = rpaEditingElementMap.get(flowId);
    rpaEditingElementMap.delete(flowId);
    return element;
  });

  // 选择元素
  addEventListener('rpa-find-selector', (evt, data) => {
    const { rpaFlowId, active = true, ...otherProps } = data;
    const chromium = getChromium({ rpaFlowId });
    if (chromium) {
      if (active) {
        chromium.bringToFront();
        chromium.inspectElement(otherProps);
      } else {
        chromium.stopInspectElement();
      }
      // dispatcherMsgToBrowserWs(chromium, 'rpa-find-selector', { active, ...otherProps });
    }
  });

  // 预览元素
  addEventListener('rpa-preview-element', async (evt, data) => {
    const { element, opt } = data;
    const chromium = getChromium({ rpaFlowId: opt.rpaFlowId });
    if (chromium) {
      // await chromium.bringToFront();
      chromium.previewElementSelector(element, opt);
    }
  });

  // 获取关联元素
  addEventListener('rpa-get-nearby-element', async (evt, data) => {
    const { element, opt } = data;
    const chromium = getChromium({ rpaFlowId: opt.rpaFlowId });
    if (chromium) {
      chromium.getNearbyElements(element, opt);
    }
  });

  // 设置容器元素
  addEventListener('set-container-element', async (evt, data) => {
    const { rpaFlowId, containerElement, index } = data;
    const chromium = getChromium({ rpaFlowId });
    if (chromium) {
      chromium.setContainer(containerElement, index);
    }
  });

  // 保存元素
  addEventListener('rpa-save-element', async (evt, data) => {
    const { rpaFlowId, element } = data;
    const win = getRpaFlowPreviewWindow(rpaFlowId);
    if (win) {
      dispatchMsg('rpa-save-element', { element }, win);
      win.show();
    }
  });

  // 录屏
  addEventListener('rpa-recorder', (evt, data) => {
    const { rpaFlowId, active = true, ...otherProps } = data;
    const chromium = getChromium({ rpaFlowId });
    if (chromium) {
      if (active) {
        chromium.bringToFront();
      }
      active ? chromium.browserActionRecorder?.resume() : chromium.browserActionRecorder?.pause();
    }
  });

  // 控制台事件
  ipcMain.handle('dispatch-rpa-event', async (evt, data) => {
    const { rpaFlowId, ...otherProps } = data;
    try {
      await dispatchEvent(rpaFlowId, otherProps);
      return true;
    } catch (e: any) {
      return e.message;
    }
  });

  // 检测用户自行设置的邮件服务器是否可用
  ipcMain.handle('check-mail-server', async (evt, data) => {
    const { host, port, secure, user, pass, proxy_mode, proxyRules } = data;
    let result = await checkEmailServer({
      host,
      port,
      secure,
      auth: { user, pass },
      proxy_mode,
      proxyRules,
    });
    return result;
  });

  ipcMain.handle('is-rpa-flow-preview-window-open', async (evt, data) => {
    const { rpaFlowId, bringUpIfOpening } = data;
    const win = getRpaFlowPreviewWindow(rpaFlowId);
    const opening = !!win;
    if (opening && bringUpIfOpening) {
      win?.show();
    }
    return opening;
  });
};

function dispatcherMsgToBrowserWs(chromium: Chromium | null, action: string, data = {}) {
  if (chromium && chromium.wsDispatcher) {
    chromium.wsDispatcher.dispatcher(JSON.stringify({ action, data }));
  }
}
