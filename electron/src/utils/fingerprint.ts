import _ from 'lodash';
// import moment from 'moment-timezone';
import uaParser from 'ua-parser-js';
import os from 'os';
import path from 'path';

import appConfig, { getDynamicPath } from '../configs/app';
import { getBrowserFromUA, getBrowserFullName, getOsName } from './userAgent';
import { getFingerprintExcludeFonts, getFingerprintForceFonts } from '@e/utils/fonts';
import { transBrowserPlatformToNodePlatform } from '@e/utils/utils';
import { getFontOffset } from '../../../src/utils/fingerprint/fonts';
import { Device } from 'donkey-puppeteer-core';
import { getBrowserBrands } from '@e/utils/brands';
import { ShopInfo } from '@e/types';
import {
  androidVoices,
  chromeVoices,
  edgeVoices,
  iosVoices,
  linuxVoices,
  macVoices,
  winVoices,
} from '@e/utils/speechVoices';
import i18n from '@e/utils/i18n';
import { app } from 'electron';

export enum FingerType {
  Disabled = 'Disabled', //禁止
  Assign = 'Assign', //指定值
  Auto = 'Auto', //自动，例如时区可以由ip位置来自动计算出，如果当前属性没法自动获取则会变成 Original
  Original = 'Original', //使用浏览器真实值
}

export type IP = {
  address?: string;
  timezone?: string;
  locale?: string;
  latitude?: number;
  longitude?: number;
  forbiddenLongLatitude?: boolean;
};

type BrowserMedia = {
  deviceId: string;
  groupId: string;
  label: string;
};

type BrowserPlugin = {
  filename: string;
  name: string;
  description: string;
  version: string;
};

// 启动参数
type ARGS_PARAMS = {
  'user-agent'?: string;
  'enable-logging'?: string;
  v?: string;
  'log-file'?: string;
};

// 环境变量
type ENV_PARAMS = {
  'donkey-navigator-useragent'?: string; // navigator.userAgent
  'donkey-intl-locale'?: string; //donkey-intl-locale
  'donkey-lang'?: string; // zh-CN,en
  'donkey-ac-lang'?: string; // zh-CN;q=0.9,en;q=0.8
  'donkey-webrtc-host'?: string;
  'donkey-webrtc-host-uuid'?: string;
  'donkey-navigator-platform'?: string;
  'donkey-brand'?: string;
  'donkey-brand-fullVersion'?: string;
  'donkey-uad-platform'?: string; //uad 是 userAgentData 的缩写
  'donkey-uah-mobile'?: string;
  'donkey-uah-model'?: string;
  'donkey-uah-platform'?: string; //uah 是 userAgentHeader 的缩写
  'donkey-uah-platformVersion'?: string;
  'donkey-uah-uaFullVersion'?: string;
  'donkey-uah-architecture': string;
  'donkey-uah-bitness'?: string;
  'donkey-webgl-vendor'?: string;
  'donkey-webgl-renderer'?: string;
  'donkey-gpu-vendor'?: string;
  'donkey-gpu-architecture'?: string;
  'donkey-timezone-offset-minutes'?: number;
  'donkey-canvas-tx'?: number;
  'donkey-webgl-dx'?: number;
  'donkey-rect-dx'?: number;
  'donkey-audio-offset'?: number;
  'donkey-screen-dpr'?: number;
  'donkey-screen-width'?: number;
  'donkey-screen-avail-width'?: number;
  'donkey-screen-height'?: number;
  'donkey-screen-avail-height'?: number;
  'donkey-color-depth'?: number;
  'donkey-battery-charging'?: number;
  'donkey-battery-charging-time'?: number;
  'donkey-battery-discharging-time'?: number;
  'donkey-battery-level'?: number;
  'donkey-fonts-excludes'?: string;
  'donkey-fonts-force'?: string;
  'donkey-navigator-cpu-num'?: number;
  'donkey-navigator-mem-num'?: number;
  'donkey-shop-title'?: string;
  'donkey-allow-developer-tools'?: boolean;
  'donkey-server-port'?: number;
  'donkey-allow-extensions'?: any;
  'donkey-geo-disabled'?: boolean;
  'donkey-geo-latitude'?: number;
  'donkey-geo-longitude'?: number;
  'donkey-timezone'?: string;
  'donkey-geo-accuracy'?: number;
  'donkey-block-media'?: number;
  'donkey-pl-title'?: string;
  'donkey-pl-url'?: string;
  'donkey-pl-domain'?: string;
  'donkey-shop-code'?: string; //店铺任务栏图标显示的标识，[a-zA-Z0-9]{1,4}，为空则不显示
  'donkey-shop-code-color'?: number; //店铺任务栏图标显示的标识背景颜色rgb值，默认 0xee8717
  'donkey-navigator-bluetooth'?: number; //控制 navigator.bluetooth.getAvailability() 返回值，0表示false，非0表示true，不传递表示不控制
  'donkey-webgl-mts'?: number; //控制webgl Max Texture Size的值，为空表示不控制
  'dk-voices'?: string;
};

export type FingerprintDetail = {
  brands: string;
} & API.FingerprintConfigVo;

export type FingerprintConfigVo = FingerprintDetail & {
  ip: IP;
  shopName: string;
  allowExtension: boolean;
  sessionId: number;
  isBlockVideo: boolean;
  shopInfo: ShopInfo;
};

/**
 * 根据指纹配置信息，生成浏览器启动参数与环境变量
 */
export default class FingerprintConfig {
  config: FingerprintConfigVo;
  devtools: boolean | undefined;
  shopCode: string | undefined;
  isIOS: boolean;
  isAndroid: boolean;
  isMobile: boolean;
  constructor(config: FingerprintConfigVo) {
    const { shopInfo } = config;
    this.config = config;
    this.devtools = undefined;
    this.shopCode = shopInfo.markCode ? shopInfo.markCode : undefined;
    const uaParserRes = uaParser(config.userAgent);
    this.isIOS = uaParserRes.os.name === 'iOS';
    this.isAndroid = /Android/.test(uaParserRes.os.name || '');
    this.isMobile = this.isIOS || this.isAndroid;
  }

  ignoreDefaultArgs() {}

  /**
   * 获取浏览器启动参数
   */
  getArgs() {
    const { config } = this;
    const { LOG_DIR } = getDynamicPath();
    const args: ARGS_PARAMS = {
      'user-agent': this.getUserAgent(),
      'enable-logging': appConfig.BROWSER_DEBUG ? '' : undefined,
      v: appConfig.BROWSER_DEBUG ? '1' : undefined,
      'log-file': appConfig.BROWSER_DEBUG
        ? path.join(LOG_DIR, `browser-${config.sessionId}.log`)
        : undefined,
    };
    const res: any = {};
    _.forEach(args, (v, k) => {
      if (!_.isUndefined(v)) {
        res[k] = v;
      }
    });
    return _.map(res, (v, k) => (v ? `--${k}=${v}` : `--${k}`));
  }

  getUserAgent() {
    return FingerprintConfig.privacyUserAgent(this.config.userAgent);
  }

  getLangArr() {
    const { config } = this;
    let langArr = (config.lang?.split(',') ?? []).filter((v) => !!v);
    if (langArr.length === 0) {
      langArr = ['Auto'];
    }
    if (!config.ip) {
      langArr = langArr.filter((l) => l !== 'Auto');
    } else {
      const idx = langArr.findIndex((l) => l === 'Auto');
      if (idx !== -1) {
        langArr[idx] = config.ip.locale ?? 'en';
        if (/\w+-\w+/.test(langArr[idx])) {
          const code = langArr[idx].split('-')[0];
          langArr.splice(idx + 1, 0, code);
        }
      }
    }
    langArr = _.uniq(langArr);
    if (langArr.indexOf('en') != -1 && langArr.indexOf('en-US') == -1) {
      langArr.splice(langArr.indexOf('en'), 0, 'en-US');
    }
    return langArr;
  }

  /**
   * 获取浏览器环境变量
   */
  getEnv(): { [k: string]: any } {
    const { config } = this;
    const { shopInfo } = config;
    const uaParserRes = uaParser(config.userAgent);
    const { browser, engine, device } = uaParserRes;
    if (browser.name == 'Chrome WebView') {
      browser.name = 'Chrome';
    }
    const browserVersionNum = parseInt(engine.version ?? '99');
    const brandsInfo = getBrowserBrands(browserVersionNum);
    const isIOS = this.isIOS;
    const isAndroid = this.isAndroid;
    const isMobile = this.isMobile;
    let langArr = this.getLangArr();
    let gpuVendor = 'Intel';
    if (isAndroid) {
      //数据库里很多android指纹的webglRenderer和webglVendor写反了
      if (config.webglRenderer == 'Qualcomm') {
        let tempVendor = (gpuVendor = config.webglRenderer);
        config.webglRenderer = config.webglVendor;
        config.webglVendor = tempVendor;
      }
    }
    (() => {
      let webglRenderer = config.webglRenderer;
      if (webglRenderer) {
        webglRenderer = webglRenderer.toLowerCase();
        if (webglRenderer.indexOf('apple') != -1) {
          gpuVendor = 'Apple';
        } else if (webglRenderer.indexOf('amd') != -1) {
          gpuVendor = 'AMD';
        } else if (webglRenderer.indexOf('nvidia') != -1) {
          gpuVendor = 'NVIDIA';
        }
      }
    })();
    const env: ENV_PARAMS = {
      'donkey-navigator-useragent': this.getUserAgent(),
      'donkey-intl-locale': (() => {
        return langArr.length > 0 ? langArr[0] : undefined;
      })(),
      'donkey-lang': (() => {
        return langArr.length > 0 ? langArr.join(',') : undefined;
      })(),
      'donkey-ac-lang': (() => {
        // zh-CN;q=0.9,en;q=0.8
        return langArr.length > 0
          ? langArr
              .map((lang, idx) => {
                return `${lang};q=0.${Math.max(1, 9 - idx)}`;
              })
              .join(',')
          : undefined;
      })(),
      // webrtc出口ip
      'donkey-webrtc-host': (() => {
        switch (config.webrtcPublicIp_type) {
          case FingerType.Assign:
            return config.webrtcPublicIp;
          case FingerType.Disabled:
          case FingerType.Auto:
            return config.ip?.address;
        }
      })(),
      // webrtc-host的uuid
      'donkey-webrtc-host-uuid': (() => {
        if (config.webrtcInnerIp_type === FingerType.Assign) return config.uuid;
      })(),
      // 浏览器平台
      'donkey-navigator-platform': (() => {
        if (isAndroid && browserVersionNum >= 118) {
          return 'Linux aarch64';
        }
        if (isIOS) {
          return 'iPhone';
        }
        return config.platform;
      })(),
      // navigator.userAgentData.brands
      'donkey-brand': (() => {
        let brands = {
          brand: `"${brandsInfo.notABrand}";v="${brandsInfo.brandVersion}"`,
          chrome: `"${getBrowserFullName(browser.name)}";v="${browserVersionNum}"`,
          chromium: `"Chromium";v="${browserVersionNum}"`,
        };
        //@ts-ignore
        return _.map(brandsInfo.brandsOrder, (brand) => brands[brand]).join(', ');
      })(),
      'donkey-brand-fullVersion': (() => {
        let brands = {
          brand: `"${brandsInfo.notABrand}";v="${brandsInfo.brandFullVersion}"`,
          chrome: `"${getBrowserFullName(browser.name)}";v="${browser.version}"`,
          chromium: `"Chromium";v="${engine.version}"`,
        };
        //@ts-ignore
        return _.map(brandsInfo.brandsOrder, (brand) => brands[brand]).join(', ');
      })(),
      //uad 是 userAgentData 的缩写
      'donkey-uah-mobile': isMobile ? 'true' : undefined,
      'donkey-uah-model': (() => {
        if (isMobile && uaParserRes.device?.model) {
          return uaParserRes.device?.model;
        }
        return undefined;
      })(),
      'donkey-uad-platform':
        parseInt(getBrowserFromUA(config.userAgent).version ?? '99') >= 93
          ? getOsName(config.userAgent)
          : undefined,
      'donkey-uah-platform': getOsName(config.userAgent), //uah 是 userAgentHeader 的缩写
      'donkey-uah-platformVersion': uaParserRes.os.version
        ? getBrowserFullVersion(uaParserRes.os.version)
        : undefined,
      'donkey-uah-uaFullVersion': browser.version,
      'donkey-uah-architecture': (() => {
        if (isAndroid) {
          return '';
        }
        if (isIOS) {
          return 'arm';
        }
        return config.webglVendor?.includes('Apple') ? 'arm' : 'x86';
      })(),
      'donkey-uah-bitness': (() => {
        if (isMobile) {
          return '';
        }
        if (browserVersionNum >= 93) {
          return '64';
        }
        return undefined;
      })(),
      'donkey-webgl-vendor': (() => {
        if (config.webgl_type === FingerType.Assign) {
          if (isIOS) {
            return 'Apple Inc.';
          }
          let kh = gpuVendor;
          if ('Intel' === kh) {
            kh = 'Intel Inc.';
          }
          return `Google Inc. (${kh})`;
        }
      })(),
      'donkey-webgl-renderer': (() => {
        if (config.webgl_type === FingerType.Assign) {
          if (isIOS) {
            return 'Apple GPU';
          }
          return config.webglRenderer;
        }
      })(),
      'donkey-gpu-vendor': (() => {
        // const adapter = await navigator.gpu.requestAdapter();
        // const adapterInfo = await adapter.requestAdapterInfo();
        if (config.webgl_type === FingerType.Assign) {
          if (isIOS) {
            return 'apple';
          }
          return gpuVendor.toLowerCase();
        }
        return undefined;
      })(),
      'donkey-gpu-architecture': (() => {
        // 验证方式与 donkey-gpu-vendor 一样
        if (config.webgl_type === FingerType.Assign) {
          if (isIOS) {
            return 'common-3';
          }
          switch (gpuVendor.toLowerCase()) {
            case 'apple':
              return 'common-3';
            default:
              return 'gen-9';
          }
        }
        return undefined;
      })(),
      // 时区偏移量
      // 'donkey-timezone-offset-minutes': (() => {
      //   switch (config.timezone_type) {
      //     case FingerType.Assign:
      //       return -moment.tz(config.timezone ?? 'Asia/Shanghai').utcOffset();
      //     case FingerType.Auto:
      //       return -moment.tz(config.ip?.timezone ?? 'Asia/Shanghai').utcOffset();
      //   }
      // })(),
      // canvas指纹偏移量
      'donkey-canvas-tx': (() => {
        return !config.canvasTX ? undefined : config.canvasTX;
      })(),
      'donkey-webgl-dx': !config.webglDX ? undefined : config.webglDX,
      'donkey-rect-dx': !config.rectDX ? undefined : config.rectDX,
      'donkey-audio-offset': config.audio === 0 ? undefined : config.audio,
      'donkey-screen-dpr': !config.dpr ? undefined : Math.round(config.dpr * 10) / 10,
      'donkey-screen-width': (() => {
        if (isMobile) {
          return parseInt((config.screenSize ?? '360x800').split('x')[0]);
        }
        return config.screenSize_type === FingerType.Assign && config.screenSize
          ? parseInt(config.screenSize?.split('x')[0])
          : undefined;
      })(),
      'donkey-screen-avail-width': (() => {
        if (isMobile) {
          return Math.max(1, parseInt((config.screenSize ?? '360x800').split('x')[0]) - 5);
        }
        return config.screenSize_type === FingerType.Assign && config.screenSize
          ? Math.max(1, parseInt(config.screenSize.split('x')[0]) - 5)
          : undefined;
      })(),
      'donkey-screen-height': (() => {
        if (isMobile) {
          return parseInt((config.screenSize ?? '360x800').split('x')[1]);
        }
        return config.screenSize_type === FingerType.Assign && config.screenSize
          ? parseInt(config.screenSize.split('x')[1])
          : undefined;
      })(),
      'donkey-screen-avail-height': (() => {
        if (isMobile) {
          return Math.max(1, parseInt((config.screenSize ?? '360x800').split('x')[1]) - 40);
        }
        return config.screenSize_type === FingerType.Assign && config.screenSize
          ? Math.max(1, parseInt(config.screenSize.split('x')[1]) - 40)
          : undefined;
      })(),
      'donkey-color-depth':
        config.colorDepth && [8, 16, 24, 32].indexOf(config.colorDepth) != -1
          ? config.colorDepth
          : undefined,
      // 是否正在充电
      'donkey-battery-charging':
        config.batteryType === FingerType.Assign ? Number(config.batteryCharging) : undefined,
      // 还有多久电池充满
      'donkey-battery-charging-time':
        config.batteryType === FingerType.Assign ? config.batteryChargingTime : undefined,
      // 还可以用多久
      'donkey-battery-discharging-time':
        config.batteryType === FingerType.Assign ? config.batteryDischargingTime : undefined,
      // 电池百分比
      'donkey-battery-level': FingerType.Assign ? config.batteryLevel : undefined,
      // 当前浏览器不支持哪些字体
      'donkey-fonts-excludes': config.fonts
        ? getFingerprintExcludeFonts(config.platform!, config.fonts)
        : undefined,
      // 强制替换字体
      'donkey-fonts-force': (() => {
        const nodePlatform = transBrowserPlatformToNodePlatform(config.platform!);
        if (os.platform() !== nodePlatform) {
          let fontListStr = getFingerprintForceFonts(config.platform!);
          return fontListStr
            .split(',')
            .map((f) => `${f}=${getFontOffset(f.replace(/"/g, ''))}`)
            .join(',');
        }
        return undefined;
      })(),
      'donkey-navigator-cpu-num': config.cpu === 0 ? undefined : config.cpu,
      'donkey-navigator-mem-num': (() => {
        if (config.mem === 0) {
          return undefined;
        }
        // Edge 浏览器 4GB pixelscan 检测无法通过
        if (browser.name === 'Edge') {
          return 8;
        }
        //内存设置过小容易出问题
        return Math.max(4, config.mem ?? 8);
      })(),
      'donkey-shop-title': `${config.shopName}-${
        appConfig.isOEM ? app.getName() : i18n.t('花漾灵动')
      }-${config.sessionId}`,
      'donkey-allow-developer-tools': this.devtools,
      'donkey-allow-extensions': config.allowExtension ? true : undefined,
      'donkey-geo-disabled': (() => {
        if (config.location_type === 'Disabled') return true;
        if (config.location_type === 'Auto' && config.ip.forbiddenLongLatitude) return true;
        return undefined;
      })(),
      'donkey-geo-latitude': (() => {
        if (config.location_type === 'Auto') {
          if (config.ip.forbiddenLongLatitude) return undefined;
          return config.ip.latitude || undefined;
        }
        if (config.location_type === 'Assign') {
          return (config.location || '').split(',')[1]
            ? Number((config.location || '').split(',')[1])
            : undefined;
        }
        return undefined;
      })(),
      'donkey-geo-longitude': (() => {
        if (config.location_type === 'Auto') {
          if (config.ip.forbiddenLongLatitude) return undefined;
          return config.ip.longitude || undefined;
        }
        if (config.location_type === 'Assign') {
          return (config.location || '').split(',')[0]
            ? Number((config.location || '').split(',')[0])
            : undefined;
        }
        return undefined;
      })(),
      'donkey-timezone': this.getTimezone(),
      'donkey-geo-accuracy': 100,
      'donkey-block-media': config.isBlockVideo ? 1 : undefined,
      'donkey-pl-title': (() => {
        //需要保证 donkey-pl-domain 有值的时候该项才返回值
        if (shopInfo.privateDomains && shopInfo.privateDomains?.length > 0) {
          return shopInfo.privateTitle ?? '私密标题';
        }
        return undefined;
      })(),
      'donkey-pl-url': (() => {
        //需要保证 donkey-pl-domain 有值的时候该项才返回值
        if (shopInfo.privateDomains && shopInfo.privateDomains?.length > 0) {
          return shopInfo.privateAddress ?? '私密链接';
        }
        return undefined;
      })(),
      'donkey-pl-domain': (() => {
        //以逗号分隔开的域名列表
        if (shopInfo.privateDomains && shopInfo.privateDomains?.length > 0) {
          return shopInfo.privateDomains.join(',');
        }
        return undefined;
      })(),
      'donkey-navigator-bluetooth': isMobile ? 1 : undefined,
      'donkey-webgl-mts': isMobile ? 8192 : undefined,
      'donkey-shop-code': this.shopCode,
      'donkey-shop-code-color': _.isNumber(shopInfo.markCodeBg) ? shopInfo.markCodeBg : 0xee8717,
      'dk-voices': (() => {
        let voices = this.getVoices().map((v) => {
          return {
            d: v.default,
            l: v.lang,
            ls: v.localService,
            n: v.name,
          };
        });
        return JSON.stringify(voices);
      })(),
    };
    const res: any = {};
    _.forEach(env, (v, k) => {
      if (!_.isUndefined(v)) {
        res[k] = v;
      }
    });
    return res;
  }

  getVoices() {
    let langArr = this.getLangArr();
    let intlLocale = langArr.length > 0 ? langArr[0] : undefined;
    let platform = this.config.platform ?? 'Win32';
    if (this.isAndroid) {
      platform = 'Android';
    } else if (this.isIOS) {
      platform = 'IOS';
    }
    return getSpeechVoices(platform, this.config.browser ?? 'Chrome', intlLocale);
  }

  getTimezone() {
    switch (this.config.timezone_type) {
      case FingerType.Assign:
        return this.config.timezone ?? 'Asia/Shanghai';
      case FingerType.Auto:
        return this.config.ip?.timezone ?? 'Asia/Shanghai';
    }
  }

  getEmulateDevice(): Device | undefined {
    if (this.isMobile) {
      let width = Math.floor(Number(this.config.screenSize?.split('x')[0] ?? 428));
      let height = Math.floor(Number(this.config.screenSize?.split('x')[1] ?? 926));
      return {
        userAgent: '',
        viewport: {
          width: width,
          height: height,
          // deviceScaleFactor: screen.getPrimaryDisplay().scaleFactor,
          isMobile: true,
          isLandscape: width >= height,
          hasTouch: true,
        },
      };
    }
    return undefined;
  }

  extendHeaders() {}

  setDevTools(enabled: boolean) {
    this.devtools = enabled ? true : undefined;
  }

  setShopCode(shopCode?: string) {
    this.shopCode = shopCode;
  }

  static privacyUserAgent(userAgent: string): string {
    const uaParserRes = uaParser(userAgent);
    const { engine } = uaParserRes;
    const browserVersionNum = parseInt(engine.version ?? '109');
    if (browserVersionNum >= 107) {
      const reg = new RegExp(`Chrome/${browserVersionNum}\\.0\\.\\d{4}\\.\\d{1,4}`);
      userAgent = userAgent.replace(reg, `Chrome/${browserVersionNum}.0.0.0`);
    }
    return userAgent;
  }
}

function getBrowserFullVersion(version: string) {
  const arr = version.split('.');
  while (arr.length < 3) {
    arr.push('0');
  }
  return arr.join('.');
}

function concatSpeechVoices(sysVoices: any[], browser: string) {
  if (browser === 'Edge') {
    return sysVoices.concat(edgeVoices);
  }
  return sysVoices.concat(chromeVoices);
}

function getSpeechVoices(platform: string, browser: string, locale?: string) {
  let voices = [];
  if (platform === 'Win32') {
    voices = concatSpeechVoices(winVoices, browser);
  } else if (platform === 'MacIntel') {
    voices = concatSpeechVoices(macVoices, browser);
  } else if (platform === 'IOS') {
    voices = iosVoices;
  } else if (platform === 'Android') {
    voices = androidVoices;
  } else {
    voices = concatSpeechVoices(linuxVoices, browser);
  }
  const _voices = _.cloneDeep(voices);
  if (_voices.length > 0) {
    if (_voices) {
      const targetVoiceIdx = _voices.findIndex((v) => v.lang === locale);
      if (targetVoiceIdx !== -1) {
        const targetVoice = _voices[targetVoiceIdx];
        targetVoice.default = true;
        if (targetVoice.localService) {
          _voices.splice(targetVoiceIdx, 1);
          _voices.unshift(targetVoice);
        }
      }
    }
    if (!_voices.some((v) => v.default)) {
      _voices[0].default = true;
    }
  }
  return _voices;
}
