import { exec } from 'child_process';
import os from 'os';
import { VM } from 'vm2';
import { Client, HTTPAgent, HTTPSAgent } from 'ssh2';
import { SocksProxyAgent } from 'socks-proxy-agent';
import request, { RequestAgent, resolveApiUrl } from '@e/services/request';
import { isLinuxPlatform, regs, resolveUrl } from '@e/utils/utils';
import https from 'https';
import http from 'http';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { HttpProxyAgent } from 'http-proxy-agent';
import db from '@e/components/db';
import _ from 'lodash';
import axios from 'axios';
import i18n from '@e/utils/i18n';
import { hyDecryptVo } from '@e/utils/crypto';

const parseUrl = require('url').parse;

const DEFAULT_PORTS: any = {
  ftp: 21,
  gopher: 70,
  http: 80,
  https: 443,
  ws: 80,
  wss: 443,
};

export function processProxyData(
  data: ProxyProps & { networkType: 'UseDirect' | 'UseProxy' | 'UseSystem'; ipv6?: boolean },
  systemProxy?: Record<string, any>,
): ProxyProps {
  const {
    proxyType,
    host,
    port,
    username = '',
    password = '',
    ipVersion = 'Auto',
    sshKey,
    teamId,
    networkType,
    ipv6,
  } = data;
  const proxyProps = {
    proxyType,
    port,
    ipVersion,
    teamId,
    networkType,
    ...hyDecryptVo({
      host,
      username,
      password: password || '',
      sshKey,
    }),
  };
  if (ipv6) {
    proxyProps.ipVersion = 'IPv6';
  } else if (ipv6 === false) {
    proxyProps.ipVersion = 'IPv4';
  }
  const useSystem = networkType === 'UseSystem';
  if (useSystem && systemProxy) {
    proxyProps.proxyType = systemProxy.proxyType;
    proxyProps.host = systemProxy.host;
    proxyProps.port = systemProxy.port;
  }
  return proxyProps;
}

/**
 * 解析 socks5://127.0.0.1:7890
 *     http://127.0.0.1:7891
 *     等
 * @param url
 */
export function parseProxyUrl(url: string) {
  let parsedUrl = typeof url === 'string' ? parseUrl(url) : url || {};
  let proto = parsedUrl.protocol;
  let hostname = parsedUrl.host;
  let port = parsedUrl.port;
  if (typeof hostname !== 'string' || !hostname || typeof proto !== 'string') {
    return undefined; // Don't proxy URLs without a valid scheme or host.
  }

  proto = proto.split(':', 1)[0];
  // Stripping ports in this way instead of using parsedUrl.hostname to make
  // sure that the brackets around IPv6 addresses are kept.
  hostname = hostname.replace(/:\d*$/, '');
  port = parseInt(port) || DEFAULT_PORTS[proto] || 0;
  return {
    proxyType: proto,
    host: hostname,
    port,
  };
}

export async function readSystemProxy(): Promise<any> {
  // 通过PAC脚本获取代理地址
  const detectProxyFromPAC = async (pacUrl: string) => {
    try {
      const pacFile = await axios.get(pacUrl, { timeout: 3000, responseType: 'text' });
      const vm = new VM({
        timeout: 1000,
        wasm: false,
      });
      const results = vm.run(
        `
        function isPlainHostName(host) { return !host.includes("."); }
        function dnsResolve(host) { return null; }
        function isInNet(host, net, mask) {
          function ipToLong(ip) {
            var parts = ip.split('.').map(Number);
            return (parts[0] << 24) | (parts[1] << 16) | (parts[2] << 8) | parts[3];
          }
          var hostIp = ipToLong(host);
          var netIp = ipToLong(net);
          var maskIp = ipToLong(mask);
          return (hostIp & maskIp) === (netIp & maskIp);
        }
        function localHostOrDomainIs(host, domain) {
          return host === domain || host.endsWith("." + domain);
        }
        function shExpMatch(str, shexp) {
          var regex = new RegExp('^' + shexp.replace(/\\./g, '\\\\.').replace(/\\*/g, '.*') + '$');
          return regex.test(str);
        }
        var findProxyForURLRes = '';
        try {
          ${pacFile.data}
          findProxyForURLRes = FindProxyForURL('https://www.google.com/', 'www.google.com');
        } catch (e) {}
        findProxyForURLRes;
        `,
      );
      let regRes = /(SOCKS5|PROXY)\s([^:;]+):(\d+)/.exec(results || '');
      if (!regRes) {
        regRes = /(SOCKS5|PROXY)\s([^:;]+):(\d+)/.exec(pacFile.data || '');
      }
      if (regRes) {
        const proxy = {
          proxyType: regRes[1].toLowerCase() === 'socks5' ? 'socks5' : 'http',
          host: regRes[2],
          port: ~~regRes[3],
        };
        return { success: true, proxy: proxy };
      } else {
        return { success: false, msg: `解析系统代理PAC脚本失败（${pacUrl}）` };
      }
    } catch (e) {
      return { success: false, msg: `解析系统代理PAC脚本失败（${pacUrl}）` };
    }
  };

  const readMacSystemProxy = () => {
    let command = 'scutil --proxy';
    return new Promise((resolve, reject) => {
      exec(command, (err, msg, errMsg) => {
        if (err || errMsg) {
          resolve({ success: false, msg: err || errMsg });
        } else {
          msg = msg.replace(/<[^<>]+>/g, '');
          try {
            let temp = {};
            let lines = msg.split('\n');
            for (let i = 0; i < lines.length; i++) {
              let line = lines[i];
              if (line && line.indexOf(':') != -1) {
                let colsRes = /^([a-zA-Z0-9]+)\s*:\s*([\S]+)$/.exec(line.trim());
                if (colsRes) {
                  let left = colsRes[1].trim();
                  if (
                    'HTTPEnable,HTTPProxy,HTTPPort,SOCKSEnable,SOCKSProxy,SOCKSPort,HTTPSEnable,HTTPSProxy,HTTPSPort,ProxyAutoConfigEnable,ProxyAutoConfigURLString'.indexOf(
                      left,
                    ) != -1
                  ) {
                    // @ts-ignore
                    temp[left] = colsRes[2].trim();
                  }
                }
              }
            }
            let proxy = undefined;
            let config = JSON.parse(JSON.stringify(temp));
            if (~~config.HTTPEnable) {
              proxy = {
                proxyType: 'http',
                host: config.HTTPProxy,
                port: ~~config.HTTPPort,
              };
            } else if (~~config.SOCKSEnable) {
              proxy = {
                proxyType: 'socks5',
                host: config.SOCKSProxy,
                port: config.SOCKSPort,
              };
            } else if (~~config.HTTPSEnable) {
              proxy = {
                proxyType: 'https',
                host: config.HTTPSProxy,
                port: config.HTTPSPort,
              };
            } else if (~~config.ProxyAutoConfigEnable && config.ProxyAutoConfigURLString) {
              resolve(detectProxyFromPAC(config.ProxyAutoConfigURLString));
            }
            resolve({ success: true, proxy: proxy });
          } catch (err1) {
            resolve({ success: false, msg: err1 });
          }
        }
      });
    });
  };

  const readWindowsSystemProxy = () => {
    let command =
      'reg query "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Internet Settings"';
    return new Promise((resolve, reject) => {
      exec(command, async (err, msg, errMsg) => {
        if (err || errMsg) {
          resolve({ success: true, msg: err || errMsg });
        } else {
          let temp = {};
          let lines = msg.split('\n');
          for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            if (
              (line && (line.indexOf('ProxyEnable') != -1 || line.indexOf('ProxyServer') != -1)) ||
              line.indexOf('AutoConfigURL') != -1
            ) {
              let cols = line.trim().split(/\s+/);
              if (cols.length == 3) {
                let left = cols[0].trim();
                // @ts-ignore
                temp[left] = cols[2].trim();
              }
            }
          }
          let proxy = undefined;
          let config = JSON.parse(JSON.stringify(temp));
          if (
            config.ProxyEnable &&
            config.ProxyEnable.toLowerCase() == '0x1' &&
            config.ProxyServer
          ) {
            let str = config.ProxyServer;
            // DAMAI-2872 兼容非标准的代理格式
            let proxyType = 'http';
            // http=http://127.0.0.1:10809;https=http://127.0.0.1:10809
            // http=127.0.0.1:10809;https=127.0.0.1:10809;ftp=127.0.0.1:10809;socks=127.0.0.1:10808
            // http://127.0.0.1:33210
            const fallbackMatch1 = /(?:https?=)?(?:http?:\/\/)?([^;]+)/.exec(str);
            // socks=127.0.0.1:10808
            const fallbackMatch2 = /socks=([^:]+):([^;]+)/.exec(str);
            const fallbackMatch3 = /([^=]+):([^;]+)/.exec(str);
            if (fallbackMatch1) {
              str = fallbackMatch1[1];
            } else if (fallbackMatch2) {
              str = `${fallbackMatch2[1]}:${fallbackMatch2[2]}`;
              proxyType = 'socks5';
            } else if (fallbackMatch3) {
              proxyType = str.includes('socks') ? 'socks5' : 'http';
              str = `${fallbackMatch3[1]}:${fallbackMatch3[2]}`;
            }
            let ar = str.split(':');
            if (ar.length == 2) {
              proxy = {
                proxyType,
                host: ar[0],
                port: ~~ar[1],
              };
              resolve({ success: true, proxy: proxy });
              return;
            } else {
              resolve({ success: false, msg: `无法解析系统代理配置（${str}）` });
            }
          } else if (config.AutoConfigURL) {
            resolve(detectProxyFromPAC(config.AutoConfigURL));
          }
          resolve({ success: true });
        }
      });
    });
  };

  if ('darwin' === os.platform()) {
    return readMacSystemProxy();
  } else if ('win32' === os.platform()) {
    return readWindowsSystemProxy();
  } else if (isLinuxPlatform()) {
    const envProxyStr = process.env.ALL_PROXY || process.env.HTTP_PROXY || process.env.HTTPS_PROXY;
    if (envProxyStr) {
      const proxy = parseProxyUrl(envProxyStr);
      if (proxy && proxy.proxyType === 'socks') {
        proxy.proxyType = 'socks5';
      }
      return { success: true, proxy };
    }
    return { success: true };
  } else {
    return { success: false, msg: `os ${os.platform()} not supported yet` };
  }
}

export type ProxyProps = {
  proxyType: 'http' | 'socks5' | 'ssh' | string;
  host: string;
  port: number;
  username: string;
  password: string;
  ipVersion?: 'Auto' | 'IPv4' | 'IPv6';
  sshKey?: string;
  teamId: number;
  networkType?: 'UseDirect' | 'UseProxy' | 'UseSystem' | 'UseLocalFrontend';
  requestAgent?: RequestAgent;
};
export type ProbeResult = {
  success: boolean;
  testingTime?: number;
  ip?: string;
  error?: string;
  code?: number;
  output?: string;
};
export const PROBE_TIME_OUT = 6; // 6秒

function getHttpHeader() {
  return {
    'Content-Type': 'application/json',
    Authorization: db.getJwt() ?? '',
    Cookie: db.getCookies().join(';') ?? '',
  };
}
export const pAny = (list: Promise<ProbeResult>[], resolve: (res: ProbeResult) => void) => {
  return Promise.any(list)
    .then((res) => {
      resolve(res);
    })
    .catch(({ errors }) => {
      let code = 0;
      let error;
      let output;
      for (let i = 0; i < errors.length; i++) {
        const item = errors[i];
        if (!error) {
          error = item.error || item.message;
        }
        if (!output) {
          output = item.output;
        }
        if (item.code > code) {
          code = item.code;
          error = item.error || item.message;
          output = item.output;
        }
      }
      resolve({
        code,
        success: false,
        output,
        error: error || i18n.t('代理服务客户端直连认证失败'),
      });
    });
};

export async function getTargetTransits(
  options: Pick<ProxyProps, 'ipVersion' | 'teamId' | 'requestAgent'>,
): Promise<API.HuaYongCheckerConfig[]> {
  const { ipVersion, teamId, requestAgent = new RequestAgent() } = options;
  const huaYongCheckers = await requestAgent
    ?.request('/api/meta/ip/checkers', { teamId })
    .then((res) => {
      return res && res.huaYongCheckers ? res.huaYongCheckers : [];
    });
  const list: API.HuaYongCheckerConfig[] = [];
  if (huaYongCheckers?.length) {
    huaYongCheckers.forEach((item: { domestic?: boolean; endpoint?: string; ipv6?: boolean }) => {
      const { endpoint, ipv6 } = item;

      switch (ipVersion) {
        case 'IPv4':
          if (!ipv6) {
            list.push(item);
          }
          break;
        case 'IPv6':
          if (ipv6) {
            list.push(item);
          }
          break;
        default:
          list.push(item);
          break;
      }
    });
  } else {
    throw new Error(i18n.t('无可用 {{ipVersion}} 接入点', { ipVersion }));
  }
  return list;
}

export function proxyProbe(options: ProxyProps): Promise<ProbeResult> {
  const { proxyType } = options;
  switch (proxyType) {
    case 'ssh':
      return probeSSHProxy(options);
    case 'socks5':
      return probeSocks5Proxy(options);
    case 'http':
      return probeHttpProxy(options);
    default:
      return Promise.resolve({
        success: false,
        error: proxyType ? i18n.t('不支持的代理类型') + '：' + proxyType : i18n.t('代理类型为空'),
      });
  }
}

export function probeByTransit(
  params: ProxyProps & { transitId: number },
  requestAgent: RequestAgent,
): Promise<ProbeResult> {
  return new Promise((resolve, reject) => {
    requestAgent
      .request('/api/ip/socks/probe', {
        method: 'PUT',
        params,
        forceHttp: true,
      })
      .then((data) => {
        if (data?.reachable && data?.success) {
          const { testingTime, remoteIp } = data;
          resolve({
            success: true,
            testingTime: testingTime!,
            ip: remoteIp,
          });
        } else {
          const output = data?.output ? `${data?.output}\n` : '';
          const error = output || data?.error || '';
          reject({ error, success: false, code: data?.code });
        }
      })
      .catch((error) => {
        const output = error?.data?.output ? `${error?.data?.output}\n` : '';
        const _error = output || error?.data?.error || error?.message;
        reject({ error: _error, success: false });
      });
  });
}

type RequestOptions = {
  url: string;
  agent?: any;
  startTime: number;
};
export function requestFromAgent(options: RequestOptions) {
  const { url, startTime, agent } = options;
  const req = url.startsWith('https') ? https : http;
  let testingTime: number;
  let ip;
  return new Promise<ProbeResult>((resolve, reject) => {
    req
      .get(
        url,
        {
          timeout: PROBE_TIME_OUT * 1000,
          agent,
          headers: getHttpHeader(),
          rejectUnauthorized: false,
        },
        (res) => {
          let body = '';
          res.on('data', (chunk) => {
            if (!testingTime) {
              testingTime = Date.now() - startTime;
            }
            body += chunk;
          });
          res.on('end', () => {
            ip = body;
            if (res.statusCode !== 200) {
              // 当作异常处理
              const headers: string[] = [];
              Object.keys(res.headers).forEach((key) => {
                headers.push(`${key}=${res.headers[key]}`);
              });
              const output =
                `HTTP/${res.httpVersion} ` +
                res.statusCode +
                ' ' +
                res.statusMessage +
                '\n' +
                headers.join('\n') +
                '\n' +
                '\n' +
                ip;
              reject({
                success: false,
                error: res.statusMessage,
                output,
                code: res.statusCode,
              });
            } else if (!regs.ipv4.test(ip) && !regs.ipv6.test(ip)) {
              resolve({
                success: false,
                error: i18n.t('未获取到有效的出口IP'),
                output: ip,
              });
            } else {
              resolve({
                success: true,
                testingTime,
                ip,
              });
            }
          });
          res.on('error', (e: any) => {
            reject({
              success: false,
              error: e?.message || i18n.t('代理服务客户端直连认证失败'),
              output: e?.message,
              code: 1,
            });
          });
          res.resume();
        },
      )
      .on('timeout', () => {
        reject({
          success: false,
          error: i18n.t('代理服务客户端连接超时'),
          output: i18n.t('代理服务客户端连接超时'),
          code: 1,
        });
      })
      .on('error', (e: any) => {
        reject({
          success: false,
          error: e?.message || i18n.t('代理服务客户端直连认证失败'),
          output: e?.message || i18n.t('代理服务客户端直连认证失败'),
          code: 1,
        });
      });
  });
}

/**
 * 获取代理的出口ip
 * @param options
 * @param agent
 */
export function getProxyIp(options: ProxyProps, agent?: any): Promise<ProbeResult> {
  const { sshKey, ...others } = options;
  let _agent = agent;
  return new Promise((resolve) => {
    getTargetTransits(others)
      .then(async (checkers) => {
        const startTime = Date.now();
        if (checkers?.length) {
          const list_6: (() => Promise<ProbeResult>)[] = [];
          const list_4: (() => Promise<ProbeResult>)[] = [];
          for (let i = 0; i < checkers.length; i++) {
            const { endpoint, ipv6 } = checkers[i];
            const url = resolveUrl(endpoint);
            if (options?.proxyType === 'ssh') {
              _agent = new (url.startsWith('https') ? HTTPSAgent : HTTPAgent)(
                {
                  ...others,
                  privateKey: sshKey,
                },
                { timeout: PROBE_TIME_OUT * 1000 },
              );
            } else if (options?.proxyType === 'http') {
              const baseOptions = {
                timeout: PROBE_TIME_OUT * 1000,
                headers:
                  others.username || others.password
                    ? {
                        'Proxy-Authorization':
                          'Basic ' +
                          Buffer.from(
                            (others.username || 'anonymous') +
                              ':' +
                              (others.password || 'anonymous'),
                          ).toString('base64'),
                      }
                    : undefined,
              };
              if (url.startsWith('https')) {
                _agent = new HttpsProxyAgent({
                  rejectUnauthorized: false,
                  host: others.host,
                  port: others.port,
                  ...baseOptions,
                });
              } else {
                _agent = new HttpProxyAgent(`http://${others.host}:${others.port}`, {
                  ...baseOptions,
                });
              }
            }

            if (ipv6) {
              list_6.push(() => requestFromAgent({ url, agent: _agent, startTime }));
            } else {
              list_4.push(() => requestFromAgent({ url, agent: _agent, startTime }));
            }
          }

          if (others.ipVersion === 'IPv6' && list_6.length) {
            pAny(
              list_6.map((ac) => ac()),
              (res) => {
                if (!res.success && list_4.length) {
                  pAny(
                    list_4.map((ac) => ac()),
                    resolve,
                  );
                } else {
                  resolve(res);
                }
              },
            );
          } else {
            // 先4，超时后6
            pAny(
              list_4.map((p) => p()),
              (res) => {
                if (!res.success && list_6.length) {
                  pAny(
                    list_6.map((i) => i()),
                    resolve,
                  );
                } else {
                  resolve(res);
                }
              },
            );
          }
        } else {
          resolve({
            success: false,
            error: i18n.t('无可用接入点'),
          });
        }
      })
      .catch((e) => {
        resolve({
          code: 1,
          success: false,
          output: `${i18n.t('代理服务客户端连接失败')}（${e.message}）`,
          error: e.message,
        });
      });
  });
}

/**
 * socks5
 * 探测（导入时）代理ip的连接状态（测速及测速）
 */
export function probeSocks5Proxy(options: ProxyProps): Promise<ProbeResult> {
  const agent = new SocksProxyAgent(
    {
      hostname: options.host,
      ..._.omit(options, 'host'),
      type: 5,
    },
    {
      timeout: PROBE_TIME_OUT * 1000,
    },
  );
  return getProxyIp(options, agent);
}
/**
 * SSH 协议
 * 探测（导入时）代理ip的连接状态（测速及测速）
 * // 建立代理，发起请求，返回测速信息(测速简单认为未获取到ip则失败吧)
 */
export function probeSSHProxy(options: ProxyProps): Promise<ProbeResult> {
  const { host, port, password, sshKey, username } = options;
  let agent: Client;
  return new Promise((rs) => {
    try {
      agent = new Client()
        .on('ready', async () => {
          getProxyIp(options).then(rs);
        })
        .on('error', (e) => {
          agent?.destroy();
          // 姑且把这种异常优先级定高一点
          rs({
            code: 5,
            success: false,
            error: `${i18n.t('SSH客户端连接失败')}（${e.message}）`,
          });
        })
        .connect({
          host,
          port,
          password,
          privateKey: sshKey,
          username,
          timeout: PROBE_TIME_OUT * 1e3,
        });
    } catch (e: any) {
      rs({
        code: 5,
        success: false,
        error: `${i18n.t('SSH客户端连接失败')}（${e.message}）`,
      });
    }
  });
}
/**
 * http协议
 * 探测（导入时）代理ip的连接状态（测速及测速）
 * // 建立代理，发起请求，返回测速信息(测速简单认为未获取到ip则失败吧)
 */
export function probeHttpProxy(options: ProxyProps): Promise<ProbeResult> {
  return getProxyIp(options);
}

export async function getAppRawProxy() {
  const appProxy = db.getDb().get('appProxy').value();
  if (appProxy) {
    switch (appProxy.mode) {
      case 'fixed_servers':
        return parseProxyUrl(appProxy.proxyRules);
      case 'system':
        let sysProxy = await readSystemProxy();
        if (sysProxy && sysProxy.success) {
          return sysProxy.proxy;
        }
    }
  }
  return undefined;
}
