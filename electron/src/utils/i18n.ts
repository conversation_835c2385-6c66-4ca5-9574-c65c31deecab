import en from '@e/locales/en';
import db from '@e/components/db';

const reg = /{{(.*?)}}/g;

const replaceParam = (template: string, params: { [x: string]: any }) => {
  return (
    template?.replace?.(reg, (item, key) => {
      return params[key] || item;
    }) || template
  );
};

export default {
  isCn() {
    return db.getLanguage().toLowerCase().startsWith('zh');
  },
  getLocale() {
    return db.getLanguage();
  },

  t(string: string, params?: Record<string, any>) {
    let templateStr = string;
    // @ts-ignore
    if (!this.isCn() && en[string]) {
      // @ts-ignore
      templateStr = en[string];
    }
    return replaceParam(templateStr, params || {});
  },
};
