// @ts-nocheck
import { RequestAgent } from '@e/services/request';
import { openShopWindow } from '@e/utils/window';
import { dialog } from 'electron';
import i18n from '@e/utils/i18n';

/**
 * 字符串混淆
 * https://www.json.cn/json/jshx.html
 */
export function getKey() {
  var _0x590d = ['Ow3DpDxxw7McwpHCriTDg8KXwpokMSbDtsKNJjHDvE/CpArDt8KcOQd/w4TCgMKw'];
  (function (_0xcc1741, _0x590d82) {
    var _0x779fc7 = function (_0x59bc4f) {
      while (--_0x59bc4f) {
        _0xcc1741['push'](_0xcc1741['shift']());
      }
    };
    _0x779fc7(++_0x590d82);
  })(_0x590d, 0x99);
  var _0x779f = function (_0xcc1741, _0x590d82) {
    _0xcc1741 = _0xcc1741 - 0x0;
    var _0x779fc7 = _0x590d[_0xcc1741];
    if (_0x779f['OpZItq'] === undefined) {
      (function () {
        var _0x1ffb70 = function () {
          var _0x1d129a;
          try {
            _0x1d129a = Function(
              'return\x20(function()\x20' + '{}.constructor(\x22return\x20this\x22)(\x20)' + ');',
            )();
          } catch (_0x5e84b4) {
            _0x1d129a = window;
          }
          return _0x1d129a;
        };
        var _0x54dec3 = _0x1ffb70();
        var _0x49cf8d = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
        _0x54dec3['atob'] ||
          (_0x54dec3['atob'] = function (_0x3ff516) {
            var _0x22f2c7 = String(_0x3ff516)['replace'](/=+$/, '');
            var _0x1eaf0d = '';
            for (
              var _0x40ee99 = 0x0, _0x47d837, _0x404e8b, _0x452961 = 0x0;
              (_0x404e8b = _0x22f2c7['charAt'](_0x452961++));
              ~_0x404e8b &&
              ((_0x47d837 = _0x40ee99 % 0x4 ? _0x47d837 * 0x40 + _0x404e8b : _0x404e8b),
              _0x40ee99++ % 0x4)
                ? (_0x1eaf0d += String['fromCharCode'](
                    0xff & (_0x47d837 >> ((-0x2 * _0x40ee99) & 0x6)),
                  ))
                : 0x0
            ) {
              _0x404e8b = _0x49cf8d['indexOf'](_0x404e8b);
            }
            return _0x1eaf0d;
          });
      })();
      var _0x5a1c19 = function (_0x323240, _0x5913f5) {
        var _0x357305 = [],
          _0x100a85 = 0x0,
          _0x642246,
          _0x3b5aa7 = '',
          _0x4fd4b1 = '';
        _0x323240 = atob(_0x323240);
        for (
          var _0x253388 = 0x0, _0x1c0bfb = _0x323240['length'];
          _0x253388 < _0x1c0bfb;
          _0x253388++
        ) {
          _0x4fd4b1 +=
            '%' + ('00' + _0x323240['charCodeAt'](_0x253388)['toString'](0x10))['slice'](-0x2);
        }
        _0x323240 = decodeURIComponent(_0x4fd4b1);
        var _0x309104;
        for (_0x309104 = 0x0; _0x309104 < 0x100; _0x309104++) {
          _0x357305[_0x309104] = _0x309104;
        }
        for (_0x309104 = 0x0; _0x309104 < 0x100; _0x309104++) {
          _0x100a85 =
            (_0x100a85 +
              _0x357305[_0x309104] +
              _0x5913f5['charCodeAt'](_0x309104 % _0x5913f5['length'])) %
            0x100;
          _0x642246 = _0x357305[_0x309104];
          _0x357305[_0x309104] = _0x357305[_0x100a85];
          _0x357305[_0x100a85] = _0x642246;
        }
        _0x309104 = 0x0;
        _0x100a85 = 0x0;
        for (var _0x46ab7c = 0x0; _0x46ab7c < _0x323240['length']; _0x46ab7c++) {
          _0x309104 = (_0x309104 + 0x1) % 0x100;
          _0x100a85 = (_0x100a85 + _0x357305[_0x309104]) % 0x100;
          _0x642246 = _0x357305[_0x309104];
          _0x357305[_0x309104] = _0x357305[_0x100a85];
          _0x357305[_0x100a85] = _0x642246;
          _0x3b5aa7 += String['fromCharCode'](
            _0x323240['charCodeAt'](_0x46ab7c) ^
              _0x357305[(_0x357305[_0x309104] + _0x357305[_0x100a85]) % 0x100],
          );
        }
        return _0x3b5aa7;
      };
      _0x779f['JNJLBW'] = _0x5a1c19;
      _0x779f['DidGRA'] = {};
      _0x779f['OpZItq'] = !![];
    }
    var _0x59bc4f = _0x779f['DidGRA'][_0xcc1741];
    if (_0x59bc4f === undefined) {
      if (_0x779f['upMdWL'] === undefined) {
        _0x779f['upMdWL'] = !![];
      }
      _0x779fc7 = _0x779f['JNJLBW'](_0x779fc7, _0x590d82);
      _0x779f['DidGRA'][_0xcc1741] = _0x779fc7;
    } else {
      _0x779fc7 = _0x59bc4f;
    }
    return _0x779fc7;
  };
  return _0x779f('0x0', 'MEY1');
}

export async function openShopByToken(token: string, noMessageBox = false) {
  const requestAgent = new RequestAgent({ token });
  try {
    let shortcutRes: API.ShopShortcutDetailVo | undefined;
    try {
      shortcutRes = await requestAgent.request(`/api/shop/shortcut/${token}`);
    } catch (e) {
      const error = typeof e === 'string' ? new Error(e) : e;
      error.code = 1007;
      throw error;
    }
    await openShopWindow({
      teamId: shortcutRes.teamId,
      shopId: shortcutRes.shopId,
      requestAgent,
    });
  } catch (e) {
    if (noMessageBox) {
      throw e;
    } else {
      dialog.showMessageBox({
        type: 'error',
        title: i18n.t('快捷方式打开失败'),
        message: (e.message || '').includes('no authentication information found')
          ? i18n.t('请先登录花漾客户端')
          : e.message,
      });
    }
  }
}
