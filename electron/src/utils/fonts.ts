import os from 'os';
import {
  isLinuxPlatform,
  isMacPlatform,
  isWinPlatform,
  transBrowserPlatformToNodePlatform,
} from '@e/utils/utils';
import { fonts_win, fonts_mac, fonts_linux } from '../../../src/utils/fingerprint/fonts';

/**
 * 浏览器指纹中的字体作为白名单传入，其他字体作为黑名单输出
 * @param platform
 * @param configFonts
 */
export function getFingerprintExcludeFonts(platform: string, configFonts?: string) {
  if (!configFonts) {
    return configFonts;
  }
  const nodePlatform = transBrowserPlatformToNodePlatform(platform);
  let blacklist: string[] = [];
  let configFontList = configFonts.split(',');
  // 剔除掉客户端所在平台多余的字体
  if (nodePlatform !== os.platform()) {
    if (isMacPlatform()) {
      blacklist = fonts_mac;
    } else if (isWinPlatform()) {
      blacklist = fonts_win;
    } else if (isLinuxPlatform()) {
      blacklist = fonts_linux;
    }
  }
  const res = blacklist
    .filter((bf) => !configFontList.includes(`"${bf}"`))
    .map((bf) => `"${bf}"`)
    .join(',');
  return res;
}

/**
 * 获取指纹中定义平台下，强制使用的字体
 * @param platform
 * @param configFonts
 */
export function getFingerprintForceFonts(platform: string) {
  let sysFontList: string[];
  if (isWinPlatform()) {
    sysFontList = fonts_win;
  } else if (isMacPlatform()) {
    sysFontList = fonts_mac;
  } else {
    sysFontList = fonts_linux;
  }
  const nodePlatform = transBrowserPlatformToNodePlatform(platform);
  let forceList: string[];
  if (nodePlatform === 'win32') {
    forceList = fonts_win;
  } else if (nodePlatform === 'darwin') {
    forceList = fonts_mac;
  } else {
    forceList = fonts_linux;
  }
  const res = forceList
    .filter((f) => !sysFontList.includes(f))
    .map((f) => `"${f}"`)
    .join(',');
  return res;
}
