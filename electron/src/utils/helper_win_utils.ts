import {ipc<PERSON><PERSON><PERSON>} from "electron";

export const addAjaxEventListener = (name: string, resId: any): Promise<any> => {
  return new Promise<any>((resolve, reject) => {
    const hd = ipcRenderer.sendSync('onAjaxEvent', { name, resId });
    resolve(hd);
  });
};

export const removeAjaxEventListener = (hd: string) => {
  return new Promise<any>((resolve, reject) => {
    ipcRenderer.send('unAjaxEvent', hd);
    resolve(1);
  });
};

export const sendRequest = (path: string, options?: { [propName: string]: any }): Promise<any> => {
  return new Promise<any>((resolve, reject) => {
    const res = ipcRenderer.sendSync('send-request', { path, options });
    if (res === 'error') {
      reject('error');
    } else {
      resolve(res);
    }
  });
};
