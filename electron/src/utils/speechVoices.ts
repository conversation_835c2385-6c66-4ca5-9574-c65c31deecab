export const chromeVoices = [
  {
    default: false,
    lang: 'de-DE',
    localService: false,
    name: 'Google Deutsch',
    voiceURI: 'Google Deutsch',
  },
  {
    default: false,
    lang: 'en-US',
    localService: false,
    name: 'Google US English',
    voiceURI: 'Google US English',
  },
  {
    default: false,
    lang: 'en-GB',
    localService: false,
    name: 'Google UK English Female',
    voiceURI: 'Google UK English Female',
  },
  {
    default: false,
    lang: 'en-GB',
    localService: false,
    name: 'Google UK English Male',
    voiceURI: 'Google UK English Male',
  },
  {
    default: false,
    lang: 'es-ES',
    localService: false,
    name: 'Google español',
    voiceURI: 'Google español',
  },
  {
    default: false,
    lang: 'es-US',
    localService: false,
    name: 'Google español de Estados Unidos',
    voiceURI: 'Google español de Estados Unidos',
  },
  {
    default: false,
    lang: 'fr-FR',
    localService: false,
    name: 'Google français',
    voiceURI: 'Google français',
  },
  {
    default: false,
    lang: 'hi-IN',
    localService: false,
    name: 'Google हिन्दी',
    voiceURI: 'Google हिन्दी',
  },
  {
    default: false,
    lang: 'id-ID',
    localService: false,
    name: 'Google Bahasa Indonesia',
    voiceURI: 'Google Bahasa Indonesia',
  },
  {
    default: false,
    lang: 'it-IT',
    localService: false,
    name: 'Google italiano',
    voiceURI: 'Google italiano',
  },
  {
    default: false,
    lang: 'ja-JP',
    localService: false,
    name: 'Google 日本語',
    voiceURI: 'Google 日本語',
  },
  {
    default: false,
    lang: 'ko-KR',
    localService: false,
    name: 'Google 한국의',
    voiceURI: 'Google 한국의',
  },
  {
    default: false,
    lang: 'nl-NL',
    localService: false,
    name: 'Google Nederlands',
    voiceURI: 'Google Nederlands',
  },
  {
    default: false,
    lang: 'pl-PL',
    localService: false,
    name: 'Google polski',
    voiceURI: 'Google polski',
  },
  {
    default: false,
    lang: 'pt-BR',
    localService: false,
    name: 'Google português do Brasil',
    voiceURI: 'Google português do Brasil',
  },
  {
    default: false,
    lang: 'ru-RU',
    localService: false,
    name: 'Google русский',
    voiceURI: 'Google русский',
  },
  {
    default: false,
    lang: 'zh-CN',
    localService: false,
    name: 'Google 普通话（中国大陆）',
    voiceURI: 'Google 普通话（中国大陆）',
  },
  {
    default: false,
    lang: 'zh-HK',
    localService: false,
    name: 'Google 粤語（香港）',
    voiceURI: 'Google 粤語（香港）',
  },
  {
    default: false,
    lang: 'zh-TW',
    localService: false,
    name: 'Google 國語（臺灣）',
    voiceURI: 'Google 國語（臺灣）',
  },
];

export const edgeVoices = [
  {
    default: false,
    lang: 'en-US',
    localService: false,
    name: 'Microsoft Aria Online (Natural) - English (United States)',
    voiceURI: 'Microsoft Aria Online (Natural) - English (United States)',
  },
  {
    default: false,
    lang: 'en-US',
    localService: false,
    name: 'Microsoft Guy Online (Natural) - English (United States)',
    voiceURI: 'Microsoft Guy Online (Natural) - English (United States)',
  },
  {
    default: false,
    lang: 'zh-CN',
    localService: false,
    name: 'Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)',
    voiceURI: 'Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)',
  },
  {
    default: false,
    lang: 'zh-CN',
    localService: false,
    name: 'Microsoft Yunyang Online (Natural) - Chinese (Mainland)',
    voiceURI: 'Microsoft Yunyang Online (Natural) - Chinese (Mainland)',
  },
  {
    default: false,
    lang: 'zh-TW',
    localService: false,
    name: 'Microsoft HanHan Online - Chinese (Taiwan)',
    voiceURI: 'Microsoft HanHan Online - Chinese (Taiwan)',
  },
  {
    default: false,
    lang: 'zh-HK',
    localService: false,
    name: 'Microsoft Tracy Online - Chinese (Hong Kong)',
    voiceURI: 'Microsoft Tracy Online - Chinese (Hong Kong)',
  },
  {
    default: false,
    lang: 'ja-JP',
    localService: false,
    name: 'Microsoft Nanami Online (Natural) - Japanese (Japan)',
    voiceURI: 'Microsoft Nanami Online (Natural) - Japanese (Japan)',
  },
  {
    default: false,
    lang: 'en-GB',
    localService: false,
    name: 'Microsoft Libby Online (Natural) - English (United Kingdom)',
    voiceURI: 'Microsoft Libby Online (Natural) - English (United Kingdom)',
  },
  {
    default: false,
    lang: 'pt-BR',
    localService: false,
    name: 'Microsoft Francisca Online (Natural) - Portuguese (Brazil)',
    voiceURI: 'Microsoft Francisca Online (Natural) - Portuguese (Brazil)',
  },
  {
    default: false,
    lang: 'es-MX',
    localService: false,
    name: 'Microsoft Dalia Online (Natural) - Spanish (Mexico)',
    voiceURI: 'Microsoft Dalia Online (Natural) - Spanish (Mexico)',
  },
  {
    default: false,
    lang: 'en-IN',
    localService: false,
    name: 'Microsoft Priya Online - English (India)',
    voiceURI: 'Microsoft Priya Online - English (India)',
  },
  {
    default: false,
    lang: 'en-CA',
    localService: false,
    name: 'Microsoft Heather Online - English (Canada)',
    voiceURI: 'Microsoft Heather Online - English (Canada)',
  },
  {
    default: false,
    lang: 'fr-CA',
    localService: false,
    name: 'Microsoft Sylvie Online (Natural) - French (Canada)',
    voiceURI: 'Microsoft Sylvie Online (Natural) - French (Canada)',
  },
  {
    default: false,
    lang: 'fr-FR',
    localService: false,
    name: 'Microsoft Denise Online (Natural) - French (France)',
    voiceURI: 'Microsoft Denise Online (Natural) - French (France)',
  },
  {
    default: false,
    lang: 'de-DE',
    localService: false,
    name: 'Microsoft Katja Online (Natural) - German (Germany)',
    voiceURI: 'Microsoft Katja Online (Natural) - German (Germany)',
  },
  {
    default: false,
    lang: 'ru-RU',
    localService: false,
    name: 'Microsoft Ekaterina Online - Russian (Russia)',
    voiceURI: 'Microsoft Ekaterina Online - Russian (Russia)',
  },
  {
    default: false,
    lang: 'en-AU',
    localService: false,
    name: 'Microsoft Hayley Online - English (Australia)',
    voiceURI: 'Microsoft Hayley Online - English (Australia)',
  },
  {
    default: false,
    lang: 'it-IT',
    localService: false,
    name: 'Microsoft Elsa Online (Natural) - Italian (Italy)',
    voiceURI: 'Microsoft Elsa Online (Natural) - Italian (Italy)',
  },
  {
    default: false,
    lang: 'ko-KR',
    localService: false,
    name: 'Microsoft SunHi Online (Natural) - Korean (Korea)',
    voiceURI: 'Microsoft SunHi Online (Natural) - Korean (Korea)',
  },
  {
    default: false,
    lang: 'nl-NL',
    localService: false,
    name: 'Microsoft Hanna Online - Dutch (Netherlands)',
    voiceURI: 'Microsoft Hanna Online - Dutch (Netherlands)',
  },
  {
    default: false,
    lang: 'es-ES',
    localService: false,
    name: 'Microsoft Elvira Online (Natural) - Spanish (Spain)',
    voiceURI: 'Microsoft Elvira Online (Natural) - Spanish (Spain)',
  },
  {
    default: false,
    lang: 'tr-TR',
    localService: false,
    name: 'Microsoft Emel Online (Natural) - Turkish (Turkey)',
    voiceURI: 'Microsoft Emel Online (Natural) - Turkish (Turkey)',
  },
  {
    default: false,
    lang: 'pl-PL',
    localService: false,
    name: 'Microsoft Paulina Online - Polish (Poland)',
    voiceURI: 'Microsoft Paulina Online - Polish (Poland)',
  },
];

export const macVoices = [
  {
    default: false,
    lang: 'en-US',
    localService: true,
    name: 'Alex',
    voiceURI: 'Alex',
  },
  {
    default: false,
    lang: 'it-IT',
    localService: true,
    name: 'Alice',
    voiceURI: 'Alice',
  },
  {
    default: false,
    lang: 'sv-SE',
    localService: true,
    name: 'Alva',
    voiceURI: 'Alva',
  },
  {
    default: false,
    lang: 'fr-CA',
    localService: true,
    name: 'Amelie',
    voiceURI: 'Amelie',
  },
  {
    default: false,
    lang: 'de-DE',
    localService: true,
    name: 'Anna',
    voiceURI: 'Anna',
  },
  {
    default: false,
    lang: 'he-IL',
    localService: true,
    name: 'Carmit',
    voiceURI: 'Carmit',
  },
  {
    default: false,
    lang: 'id-ID',
    localService: true,
    name: 'Damayanti',
    voiceURI: 'Damayanti',
  },
  {
    default: false,
    lang: 'en-GB',
    localService: true,
    name: 'Daniel',
    voiceURI: 'Daniel',
  },
  {
    default: false,
    lang: 'es-AR',
    localService: true,
    name: 'Diego',
    voiceURI: 'Diego',
  },
  {
    default: false,
    lang: 'nl-BE',
    localService: true,
    name: 'Ellen',
    voiceURI: 'Ellen',
  },
  {
    default: false,
    lang: 'en-SCOTLAND',
    localService: true,
    name: 'Fiona',
    voiceURI: 'Fiona',
  },
  {
    default: false,
    lang: 'en-US',
    localService: true,
    name: 'Fred',
    voiceURI: 'Fred',
  },
  {
    default: false,
    lang: 'ro-RO',
    localService: true,
    name: 'Ioana',
    voiceURI: 'Ioana',
  },
  {
    default: false,
    lang: 'pt-PT',
    localService: true,
    name: 'Joana',
    voiceURI: 'Joana',
  },
  {
    default: false,
    lang: 'es-ES',
    localService: true,
    name: 'Jorge',
    voiceURI: 'Jorge',
  },
  {
    default: false,
    lang: 'es-MX',
    localService: true,
    name: 'Juan',
    voiceURI: 'Juan',
  },
  {
    default: false,
    lang: 'th-TH',
    localService: true,
    name: 'Kanya',
    voiceURI: 'Kanya',
  },
  {
    default: false,
    lang: 'en-AU',
    localService: true,
    name: 'Karen',
    voiceURI: 'Karen',
  },
  {
    default: false,
    lang: 'ja-JP',
    localService: true,
    name: 'Kyoko',
    voiceURI: 'Kyoko',
  },
  {
    default: false,
    lang: 'sk-SK',
    localService: true,
    name: 'Laura',
    voiceURI: 'Laura',
  },
  {
    default: false,
    lang: 'hi-IN',
    localService: true,
    name: 'Lekha',
    voiceURI: 'Lekha',
  },
  {
    default: false,
    lang: 'it-IT',
    localService: true,
    name: 'Luca',
    voiceURI: 'Luca',
  },
  {
    default: false,
    lang: 'pt-BR',
    localService: true,
    name: 'Luciana',
    voiceURI: 'Luciana',
  },
  {
    default: false,
    lang: 'ar-SA',
    localService: true,
    name: 'Maged',
    voiceURI: 'Maged',
  },
  {
    default: false,
    lang: 'hu-HU',
    localService: true,
    name: 'Mariska',
    voiceURI: 'Mariska',
  },
  {
    default: false,
    lang: 'zh-TW',
    localService: true,
    name: 'Mei-Jia',
    voiceURI: 'Mei-Jia',
  },
  {
    default: false,
    lang: 'el-GR',
    localService: true,
    name: 'Melina',
    voiceURI: 'Melina',
  },
  {
    default: false,
    lang: 'ru-RU',
    localService: true,
    name: 'Milena',
    voiceURI: 'Milena',
  },
  {
    default: false,
    lang: 'en-IE',
    localService: true,
    name: 'Moira',
    voiceURI: 'Moira',
  },
  {
    default: false,
    lang: 'es-ES',
    localService: true,
    name: 'Monica',
    voiceURI: 'Monica',
  },
  {
    default: false,
    lang: 'nb-NO',
    localService: true,
    name: 'Nora',
    voiceURI: 'Nora',
  },
  {
    default: false,
    lang: 'es-MX',
    localService: true,
    name: 'Paulina',
    voiceURI: 'Paulina',
  },
  {
    default: false,
    lang: 'en-IN',
    localService: true,
    name: 'Rishi',
    voiceURI: 'Rishi',
  },
  {
    default: false,
    lang: 'en-US',
    localService: true,
    name: 'Samantha',
    voiceURI: 'Samantha',
  },
  {
    default: false,
    lang: 'da-DK',
    localService: true,
    name: 'Sara',
    voiceURI: 'Sara',
  },
  {
    default: false,
    lang: 'fi-FI',
    localService: true,
    name: 'Satu',
    voiceURI: 'Satu',
  },
  {
    default: false,
    lang: 'zh-HK',
    localService: true,
    name: 'Sin-ji',
    voiceURI: 'Sin-ji',
  },
  {
    default: false,
    lang: 'en-ZA',
    localService: true,
    name: 'Tessa',
    voiceURI: 'Tessa',
  },
  {
    default: false,
    lang: 'fr-FR',
    localService: true,
    name: 'Thomas',
    voiceURI: 'Thomas',
  },
  {
    default: false,
    lang: 'zh-CN',
    localService: true,
    name: 'Ting-Ting',
    voiceURI: 'Ting-Ting',
  },
  {
    default: false,
    lang: 'en-IN',
    localService: true,
    name: 'Veena',
    voiceURI: 'Veena',
  },
  {
    default: false,
    lang: 'en-US',
    localService: true,
    name: 'Victoria',
    voiceURI: 'Victoria',
  },
  {
    default: false,
    lang: 'nl-NL',
    localService: true,
    name: 'Xander',
    voiceURI: 'Xander',
  },
  {
    default: false,
    lang: 'tr-TR',
    localService: true,
    name: 'Yelda',
    voiceURI: 'Yelda',
  },
  {
    default: false,
    lang: 'ko-KR',
    localService: true,
    name: 'Yuna',
    voiceURI: 'Yuna',
  },
  {
    default: false,
    lang: 'ru-RU',
    localService: true,
    name: 'Yuri',
    voiceURI: 'Yuri',
  },
  {
    default: false,
    lang: 'pl-PL',
    localService: true,
    name: 'Zosia',
    voiceURI: 'Zosia',
  },
  {
    default: false,
    lang: 'cs-CZ',
    localService: true,
    name: 'Zuzana',
    voiceURI: 'Zuzana',
  },
];

export const winVoices = [
  {
    default: false,
    lang: 'en-US',
    localService: true,
    name: 'Microsoft Mark - English (United States)',
    voiceURI: 'Microsoft Mark - English (United States)',
  },
  {
    default: false,
    lang: 'en-US',
    localService: true,
    name: 'Microsoft Zira - English (United States)',
    voiceURI: 'Microsoft Zira - English (United States)',
  },
  {
    default: false,
    lang: 'en-US',
    localService: true,
    name: 'Microsoft David - English (United States)',
    voiceURI: 'Microsoft David - English (United States)',
  },
  {
    default: false,
    lang: 'zh-CN',
    localService: true,
    name: 'Microsoft Huihui - Chinese (Simplified, PRC)',
    voiceURI: 'Microsoft Huihui - Chinese (Simplified, PRC)',
  },
  {
    default: false,
    lang: 'zh-CN',
    localService: true,
    name: 'Microsoft Kangkang - Chinese (Simplified, PRC)',
    voiceURI: 'Microsoft Kangkang - Chinese (Simplified, PRC)',
  },
  {
    default: false,
    lang: 'zh-CN',
    localService: true,
    name: 'Microsoft Yaoyao - Chinese (Simplified, PRC)',
    voiceURI: 'Microsoft Yaoyao - Chinese (Simplified, PRC)',
  },
];

export const linuxVoices: string[] = [
  //empty
];

export const iosVoices = [
  {
    name: 'Majed',
    lang: 'ar-001',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.ar-001.Maged',
  },
  {
    name: 'Daria',
    lang: 'bg-BG',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.bg-BG.Daria',
  },
  {
    name: 'Montse',
    lang: 'ca-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.ca-ES.Montserrat',
  },
  {
    name: 'Zuzana',
    lang: 'cs-CZ',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.cs-CZ.Zuzana',
  },
  {
    name: 'Sara',
    lang: 'da-DK',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.da-DK.Sara',
  },
  {
    name: 'Sandy',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Sandy',
  },
  {
    name: 'Shelley',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Shelley',
  },
  {
    name: 'Grandma',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Grandma',
  },
  {
    name: 'Grandpa',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Grandpa',
  },
  {
    name: 'Eddy',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Eddy',
  },
  {
    name: 'Reed',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Reed',
  },
  {
    name: 'Anna',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.de-DE.Anna',
  },
  {
    name: 'Rocko',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Rocko',
  },
  {
    name: 'Flo',
    lang: 'de-DE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.de-DE.Flo',
  },
  {
    name: 'Melina',
    lang: 'el-GR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.el-GR.Melina',
  },
  {
    name: 'Karen',
    lang: 'en-AU',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.en-AU.Karen',
  },
  {
    name: 'Rocko',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Rocko',
  },
  {
    name: 'Shelley',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Shelley',
  },
  {
    name: 'Daniel',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.en-GB.Daniel',
  },
  {
    name: 'Grandma',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Grandma',
  },
  {
    name: 'Grandpa',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Grandpa',
  },
  {
    name: 'Flo',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Flo',
  },
  {
    name: 'Eddy',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Eddy',
  },
  {
    name: 'Reed',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Reed',
  },
  {
    name: 'Sandy',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-GB.Sandy',
  },
  {
    name: 'Moira',
    lang: 'en-IE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.en-IE.Moira',
  },
  {
    name: 'Rishi',
    lang: 'en-IN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.en-IN.Rishi',
  },
  {
    name: 'Flo',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Flo',
  },
  {
    name: 'Bahh',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Bahh',
  },
  {
    name: 'Albert',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Albert',
  },
  {
    name: 'Fred',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Fred',
  },
  {
    name: 'Jester',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Hysterical',
  },
  {
    name: 'Organ',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Organ',
  },
  {
    name: 'Cellos',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Cellos',
  },
  {
    name: 'Zarvox',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Zarvox',
  },
  {
    name: 'Rocko',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Rocko',
  },
  {
    name: 'Shelley',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Shelley',
  },
  {
    name: 'Superstar',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Princess',
  },
  {
    name: 'Grandma',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Grandma',
  },
  {
    name: 'Eddy',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Eddy',
  },
  {
    name: 'Bells',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Bells',
  },
  {
    name: 'Grandpa',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Grandpa',
  },
  {
    name: 'Trinoids',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Trinoids',
  },
  {
    name: 'Kathy',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Kathy',
  },
  {
    name: 'Reed',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Reed',
  },
  {
    name: 'Boing',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Boing',
  },
  {
    name: 'Whisper',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Whisper',
  },
  {
    name: 'Wobble',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Deranged',
  },
  {
    name: 'Good News',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.GoodNews',
  },
  {
    name: 'Bad News',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.BadNews',
  },
  {
    name: 'Bubbles',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Bubbles',
  },
  {
    name: 'Sandy',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.en-US.Sandy',
  },
  {
    name: 'Junior',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Junior',
  },
  {
    name: 'Ralph',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.speech.synthesis.voice.Ralph',
  },
  {
    name: 'Tessa',
    lang: 'en-ZA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.en-ZA.Tessa',
  },
  {
    name: 'Shelley',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Shelley',
  },
  {
    name: 'Grandma',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Grandma',
  },
  {
    name: 'Rocko',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Rocko',
  },
  {
    name: 'Grandpa',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Grandpa',
  },
  {
    name: 'Mónica',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.es-ES.Monica',
  },
  {
    name: 'Sandy',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Sandy',
  },
  {
    name: 'Flo',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Flo',
  },
  {
    name: 'Eddy',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Eddy',
  },
  {
    name: 'Reed',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-ES.Reed',
  },
  {
    name: 'Rocko',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Rocko',
  },
  {
    name: 'Paulina',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.es-MX.Paulina',
  },
  {
    name: 'Flo',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Flo',
  },
  {
    name: 'Sandy',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Sandy',
  },
  {
    name: 'Eddy',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Eddy',
  },
  {
    name: 'Shelley',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Shelley',
  },
  {
    name: 'Grandma',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Grandma',
  },
  {
    name: 'Reed',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Reed',
  },
  {
    name: 'Grandpa',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.es-MX.Grandpa',
  },
  {
    name: 'Shelley',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Shelley',
  },
  {
    name: 'Grandma',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Grandma',
  },
  {
    name: 'Grandpa',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Grandpa',
  },
  {
    name: 'Sandy',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Sandy',
  },
  {
    name: 'Satu',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.fi-FI.Satu',
  },
  {
    name: 'Eddy',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Eddy',
  },
  {
    name: 'Rocko',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Rocko',
  },
  {
    name: 'Reed',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Reed',
  },
  {
    name: 'Flo',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fi-FI.Flo',
  },
  {
    name: 'Shelley',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Shelley',
  },
  {
    name: 'Grandma',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Grandma',
  },
  {
    name: 'Grandpa',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Grandpa',
  },
  {
    name: 'Rocko',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Rocko',
  },
  {
    name: 'Eddy',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Eddy',
  },
  {
    name: 'Reed',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Reed',
  },
  {
    name: 'Amélie',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.fr-CA.Amelie',
  },
  {
    name: 'Flo',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Flo',
  },
  {
    name: 'Sandy',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-CA.Sandy',
  },
  {
    name: 'Grandma',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Grandma',
  },
  {
    name: 'Flo',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Flo',
  },
  {
    name: 'Rocko',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Rocko',
  },
  {
    name: 'Grandpa',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Grandpa',
  },
  {
    name: 'Sandy',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Sandy',
  },
  {
    name: 'Eddy',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Eddy',
  },
  {
    name: 'Thomas',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.fr-FR.Thomas',
  },
  {
    name: 'Jacques',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Jacques',
  },
  {
    name: 'Shelley',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.fr-FR.Shelley',
  },
  {
    name: 'Carmit',
    lang: 'he-IL',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.he-IL.Carmit',
  },
  {
    name: 'Lekha',
    lang: 'hi-IN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.hi-IN.Lekha',
  },
  {
    name: 'Lana',
    lang: 'hr-HR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.hr-HR.Lana',
  },
  {
    name: 'Tünde',
    lang: 'hu-HU',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.hu-HU.Mariska',
  },
  {
    name: 'Damayanti',
    lang: 'id-ID',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.id-ID.Damayanti',
  },
  {
    name: 'Eddy',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Eddy',
  },
  {
    name: 'Sandy',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Sandy',
  },
  {
    name: 'Reed',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Reed',
  },
  {
    name: 'Shelley',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Shelley',
  },
  {
    name: 'Grandma',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Grandma',
  },
  {
    name: 'Grandpa',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Grandpa',
  },
  {
    name: 'Flo',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Flo',
  },
  {
    name: 'Rocko',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.it-IT.Rocko',
  },
  {
    name: 'Alice',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.it-IT.Alice',
  },
  {
    name: 'Kyoko',
    lang: 'ja-JP',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.ja-JP.Kyoko',
  },
  {
    name: 'Amira',
    lang: 'ms-MY',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.ms-MY.Amira',
  },
  {
    name: 'Nora',
    lang: 'nb-NO',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.nb-NO.Nora',
  },
  {
    name: 'Ellen',
    lang: 'nl-BE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.nl-BE.Ellen',
  },
  {
    name: 'Xander',
    lang: 'nl-NL',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.nl-NL.Xander',
  },
  {
    name: 'Zosia',
    lang: 'pl-PL',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.pl-PL.Zosia',
  },
  {
    name: 'Reed',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Reed',
  },
  {
    name: 'Luciana',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.pt-BR.Luciana',
  },
  {
    name: 'Shelley',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Shelley',
  },
  {
    name: 'Grandma',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Grandma',
  },
  {
    name: 'Grandpa',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Grandpa',
  },
  {
    name: 'Rocko',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Rocko',
  },
  {
    name: 'Flo',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Flo',
  },
  {
    name: 'Sandy',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Sandy',
  },
  {
    name: 'Eddy',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.eloquence.pt-BR.Eddy',
  },
  {
    name: 'Joana',
    lang: 'pt-PT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.pt-PT.Joana',
  },
  {
    name: 'Ioana',
    lang: 'ro-RO',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.ro-RO.Ioana',
  },
  {
    name: 'Milena',
    lang: 'ru-RU',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.ru-RU.Milena',
  },
  {
    name: 'Laura',
    lang: 'sk-SK',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.sk-SK.Laura',
  },
  {
    name: 'Alva',
    lang: 'sv-SE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.sv-SE.Alva',
  },
  {
    name: 'Kanya',
    lang: 'th-TH',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.th-TH.Kanya',
  },
  {
    name: 'Yelda',
    lang: 'tr-TR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.tr-TR.Yelda',
  },
  {
    name: 'Lesya',
    lang: 'uk-UA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.uk-UA.Lesya',
  },
  {
    name: 'Linh',
    lang: 'vi-VN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.vi-VN.Linh',
  },
  {
    name: '婷婷',
    lang: 'zh-CN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.zh-CN.Tingting',
  },
  {
    name: '善怡',
    lang: 'zh-HK',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.zh-HK.Sinji',
  },
  {
    name: '美嘉',
    lang: 'zh-TW',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.compact.zh-TW.Meijia',
  },
  {
    name: 'Montse',
    lang: 'ca-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.ca-ES.Montserrat',
  },
  {
    name: 'Daniel',
    lang: 'en-GB',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.en-GB.Daniel',
  },
  {
    name: '善怡',
    lang: 'zh-HK',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.zh-HK.Sinji',
  },
  {
    name: 'Amélie',
    lang: 'fr-CA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.fr-CA.Amelie',
  },
  {
    name: 'Thomas',
    lang: 'fr-FR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.fr-FR.Thomas',
  },
  {
    name: 'Samantha',
    lang: 'en-US',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.en-US.Samantha',
  },
  {
    name: 'Damayanti',
    lang: 'id-ID',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.id-ID.Damayanti',
  },
  {
    name: 'Mónica',
    lang: 'es-ES',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.es-ES.Monica',
  },
  {
    name: 'Zosia',
    lang: 'pl-PL',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.pl-PL.Zosia',
  },
  {
    name: 'Lana',
    lang: 'hr-HR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.hr-HR.Lana',
  },
  {
    name: 'Luciana',
    lang: 'pt-BR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.pt-BR.Luciana',
  },
  {
    name: 'Ioana',
    lang: 'ro-RO',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.ro-RO.Ioana',
  },
  {
    name: 'Moira',
    lang: 'en-IE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.en-IE.Moira',
  },
  {
    name: 'Nora',
    lang: 'nb-NO',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.nb-NO.Nora',
  },
  {
    name: 'Xander',
    lang: 'nl-NL',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.nl-NL.Xander',
  },
  {
    name: 'Yuna',
    lang: 'ko-KR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.ko-KR.Yuna',
  },
  {
    name: 'Rishi',
    lang: 'en-IN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.en-IN.Rishi',
  },
  {
    name: 'Alice',
    lang: 'it-IT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.it-IT.Alice',
  },
  {
    name: 'Kanya',
    lang: 'th-TH',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.th-TH.Kanya',
  },
  {
    name: 'Kyoko',
    lang: 'ja-JP',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.ja-JP.Kyoko',
  },
  {
    name: 'Linh',
    lang: 'vi-VN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.vi-VN.Linh',
  },
  {
    name: 'Paulina',
    lang: 'es-MX',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.es-MX.Paulina',
  },
  {
    name: '婷婷',
    lang: 'zh-CN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.zh-CN.Tingting',
  },
  {
    name: 'Karen',
    lang: 'en-AU',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.en-AU.Karen',
  },
  {
    name: 'Melina',
    lang: 'el-GR',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.el-GR.Melina',
  },
  {
    name: 'Lekha',
    lang: 'hi-IN',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.hi-IN.Lekha',
  },
  {
    name: 'Amira',
    lang: 'ms-MY',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.ms-MY.Amira',
  },
  {
    name: 'Satu',
    lang: 'fi-FI',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.fi-FI.Satu',
  },
  {
    name: 'Tünde',
    lang: 'hu-HU',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.hu-HU.Mariska',
  },
  {
    name: 'Joana',
    lang: 'pt-PT',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.pt-PT.Joana',
  },
  {
    name: 'Daria',
    lang: 'bg-BG',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.bg-BG.Daria',
  },
  {
    name: 'Ellen',
    lang: 'nl-BE',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.nl-BE.Ellen',
  },
  {
    name: '美嘉',
    lang: 'zh-TW',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.zh-TW.Meijia',
  },
  {
    name: 'Laura',
    lang: 'sk-SK',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.sk-SK.Laura',
  },
  {
    name: 'Lesya',
    lang: 'uk-UA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.uk-UA.Lesya',
  },
  {
    name: 'Majed',
    lang: 'ar-001',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.ar-001.Maged',
  },
  {
    name: 'Zuzana',
    lang: 'cs-CZ',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.cs-CZ.Zuzana',
  },
  {
    name: 'Milena',
    lang: 'ru-RU',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.ru-RU.Milena',
  },
  {
    name: 'Tessa',
    lang: 'en-ZA',
    default: true,
    localService: true,
    voiceURI: 'com.apple.voice.super-compact.en-ZA.Tessa',
  },
];

export const androidVoices = [
  // empty
];
