import fs from 'fs-extra';
import path from 'path';
import Downloader from 'nodejs-file-downloader';
import _ from 'lodash';
import os from 'os';
import { exec } from 'child_process';

import db from '@e/components/db';

type ProgressItem = {
  downloadedSize: number;
  percentage: number;
  totalSize: number;
};

function getFFmpegExeName() {
  const platform = os.platform();
  switch (platform) {
    case 'win32':
      return 'ffmpeg.exe';
    default:
      return 'ffmpeg';
  }
}

function getFFmpegDownloadUrl() {
  const platform = os.platform();
  const DOWNLOAD_URL = `${db.getDlUrl()}/downloads/ffmpeg`;
  switch (platform) {
    case 'win32':
      return `${DOWNLOAD_URL}/win/ffmpeg.exe`;
    case 'darwin':
      return `${DOWNLOAD_URL}/mac/ffmpeg`;
    default:
      return `${DOWNLOAD_URL}/linux/ffmpeg`;
  }
}

const cache_dir = '.cache';
let ffmpegDownloadState = false;
let downloadFFmpegPromise: any = null;

export default class RecorderDownloader {
  basePath: string;
  cachePath: string;
  ffmpegExePath: string;

  downloadProgress: ProgressItem[];
  sessionId: number;
  urls: string[];
  creating: boolean;
  reportProgressTimer: any;
  onProgress: (progress: number) => void;

  constructor(
    basePath: string,
    sessionId: number,
    urls: string[],
    onProgress: (progress: number) => void,
  ) {
    this.downloadProgress = [];
    this.basePath = basePath;
    this.sessionId = sessionId;
    this.urls = urls;
    this.creating = false;
    this.onProgress = onProgress;
    const { dataDir } = db.getDb().get('sysPres').value();
    this.ffmpegExePath = path.join(dataDir, getFFmpegExeName());
    ffmpegDownloadState = fs.existsSync(this.ffmpegExePath);
    if (!fs.existsSync(this.basePath)) {
      fs.mkdirSync(this.basePath, { recursive: true });
    }
    this.cachePath = path.join(this.basePath, cache_dir);
    if (!fs.existsSync(this.cachePath)) {
      fs.mkdirSync(this.cachePath, { recursive: true });
    }
    this.downloadSessionRecord();
    this.reportProgressTimer = setInterval(() => {
      this.reportProgress();
    }, 1000);
  }

  async reportProgress() {
    const percentage = await this.getDownloadProgress();
    this.onProgress(percentage);
  }

  /**
   * 会话的录像是否已经下载了
   * @param sessionId
   */
  async isSessionRecordDownloaded(): Promise<boolean> {
    const mp4Path = path.join(this.basePath, this.sessionId + '.mp4');
    return fs.existsSync(mp4Path);
  }

  removeCacheDir() {
    fs.removeSync(path.join(this.cachePath, String(this.sessionId)));
  }

  /**
   * 下载会话录像
   * @param sessionId
   */
  async downloadSessionRecord() {
    const downloaded = await this.isSessionRecordDownloaded();
    if (downloaded) {
      clearInterval(this.reportProgressTimer);
      return;
    }
    this.removeCacheDir();
    // 请求所有slice
    for (let i = 0; i < this.urls.length; i++) {
      const url = this.urls[i];
      const download = new Downloader({
        url,
        directory: path.join(this.cachePath, String(this.sessionId)),
        fileName: `${i}.mp4`,
        maxAttempts: 10,
        onResponse: (res) => {
          this.downloadProgress[i] = {
            downloadedSize: 0,
            percentage: 0,
            totalSize: Number(res.headers['content-length'] ?? '0'),
          };
        },
        onProgress: (percentage: string, chunk: object, remaningSize: number) => {
          this.downloadProgress[i].percentage = Number(percentage);
          this.downloadProgress[i].downloadedSize =
            this.downloadProgress[i].totalSize - remaningSize;
          if (parseInt(percentage) === 100) {
            this.checkDownloadProgress();
          }
        },
        onError: (e: Error) => {
          console.error('download record slice error', e);
        },
      });
      download.download();
    }
  }

  /**
   * 检查进度，如果所有 slice 都下载完了，就开始合并
   */
  async checkDownloadProgress() {
    const done = !_.some(this.downloadProgress, (item) => item.percentage < 100);
    if (done && !this.creating) {
      this.creating = true;
      // 合并视频
      try {
        setTimeout(() => {
          this.concatFile();
        }, 1000);
      } catch (e) {
        this.creating = false;
        // 清理临时目录
        this.removeCacheDir();
        throw e;
      }
    }
  }

  downloadFFmpeg() {
    return new Promise((resolve) => {
      try {
        const { dataDir } = db.getDb().get('sysPres').value();
        const download = new Downloader({
          url: getFFmpegDownloadUrl(),
          directory: dataDir,
          fileName: getFFmpegExeName(),
          maxAttempts: 10,
        });
        download.download().then(() => {
          ffmpegDownloadState = true;
          fs.chmod(this.ffmpegExePath, 0o755, (err) => {
            if (err) throw err;
            resolve(true);
          });
        });
      } catch (e) {
        setTimeout(() => {
          this.downloadFFmpeg();
        }, 5000);
      }
    });
  }

  concatFile() {
    if (!ffmpegDownloadState) {
      if (!downloadFFmpegPromise) {
        downloadFFmpegPromise = this.downloadFFmpeg();
      }
      downloadFFmpegPromise.then(() => {
        this.doConcatFile();
      });
    } else {
      this.doConcatFile();
    }
  }

  doConcatFile() {
    const dir = path.join(this.cachePath, String(this.sessionId));
    const files = fs.readdirSync(dir);
    const filePathList = [];
    for (const filePath of files) {
      filePathList.push(path.join(dir, filePath));
    }
    const destFilePath = path.join(this.basePath, `${this.sessionId}.webm`);
    const concatTxtPath = path.join(this.cachePath, String(this.sessionId), 'concat.txt');
    //生成 concat.txt 文件
    let txt = '';
    for (let mp4 of filePathList) {
      txt += `file ${mp4.replace(/\\/g, '\\\\').replace(/\s/g, '\\ ')}\n`;
    }
    fs.writeFileSync(concatTxtPath, txt);
    let command = `"${this.ffmpegExePath}" -safe 0 -y -f concat -i "${concatTxtPath}" -c copy "${destFilePath}"`;
    exec(command, (error, stdout, stderr) => {
      this.creating = false;
      // 清理临时目录
      this.removeCacheDir();
      if (error) {
        console.error(error);
      }
      console.log('concat slice success');
      this.reportProgress();
      clearInterval(this.reportProgressTimer);
    });
  }

  /**
   * 获取某个录像的下载进度。
   * 如果录像未下载也未点下载，返回0。如果已经下载过返回100。正在下载的返回真实进度
   * @param sessionId
   */
  async getDownloadProgress() {
    const downloaded = await this.isSessionRecordDownloaded();
    if (downloaded) {
      return 100;
    }
    //未下载，从正在下载列表里找
    const totalSize = _.sumBy(this.downloadProgress, (item) => item.totalSize);
    const downloadedSize = _.sumBy(this.downloadProgress, (item) => item.downloadedSize);
    return Math.min(this.creating ? 95 : 100, (downloadedSize * 100) / totalSize);
  }
}
