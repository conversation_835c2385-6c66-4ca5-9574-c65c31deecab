import { app, BrowserWindow, dialog, ipcMain, IpcMainEvent, shell } from 'electron';
import AutoLaunch from 'electron-auto-launch';
import fs from 'fs-extra';
import path from 'path';
import _ from 'lodash';
import psList from 'ps-list';
import log from 'electron-log';
import wmic from 'ms-wmic';
import { IPC } from '../types';
import db, { getDataSavePath } from '../components/db';
import * as windowUtil from './window';
import {
  getChromium,
  getChromiums,
  getMainWindow,
  getShopChromiums,
  openSystemPrefWindow,
  shopBrowsersLock,
} from './window';
import RecorderDownloader from './recorder-downloader';
import logger from '@e/services/logger';
import { getFingerprint } from '@e/utils/fpCollect';
import browserExeFetcher from '@e/utils/fpCollect/browserExeFetcher';
import {
  cleanLocalShopData,
  convertCvs2Json,
  convertJson2Cvs,
  getShopDataPath,
  isWinPlatform,
  tcpPing,
} from '@e/utils/utils';
import common from '@e/utils/ipc/common';
import browser from '@e/utils/ipc/browser';
import rpa from '@e/utils/ipc/rpa';
import disk from '@e/utils/ipc/disk';
import browseSync from '@e/utils/ipc/browser-sync';
import updater from '@e/utils/ipc/updater';
import helper from '@e/utils/ipc/helper';
import recorder from '@e/utils/ipc/recorder';
import mobile from '@e/utils/ipc/mobile';
import requests_ipc from '@e/utils/ipc/requests_ipc';
import CookiesSync from '@e/components/cookies';
import BookmarksSync from '@e/components/bookmarks';
import LoginDatabase from '@e/components/loginDatabase';
import HistorySync from '@e/components/history';
import {
  getTargetTransits,
  processProxyData,
  proxyProbe,
  ProxyProps,
  readSystemProxy,
} from '@e/utils/proxy_util';
import { appPowerSaveBlocker } from '@e/components/powerSaveBlocker';
import { backendTaskIns } from '@e/components/backendTask';
import ping, { client2TransitDetector } from '@e/utils/ipc/ping';
import { createShopShortcut } from '@e/components/shopShortcut';
import i18n from '@e/utils/i18n';
import RecordsDownloader from '@e/utils/records-downloader';
import request, { rawRequest, resolveApiUrl } from '@e/services/request';
import { getDynamicPath } from '@e/configs/app';
import KernelManage from '@e/components/kernelManage';
import bookmarks from '@e/utils/ipc/bookmarks';
import aiAgent from '@e/utils/ipc/ai-agent';
import axios from 'axios';
import { createPipeTunnel, createSocksTunnel } from '@e/utils/tunnels/direct/frontend';
import autofill from '@e/utils/ipc/autofill';

type EvtCallback = {
  (evt: IpcMainEvent, data: any): void;
};

type removeEventListenerFn = {
  (): void;
};

export const channel = 'asynchronous-message';
const listener: {
  [p: string]: { fun: EvtCallback; once: boolean }[];
} = {};

export const wrap = (event: string, data: any) => {
  return {
    event,
    data,
  };
};

/**
 * 清理浏览器残留进程
 */
export async function killBrowserProcess() {
  const arr = await new Promise<psList.ProcessDescriptor[]>((resolve) => {
    const timer = setTimeout(() => resolve([]), 5 * 1000);
    if (isWinPlatform()) {
      wmic.process.get(
        {
          where: { name: 'chrome.exe' },
          get: ['Name', 'ProcessId', 'ExecutablePath', 'ParentProcessId'],
        },
        (err: any, processes: any[]) => {
          if (err) {
            logger.error('[APP] wmic get process failed', err);
            psList({ all: false }).then((arr) => {
              clearTimeout(timer);
              resolve(arr);
            });
            return;
          }
          resolve(
            (processes || [])
              .filter((p) =>
                p.ExecutablePath.includes(path.join(getDataSavePath('appData'), app.getName())),
              )
              .map((p) => {
                return {
                  pid: Number(p.ProcessId),
                  ppid: Number(p.ParentProcessId),
                  name: p.Name,
                  force: true,
                };
              }),
          );
        },
      );
    } else {
      psList({ all: false }).then((arr) => {
        clearTimeout(timer);
        resolve(arr);
      });
    }
  });
  const browserProcessName = isWinPlatform() ? 'chrome.exe' : 'Chromium';
  const targetProcess = arr.filter(
    // @ts-ignore
    (vo) => vo.name === browserProcessName && (vo.ppid === process.pid || vo.force),
  );
  try {
    for (let p of targetProcess) {
      logger.info(`[APP] kill browser process - ${p.pid}`);
      process.kill(p.pid);
    }
  } catch (e) {
    logger.error('[APP] kill browser process error', e);
  }
  return targetProcess;
}

export const addEventListener = (
  name: IPC.Event | string,
  callback: EvtCallback,
  once = false,
): removeEventListenerFn => {
  const arr = listener[name] || [];
  listener[name] = arr;
  const idx = arr.length;
  arr.push({ fun: callback, once });
  if (!once) {
    return () => {
      if (!arr) return;
      const idx = arr.findIndex(({ fun }) => fun === callback);
      if (idx !== -1) {
        arr.splice(idx, 1);
      }
    };
  }
  return () => {};
};

/**
 * 主动向渲染进程发消息
 * @param evtName
 * @param data
 * @param browser
 */
export const dispatchMsg = (evtName: string, data = {}, browser = windowUtil.getMainWindow()) => {
  try {
    if (browser) {
      const webContent = browser.webContents;
      logger.verbose(`[IPC] post message to webContent`);
      webContent.postMessage(channel, {
        event: evtName,
        data,
      });
    }
  } catch (e) {
    logger.error('[IPC] dispatchEvent error', e);
  }
};

export const init = () => {
  ipcMain.on(channel, (evt: IpcMainEvent, args: any) => {
    logger.verbose(`[IPC] receive message from channel: ${channel}`, args);
    if (!args) {
      return;
    }
    const { event, data } = args;
    const callbacks = listener[event] ?? [];
    if (callbacks.length > 0) {
      for (let i = callbacks.length - 1; i >= 0; i -= 1) {
        const callback = callbacks[i];
        callback.fun(evt, data);
        if (callback.once) {
          callbacks.splice(i, 1);
        }
      }
      if (callbacks.length === 0) {
        // 如果没有监听者了，取消监听
        delete listener[event];
      }
    }
  });

  // 获取当前登录用户 jwt，（同步消息）
  ipcMain.on('get-jwt', (evt: IpcMainEvent, args) => {
    evt.returnValue = db.getJwt();
  });
  ipcMain.on('remove-default-team', (evt, { userId }) => {
    if (userId) {
      evt.returnValue = db.getDb().set(['defaultTeam', userId], undefined).write();
    } else {
      evt.returnValue = db.getDb().set('defaultTeam', {}).write();
    }
  });
  ipcMain.on('set-default-team', (evt, { userId, teamId }) => {
    evt.returnValue = db.getDb().set(['defaultTeam', userId], teamId).write();
  });
  ipcMain.on('get-default-team', (evt, { userId }) => {
    evt.returnValue = db.getDb().get(['defaultTeam', userId], undefined).value();
  });
  // 获取门户 endpoint
  ipcMain.on(IPC.Event.GET_PORTAL_URL, (evt) => {
    evt.returnValue = db.getPortalUrl();
  });
  // 获取 API endpoint
  ipcMain.on(IPC.Event.GET_API_URL, (evt) => {
    evt.returnValue = db.getApiUrl();
  });
  ipcMain.handle('get-download-url', (evt) => {
    return db.getDlUrl();
  });
  // 获取系统偏好设置
  ipcMain.on('get-sys-pres', (evt) => {
    evt.returnValue = db.getDb().get('sysPres').value();
  });
  // 执行窗口指令
  ipcMain.handle('do-window-action', (event, args) => {
    const win = BrowserWindow.fromWebContents(event.sender);
    if (!win) return;
    const { action, data } = args;
    switch (action) {
      case 'minimize':
        win.minimize();
        break;
      case 'maximize':
        if (win.isMaximized()) {
          win.unmaximize();
        } else {
          win.maximize();
        }
        break;
      case 'close':
        win.close();
        break;
    }
  });
  // 解压浏览器内核
  ipcMain.handle('unzip-browser', async (evt, data) => {
    try {
      const { kernelVersion, shopId, unzip = true, killProcess, deleteShopData } = data || {};
      if (killProcess) {
        await killBrowserProcess();
      }
      if (unzip) {
        await KernelManage.getInstance().unzipKernel(kernelVersion);
      }
      if (deleteShopData && shopId) {
        cleanLocalShopData(shopId);
      }
      return true;
    } catch (e) {
      throw e;
    }
  });
  // 获取当前打开的会话 ids
  ipcMain.on('get-opening-session-ids', (evt, { teamId }) => {
    evt.returnValue = windowUtil.getSessionIds(teamId) || [];
  });
  // 获取当前已打开的浏览器
  ipcMain.handle('get-opening-chromiums', (evt) => {
    return Object.values(windowUtil.getChromiums())
      .filter((v) => !!v)
      .map((c) => ({
        kernelPath: c!.kernelPath,
        sessionId: c!.sessionId,
        shopId: c!.shopInfo.id,
        rpaFlowId: c!.rpaFlowId,
        rpaTaskId: c!.rpaTaskId,
        rpaTaskItemId: c!.rpaTaskItemId,
      }));
  });
  // 关闭当前打开的浏览器
  ipcMain.handle('close-all-browsers', () => {
    return new Promise((resolve, reject) => {
      let timer: any = 0;
      try {
        const chromiums = getChromiums();
        _.forEach(chromiums, (chromium) => {
          chromium?.close();
        });
        timer = setInterval(() => {
          if (!_.some(chromiums, (v) => !!v)) {
            clearInterval(timer);
            resolve(true);
          }
        }, 1000);
      } catch (e) {
        clearInterval(timer);
        reject(e);
      }
    });
  });
  // 打开mstsc rdp会话，及密码代填
  ipcMain.handle('open-host-session', (evt, data) => {
    throw new Error('not supported');
  });
  // 获取浏览器执行路径
  ipcMain.handle('get-browser-location', (evt, data) => {
    return browserExeFetcher(data.browser);
  });
  ipcMain.handle('get-fingerprint', async (evt, data) => {
    try {
      const config = await getFingerprint(data.browser);
      return {
        success: true,
        config,
      };
    } catch (e: any) {
      return {
        success: false,
        message: e.message,
      };
    }
  });
  // 测速
  ipcMain.handle('get-endpoint-ping', async (evt, data) => {
    const { host, port } = data;
    return await tcpPing(host, port);
  });
  // 探测IP代理
  ipcMain.handle(
    'ip-proxy-probe',
    async (
      evt,
      data: ProxyProps & {
        networkType: 'UseDirect' | 'UseProxy' | 'UseSystem' | 'UseLocalFrontend';
        ipv6?: boolean;
      },
    ) => {
      if (data.networkType === 'UseDirect') {
        // 本机直连
        return axios
          .get(resolveApiUrl('/api/transitMyIp'), {
            headers: {
              'Content-Type': 'application/json',
            },
          })
          .then((res) => {
            const { status, data } = res;
            return {
              success: status === 200 && !!data,
              ip: data,
            };
          });
      }
      let systemProxy;
      if (data.networkType === 'UseSystem') {
        // 系统代理
        systemProxy = (await readSystemProxy()).proxy;
        if (!systemProxy || !systemProxy.host || !systemProxy.port) {
          return Promise.resolve({
            success: false,
            noSystemProxy: true,
          });
        }
      }
      if (data.networkType === 'UseLocalFrontend') {
        // 通过本地前置代理探测代理IP
        const sysPres = db.getSysPres();
        let proxyUrl = '';
        if (sysPres.localFrontendProxyEnabled) {
          // 开启了本地前置代理
          if (sysPres.localFrontendProxyMode === 'system') {
            const res = await readSystemProxy();
            if (res.success && res.proxy) {
              proxyUrl = `${res.proxy.proxyType}://${res.proxy.host}:${res.proxy.port}`;
            }
          } else if (sysPres.localFrontendProxyUrl) {
            proxyUrl = sysPres.localFrontendProxyUrl;
          }
        }
        if (proxyUrl) {
          const frontendProxy = createPipeTunnel(proxyUrl, data.host, data.port)!;
          try {
            let frontInfo = await frontendProxy.open();
            data.host = frontInfo.host;
            data.port = frontInfo.port;
            const proxyProps = processProxyData(data);
            return await proxyProbe(proxyProps);
          } catch (e) {
            logger.error('[IPC] open local frontend proxy failed', e);
            return Promise.resolve({
              success: false,
              error: i18n.t('用户自定义代理探测失败'),
            });
          } finally {
            frontendProxy.close();
          }
        } else {
          return Promise.resolve({
            success: false,
            error: i18n.t('偏好设置中未开启海外IP加速'),
          });
        }
      }
      const proxyProps = processProxyData(data, systemProxy);
      return proxyProbe(proxyProps);
    },
  );

  // 打开登录页面
  addEventListener(IPC.Event.OPEN_LOGIN_WINDOW, (evt, data) => {
    const {} = data;
    const wins = BrowserWindow.getAllWindows();
    windowUtil.openLoginWindow('/login', true);
    // 关闭其它窗口
    wins.forEach((win) => {
      console.log(win.title);
    });
  });

  // 打开主页面
  addEventListener(IPC.Event.OPEN_HOME_WINDOW, (evt, data) => {
    windowUtil.openMainWindow(data.url);
    windowUtil.closeLoginWindow();
  });

  // 打开店铺页面, 监听进度
  addEventListener(IPC.Event.OPEN_SHOP_WINDOW, (evt, data) => {
    try {
      windowUtil.openShopWindow(data, (shopId, uuid, vo) => {
        evt.reply(channel, { event: 'open-shop-progress', data: { shopId, uuid, vo } });
      });
    } catch (e) {}
  });
  // 功能屏蔽回传
  addEventListener(IPC.Event.CREATE_BLOCK_ELEMENT_RULE, (evt, data) => {
    evt.reply(channel, { event: IPC.Event.CREATE_BLOCK_ELEMENT_RULE, data });
  });

  // 打开店铺页面, 返回最终状态
  ipcMain.handle(IPC.Event.OPEN_SHOP_WINDOW, async (evt, data) => {
    try {
      const status = await new Promise((resolve) => {
        windowUtil.openShopWindow(data, (shopId, uuid, vo) => {
          dispatchMsg('open-shop-progress', { shopId, uuid, vo });
          if (['success', 'fail'].includes(vo.status)) {
            resolve(vo);
          }
        });
      });
      return status;
    } catch (e) {
      return 'fail';
    }
  });

  // 是否存在已打开的会话
  ipcMain.handle('is-exist-shop-session', (evt, data) => {
    const c = getChromium(data);
    return !!c && c.getBrowser()?.isConnected();
  });

  ipcMain.handle('get-all-window', (evt, data) => {
    const wins = BrowserWindow.getAllWindows();
    // @ts-ignore
    return wins.map((win) => ({ type: win._winType, flowId: win._flowId }));
  });

  // 发送浏览器指令
  ipcMain.handle('send-browser-cmd', (evt, data) => {
    const { cmd, shopId, sessionId } = data;
    const chromium = getChromium(data);
    if (cmd === 'close-browser') {
      const releaseLockTimer = setTimeout(() => {
        shopBrowsersLock[shopId] = false;
        getChromiums()[sessionId] = null;
        getShopChromiums()[shopId] = null;
        clearTimeout(releaseLockTimer);
      }, 5 * 1000);
      chromium?.addAfterCloseListener(() => {
        shopBrowsersLock[shopId] = false;
        clearTimeout(releaseLockTimer);
      });
    }
    if (!chromium) return;
    chromium.handleCmd(data.cmd, data.data);
  });

  ipcMain.on('ipc-log-to-main', (evt, string) => {
    if (string) {
      logger.error('ipc log to main', string);
    }
  });

  // 更新系统偏好设置
  ipcMain.handle('set-sys-pres', async (evt, data) => {
    return new Promise(async (resolve) => {
      try {
        const appAutoLauncher = new AutoLaunch({
          name: 'DaMai',
        });
        // 开机自启动设置
        const isEnabled = await appAutoLauncher.isEnabled();
        if (data.autoLaunch && !isEnabled) {
          await appAutoLauncher.enable();
        } else if (!data.autoLaunch && isEnabled) {
          await appAutoLauncher.disable();
        }
      } catch (e) {
        logger.error('[APP] AutoLaunch error', e);
      }
      appPowerSaveBlocker.update(data.preventSleep);
      let jobTimer = setTimeout(() => {
        db.getDb().set('sysPres', data).write();
        resolve({ success: true });
      }, 60 * 1000);
      try {
        const { dataDir, rpaDir } = db.getDb().get('sysPres').value();
        const fileTransQueue: Promise<void>[] = [];
        if (data.dataDir !== dataDir && fs.existsSync(dataDir)) {
          // 移动文件
          fileTransQueue.push(
            new Promise((resolve, reject) => {
              fs.move(dataDir, data.dataDir, { overwrite: true }, (err) => {
                if (err) {
                  logger.error('[APP] move browser data dir failed', err);
                  reject(err);
                }
                resolve();
              });
            }),
          );
        }
        if (data.rpaDir !== rpaDir && fs.existsSync(rpaDir)) {
          fileTransQueue.push(
            new Promise((resolve, reject) => {
              fs.move(rpaDir, data.rpaDir, { overwrite: true }, (err) => {
                if (err) {
                  logger.error('[APP] move rpa dir failed', err);
                  reject(err);
                }
                resolve();
              });
            }),
          );
        }
        if (fileTransQueue.length) {
          log.transports.file.level = false;
          await backendTaskIns.stopGcHelper();
          await Promise.all(fileTransQueue);
        }
        db.getDb().set('sysPres', data).write();
        resolve({ success: true });
      } catch (e: any) {
        logger.error('[APP] set sysPres error', e);
        resolve({ success: false, message: e.message });
      } finally {
        clearTimeout(jobTimer);
        if (!log.transports.file.level) {
          log.transports.file.level = db.getDb().get('LOG_LEVEL').value();
        }
      }
    });
  });

  // 用户登录
  addEventListener(IPC.Event.JWT_UPDATE, (evt, data) => {
    logger.info('[APP] JWT_UPDATE');
    if (!data.jwt) {
      logger.info('[APP] 客户端收到清空JWT指令');
      if (!windowUtil.getMainWindow()) {
        db.getDb().set('account', data).write();
        backendTaskIns.stop();
      }
    } else {
      db.getDb().set('account', data).write();
    }
  });

  addEventListener(IPC.Event.SET_API_URL, (evt, data) => {
    db.getDb().set('API_URL', data.API_URL).write();
  });

  // 通过外部浏览器打开链接
  addEventListener(IPC.Event.OPEN_EXTERNAL_URL, (evt, data) => {
    if (data.url) {
      shell.openExternal(data.url);
    }
  });

  // 打开路径
  addEventListener('open-path', async (evt, data) => {
    if (data.path) {
      await fs.ensureDir(data.path);
      shell.openPath(data.path);
    }
  });

  // 选择路径
  addEventListener('show-path-select-dialog', (evt, data) => {
    dialog
      .showOpenDialog({
        title: i18n.t('下载文件到'),
        defaultPath: data.defaultPath ?? '',
        properties: ['openDirectory', 'createDirectory'],
      })
      .then(({ filePaths }) => {
        if (filePaths.length > 0) {
          evt.reply(channel, {
            event: 'path-selected',
            data: { key: data.key, path: filePaths[0], separator: path.sep },
          });
        }
      });
  });

  // 下载录像
  addEventListener('download-session-record', (evt, data) => {
    logger.verbose('[IPC] download-session-record', data);
    const { videoDir } = db.getDb().get('sysPres').value();
    new RecorderDownloader(videoDir, data.sessionId, data.urls, (progress) => {
      evt.reply(channel, {
        event: 'session-record-download-info',
        data: { progress },
      });
    });
  });
  // 下载录像
  addEventListener('batch-download-session-records', (evt, data) => {
    logger.verbose('[IPC] download-session-record', data);
    const { videoDir } = db.getDb().get('sysPres').value();
    new RecordsDownloader(videoDir, data, (progress) => {
      const { taskId, teamId } = data;
      request(`/api/task/${taskId}/progress`, {
        method: 'put',
        teamId,
        params: {
          progress,
        },
      });
      if (progress === 100) {
        request(`/api/task/${taskId}/finished`, {
          method: 'put',
          teamId,
          params: {
            progress,
            status: 'Success',
          },
        });
      }
      evt.reply(channel, {
        event: 'batch-download-session-records-progress',
        data: { progress, taskId: data.taskId, path: videoDir },
      });
    });
  });

  // 获取录像下载状态
  addEventListener('get-session-record-download-info', (evt, data) => {
    const { sessionId } = data;
    let downloaded = false;
    if (sessionId) {
      const { videoDir } = db.getDb().get('sysPres').value();
      downloaded = fs.existsSync(path.join(videoDir, `${sessionId}.webm`));
    }
    evt.reply(channel, {
      event: 'session-record-download-info',
      data: { progress: downloaded ? 100 : 0 },
    });
  });

  // 打开录像下载地址
  addEventListener('show-record-download-path', () => {
    const { videoDir } = db.getDb().get('sysPres').value();
    shell.openPath(path.join(videoDir));
  });

  // 清理cookie
  addEventListener('clear-cookies', (evt, data) => {
    const { shopId } = data;
    try {
      CookiesSync.clear(shopId);
    } catch (e) {
      logger.reportError(e, { noNotify: true });
    }
  });
  // 清理书签
  addEventListener('clear-bookmarks', (evt, data) => {
    const { shopId } = data;
    try {
      BookmarksSync.clear(shopId);
    } catch (e) {
      logger.reportError(e, { noNotify: true });
    }
  });
  // 清理缓存
  addEventListener('clear-cache', (evt, data) => {
    const { shopId } = data;
    cleanLocalShopData(shopId, ['Cache', 'Code Cache']);
  });
  // 清理indexDB
  addEventListener('clear-indexDB', (evt, data) => {
    const { shopId } = data;
    cleanLocalShopData(shopId, ['IndexedDB']);
  });
  // 清理localStorage
  addEventListener('clear-localstorage', (evt, data) => {
    const { shopId } = data;
    const dir = getShopDataPath(shopId);
    cleanLocalShopData(shopId, ['Local Storage']);
  });
  // 删除账号清理shop-data
  addEventListener('clear-shop', (evt, data) => {
    const { ids } = data;
    ids.forEach((id: number) => {
      cleanLocalShopData(id);
    });
  });
  // 清理历史记录
  addEventListener('clear-history', (evt, data) => {
    const { shopId } = data;
    try {
      HistorySync.clear(shopId);
    } catch (e) {
      logger.reportError(e, { noNotify: true });
    }
  });
  // 清理密码
  addEventListener('clear-passwords', (evt, data) => {
    const { shopId } = data;
    try {
      LoginDatabase.clear(shopId);
    } catch (e) {
      logger.reportError(e, { noNotify: true });
    }
  });

  addEventListener('download-content', (evt, data) => {
    const { content = '', filename, json2Cvs = false } = data;
    let file = 'tmp.txt';
    let str = '';
    if (filename) {
      file = filename;
    }
    if (json2Cvs) {
      str = convertJson2Cvs(content, { quotes: true });
      const BOM = Buffer.from('\uFEFF');
      str = Buffer.concat([BOM, Buffer.from(str)]).toString();
    } else {
      str = content;
    }
    dialog
      .showSaveDialog({
        defaultPath: path.join(getDataSavePath('documents'), file),
      })
      .then(({ filePath }) => {
        if (filePath) {
          // 写文件
          fs.outputFile(filePath, str);
        }
      });
  });

  addEventListener('open-system-pref', (evt, data) => {
    if (db.isRuntimeMode()) {
      openSystemPrefWindow();
    } else {
      getMainWindow()?.show();
      dispatchMsg('open-system-pref', data);
    }
  });

  ipcMain.handle('read-file-content', (evt, data) => {
    const { maxSize = 10 * 1024 * 1024 } = data;
    return new Promise((resolve) => {
      dialog
        .showOpenDialog({
          defaultPath: getDataSavePath('documents'),
          properties: ['openFile'],
        })
        .then(({ filePaths }) => {
          if (filePaths.length > 0) {
            const filepath = filePaths[0];
            fs.stat(filepath).then((stats) => {
              if (stats.size > maxSize) {
                dialog.showErrorBox(
                  i18n.t('文件大小超过限制'),
                  i18n.t('文件最大不得超过{{size}}MB', { size: maxSize / (1024 * 1024) }),
                );
              } else {
                fs.readFile(filepath, 'utf8').then((str) => {
                  resolve({ filepath, content: str });
                });
              }
            });
          }
        });
    });
  });

  ipcMain.handle('upload-csv-file', (evt, data) => {
    return new Promise((resolve) => {
      dialog
        .showOpenDialog({
          defaultPath: getDataSavePath('documents'),
          filters: [{ name: 'CSV', extensions: ['csv'] }],
          properties: ['openFile'],
        })
        .then(({ filePaths }) => {
          if (filePaths.length > 0) {
            const filepath = filePaths[0];
            fs.readFile(filepath, 'utf8').then((str) => {
              const res = convertCvs2Json(str, { delimiter: ',' });
              resolve({ filepath, ...res });
            });
          }
        });
    });
  });

  ipcMain.handle('select-path', (evt, data = {}) => {
    return new Promise((resolve) => {
      dialog
        .showOpenDialog({
          title: i18n.t('请选择资源位置'),
          defaultPath: data.defaultPath ?? '',
          filters: data.filters ?? [],
          properties: data.properties || ['openDirectory', 'createDirectory'],
        })
        .then(({ filePaths }) => {
          resolve(filePaths.length > 0 ? filePaths[0] : '');
        });
    });
  });

  ipcMain.handle('user-login-callback', () => {
    backendTaskIns.start();
    client2TransitDetector.start();
  });
  ipcMain.handle('open-shop-data-dir', async (evt, data) => {
    if (data.shopId) {
      const _path = getShopDataPath(data.shopId);
      await fs.ensureDir(_path);
      shell.openPath(_path);
    }
  });
  ipcMain.handle('open-log-dir', async (evt, data) => {
    const { LOG_DIR } = getDynamicPath();
    await fs.ensureDir(LOG_DIR);
    shell.openPath(LOG_DIR);
  });
  ipcMain.handle('native-fetch', async (evt, data) => {
    const { url, options = {} } = data || {};
    if (!url) {
      return Promise.resolve({
        success: false,
        data: 'No Url',
      });
    }
    return new Promise((resolve, reject) => {
      rawRequest(url, options || {})
        .then((res) => {
          resolve({
            success: true,
            data: res,
          });
        })
        .catch((e) => {
          resolve({
            success: false,
            data: e.message,
          });
        });
    });
  });
  // 本地前置代理测试
  ipcMain.handle('local-frontend-proxy-test', async (evt, data) => {
    const { localFrontendProxyMode, localFrontendProxyUrl, testUrls } = data;
    let proxyUrl = '';
    if (localFrontendProxyMode === 'system') {
      const res = await readSystemProxy();
      if (res.success && res.proxy) {
        proxyUrl = `${res.proxy.proxyType}://${res.proxy.host}:${res.proxy.port}`;
      } else {
        return {
          success: false,
          message: '未检测到系统代理',
        };
      }
    } else {
      if (!localFrontendProxyUrl) {
        return {
          success: false,
          message: '代理服务地址不能为空',
        };
      }
      proxyUrl = localFrontendProxyUrl;
    }
    try {
      const frontendProxy = createSocksTunnel(proxyUrl)!;
      const pingRes = await frontendProxy.ping(testUrls);
      frontendProxy.close();
      return {
        success: true,
        delay: pingRes,
      };
    } catch (e: any) {
      return {
        success: false,
        message: typeof e === 'string' ? e : e.message,
      };
    }
  });
  // 本地前置代理测试
  ipcMain.handle('local-frontend-proxy-probe', async (evt, data) => {
    const { teamId } = data;
    const { localFrontendProxyMode, localFrontendProxyUrl, localFrontendProxyEnabled } = db
      .getDb()
      .get('sysPres')
      .value();
    if (localFrontendProxyEnabled) {
      let proxyUrl = '';
      if (localFrontendProxyMode === 'system') {
        const res = await readSystemProxy();
        if (res.success && res.proxy) {
          proxyUrl = `${res.proxy.proxyType}://${res.proxy.host}:${res.proxy.port}`;
        } else {
          return {
            success: false,
            error: '未检测到系统代理',
          };
        }
      } else {
        if (!localFrontendProxyUrl) {
          return {
            success: false,
            error: '代理服务地址不能为空',
          };
        }
        proxyUrl = localFrontendProxyUrl;
      }
      const _list: any[] = [];
      return await getTargetTransits({
        teamId,
        ipVersion: 'Auto',
      }).then(async (res) => {
        const frontendProxy = createSocksTunnel(proxyUrl)!;
        await frontendProxy.open();
        res.forEach((item) => {
          _list.push(frontendProxy.request(item.endpoint!));
        });
        try {
          return await new Promise((resolve) => {
            Promise.any(_list).then((_res) => {
              resolve({
                success: true,
                ip: _res,
              });
              if (frontendProxy) {
                frontendProxy.close();
              }
            });
          });
        } catch (e: any) {
          if (frontendProxy) {
            frontendProxy.close();
          }
          return {
            success: false,
            error: typeof e === 'string' ? e : e.message,
          };
        }
      });
    }
  });
  // 重启APP
  addEventListener('relaunch-app', () => {
    app.relaunch();
    app.exit(0);
  });
  // 创建快捷方式
  ipcMain.handle('create-shop-shortcut', async (evt, data) => {
    return createShopShortcut({
      ...data,
    });
  });

  //监听electron rpc请求
  common();
  rpa();
  disk();
  updater();
  helper();
  recorder();
  ping();
  browseSync();
  browser();
  mobile();
  requests_ipc();
  bookmarks();
  aiAgent();
  autofill();
};
