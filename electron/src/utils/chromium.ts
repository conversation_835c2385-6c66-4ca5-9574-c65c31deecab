import os from 'os';
import path from 'path';
import { screen, net, app } from 'electron';
import puppeteer, { <PERSON>rowser, CDPSession, Dialog, Page, Target } from 'donkey-puppeteer-core';
import _ from 'lodash';
import { ChildProcess, exec } from 'child_process';

import type { ShopInfo } from '../types';
import { RecordInfo } from '../types';
import appConfig, { getDynamicPath } from '../configs/app';
import { RequestAgent } from '../services/request';
import logger from '@e/services/logger';
import FingerprintConfig, { FingerprintDetail, IP } from './fingerprint';
import db from '../components/db';
import { AjaxEventClient } from './AjaxEventClient';
import { MessageDispatcher } from '@e/components/messageDispatcher';
import CookiesSync from '@e/components/cookies';
import { calcExtensionId, isMacPlatform, isWinPlatform } from '@e/utils/utils';
import { baseBackendTaskIns, getAjaxEventClientIns } from '@e/components/backendTask';
import type { RecordControllerOwner } from '@e/recorder/controller';
import BrowserActionRecorder from '@e/components/browserActionRecorder';
import { getChromiums, getRpaFlowPreviewWindow, getRpaSelectorPickerWindow } from '@e/utils/window';
import ChromiumInspect from './chromiumInspect';
import i18n from '@e/utils/i18n';
import { TunnelRouter } from '@e/utils/tunnelRouter';
import KernelManage from '@e/components/kernelManage';

export interface Props {
  wsDispatcher: MessageDispatcher;
  shopInfo: ShopInfo;
  userDataDir: string;
  ipDetail?: API.TeamIpDto;
  fingerprintConfig: FingerprintConfig;
  paymentPlatformInfo?: API.ShopPlatformVo;
  mailPlatformInfo?: API.ShopPlatformVo;
  proxyPacArg: string;
  tunnelRouter: TunnelRouter;
  cookiesSync?: CookiesSync;
  sessionId: number;
  recorderController: RecordControllerOwner;
  extensionPaths: string[];
  firstInstallExtensionIds: string[];
  rpaFlowId?: number;
  rpaTaskId?: number;
  rpaTaskItemId?: number;
  rpaPreview?: boolean;
  windowSize?: string;
  windowPosition?: string;
  remoteDebugPort?: number;
  browserSwitches?: string;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
  initUrl?: string;
  browserLanguage: string;
  headless?: boolean;
}

// 用不上了，暂时注释
// const StealthPlugin = PluginStealth({
//   enabledEvasions: new Set(['chrome.runtime'/*, 'navigator.webdriver'*/, 'defaultArgs']),
// });
// puppeteer.use(StealthPlugin);
class WindowPosition {
  private x: number;
  private y: number;
  private available: boolean;
  constructor(x: number, y: number) {
    this.x = x;
    this.y = y;
    this.available = true;
  }

  get() {
    this.available = false;
    return `${this.x},${this.y}`;
  }

  release() {
    this.available = true;
  }

  isAvailable() {
    return this.available;
  }
}
const windowPositionContainer: WindowPosition[] = [];
for (let i = 0; i < 50; i++) {
  windowPositionContainer.push(new WindowPosition(i * 5, i * 5));
}
function getWindowPosition() {
  return windowPositionContainer.find((p) => p.isAvailable());
}

const SERVER_URL =
  process.env.OEM_NAME === 'gg' ? 'http://ggbrowser.local/' : 'http://szdamai.local/';
const NEW_TAB_URL = process.env.OEM_NAME === 'gg' ? 'http://ggbrowser.tab/' : 'http://szdamai.tab/';
let lastOpenBrowserTimestamp = 0; // 上次打开浏览器的时间戳
let openBrowserIntervalMsDelta = 0; // 打开浏览器的间隔惩罚时间
let openBrowserIntervalTimer: any = 0;

/**
 * chromium 浏览器
 * 负责唤醒浏览器，以及后续的管理
 */
export class Chromium extends ChromiumInspect {
  wsDispatcher: MessageDispatcher;
  shopInfo: ShopInfo;
  browserTitle: string;
  paymentPlatformInfo?: API.ShopPlatformVo;
  mailPlatformInfo?: API.ShopPlatformVo;
  recordInfo: RecordInfo | null;
  fingerprintConfig: FingerprintConfig;
  ipDetail?: API.TeamIpDto;
  proxyPacArg: string;
  tunnelRouter: TunnelRouter;
  cookies: any[];
  cookiesSync?: CookiesSync;
  afterCloseListeners?: (() => void)[] = [];
  closeCallback?: () => Promise<void>;
  collectionCookiesDebounce: () => void;
  targetDestroyedDebounce: () => void;
  fetchMyIpDebounce: () => void;
  collectionCookiesTimer: any;
  sessionId: number;
  extensionPaths: string[];
  firstInstallExtensionIds: string[];
  rpaTaskId?: number;
  rpaTaskItemId?: number;
  openByRpaTask?: number; // 该浏览器窗口是通过哪个RPA任务打开的
  rpaPreview?: boolean;
  windowSize?: string;
  windowPosition?: string;
  tabUrls: string[];

  ajaxEventClient: AjaxEventClient;

  recorderController: RecordControllerOwner;
  autofillTimer: number;

  //当ip有切换时，有些环境变量需要动态调整，比如 'donkey-webrtc-host', 'donkey-lang', 'donkey-ac-lang', 'donkey-timezone-offset-minutes', 'donkey-geo-latitude', 'donkey-geo-longitude', 'donkey-geo-accuracy'
  dynamicEnvs?: string[]; //['donkey-webrtc-host=*******', ...]

  dialogListener?: (page: Page, dialog: Dialog) => any;
  chromiumCloseListeners?: (() => void)[] = [];
  remoteDebugPort?: number;
  browserSwitches?: string;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
  private userDataDir: string;
  initUrl?: string;
  private setCookieFailed: boolean;
  browserActionRecorder?: BrowserActionRecorder;
  cdpSession?: CDPSession;
  killBrowserTimer?: any;
  browserLanguage: string;
  headless: boolean;
  private fingerWhiteConfig: Record<string, string>;
  transitAutoChangeTransitProp: Record<string, any>;
  devtoolsConsole = false;
  kernelPath = '';
  speedLimit = 0;
  private speechSynthesisVoices: any[];
  private isSpawnedTimout = false;
  teamAiConfig: API.TeamAiConfig = {};

  constructor(props: Props) {
    super(props);
    let { devtoolsConsole } = db.getDb().get('sysPres').value();
    this.wsDispatcher = props.wsDispatcher;
    this.shopInfo = props.shopInfo;
    this.ipDetail = props.ipDetail;
    this.paymentPlatformInfo = props.paymentPlatformInfo;
    this.mailPlatformInfo = props.mailPlatformInfo;
    this.proxyPacArg = props.proxyPacArg;
    this.tunnelRouter = props.tunnelRouter;
    this.cookiesSync = props.cookiesSync;
    this.cookies = props.cookiesSync?.getCookies() ?? [];
    this.sessionId = props.sessionId;
    this.browserTitle = `${this.shopInfo.name}-${
      appConfig.isOEM ? app.getName() : i18n.t('花漾灵动')
    }-${this.sessionId}`;
    this.recordInfo = null;
    this.ajaxEventClient = getAjaxEventClientIns()!;
    this.recorderController = props.recorderController;
    this.extensionPaths = props.extensionPaths;
    this.firstInstallExtensionIds = props.firstInstallExtensionIds;
    this.rpaTaskId = props.rpaTaskId;
    this.openByRpaTask = props.rpaTaskId;
    this.rpaTaskItemId = props.rpaTaskItemId;
    this.rpaPreview = props.rpaPreview;
    this.windowSize = props.windowSize;
    this.windowPosition = props.windowPosition;
    this.tabUrls = this.readTabUrls();
    this.remoteDebugPort = props.remoteDebugPort;
    this.browserSwitches = props.browserSwitches;
    this.requestAgent = props.requestAgent;
    this.tempShopDataSubDir = props.tempShopDataSubDir;
    this.initUrl = props.initUrl;
    this.browserLanguage = props.browserLanguage;
    this.headless = props.headless ?? false;
    this.fingerprintConfig = props.fingerprintConfig;
    this.chromium = null;
    this.userDataDir = props.userDataDir;
    this.afterCloseListeners = [];
    this.closeCallback = () => {
      logger.error(
        `[BROWSER] closeCallback not set (sessionId: ${this.sessionId}, shopId: ${this.shopInfo.id})`,
      );
      return Promise.resolve();
    };
    this.setCookieFailed = false;
    this.collectionCookiesDebounce = _.debounce(this._collectionCookies.bind(this), 500, {
      leading: true,
      trailing: true,
    });
    this.targetDestroyedDebounce = _.debounce(this._handleTargetDestroyed.bind(this), 500, {
      leading: false,
      trailing: true,
    });
    this.fetchMyIpDebounce = _.debounce(this.fetchMyIp.bind(this), 500, {
      leading: false,
      trailing: true,
    });
    this.autofillTimer = 0;
    this.killBrowserTimer = 0;
    this.fingerWhiteConfig = {};
    this.transitAutoChangeTransitProp = this.tunnelRouter.getAutoChangeTransitProp();
    this.tunnelRouter.getChannelList().forEach((channel) => {
      if (channel && (channel.speedLimit ?? 0) > 0) {
        if (this.speedLimit === 0) {
          this.speedLimit = channel.speedLimit ?? 0;
        } else {
          this.speedLimit = Math.min(this.speedLimit, channel.speedLimit ?? 0);
        }
      }
    });
    this.devtoolsConsole = devtoolsConsole;
    this.speechSynthesisVoices = this.fingerprintConfig.getVoices();
  }

  init(): Promise<Chromium> {
    this.recordInfo = {
      sessionId: this.sessionId,
      windowName: this.browserTitle,
      cachePath: this.userDataDir,
      wsUrl: this.recorderController?.getWSUrl(),
      sessionStartTime: new Date().getTime(),
      userId: this.requestAgent.getProps().userId,
    };
    return new Promise((resolve, reject) => {
      const apiRequestArr: Promise<any>[] = [];
      apiRequestArr.push(
        this.requestAgent.request('/api/team/settings/browserDebug', {
          teamId: this.shopInfo.teamId,
        }),
      );
      apiRequestArr.push(
        this.requestAgent.request('/api/shop/settings/fingerWhiteConfig', {
          teamId: this.shopInfo.teamId,
        }),
      );
      apiRequestArr.push(
        this.requestAgent.request('/api/team/settings/shopCodeStatus', {
          teamId: this.shopInfo.teamId,
        }),
      );
      apiRequestArr.push(
        this.requestAgent.request('/api/rpa/task/ai/getAiConfig', {
          teamId: this.shopInfo.teamId,
        }),
      );
      Promise.allSettled(apiRequestArr)
        .then(([debugCodeRes, fingerWhiteConfigRes, shopCodeStatusRes, teamAiConfigRes]) => {
          let debugCode;
          if (debugCodeRes.status === 'fulfilled') {
            debugCode = debugCodeRes.value;
          }
          if (fingerWhiteConfigRes.status === 'fulfilled') {
            this.fingerWhiteConfig = fingerWhiteConfigRes.value.whitelist ?? {};
            process.env['donkey-pp-runtime-white-list'] =
              this.fingerWhiteConfig['donkey-pp-runtime-white-list'];
          }
          if (shopCodeStatusRes.status === 'fulfilled') {
            if (shopCodeStatusRes.value === 'disabled') {
              this.fingerprintConfig.setShopCode();
            }
          }
          let { devtools } = db.getDb().get('sysPres').value();
          if ((devtools && debugCode !== 'disabled') || db.isRpaExecutor()) {
            // 联营账号不允许开启 devtools
            if (!this.shopInfo.parentShopId || appConfig.DEBUG) {
              this.fingerprintConfig.setDevTools(true);
            }
          }
          if (appConfig.isOEM) {
            this.fingerprintConfig.setDevTools(false);
          }
          if (teamAiConfigRes.status === 'fulfilled') {
            this.teamAiConfig = teamAiConfigRes.value;
          }
        })
        .then(async () => {
          try {
            await this._spawn();
            resolve(this);
          } catch (e) {
            reject(e);
          }
        })
        .then(async () => {
          // 监听 cookie 变化
          this.wsDispatcher.getEmitter().on('cookieUpdated', this.collectionCookiesDebounce);
          // 监听显示切换接入点对话框事件
          this.wsDispatcher.getEmitter().on('show-switch-transit-modal', async () => {
            if (!this.chromium) return;
            try {
              const pages = await this.chromium.pages();
              let page = pages.find((p) => p.url().indexOf(SERVER_URL) === 0);
              if (!page) {
                page = await this.chromium.newPage();
              }
              await page.bringToFront();
              await page.goto(`${SERVER_URL}?switchTransit=true`);
            } catch (e) {}
          });
          // 监听元素选择器选中事件
          this.wsDispatcher.getEmitter().on('selector-finder-res', async () => {
            if (!this.rpaFlowId) return;
            const win = getRpaSelectorPickerWindow(this.rpaFlowId);
            if (!win) return;
            win.show();
            this.wsDispatcher.dispatcher(JSON.stringify({ action: 'get-pick-selector' }));
          });
          // 监听元素选择器选中结果事件
          this.wsDispatcher.getEmitter().on('get-pick-selector-res', (data) => {
            if (!this.rpaFlowId) return;
            const win = getRpaSelectorPickerWindow(this.rpaFlowId);
            win?.webContents.send('pick-selector-res', data);
          });
          this.wsDispatcher.getEmitter().on('tab-update', (urls) => {
            this.updateTabUrls(urls);
          });
          // 汇报 remoteDebugPort
          if (this.chromium) {
            const wsEndpoint = this.chromium.wsEndpoint();
            this.requestAgent.request(`/api/shop/session/${this.sessionId}/onOpen`, {
              method: 'PUT',
              teamId: this.shopInfo.teamId,
              data: {
                success: true,
                remoteDebugPort: wsEndpoint ? new URL(wsEndpoint).port : undefined,
                remoteProxyType: this.tunnelRouter.getDefaultChannel()?.getProxyInfo().type,
                remoteProxyPort: this.tunnelRouter.getDefaultChannel()?.getProxyInfo().port,
              },
            });
          }
          // 录像相关
          await this.recorderController.onSessionOpen(
            this.shopInfo,
            this.recordInfo,
            this.requestAgent,
          );
        });
    });
  }

  _getInitTabUrls() {
    if (this.initUrl) return [this.initUrl];
    switch (this.shopInfo.homePage) {
      case '_blank':
        return [`${NEW_TAB_URL}newTab`];
      case 'szdamai.local':
        return [SERVER_URL];
      case 'restoreSession':
        return this.readTabUrls();
      default:
        if (this.shopInfo.homePage) {
          return this.shopInfo.homePage
            .trim()
            .split('\n')
            .map((url) => {
              let _url = url.trim();
              if (!/^https?:\/\//.test(_url)) {
                _url = `https://${_url}`;
              }
              return _url;
            });
        } else {
          return [SERVER_URL];
        }
    }
  }

  /**
   * 设置浏览器 cookies
   */
  async _setCookie() {
    if (!this.cookiesSync?.isEnabled()) return;
    if (this.cdpSession) {
      // 先清理掉旧 cookie
      try {
        await this.cdpSession.send('Storage.clearCookies');
      } catch (e) {
        logger.error('[BROWSER] clear cookie error', e);
      }
      try {
        const cookies = _.map(this.cookies, (cookie) => ({
          ...cookie,
          path: cookie.path.replace(/\/data:image\/.+/g, ''),
          sameSite: cookie.sameSite === null ? undefined : cookie.sameSite,
        }));
        await this.cdpSession.send('Storage.setCookies', {
          cookies,
        });
      } catch (e) {
        this.setCookieFailed = true;
        this.cookiesSync?.exceptionOccur();
        logger.error('[BROWSER] set cookie error', e);
      }
    } else {
      this.cookiesSync?.exceptionOccur();
      logger.error('[BROWSER] skip set cookie because no CDPSession');
    }
  }

  /**
   * 收集 cookies
   */
  async _collectionCookies() {
    if (
      !this.cookiesSync?.isEnabled() ||
      this.setCookieFailed ||
      this.cookiesSync?.isOnlyDownload()
    )
      return;
    try {
      if (!this.chromium) return;
      if (!this.chromium.isConnected()) return;
      const res = await this.cdpSession?.send('Storage.getCookies');
      if (res && res.cookies) {
        this.cookiesSync.updateTempCookies(res.cookies);
      }
    } catch (e) {
      logger.error('[BROWSER] collection cookie fail', e);
    }
  }

  readTabUrls() {
    if (db.isRpaExecutor() || this.shopInfo.homePage !== 'restoreSession') return [];
    const urls = db.getDb().get('shopTabUrls').value()?.[this.shopInfo.id!] ?? [];
    if (_.isArray(urls)) return urls;
    return [];
  }
  saveTabUrls() {
    if (db.isRpaExecutor() || this.shopInfo.homePage !== 'restoreSession') return;
    try {
      db.getDb()
        .set('shopTabUrls', {
          ...db.getDb().get('shopTabUrls').value(),
          [this.shopInfo.id!]: this.tabUrls,
        })
        .write();
    } catch (e) {
      logger.error('[BROWSER] saveTabUrls error', e);
    }
  }
  updateTabUrls(urls: string[]) {
    this.tabUrls = urls;
  }

  /**
   * 开始"收集 cookies"定时任务
   */
  _startCookiesCollectionTask() {
    this.collectionCookiesTimer = setInterval(async () => {
      this.collectionCookiesDebounce();
    }, 5 * 1000);
  }

  networkThrottle(page: Page) {
    // 校验是否需要限速
    if (this.speedLimit === 0) {
      return;
    }
    if (page.url().startsWith('http://szdamai.')) {
      page.emulateNetworkConditions(null).catch((e) => {});
      return;
    }
    page
      .emulateNetworkConditions({
        download: (this.speedLimit * 1024) / 8,
        upload: 0,
        latency: 0,
      })
      .catch((e) => {
        // logger.error('[BROWSER] emulateNetworkConditions error', e);
      });
  }

  /**
   * 处理打开新页面事件
   * @param target
   */
  async _handleTargetCreated(target: Target) {
    try {
      if (target.type() !== 'page') {
        return;
      }
      const page = await target.page();
      if (!page) return;
      if (page.isClosed()) return;
      // 设置时区
      try {
        await page.emulateTimezone(this.fingerprintConfig.getTimezone());
      } catch (e) {
        logger.verbose('[BROWSER] emulateTimezone error');
      }
      this.watchDialog(page);
      await this.stealthPage(page);
      this.networkThrottle(page);
      page.on('framenavigated', (frame) => {
        this.networkThrottle(frame.page());
      });
    } catch (e) {
      logger.error(`[BROWSER] handle targetcreated error`, e);
    }
  }

  async _handleTargetDestroyed() {
    if (!this.chromium) return;
    const targets = this.chromium.targets();
    // console.log(
    //   '@@@targets',
    //   targets.map((v) => ({ type: v.type(), url: v.url() })),
    // );
    const pageTargets = targets.filter(
      (v) =>
        ['tab', 'page'].includes(v.type()) &&
        !/^chrome-extension:\/\/.+offscreen\.html$/.test(v.url()),
    );
    if (pageTargets.length === 0) {
      logger.info(`[BROWSER] closing (shopId: ${this.shopInfo.id}, session:${this.sessionId})`);
      try {
        await this.chromium.close();
      } catch (e) {
        logger.error(
          `[BROWSER] close browser error (shopId: ${this.shopInfo.id}, session:${this.sessionId})`,
          e,
        );
        throw e;
      }
    }
  }

  async _handleTargetChanged(target: Target) {
    try {
      if (
        (this.fingerWhiteConfig['donkey-pp-runtime-white-list'] || '')
          .split(',')
          .includes(new URL(target.url()).hostname)
      ) {
        const targetSession = await target.createCDPSession();
        await targetSession.send('Runtime.disable');
        targetSession.detach();
      }
    } catch (e) {
      logger.error(`[BROWSER] handle targetchanged error`, e);
    }
  }

  async fetchMyIp() {
    const clientIpLocation = await this.requestAgent.request(`/api/meta/ip/myLocation`);
    logger.info('[APP] myLocation', JSON.stringify(clientIpLocation, null, 2));
  }

  watchDialog(page: Page) {
    page.on('dialog', (dialog) => this.dialogListener && this.dialogListener(page, dialog));
  }

  addChromiumCloseListener(listener: () => any) {
    this.chromiumCloseListeners?.push(listener);
  }

  addAfterCloseListener(listener: () => any) {
    this.afterCloseListeners?.push(listener);
  }

  async stealthPage(page: Page) {
    let device = this.fingerprintConfig.getEmulateDevice();
    if (!!device) {
      try {
        await page.emulate(device);
        const client = await page.target().createCDPSession();
        await client.send('Emulation.setEmitTouchEventsForMouse', {
          enabled: true,
        });
        await client.send('Emulation.setTouchEmulationEnabled', {
          enabled: true,
          maxTouchPoints: 5,
        });
        await client.send('Emulation.setScrollbarsHidden', {
          hidden: true,
        });
      } catch (e) {
        logger.error('[BROWSER] emulate device error', e);
      }
    }
    const uaVersion = Number(path.basename(this.kernelPath).split('_')[1]);
    const promiseArr: Promise<any>[] = [];
    // mock speechSynthesis API
    // 109和120内核不支持getVoices，所以必须继续由js模拟
    if (uaVersion < 125) {
      // @ts-ignore
      const rewriteGetVoices = (voices) => {
        Object.defineProperty(window.speechSynthesis, 'getVoices', {
          writable: true,
          enumerable: true,
          configurable: true,
          value: () => {
            return voices;
          },
        });
        Object.defineProperty(window.speechSynthesis.getVoices, 'toString', {
          writable: true,
          enumerable: true,
          configurable: true,
          value: () => 'function getVoices() { [native code] }',
        });
      };
      promiseArr.push(page.evaluateOnNewDocument(rewriteGetVoices, this.speechSynthesisVoices));
      promiseArr.push(page.evaluate(rewriteGetVoices, this.speechSynthesisVoices));
    }
    const expression = `Object.defineProperty(window, '__hy_transitAutoChangeProp', {
      value: ${JSON.stringify(this.transitAutoChangeTransitProp)},
      enumerable: false,
    });`;
    promiseArr.push(page.evaluateOnNewDocument(expression));
    promiseArr.push(page.evaluate(expression));
    try {
      if (!this.transitAutoChangeTransitProp.disabled) {
        promiseArr.push(
          page.exposeFunction('__hy_autoChangeTransit', () => {
            // v11.4 不再支持由JS切换通道
            // this.tunnelRouter.autoSwitchPrimaryChannelTransit();
          }),
        );
      }
      promiseArr.push(
        page.exposeFunction('__hy_log', (data: string) => {
          if (data.startsWith('[NET_ERROR_PAGE]')) {
            // 页面加载失败了，打印 连接方式、会话ID、电脑的网络状态，同时发一个请求获取客户端的出口IP
            logger.info(
              `[BROWSER-LOG] (shopId: ${this.shopInfo.id}, sessionId: ${this.sessionId}, nodeId: ${
                this.tunnelRouter.getDefaultChannel()?.getCurrentEndpointVo()?.nodeId
              }, net: ${net.isOnline() ? 'online' : 'offline'}) ${data}}`,
            );
            this.fetchMyIpDebounce();
          } else {
            logger.info(`[BROWSER-LOG] ${data}`);
          }
        }),
      );
    } catch (e) {}
    await Promise.allSettled(promiseArr);
  }

  isMobile() {
    return !!this.fingerprintConfig.getEmulateDevice();
  }

  /**
   * socks5 代理
   */
  _getProxyArgs() {
    if (this.proxyPacArg) {
      return this.proxyPacArg;
      // return `--proxy-server=socks5://${this.socks5Url}`;
      // return `--proxy-server=socks5://127.0.0.1:1086`;
    }
    return '';
  }

  _getHyExtensionPaths() {
    const userInfoExPath = path.resolve(__dirname, '../extensions-dist/userInfo');
    const recorderExPath = path.resolve(__dirname, '../extensions-dist/recorder');
    const actionBlockExPath = path.resolve(__dirname, '../extensions-dist/actionBlock');
    const rpaToolboxExPath = path.resolve(
      __dirname,
      `../extensions-dist/${process.env.EXT_RPA_NAME}`,
    );
    const aiAgentExPath = path.resolve(
      __dirname,
      `../extensions-dist/${process.env.EXT_AI_AGENT_NAME}`,
    );
    const paths = [userInfoExPath, recorderExPath, actionBlockExPath, rpaToolboxExPath];
    if (this.teamAiConfig.aiAgent) {
      paths.unshift(aiAgentExPath);
    }
    return paths;
  }

  /**
   * 浏览器插件
   */
  _getExtensionArgs() {
    return [
      `--load-extension=${this._getHyExtensionPaths().join(',')}${
        this.extensionPaths.length > 0 ? `,${this.extensionPaths.join(',')}` : ''
      }`,
    ];
  }

  /**
   * 唤醒 chromium 浏览器
   */
  async _spawn() {
    const { LOG_DIR } = getDynamicPath();
    logger.info(
      `[BROWSER] starting（session:${this.sessionId}, shopId:${this.shopInfo.id}${
        this.rpaTaskId ? `, taskId: ${this.rpaTaskId}` : ''
      }）`,
    );
    try {
      const args = [
        '--no-default-browser-check',
        '--disable-features=TrackingProtection3pcd,PdfOopif',
        '--log-level=0',
        `--log-file=${path.resolve(LOG_DIR, 'chromium.' + this.shopInfo.id + '.log')}`,
        this._getProxyArgs(),
        ...this._getExtensionArgs(),
        ...this.fingerprintConfig.getArgs(),
      ];
      if (this.browserLanguage) {
        args.push(`--lang=${this.browserLanguage}`);
      }
      if (this.remoteDebugPort) {
        args.push(`--remote-debugging-port=${this.remoteDebugPort || 0}`);
      }
      let { gpu, sandbox, browserSwitches } = db.getDb().get('sysPres').value();
      if (!gpu) {
        args.push('--disable-gpu');
      }
      if (!sandbox) {
        args.push('--no-sandbox');
      }
      if (isMacPlatform()) {
        // fix: intel macos canvas2d 噪音检测不通过
        args.push('--disable-accelerated-2d-canvas');
      }
      const size = screen.getPrimaryDisplay().workAreaSize;
      logger.info(`[BROWSER] getPrimaryDisplay size：${size.width}x${size.height}`);
      if (this.windowSize === 'maximize') {
        // 最大化
        let w = size.width < 800 ? 1920 : size.width;
        let h = size.width < 800 ? 1080 : size.height;
        args.push(`--window-size=${w},${h}`);
      } else if (/^\d+,\d+$/.test(this.windowSize ?? '')) {
        // 指定大小
        args.push(`--window-size=${this.windowSize}`);
      } else if (this.headless) {
        // 无头模式
        args.push(`--window-size=2220,1080`);
      }
      let windowPosition: WindowPosition | undefined;
      if (db.isRpaExecutor()) {
        args.push('--no-sandbox');
        // 确保rpa执行器的浏览器窗口不被完全遮挡
        windowPosition = getWindowPosition();
        if (windowPosition) {
          args.push(`--window-position=${windowPosition.get()}`);
        }
      } else {
        // 本地执行最大化
        if (this.windowSize === 'maximize' || this.headless) {
          args.push(`--window-position=0,0`);
        } else if (/^\d+,\d+$/.test(this.windowPosition ?? '')) {
          args.push(`--window-position=${this.windowPosition}`);
        }
      }
      if (!this.shopInfo.webSecurity) {
        args.push('--disable-web-security');
        args.push('--allow-running-insecure-content');
      }
      if (browserSwitches) {
        args.push(...browserSwitches.split('\n'));
      }
      if (this.browserSwitches) {
        args.push(...this.browserSwitches.split('\n'));
      }

      let chromium: Browser;
      const kernelPath = await KernelManage.getInstance().findKernelByUA(
        this.fingerprintConfig.getUserAgent(),
      );
      this.kernelPath = kernelPath;
      const kernelVersion = Number(path.basename(this.kernelPath ?? '').split('_')[1] ?? 0);
      try {
        let extraEnv: any = {
          'donkey-sec-white-list': this.fingerWhiteConfig['donkey-sec-white-list'], //允许gmail/yahoo跨域加载资源
          'donkey-webgldx-white-list': this.fingerWhiteConfig['donkey-webgldx-white-list'], //pixelscan.net不设备webgl偏移
          'donkey-canvas-white-list': this.fingerWhiteConfig['donkey-canvas-white-list'],
        };
        if (isWinPlatform()) {
          extraEnv['donkey-smooth-scrolling'] = 'true';
        }
        extraEnv['dk-initial-extensions'] = this._getHyExtensionPaths()
          .map((v) => calcExtensionId(v))
          .concat(this.firstInstallExtensionIds)
          .join(',');
        if (this.rpaFlowId) {
          extraEnv['donkey-rpa-mode'] = true;
        }
        if (this.devtoolsConsole) {
          //是否开启 console.log 日志，开启可能会导致虾皮检测指纹不通过
          extraEnv['dk-runtime-log-on'] = true;
        }
        let isGGBrowser = process.env.OEM_NAME === 'gg';
        if (isGGBrowser) {
          //GG浏览器隐藏内置插件
          extraEnv['dk-hide-internal-extension'] = true;
          //使用原生的新标签页
          extraEnv['dk-origin-newTab'] = true;
        }
        // 避免同时打开过多的浏览器
        const now = Date.now();
        let waitMs = 0;
        const sysPres = db.getSysPres();
        const intervalMs = Math.min(
          (sysPres.openBrowserIntervalSecMax ?? 8) * 1000,
          (sysPres.openBrowserIntervalSecMin ?? 2) * 1000 + openBrowserIntervalMsDelta,
        ); // 打开浏览器的最小间隔时间
        if (now - lastOpenBrowserTimestamp < intervalMs) {
          waitMs = lastOpenBrowserTimestamp + intervalMs - now;
          logger.info(
            `[BROWSER] 距离上次打开浏览器太近，等待 ${waitMs}ms 后再打开浏览器（${this.shopInfo.name}）`,
          );
          lastOpenBrowserTimestamp += intervalMs;
          await new Promise((resolve) => setTimeout(resolve, waitMs));
        } else {
          lastOpenBrowserTimestamp = now;
        }
        openBrowserIntervalMsDelta = openBrowserIntervalMsDelta + 1000;
        clearTimeout(openBrowserIntervalTimer);
        openBrowserIntervalTimer = setTimeout(() => {
          openBrowserIntervalMsDelta = 0;
        }, 10 * 1000);
        let executablePath = path.join(kernelPath, KernelManage.getPlatformPath());
        // executablePath = '/Volumes/t7/chromium138/src/out/Release/HuaYoungBrowser.app/Contents/MacOS/Chromium';
        chromium = await new Promise(async (resolve, reject) => {
          const launchTimer = setTimeout(() => {
            logger.error(
              `[BROWSER] launch timeout (shopId: ${this.shopInfo.id}, session:${this.sessionId})`,
            );
            this.isSpawnedTimout = true;
            reject(new Error('唤醒浏览器超时'));
          }, 2 * 65 * 1000);
          try {
            const launchStartTime = Date.now();
            let browserIns: Browser | undefined;
            for (let launchTryCount = 1; launchTryCount <= 2; launchTryCount++) {
              try {
                const ignoreDefaultArgs = [
                  '--disable-features=Translate,BackForwardCache,AcceptCHFrame,MediaRouter,OptimizationHints,DialMediaRouteProvider,ProcessPerSiteUpToMainFrameThreshold',
                  '--password-store=basic',
                  '--disable-component-extensions-with-background-pages',
                ];
                if(kernelVersion < 138) {
                  ignoreDefaultArgs.push('--enable-automation');
                }
                const pptrIgnoreArgs = db.getDb().get('pptrIgnoreArgs').value();
                if (_.isArray(pptrIgnoreArgs)) {
                  pptrIgnoreArgs.forEach((arg) => {
                    ignoreDefaultArgs.push(arg);
                  });
                }
                browserIns = await puppeteer.launch({
                  executablePath: executablePath,
                  headless: this.headless ? 'new' : false,
                  defaultViewport: null,
                  args,
                  ignoreDefaultArgs,
                  env: {
                    TZ: this.fingerprintConfig.getTimezone(),
                    ...process.env,
                    ...extraEnv,
                    ...this.fingerprintConfig.getEnv(),
                  },
                  userDataDir: this.userDataDir,
                  pipe: !this.remoteDebugPort,
                  waitForInitialPage: false,
                  timeout: 60 * 1000,
                  protocolTimeout: 10 * 60 * 1000,
                });
                break;
              } catch (e: any) {
                logger.error(
                  `[BROWSER] launch error (shopId: ${this.shopInfo.id}, session:${this.sessionId}) ${e.name} - ${e.message}`,
                );
                if (launchTryCount === 2 || e.name !== 'TimeoutError') {
                  throw e;
                } else {
                  await new Promise((r) => setTimeout(r, 5 * 1000));
                }
              }
            }
            if (!browserIns) {
              throw new Error('唤醒浏览器失败');
            }
            logger.info(
              `[BROWSER] launch success in ${Date.now() - launchStartTime}ms (shopId: ${
                this.shopInfo.id
              }, session:${this.sessionId})`,
            );
            if (this.isSpawnedTimout) {
              // 如果已经被标记为已超时，需要关闭浏览器，防止后续打开浏览器失败
              logger.info(
                `[BROWSER] close a timeout browser (shopId: ${this.shopInfo.id}, session:${this.sessionId})`,
              );
              await browserIns.close();
              this.killProcess(browserIns.process());
            }
            resolve(browserIns);
          } catch (e) {
            reject(e);
          } finally {
            clearTimeout(launchTimer);
          }
        });
      } catch (e: any) {
        logger.error(
          `启动花漾浏览器进程失败(shopId: ${this.shopInfo.id}, session:${this.sessionId})`,
          e,
        );
        if (!this.isSpawnedTimout) {
          e.code = 'browserLaunchError';
        }
        e.kernelVersion = path.basename(kernelPath);
        e.shopName = this.shopInfo.name;
        e.shopId = this.shopInfo.id;
        e.message = this.isSpawnedTimout
          ? i18n.t('启动花漾浏览器进程超时')
          : i18n.t('启动花漾浏览器进程失败');
        throw e;
      }
      logger.info(`[BROWSER] started（session:${this.sessionId}, shopId:${this.shopInfo.id}）`);
      // @ts-ignore
      chromium.findTargetProxy = this.findTargetProxy.bind(this);
      this.chromium = chromium;
      const chromiumProcess = chromium.process();
      this.cdpSession = await chromium.target()?.createCDPSession();
      chromium.on('targetcreated', (t) => this._handleTargetCreated(t));
      // chromium.on('targetchanged', (t) => this._handleTargetChanged(t));
      chromium.on('targetdestroyed', this.targetDestroyedDebounce);
      chromium.on('disconnected', () => {
        logger.info(
          `[BROWSER] disconnected (session:${this.sessionId}, shopId:${this.shopInfo.id})`,
        );
        windowPosition?.release();
        this.wsDispatcher.getEmitter().removeAllListeners();
        clearInterval(this.collectionCookiesTimer);
        this.saveTabUrls();
        this.killBrowserTimer = setTimeout(() => {
          try {
            this.closeCallback?.().finally(() => {
              this._afterClose();
              this.destroy();
            });
            this.killProcess(chromiumProcess);
          } catch (e) {
            logger.error(`[BROWSER] kill browser process failed (session:${this.sessionId})`, e);
          }
        }, 5000);
        this.recorderController?.onSessionClose(this.recordInfo!.sessionId);
        this.chromiumCloseListeners?.forEach((listener) => {
          listener();
        });
        this.browserActionRecorder?.stop();
        // this.recoder?.close();
        // this.recoder = null;
      });
      chromium.on('error', (e) => {
        logger.error(`[BROWSER] error (session:${this.sessionId})`, e);
      });
      chromium.process()?.on('close', () => {
        this.closeCallback?.().finally(() => {
          this._afterClose();
          this.destroy();
        });
        clearTimeout(this.killBrowserTimer);
      });
      chromium.process()?.on('error', (e) => {
        logger.error(`[BROWSER] process error (session:${this.sessionId})`, e);
      });
    } catch (e) {
      logger.error(
        `[BROWSER] spawn error (session:${this.sessionId}, shopId:${this.shopInfo.id})`,
        e,
      );
      throw e;
    }
  }

  async afterSpawn() {
    try {
      const chromium = this.chromium!;
      await this._setCookie();
      logger.info(`[BROWSER] set cookie success (session:${this.sessionId})`);
      this._startCookiesCollectionTask();
      if (this.rpaPreview && this.rpaFlowId) {
        this.browserActionRecorder = new BrowserActionRecorder(
          chromium,
          getRpaFlowPreviewWindow(this.rpaFlowId),
          this.wsDispatcher,
        );
      }
      const pages = await chromium.pages();
      let initPage: Page | undefined;
      let shouldOpenHomePage = false; // 是否要打开浏览器检测页
      let checkingPageTimer: any = 0;
      if (pages.length > 0) {
        initPage = pages[0];
        await new Promise((resolve) => {
          initPage
            ?.exposeFunction('_checkCallback', async (code: number) => {
              if (!appConfig.isOEM && code !== 1) {
                shouldOpenHomePage = true;
              }
              resolve(true);
            })
            .then(async () => {
              checkingPageTimer = setTimeout(() => {
                resolve(true);
              }, 5 * 1000);
              await initPage?.goto(`${SERVER_URL}checking`);
            })
            .catch((e) => {
              resolve(true);
              logger.error('[BROWSER] exposeFunction _checkCallback error', e);
            });
          initPage?.once('close', () => {
            resolve(true);
          });
        });
        clearTimeout(checkingPageTimer);
      }
      logger.info(`[BROWSER] start open init tab (session:${this.sessionId})`);
      // 初始 tab 页签
      let urls = this._getInitTabUrls();
      if (urls.length === 0) {
        urls.push(SERVER_URL);
      }
      if (shouldOpenHomePage && !urls.includes(SERVER_URL)) {
        urls.unshift(SERVER_URL);
      }
      const waitPageCreated: Promise<any>[] = [];
      for (const url of urls) {
        waitPageCreated.push(
          new Promise(async (resolve) => {
            try {
              const p = await chromium.newPage();
              if (shouldOpenHomePage && url === SERVER_URL) {
                setTimeout(() => {
                  p.bringToFront();
                }, 100);
              }
              logger.verbose(`[BROWSER] goto init tab (${url}) (session:${this.sessionId})`);
              p.goto(url ?? SERVER_URL, { timeout: 1000 })
                .catch((e) => {})
                .finally(() => {
                  resolve(true);
                });
            } catch (e) {
              logger.error(`[BROWSER] open init tab (${url}) error (session:${this.sessionId})`, e);
              resolve(true);
            }
          }),
        );
      }
      try {
        await Promise.race(waitPageCreated);
        await initPage?.close();
        logger.info(`[BROWSER] open init tab success (session:${this.sessionId})`);
      } catch (e) {
        logger.error(`[BROWSER] open init tab failed (session:${this.sessionId})`, e);
      }
      if (db.isRuntimeMode()) {
        this.bringToFront();
      }
      if (this.rpaTaskId) {
        logger.info(
          `[BROWSER] ready for rpa task (taskId: ${this.rpaTaskId}, session:${this.sessionId})`,
        );
      }
    } catch (e) {
      logger.error('[BROWSER] after spawn, error occur', e);
    }
  }

  getBrowser() {
    return this.chromium;
  }

  getUserDataDir() {
    return this.userDataDir;
  }

  /**
   * 获取焦点
   */
  bringToFront(bounds: Record<string, any> = {}) {
    if (isWinPlatform() || Object.keys(bounds).length > 0) {
      this.handleCmd('bring-window-to-front', bounds);
    } else {
      this.handleCmd('update-window', {
        focused: true,
      });
    }
  }

  triggerPacScriptChanged() {
    this.wsDispatcher.dispatcher(JSON.stringify({ action: 'pac-script-changed', data: 'ok' }));
  }

  async triggerNetErrorPageReload() {
    const pages = await this.chromium?.pages();
    pages?.forEach((page) => {
      page.evaluate(`window.__hy_reloadPage?.()`).catch(() => {});
    });
  }

  /**
   * 由客户端主动发起关闭浏览器
   */
  async close() {
    try {
      await this.chromium?.close();
      // this.recoder?.close();
      // this.recoder = null;
    } catch (e) {
      logger.error('[BROWSER] close browser failed', e);
    }
  }

  /**
   * 设置浏览器关闭是的回调
   * @param callback
   */
  onClose(callback: () => Promise<void>) {
    this.closeCallback = callback;
  }

  _afterClose() {
    if (this.afterCloseListeners) {
      logger.info(
        `[BROWSER] after close called(${this.afterCloseListeners.length}) (session:${this.sessionId}, shopId:${this.shopInfo.id})`,
      );
      this.afterCloseListeners.forEach((listener) => {
        listener();
      });
      this.afterCloseListeners = [];
    }
  }

  handleCmd(cmdName: string, data: any) {
    switch (cmdName) {
      case 'open-url':
        if (data.url) this.chromium?.newPage().then((p) => p.goto(data.url));
        break;
      case 'clear-hy-rpa-user-action-layer':
      case 'show-hy-rpa-user-action-layer':
      case 'hide-hy-rpa-user-action-layer':
        this.wsDispatcher.dispatcher(JSON.stringify({ action: cmdName }));
        break;
      case 'close-browser':
        const releaseLockTimer = setTimeout(() => {
          getChromiums()[this.sessionId] = null;
          clearTimeout(releaseLockTimer);
        }, 5 * 1000);
        if (this.chromium?.isConnected()) {
          this.chromium?.close();
          this.addAfterCloseListener(() => {
            clearTimeout(releaseLockTimer);
          });
        }
        break;
      case 'update-window':
        this.wsDispatcher.dispatcher(JSON.stringify({ action: cmdName, data }));
        break;
      case 'bring-window-to-front':
        this.wsDispatcher.dispatcher(JSON.stringify({ action: cmdName, data }));
        break;
      default:
        logger.error(`[APP] browser command:${cmdName} is not supported`);
    }
  }

  findTargetProxy(url: string) {
    return this.tunnelRouter.findTargetProxy(url);
  }

  killProcess(process?: ChildProcess | null) {
    if (!process) return;
    if (!process.killed) {
      logger.info(
        `[BROWSER] kill browser process - ${process.pid} (shopId: ${this.shopInfo.id}, session:${this.sessionId})`,
      );
      process.kill();
      try {
        if ('win32' == os.platform() && process.pid) {
          //只有windows出现过杀不掉的情况 /T 杀指定进程和其子进程 /F 强制
          //https://learn.microsoft.com/zh-tw/windows-server/administration/windows-commands/taskkill
          exec(`taskkill /T /F /PID ${process.pid}`, (e) => {
            //do some thing?
          });
        } else {
          exec(`kill -9 ${process.pid}`, (e) => {
            //do some thing?
          });
        }
      } catch (e: any) {
        console.error('force kill browser error: ', e);
      }
      process.emit('exit');
    }
  }

  destroy() {
    this.chromium?.removeAllListeners();
    this.chromium = null;
    this.chromiumCloseListeners = undefined;
    this.afterCloseListeners = [];
    this.wsDispatcher.destroy();
    this.closeCallback = undefined;
    this.cookies = [];
    this.cookiesSync = undefined;
    this.ipDetail = undefined;
    this.cdpSession = undefined;
    this.proxyPacArg = '';
  }
}
