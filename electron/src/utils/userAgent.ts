import uaParser from 'ua-parser-js';

export const OS_MAP = {
  WindowsPhone: 'Windows Phone',
  Windows: 'Windows',
  MacOS: 'macOS',
  iOS: 'iOS',
  Android: 'Android',
  WebOS: 'WebOS',
  BlackBerry: 'BlackBerry',
  Bada: 'Bada',
  Tizen: 'Tizen',
  Linux: 'Linux',
  ChromeOS: 'Chrome OS',
  PlayStation4: 'PlayStation 4',
  Roku: 'Roku',
};

export function getEngineFromUA(uaStr: string) {
  const ua = uaParser(uaStr);
  return ua.engine;
}

/**
 * 从UA字符串中获取浏览器信息
 * @param uaStr
 */
export function getBrowserFromUA(uaStr: string) {
  const ua = uaParser(uaStr);
  return ua.browser;
}

export function getBrowserFullName(name: string = '') {
  switch (name) {
    case 'Chrome':
      return 'Google Chrome';
    case 'Edge':
      return 'Microsoft Edge';
    default:
      return name;
  }
}

export function getOsName(uaStr: string) {
  const ua = uaParser(uaStr);
  const name = (ua.os.name ?? 'Windows').replace(/\s/g, '');
  // @ts-ignore
  return OS_MAP[name] ?? 'Windows';
}
