import path from 'path';
import OSS from 'ali-oss';
import fs from 'fs-extra';
import crypto from 'crypto';
import extract from 'extract-zip';
import archiver from 'archiver';
import appConfig, { getDynamicPath } from '@e/configs/app';
import { RequestAgent } from '@e/services/request';
import logger from '@e/services/logger';
import db from '@e/components/db';

interface Props {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  shopPolicy: API.ShopSyncPolicyVo;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
  folder: string;
}

async function fileMd5(filePath: string): Promise<string> {
  const fileStream = fs.createReadStream(filePath);
  const hash = crypto.createHash('md5');
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error(`calc ${filePath} md5 timeout`));
    }, 5000);
    fileStream.on('error', (err) => {
      reject(err);
      clearTimeout(timer);
    });
    hash.on('error', (err) => {
      reject(err);
      clearTimeout(timer);
    });
    hash.on('finish', () => {
      const md5 = hash.digest('hex');
      resolve(md5);
      clearTimeout(timer);
    });
    fileStream.pipe(hash);
  });
}

async function isSameMd5(path1: string, path2: string) {
  const promiseArr: Promise<string>[] = [];
  if (!fs.existsSync(path1) || !fs.existsSync(path2)) {
    return Promise.resolve(false);
  }
  promiseArr.push(fileMd5(path1));
  promiseArr.push(fileMd5(path2));
  return Promise.all(promiseArr).then(([md51, md52]) => {
    return md51 === md52;
  });
}

export default class LevelDbDatabase {
  props: Props;
  shopDataDir: string;
  private errorOccur: boolean;
  private ossClient?: OSS;
  fileName: string;
  basePath: string;
  constructor(props: Props) {
    this.props = props;
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    this.shopDataDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${this.props.shopId}`,
      this.props.tempShopDataSubDir,
    );
    this.errorOccur = false;
    this.fileName = `${this.props.folder}.zip`;
    this.basePath = `shop-data/${this.props.teamId}/${this.props.shopId}/`;
  }

  getObjectName() {
    return this.basePath + this.fileName;
  }

  getFolderPath() {
    return path.join(this.shopDataDir, 'Default', this.props.folder);
  }

  async init(): Promise<any> {
    try {
      await this._prepareOssClient();
      const header = await this.ossClient?.head(this.getObjectName());
      fs.ensureDirSync(this.getFolderPath());
      const zipFilePath = path.join(this.shopDataDir, 'Default', this.fileName);
      if (
        header?.meta?.['x-oss-meta-last-upload-device-id'] !== db.getDeviceIdFromCookies() ||
        !fs.existsSync(zipFilePath) ||
        !fs.existsSync(this.getFolderPath())
      ) {
        if (fs.existsSync(zipFilePath)) {
          await fs.rm(zipFilePath, { recursive: true, maxRetries: 5, retryDelay: 1000 });
        }
        await this.ossClient?.get(this.getObjectName(), zipFilePath);
        await extract(zipFilePath, {
          dir: this.getFolderPath(),
        });
      }
    } catch (e: any) {
      if (e.code === 'NoSuchKey') {
        await this.removeLocalData();
      } else {
        this.errorOccur = true;
        logger.error(`[APP] init ${this.fileName} failed`, e);
      }
    }
  }

  async removeLocalData() {
    if (fs.existsSync(this.getFolderPath())) {
      await fs.emptydir(this.getFolderPath());
      await fs.rm(this.getFolderPath(), { recursive: true });
    }
  }

  async upload() {
    if (this.errorOccur) return;
    let fileSize = -1;
    try {
      if (!fs.existsSync(this.getFolderPath())) {
        await this.ossClient?.delete(this.getObjectName());
        logger.info(`[APP] delete ${this.fileName} success`);
        return;
      }
      const targetFolder = this.getFolderPath();
      const bakTargetFolder = targetFolder + '_bak';
      await fs.copy(targetFolder, bakTargetFolder);
      const zipFilePath = path.join(this.shopDataDir, 'Default', this.fileName);
      if (fs.existsSync(zipFilePath)) {
        await fs.rename(zipFilePath, zipFilePath + '.bak');
      }
      const archive = archiver('zip', { zlib: { level: 9 } });
      const output = fs.createWriteStream(zipFilePath);
      await new Promise((resolve, reject) => {
        output.on('close', () => {
          resolve(true);
        });
        archive
          .directory(bakTargetFolder, false)
          .on('error', function (err) {
            reject(err);
          })
          .pipe(output);
        archive.finalize();
      });
      fs.rm(bakTargetFolder, { recursive: true });
      // let needUpload = true;
      // try {
      //   const skip = await isSameMd5(zipFilePath, zipFilePath + '.bak');
      //   if (skip) {
      //     needUpload = false;
      //   }
      // } catch (e) {}
      // if (!needUpload) {
      //   logger.info(`[APP] file no change, skip upload ${this.fileName} (size: ${fileSize})`);
      //   return;
      // }
      const fStat = fs.statSync(zipFilePath);
      fileSize = fStat.size;
      await this.ossClient?.put(this.getObjectName(), zipFilePath, { timeout: 10 * 60 * 1000 });
      this.ossClient
        ?.putMeta(
          this.getObjectName(),
          // @ts-ignore
          {
            'x-oss-meta-last-upload-device-id': db.getDeviceIdFromCookies() ?? '',
            'x-oss-meta-last-upload-timestamp': Date.now(),
          },
          { timeout: 10 * 1000 },
        )
        .catch((e) => {
          logger.error('[APP] putMeta failed', e.message);
        });
      logger.info(`[APP] upload ${this.fileName} (size: ${fileSize}) success`);
    } catch (e) {
      logger.error(`[APP] upload ${this.fileName} (size: ${fileSize}) failed`, e);
    }
  }

  async _prepareOssClient() {
    if (!this.ossClient) {
      let signature = await this._refreshPostSignature();
      this.basePath = /^[^*]*/.exec(signature.url)?.[0] ?? this.basePath;
      // @ts-ignore
      this.ossClient = new OSS({
        region: signature.region,
        accessKeyId: signature.accessKeyId,
        accessKeySecret: signature.accessKeySecret,
        bucket: signature.bucketName,
        stsToken: signature.securityToken,
        secure: true,
        refreshSTSToken: async () => {
          const info = await this._refreshPostSignature();
          return {
            accessKeyId: info.accessKeyId,
            accessKeySecret: info.accessKeySecret,
            stsToken: info.securityToken,
          };
        },
        refreshSTSTokenInterval: 15 * 60 * 1000, //15min刷新sts token
        timeout: 10 * 60 * 1000,
      });
    }
  }

  async _refreshPostSignature() {
    const signature = await this.props.requestAgent.request(
      `/api/shop/${this.props.shopId}/signatureShopData`,
      {
        teamId: this.props.teamId,
      },
    );
    return signature;
  }
}
