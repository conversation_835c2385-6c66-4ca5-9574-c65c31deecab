import ws, { Server } from 'ws';
import EventEmitter from 'events';
import { getRpaFlowPreviewWindow } from '@e/utils/window';
import { dispatchMsg } from '@e/utils/ipc';

type Props = {
  rpaFlowId?: number;
};

/**
 * 用于会话中的消息分发
 */
export class MessageDispatcher {
  port: number;
  wss: Server;
  eventEmitter: EventEmitter;
  pingTimer: any;
  ready: boolean;
  constructor(props: Props) {
    this.eventEmitter = new EventEmitter();
    this.eventEmitter.setMaxListeners(0);
    this.wss = new ws.Server({ port: 0 });
    this.ready = true;
    this.wss.on('connection', (ws, msg) => {
      ws.on('message', (msgData) => {
        if (typeof msgData !== 'string') return;
        if (/identify=(.+)/.test(msgData)) {
          //@ts-ignore
          ws.__identify = RegExp.$1;
          return;
        }
        // 单一事件
        if (/^[A-Za-z0-9-_\s]+$/.test(msgData)) {
          this.eventEmitter.emit(msgData);
        } else {
          // 带数据的事件
          try {
            const payload = JSON.parse(msgData);
            const { action, data } = payload;
            switch (action) {
              case 'get-pick-selector-res':
                this.eventEmitter.emit(action, data);
                break;
              case 'motion-recorder-res':
                if (props.rpaFlowId) {
                  const win = getRpaFlowPreviewWindow(props.rpaFlowId);
                  if (win) {
                    dispatchMsg(action, data, win);
                  }
                }
                break;
              case 'tab-update':
                this.eventEmitter.emit(action, data);
                break;
              case 'rpc-callback':
                this.rpcCallback(payload.requestId, payload.success, data);
                break;
              default:
            }
          } catch (e) {
            console.error('[APP] MessageDispatcher JSON parse failed', e);
          }
        }
      });
      ws.send('hello from WebsocketDispatcher');
    });
    this.pingTimer = setInterval(() => {
      this.wss.clients.forEach((ws) => {
        ws.ping();
      });
    }, 25 * 1000);
    // @ts-ignore
    this.port = this.wss.address()?.port;
  }

  getPort() {
    return this.port;
  }

  getEmitter() {
    return this.eventEmitter;
  }

  dispatcher(msg: string) {
    if (!this.ready) return;
    this.wss.clients.forEach((ws) => {
      ws.send(msg);
    });
  }

  getClientByIdentify(identify: string) {
    let targetClient = null;
    for (let client of this.wss.clients) {
      // @ts-ignore
      if (client.__identify == identify) {
        targetClient = client;
        break;
      }
    }
    return targetClient;
  }

  private rpcCallbacks = new Map();
  async rpc(identify: string, fun: string, data: any, timeoutMs = 100000) {
    let targetClient = this.getClientByIdentify(identify);
    if (!targetClient) {
      throw `${identify} not found`;
    }
    let requestId = Math.random();
    targetClient.send(
      JSON.stringify({
        action: 'rpc',
        requestId,
        fun,
        data,
      }),
    );
    return new Promise((resolve, reject) => {
      let timeout = setTimeout(() => {
        reject('调用超时');
        this.rpcCallbacks.delete(requestId);
      }, timeoutMs);
      this.rpcCallbacks.set(requestId, { timeout, resolve, reject });
    });
  }

  async rpaSpirit(fun: string, data: any): Promise<any> {
    return this.rpc('plugin:rpa', fun, data);
  }

  private rpcCallback(requestId: any, success: boolean, data: any) {
    if (this.rpcCallbacks.has(requestId)) {
      let callback = this.rpcCallbacks.get(requestId);
      this.rpcCallbacks.delete(requestId);
      clearTimeout(callback.timeout);
      if (success) {
        callback.resolve(data);
      } else {
        callback.reject(data);
      }
    }
  }

  appendLogToRpaExt(log: string) {
    const targetClient = this.getClientByIdentify('plugin:rpa');
    if (!targetClient) return;
    targetClient.send(
      JSON.stringify({
        action: 'task-log',
        data: log,
      }),
    );
  }

  destroy() {
    this.ready = false;
    clearInterval(this.pingTimer);
    this.eventEmitter.removeAllListeners();
    this.wss.close();
  }
}
