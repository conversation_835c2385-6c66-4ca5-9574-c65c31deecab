import { app, BrowserWindow } from 'electron';
import fs from 'fs-extra';
import path from 'path';
import uaParser from 'ua-parser-js';
import Downloader from 'nodejs-file-downloader';
import db, { getDataSavePath } from '@e/components/db';
import appConfig from '@e/configs/app';
import logger from '@e/services/logger';
import extract from 'extract-zip';
import request from '@e/services/request';
import { isLinuxPlatform, isMacPlatform, isWin7Platform } from '@e/utils/utils';
import i18n from '@e/utils/i18n';
import os from 'os';
import { dispatchMsg } from '@e/utils/ipc';

let kernelManageIns: KernelManage | null = null;
let isExtractingBundleKernelFlag = false;
const bundleKernelDestDirName = `chrome_${appConfig.BROWSER_VERSION}_${appConfig.BROWSER_BUILD_NUMBER}`;
const kernelDownloadProgress: {
  [filename: string]: {
    finished: boolean;
    versionNumber: number;
    percentage: string;
    unzipping: boolean;
  };
} = {};

export default class KernelManage {
  installedVersions: string[] = [];
  destDir: string;
  bundleKernelZip: string;
  downloadDir: string;

  constructor() {
    this.destDir = path.join(getDataSavePath('appData'), app.getName());
    this.bundleKernelZip = path.resolve(__dirname, '../extra', KernelManage.getZipFileName());
    this.downloadDir = path.join(this.destDir, 'kernel-download');
    this.init();
  }

  static getInstance() {
    if (!kernelManageIns) {
      kernelManageIns = new KernelManage();
    }
    return kernelManageIns;
  }

  static getZipFileName() {
    const platform = os.platform();

    switch (platform) {
      // @ts-ignore
      case 'mas':
      case 'darwin':
        return 'chrome-mac.zip';
      case 'freebsd':
      case 'openbsd':
      case 'linux':
        return 'chrome-linux.zip';
      case 'win32':
        return 'chrome-win.zip';
      default:
        throw new Error('Chromium builds are not available on platform: ' + platform);
    }
  }

  /**
   * 获取 chromium 在各个平台下的执行程序相对路径
   */
  static getPlatformPath() {
    const platform = os.platform();

    switch (platform) {
      // @ts-ignore
      case 'mas':
      case 'darwin':
        if (process.env.OEM_NAME === 'gg') {
          return 'GGBrowser.app/Contents/MacOS/Chromium';
        }
        return 'HuaYoungBrowser.app/Contents/MacOS/Chromium';
      case 'freebsd':
      case 'openbsd':
      case 'linux':
        return 'chromium/chrome';
      case 'win32':
        return 'chrome.exe';
      default:
        throw new Error('Chromium builds are not available on platform: ' + platform);
    }
  }

  init() {
    this.getInstalledVersionPaths();
    this.checkBundleKernel().then(() => {
      this.cleanOldKernel();
    });
  }

  getInstalledVersionPaths() {
    const files = fs.readdirSync(this.destDir);
    let kernels = files.filter((file) => file.startsWith('chrome_'));
    const kernelPaths = kernels
      .map((fName) => {
        return path.join(this.destDir, fName);
      })
      .filter((fPath) => fs.statSync(fPath).isDirectory());
    kernelPaths.sort().reverse();
    this.installedVersions = kernelPaths;
    dispatchMsg('installed-browser-versions-change', this.getInstalledKernelInfo());
  }

  async uninstallKernel(buildNumber: number | string) {
    const promiseArr: Promise<void>[] = [];
    const kernelPath = this.installedVersions.find((fPath) => {
      return path.basename(fPath).includes(`_${buildNumber}`);
    });
    if (!kernelPath) return;
    promiseArr.push(fs.rm(kernelPath, { recursive: true, maxRetries: 3, retryDelay: 1000 }));
    const zipPath = path.join(this.downloadDir, `${path.basename(kernelPath)}.zip`);
    if (fs.existsSync(zipPath)) {
      promiseArr.push(fs.rm(zipPath));
    }
    await Promise.allSettled(promiseArr);
    this.getInstalledVersionPaths();
  }

  cleanOldKernel() {
    if (this.installedVersions.length < 2) {
      return;
    }
    let lastKernel = this.installedVersions[0];
    const promiseArr: Promise<void>[] = [];
    for (let i = 1; i < this.installedVersions.length; i++) {
      const kernel = this.installedVersions[i];
      if (path.basename(kernel).split('_')[1] === path.basename(lastKernel).split('_')[1]) {
        // 相同主版本号，删除旧的构建版本
        promiseArr.push(fs.rm(kernel, { recursive: true, maxRetries: 3, retryDelay: 1000 }));
        const zipPath = path.join(this.downloadDir, `${path.basename(kernel)}.zip`);
        if (fs.existsSync(zipPath)) {
          promiseArr.push(fs.rm(zipPath));
        }
      } else {
        // 不同主版本号，保留最新的主版本号
        lastKernel = kernel;
      }
    }
    if (promiseArr.length > 0) {
      Promise.allSettled(promiseArr).then(() => {
        this.getInstalledVersionPaths();
      });
    }
  }

  async checkBundleKernel() {
    if (
      !this.installedVersions.some((fPath) => {
        const isSameMajorVersionPath = path
          .basename(fPath)
          .includes(`chrome_${appConfig.BROWSER_VERSION}`);
        if (isSameMajorVersionPath) {
          const existExecutable = fs.existsSync(path.join(fPath, KernelManage.getPlatformPath()));
          const buildNumber = Number(path.basename(fPath).split('_')[2] || 'NaN');
          return buildNumber >= appConfig.BROWSER_BUILD_NUMBER && existExecutable;
        }
        return false;
      })
    ) {
      await this.installBundleKernel();
      this.getInstalledVersionPaths();
    }
  }

  async installBundleKernel() {
    isExtractingBundleKernelFlag = true;
    try {
      const hasZipFile = await fs.pathExists(this.bundleKernelZip);
      if (hasZipFile) {
        await this.extractZipFile(this.bundleKernelZip, bundleKernelDestDirName);
      } else {
        // mac 安装包中只有已解压的内核目录，直接拷贝到目标目录
        const hasBundleKernelDir = await fs.pathExists(this.bundleKernelZip.replace('.zip', ''));
        if (hasBundleKernelDir) {
          await fs.emptyDir(path.join(this.destDir, bundleKernelDestDirName));
          await fs.copy(
            this.bundleKernelZip.replace('.zip', ''),
            path.join(this.destDir, bundleKernelDestDirName),
            {
              recursive: true,
              overwrite: true,
            },
          );
        }
      }
    } catch (e) {
      logger.error('[APP] installBundleKernel failed', e);
    } finally {
      isExtractingBundleKernelFlag = false;
    }
  }

  async installKernel(
    kernelVo: API.ChromiumKernelVo,
    onProgress?: (info: {
      versionNumber: number;
      percentage: string;
      unzipping?: boolean;
      finished?: boolean;
    }) => void,
  ) {
    try {
      const downloadFileName = `chrome_${kernelVo.versionNumber}_${kernelVo.buildNumber}.zip`;
      if (
        kernelDownloadProgress[downloadFileName] &&
        !kernelDownloadProgress[downloadFileName].finished
      ) {
        // 如果已经有相同内核的下载任务
        await new Promise((resolve) => {
          const timer = setInterval(() => {
            if (
              !kernelDownloadProgress[downloadFileName] ||
              kernelDownloadProgress[downloadFileName]?.finished
            ) {
              clearInterval(timer);
              resolve();
            } else if (kernelDownloadProgress[downloadFileName]) {
              onProgress?.(kernelDownloadProgress[downloadFileName]);
            }
          }, 1000);
        });
        return;
      }
      kernelDownloadProgress[downloadFileName] = {
        versionNumber: kernelVo.versionNumber!,
        percentage: '0',
        unzipping: false,
        finished: false,
      };
      await new Downloader({
        url: kernelVo.downloadLink!,
        fileName: downloadFileName,
        cloneFiles: false,
        skipExistingFileName: true,
        directory: this.downloadDir,
        maxAttempts: 3,
        onProgress: (percentage: string) => {
          kernelDownloadProgress[downloadFileName].percentage = percentage;
          onProgress?.({
            versionNumber: kernelVo.versionNumber!,
            percentage,
          });
        },
      }).download();
      kernelDownloadProgress[downloadFileName].unzipping = true;
      onProgress?.({
        versionNumber: kernelVo.versionNumber!,
        percentage: '100',
        unzipping: true,
      });
      await this.extractZipFile(
        path.join(this.downloadDir, downloadFileName),
        downloadFileName.replace('.zip', ''),
        true,
      );
      kernelDownloadProgress[downloadFileName].unzipping = false;
      kernelDownloadProgress[downloadFileName].finished = true;
    } catch (e) {
      logger.error(`[APP] installKernel ${kernelVo.buildNumber} failed`, e);
    }
  }

  async extractZipFile(zipFile: string, dirname: string, exposeException = false) {
    const extractDest = path.join(this.destDir, dirname);
    try {
      if (fs.existsSync(extractDest)) {
        logger.info('[APP] start cleaning old chromium file', extractDest);
        await fs.emptyDir(extractDest);
        await fs.rm(extractDest, { recursive: true, maxRetries: 5, retryDelay: 1000 });
        this.getInstalledVersionPaths();
      }
    } catch (e) {
      logger.error(`[APP] cleaning ${extractDest} failed`, e);
      if (exposeException) {
        throw e;
      }
    }
    try {
      if (!fs.existsSync(zipFile)) {
        logger.info(`[APP] no ${zipFile}, skip extract`);
        return;
      }
      fs.ensureDirSync(extractDest);
      logger.info('[APP] start extract browser package', zipFile);
      await extract(zipFile, {
        dir: extractDest,
      });
      logger.info(`[APP] extract ${zipFile} success`);
      this.getInstalledVersionPaths();
    } catch (e) {
      logger.error(`[APP] extract ${zipFile} failed`, e);
      await fs.rm(extractDest, { recursive: true, maxRetries: 5, retryDelay: 1000 });
      await fs.rm(zipFile, { maxRetries: 5, retryDelay: 1000 });
      this.getInstalledVersionPaths();
      if (exposeException) {
        throw e;
      }
    }
  }

  async unzipKernel(kernelName: string) {
    if (bundleKernelDestDirName === kernelName) {
      await this.installBundleKernel();
    } else {
      await this.extractZipFile(path.join(this.downloadDir, `${kernelName}.zip`), kernelName, true);
    }
  }

  async waitForBundleKernel() {
    if (!isExtractingBundleKernelFlag) {
      return;
    }
    const loadingWin = new BrowserWindow({
      width: 500,
      height: 200,
      center: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      frame: false,
    });
    loadingWin.webContents.loadFile(path.resolve(__dirname, 'html', 'loading.html'), {
      query: {
        title: i18n.t('正在为您准备浏览器运行环境'),
        description: i18n.t('这可能需要一点时间，请稍候...'),
      },
    });
    let checkCount = 0;
    while (checkCount < 60) {
      checkCount++;
      if (isExtractingBundleKernelFlag) {
        await new Promise((resolve) => {
          setTimeout(resolve, 1000);
        });
      } else {
        loadingWin.close();
        break;
      }
    }
  }

  getMyPlatform() {
    let myPlatform = 'windows10';
    if (isWin7Platform()) {
      myPlatform = 'windows7';
    } else if (isMacPlatform()) {
      myPlatform = 'macos';
      myPlatform += os.version().includes('X86_64') ? '_x64' : '_arm64';
    } else if (isLinuxPlatform()) {
      myPlatform = 'linux';
    }
    return myPlatform;
  }

  async prepareKernelForUA(
    userAgent: string,
    onProgress?: (info: {
      versionNumber: number;
      percentage: string;
      unzipping?: boolean;
      finished?: boolean;
    }) => void,
  ) {
    await this.waitForBundleKernel();
    const ua = uaParser(userAgent);
    let uaVersion = ua.browser.major;
    if (!uaVersion && this.installedVersions[0]) {
      uaVersion = path.basename(this.installedVersions[0]).split('_')[1];
    }
    try {
      const kernelVo: API.ChromiumKernelVo = await request(
        '/api/meta/chromium/findSuitableKernel',
        {
          params: {
            platform: this.getMyPlatform(),
            uaVersion: uaVersion ?? appConfig.BROWSER_VERSION,
            bundleKernelVersion: process.env.BUNDLE_KERNEL_VERSION,
            oemName: process.env.OEM_NAME !== 'default' ? process.env.OEM_NAME : undefined,
          },
        },
      );
      if (!kernelVo) {
        return Number(path.basename(this.installedVersions[0]).split('_')[1]);
      }
      const installedSuitableKernel = this.installedVersions.filter((fPath) => {
        return path.basename(fPath).includes(`chrome_${kernelVo.versionNumber}`);
      });
      if (installedSuitableKernel.length === 0) {
        // 下载新的内核
        await this.installKernel(kernelVo, onProgress);
      } else {
        // 检查是否需要更新
        const hasUpdate = !installedSuitableKernel.some((fPath) =>
          path.basename(fPath).includes(`chrome_${kernelVo.versionNumber}_${kernelVo.buildNumber}`),
        );
        if (hasUpdate) {
          // 异步更新内核
          this.installKernel(kernelVo);
        }
      }
      return kernelVo.versionNumber;
    } catch (e) {
      logger.info('[BROWSER] prepareKernelForUA error', e);
      throw new Error('安装浏览器内核失败');
    }
  }

  async findKernelByUA(userAgent: string) {
    const ua = uaParser(userAgent);
    const uaVersion = ua.browser.major;
    let kernelPath = '';
    try {
      const kernelVo: API.ChromiumKernelVo = await request(
        '/api/meta/chromium/findSuitableKernel',
        {
          params: {
            platform: this.getMyPlatform(),
            uaVersion: uaVersion,
          },
        },
      );
      if (!kernelVo) {
        return this.installedVersions[0];
      }
      const installedSuitableKernel = this.installedVersions.filter((fPath) => {
        return path.basename(fPath).includes(`chrome_${kernelVo.versionNumber}`);
      });
      if (installedSuitableKernel.length > 0) {
        kernelPath = installedSuitableKernel[0];
      } else {
        kernelPath = this.installedVersions[0];
      }
    } catch (e) {
      kernelPath = this.installedVersions[0];
      logger.info('[BROWSER] findsSuitableKernel error', e);
    }
    return kernelPath;
  }

  getInstalledVersions() {
    const installedVersions = this.installedVersions;
    return installedVersions.map((v) => {
      return Number(path.basename(v ?? '').split('_')[1]);
    });
  }

  getInstalledKernelInfo() {
    const installedVersions = this.installedVersions;
    return installedVersions.map((v) => {
      const [, version, buildNumber] = path.basename(v ?? '').split('_');
      return {
        path: v,
        version: Number(version),
        buildNumber: Number(buildNumber),
      };
    });
  }
}
