import os from 'os';
import path from 'path';
import fs from 'fs-extra';
import sqlite3, { Database } from 'better-sqlite3';
import crypto from 'crypto';
import _ from 'lodash';

import appConfig, { getDynamicPath } from '../configs/app';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import { isWin7Platform } from '@e/utils/utils';
import { Win32crypt } from '@e/helper/win_crypt';

type Props = {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  shopPolicy: API.ShopSyncPolicyVo;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
  kernelNumber: number;
};

/**
 * 分身密码代填
 */
export default class LoginDatabase {
  props: Props;
  passwords: API.ShopPasswordsDto[];
  shopDataDir: string;
  loginDataDbPath: string;
  webDataDbPath: string;
  dbConn?: Database;
  errorOccur: boolean;
  enabled: boolean;
  keepLocalFile = true;
  private masterKey?: Buffer;
  constructor(p: Props) {
    this.props = p;
    this.passwords = [];
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    this.shopDataDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${this.props.shopId}`,
      this.props.tempShopDataSubDir,
    );
    this.errorOccur = false;
    this.loginDataDbPath = path.join(this.shopDataDir, 'Default', 'Login Data');
    this.webDataDbPath = path.join(this.shopDataDir, 'Default', 'Web Data');
    this.enabled = this.getEnabled();
    if (p.shopInfo.stateless && !p.shopInfo.statelessSyncPolicyVo?.passwords) {
      this.keepLocalFile = false;
    }
  }

  getEnabled() {
    if (this.props.shopInfo.stateless) {
      return (
        !!this.props.shopInfo.statelessSyncPolicyVo?.passwords && !!this.props.shopPolicy.passwords
      );
    }
    return !!this.props.shopPolicy.passwords;
  }

  async init() {
    // 拉取浏览器密码
    this.passwords = await this.props.requestAgent.request(
      `/api/shop/settings/${this.props.shopId}/passwords`,
      {
        teamId: this.props.teamId,
      },
    );
    if (this.enabled) {
      await this.writeData();
    }
    return this;
  }
  static clear(shopId: number) {
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    const dir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${shopId}`,
    );
    const filePath = path.join(dir, 'Default', 'Login Data');
    logger.verbose('[APP] clear login data table', filePath);
    const db = sqlite3(filePath);
    this.doClear(db);
  }

  static doClear(db: Database, kernelNumber?: number) {
    try {
      db.exec(`DROP TABLE IF EXISTS logins`);
      if (isWin7Platform()) {
        db.exec(
          `CREATE TABLE logins (origin_url VARCHAR NOT NULL, action_url VARCHAR, username_element VARCHAR, username_value VARCHAR, password_element VARCHAR, password_value BLOB, submit_element VARCHAR, signon_realm VARCHAR NOT NULL, date_created INTEGER NOT NULL, blacklisted_by_user INTEGER NOT NULL, scheme INTEGER NOT NULL, password_type INTEGER, times_used INTEGER, form_data BLOB, display_name VARCHAR, icon_url VARCHAR, federation_url VARCHAR, skip_zero_click INTEGER, generation_upload_status INTEGER, possible_username_pairs BLOB, id INTEGER PRIMARY KEY AUTOINCREMENT, date_last_used INTEGER NOT NULL DEFAULT 0, moving_blocked_for BLOB, date_password_modified INTEGER NOT NULL DEFAULT 0, UNIQUE (origin_url, username_element, username_value, password_element, signon_realm))`,
        );
      } else {
        db.exec(
          `CREATE TABLE logins (origin_url VARCHAR NOT NULL, action_url VARCHAR, username_element VARCHAR, username_value VARCHAR, password_element VARCHAR, password_value BLOB, submit_element VARCHAR, signon_realm VARCHAR NOT NULL, date_created INTEGER NOT NULL, blacklisted_by_user INTEGER NOT NULL, scheme INTEGER NOT NULL, password_type INTEGER, times_used INTEGER, form_data BLOB, display_name VARCHAR, icon_url VARCHAR, federation_url VARCHAR, skip_zero_click INTEGER, generation_upload_status INTEGER, possible_username_pairs BLOB, id INTEGER PRIMARY KEY AUTOINCREMENT, date_last_used INTEGER NOT NULL DEFAULT 0, moving_blocked_for BLOB, date_password_modified INTEGER NOT NULL DEFAULT 0, sender_email VARCHAR, sender_name VARCHAR, date_received INTEGER, sharing_notification_displayed INTEGER NOT NULL DEFAULT 0, keychain_identifier BLOB, sender_profile_image_url VARCHAR, UNIQUE (origin_url, username_element, username_value, password_element, signon_realm))`,
        );
      }
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] clear db table fail', e);
      throw e;
    }
  }

  getPasswords() {
    return this.passwords;
  }

  async writeData() {
    // 目前只支持 windows 平台
    if (os.platform() !== 'win32') {
      return;
    }
    try {
      if (!fs.existsSync(path.join(this.shopDataDir, 'Local State'))) {
        fs.mkdirSync(this.shopDataDir, { recursive: true });
        const buf = crypto.randomBytes(32);
        const encryptedKey: string = await new Promise((resolve, reject) => {
          const timeoutTimer = setTimeout(() => {
            reject(new Error('[APP] create master key timeout'));
          }, 10000);
          Win32crypt.encryptData(buf)
            .then((res) => {
              resolve(res);
            })
            .catch((e) => {
              reject(e);
            })
            .finally(() => {
              clearTimeout(timeoutTimer);
            });
        });
        const key = Buffer.concat([
          Buffer.from('DPAPI', 'utf8'),
          Buffer.from(encryptedKey, 'base64'),
        ]);
        const text = JSON.stringify({
          os_crypt: {
            encrypted_key: key.toString('base64'),
          },
        });
        fs.writeFileSync(path.join(this.shopDataDir, 'Local State'), text);
      } else {
        await this.getMasterKey();
        await fs.rm(this.loginDataDbPath);
      }
      await this.createDb();
      await this.repairData();
      await this.insertPassword();
    } catch (e) {
      this.errorOccur = true;
    } finally {
      this.closeDb();
      logger.info(`[BROWSER] LoginDatabaseSync initialized (shopId: ${this.props?.shopId})`);
    }
  }

  hasException() {
    return this.errorOccur;
  }

  async getDbConn(readonly = false) {
    if (!this.dbConn) {
      let sqlFilePath = this.loginDataDbPath;
      if (readonly) {
        const bakPath = `${this.loginDataDbPath}.bak`;
        await fs.copyFile(this.loginDataDbPath, bakPath);
        const hasBakPath = await fs.pathExists(bakPath);
        if (hasBakPath) {
          sqlFilePath = bakPath;
        }
      }
      this.dbConn = sqlite3(sqlFilePath, { readonly });
    }
    return this.dbConn;
  }

  closeDb() {
    if (this.dbConn) {
      this.dbConn.close();
      this.dbConn = undefined;
    }
  }

  async getMasterKey(): Promise<Buffer> {
    try {
      if (os.platform() !== 'win32') return Promise.resolve(Buffer.from([]));
      if (this.masterKey) return this.masterKey;
      const filePath = path.join(this.shopDataDir, 'Local State');
      let ret: Buffer = await new Promise((resolve, reject) => {
        fs.readJson(filePath, { encoding: 'utf-8' }, (err, data) => {
          if (err) {
            reject(err);
            return;
          }
          const base64Key = data.os_crypt?.encrypted_key ?? '';
          let buf = Buffer.from(base64Key, 'base64');
          buf = buf.slice(5); // remove DPAPI prefix
          const timeoutTimer = setTimeout(() => {
            reject(new Error('[APP] get master key timeout'));
          }, 10000);
          Win32crypt.decryptData(buf)
            .then((key) => {
              resolve(Buffer.from(key, 'base64'));
            })
            .catch((e) => {
              reject(e);
            })
            .finally(() => {
              clearTimeout(timeoutTimer);
            });
        });
      });
      this.masterKey = ret;
      return this.masterKey;
    } catch (e) {
      logger.error('[APP] get master key failed', e);
      this.errorOccur = true;
      return Promise.resolve(Buffer.from([]));
    }
  }

  /**
   * 创建数据库
   */
  async createDb() {
    logger.verbose('[APP] create database', this.loginDataDbPath);
    fs.ensureFileSync(this.loginDataDbPath);
    const db = await this.getDbConn();
    try {
      db.exec(`DROP TABLE IF EXISTS insecure_credentials`);
      db.exec(`DROP TABLE IF EXISTS logins`);
      db.exec(`DROP TABLE IF EXISTS field_info`);
      db.exec(`DROP TABLE IF EXISTS meta`);
      db.exec(`DROP TABLE IF EXISTS password_notes`);
      db.exec(`DROP TABLE IF EXISTS stats`);
      db.exec(`DROP TABLE IF EXISTS sync_entities_metadata`);
      db.exec(`DROP TABLE IF EXISTS sync_model_metadata`);
      if (isWin7Platform()) {
        db.exec(
          `CREATE TABLE insecure_credentials (parent_id INTEGER REFERENCES logins ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED, insecurity_type INTEGER NOT NULL, create_time INTEGER NOT NULL, is_muted INTEGER NOT NULL DEFAULT 0, UNIQUE (parent_id, insecurity_type))`,
        );
        db.exec(
          `CREATE TABLE logins (origin_url VARCHAR NOT NULL, action_url VARCHAR, username_element VARCHAR, username_value VARCHAR, password_element VARCHAR, password_value BLOB, submit_element VARCHAR, signon_realm VARCHAR NOT NULL, date_created INTEGER NOT NULL, blacklisted_by_user INTEGER NOT NULL, scheme INTEGER NOT NULL, password_type INTEGER, times_used INTEGER, form_data BLOB, display_name VARCHAR, icon_url VARCHAR, federation_url VARCHAR, skip_zero_click INTEGER, generation_upload_status INTEGER, possible_username_pairs BLOB, id INTEGER PRIMARY KEY AUTOINCREMENT, date_last_used INTEGER NOT NULL DEFAULT 0, moving_blocked_for BLOB, date_password_modified INTEGER NOT NULL DEFAULT 0, UNIQUE (origin_url, username_element, username_value, password_element, signon_realm))`,
        );
      } else {
        db.exec(
          `CREATE TABLE insecure_credentials (parent_id INTEGER REFERENCES logins ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED, insecurity_type INTEGER NOT NULL, create_time INTEGER NOT NULL, is_muted INTEGER NOT NULL DEFAULT 0, trigger_notification_from_backend INTEGER NOT NULL DEFAULT 0, UNIQUE (parent_id, insecurity_type))`,
        );
        db.exec(
          `CREATE TABLE logins (origin_url VARCHAR NOT NULL, action_url VARCHAR, username_element VARCHAR, username_value VARCHAR, password_element VARCHAR, password_value BLOB, submit_element VARCHAR, signon_realm VARCHAR NOT NULL, date_created INTEGER NOT NULL, blacklisted_by_user INTEGER NOT NULL, scheme INTEGER NOT NULL, password_type INTEGER, times_used INTEGER, form_data BLOB, display_name VARCHAR, icon_url VARCHAR, federation_url VARCHAR, skip_zero_click INTEGER, generation_upload_status INTEGER, possible_username_pairs BLOB, id INTEGER PRIMARY KEY AUTOINCREMENT, date_last_used INTEGER NOT NULL DEFAULT 0, moving_blocked_for BLOB, date_password_modified INTEGER NOT NULL DEFAULT 0, sender_email VARCHAR, sender_name VARCHAR, date_received INTEGER, sharing_notification_displayed INTEGER NOT NULL DEFAULT 0, keychain_identifier BLOB, sender_profile_image_url VARCHAR, UNIQUE (origin_url, username_element, username_value, password_element, signon_realm))`,
        );
      }
      db.exec(
        `CREATE TABLE field_info (form_signature INTEGER NOT NULL, field_signature INTEGER NOT NULL, field_type INTEGER NOT NULL, create_time INTEGER NOT NULL, UNIQUE (form_signature, field_signature))`,
      );
      db.exec(`CREATE TABLE meta(key LONGVARCHAR NOT NULL UNIQUE PRIMARY KEY, value LONGVARCHAR)`);
      db.exec(
        `CREATE TABLE password_notes (id INTEGER PRIMARY KEY AUTOINCREMENT, parent_id INTEGER NOT NULL REFERENCES logins ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED, key VARCHAR NOT NULL, value BLOB, date_created INTEGER NOT NULL, confidential INTEGER, UNIQUE (parent_id, key))`,
      );
      db.exec(
        `CREATE TABLE stats (origin_domain VARCHAR NOT NULL, username_value VARCHAR, dismissal_count INTEGER, update_time INTEGER NOT NULL, UNIQUE(origin_domain, username_value))`,
      );
      db.exec(
        `CREATE TABLE sync_entities_metadata (storage_key INTEGER PRIMARY KEY AUTOINCREMENT, metadata VARCHAR NOT NULL)`,
      );
      db.exec(
        `CREATE TABLE sync_model_metadata (id INTEGER PRIMARY KEY AUTOINCREMENT, model_metadata VARCHAR NOT NULL)`,
      );
      db.exec(`CREATE INDEX field_info_index ON field_info (form_signature, field_signature)`);
      db.exec(`CREATE INDEX foreign_key_index ON insecure_credentials (parent_id)`);
      db.exec(`CREATE INDEX foreign_key_index_notes ON password_notes (parent_id)`);
      db.exec(`CREATE INDEX logins_signon ON logins (signon_realm)`);
      db.exec(`CREATE INDEX stats_origin ON stats(origin_domain)`);
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] Init login data db fail', e);
      throw e;
    }
  }

  /**
   * 清除表中已有的数据
   */
  async clearTable() {
    logger.verbose('[APP] clear login data table', this.loginDataDbPath);
    const db = await this.getDbConn();
    LoginDatabase.doClear(db, this.props.kernelNumber);
  }

  async repairData() {
    const db = await this.getDbConn();
    try {
      const metaTable = db
        .prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`)
        .get('meta');
      if (!metaTable) {
        db.exec(
          `CREATE TABLE meta(key LONGVARCHAR NOT NULL UNIQUE PRIMARY KEY, value LONGVARCHAR)`,
        );
      }
      const last_compatible_version = isWin7Platform() ? '33' : '40';
      const version = isWin7Platform() ? '33' : '41';
      const v1: any = db.prepare(`SELECT * FROM meta WHERE key=?`).get('last_compatible_version');
      if (!v1) {
        db.prepare(`INSERT INTO meta (key, value) VALUES (?, ?)`).run(
          'last_compatible_version',
          last_compatible_version,
        );
      } else if (v1.value && v1.value !== last_compatible_version) {
        db.prepare(`UPDATE meta SET value = ? WHERE key = ?`).run(
          last_compatible_version,
          'last_compatible_version',
        );
      }
      const v2: any = db.prepare(`SELECT * FROM meta WHERE key=?`).get('version');
      if (!v2) {
        db.prepare(`INSERT INTO meta (key, value) VALUES (?, ?)`).run('version', version);
      } else if (v2.value && v2.value !== version) {
        db.prepare(`UPDATE meta SET value = ? WHERE key = ?`).run(version, 'version');
      }
    } catch (e) {
      logger.error('[APP] repair db table fail', e);
    }
  }

  /**
   * 插入密码
   */
  async insertPassword() {
    if (!this.passwords) return;
    const db = await this.getDbConn();
    const len = this.passwords.length;
    logger.verbose(`[APP] insert ${len} rows to login data table`);
    try {
      const masterKey = await this.getMasterKey();
      if (this.errorOccur) return;
      for (let i = 0; i < len; i++) {
        const passVo = this.passwords[i];
        const iv = crypto.randomBytes(12);
        const cipher = crypto.createCipheriv('aes-256-gcm', masterKey, iv);
        let enc = cipher.update(passVo.passwordValue ?? '', 'utf8', 'base64');
        enc += cipher.final('base64');
        const pwdBlob = Buffer.concat([
          Buffer.from('v10'),
          iv,
          Buffer.from(enc, 'base64'),
          cipher.getAuthTag(),
        ]);
        for (let i = 3; i < pwdBlob.length; i++) {
          const mask = (0x7e6 * i) % 64;
          pwdBlob[i] = pwdBlob[i] ^ mask;
        }
        const dateCreated = !passVo.dateCreated ? 0 : (passVo.dateCreated + 11644473600) * 1000000;
        const stmt = db.prepare(
          `INSERT INTO logins (origin_url, action_url, username_element, password_element, username_value, password_value, signon_realm, date_created, blacklisted_by_user, scheme, password_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        );
        let signonRealm = passVo.signonRealm || passVo.originUrl;
        try {
          if (!passVo.signonRealm) {
            signonRealm = new URL(signonRealm ?? '').origin + '/';
          }
        } catch (e) {}
        stmt.run(
          passVo.originUrl,
          passVo.actionUrl,
          passVo.usernameElement,
          passVo.passwordElement,
          passVo.usernameValue,
          pwdBlob,
          signonRealm,
          dateCreated,
          passVo.blacklistedByUser || 0,
          passVo.scheme || 0,
          passVo.passwordType || 0,
        );
      }
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] insert login data fail', e);
      throw e;
    }
  }

  /**
   * 获取本地已存储的数据
   */
  async getLocalData() {
    if (os.platform() !== 'win32') return [];
    try {
      const masterKey = await this.getMasterKey();
      const db = await this.getDbConn(true);
      const stmt = db.prepare(
        'SELECT origin_url, action_url, username_element, username_value, password_element, password_value, submit_element, signon_realm, date_created, blacklisted_by_user, scheme, password_type FROM logins',
      );
      const result = [];
      for (const row of stmt.iterate() as any) {
        const pwdBuf = row.password_value;
        for (let i = 3; i < pwdBuf.length; i++) {
          const mask = (0x7e6 * i) % 64;
          pwdBuf[i] = pwdBuf[i] ^ mask;
        }
        const iv = pwdBuf.slice(3, 15);
        const payload = pwdBuf.slice(15, -16);
        const decipher = crypto.createDecipheriv(
          'aes-256-gcm',
          // @ts-ignore
          masterKey,
          iv,
        );
        const decryptedValue = decipher.update(payload, undefined, 'utf8');
        // 过期时间换算参考 https://stackoverflow.com/questions/43518199/cookies-expiration-time-format
        if ((row.origin_url || row.action_url) && (!!row.username_value || !!decryptedValue)) {
          result.push({
            originUrl: row.origin_url,
            actionUrl: row.action_url,
            usernameElement: row.username_element,
            usernameValue: row.username_value,
            passwordElement: row.password_element,
            passwordValue: decryptedValue,
            submitElement: row.submit_element,
            signonRealm: row.signon_realm,
            dateCreated: Math.max(0, row.date_created / 1000000 - 11644473600),
            blacklistedByUser: row.blacklisted_by_user,
            scheme: row.scheme,
            passwordType: row.password_type,
          });
        }
      }
      return result;
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] get login data fail');
      throw e;
    }
  }

  async tryClearLocalFile() {
    if (!this.keepLocalFile) {
      if (fs.existsSync(this.loginDataDbPath)) {
        await fs.rm(this.loginDataDbPath).catch((e) => {
          logger.error('[APP] remove Login Data file failed', e);
        });
      }
      if (fs.existsSync(this.webDataDbPath)) {
        await fs.rm(this.webDataDbPath).catch((e) => {
          logger.error('[APP] remove Web Data file failed', e);
        });
      }
    }
  }

  async upload() {
    if (!this.enabled) return;
    if (os.platform() === 'win32' && !this.errorOccur) {
      try {
        const newPasswords = await this.getLocalData();
        const _oldPasswords = _.sortBy(
          this.passwords.map((p) =>
            _.pick(p, [
              'originUrl',
              'actionUrl',
              'usernameElement',
              'usernameValue',
              'passwordElement',
              'passwordValue',
            ]),
          ),
          ['originUrl'],
        );
        const _newPasswords = _.sortBy(
          newPasswords.map((p) =>
            _.pick(p, [
              'originUrl',
              'actionUrl',
              'usernameElement',
              'usernameValue',
              'passwordElement',
              'passwordValue',
            ]),
          ),
          ['originUrl'],
        );
        if (_.isEqual(_oldPasswords, _newPasswords)) {
          logger.verbose('[APP] login data not changed');
          return;
        }
        // 汇报 newPasswords
        appConfig.DEBUG && logger.verbose('[APP] update remote passwords', newPasswords);
        await this.props.requestAgent.request(`/api/shop/settings/${this.props.shopId}/passwords`, {
          data: newPasswords,
          teamId: this.props.teamId,
          method: 'POST',
        });
      } finally {
        this.closeDb();
      }
    }
  }
}
