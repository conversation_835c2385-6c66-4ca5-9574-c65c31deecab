// @ts-nocheck
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'donkey-puppeteer-core';
import { dispatchMsg } from '@e/utils/ipc';
import { BrowserWindow } from 'electron';
import fs from 'fs-extra';
import path from 'path';
import { MessageDispatcher } from '@e/components/messageDispatcher';

export default class BrowserActionRecorder {
  private browser: Browser | null;
  private rpaEditorWin?: BrowserWindow;
  private state: string;
  private lastClickTimestamp: any;
  private wsDispatcher: MessageDispatcher;
  constructor(browser: Browser, rpaEditorWin?: BrowserWindow, wsDispatcher: MessageDispatcher) {
    this.browser = browser;
    this.rpaEditorWin = rpaEditorWin;
    this.wsDispatcher = wsDispatcher;
    this.state = 'stopped';
    this.lastClickTimestamp = 0;
    this.lastActiveTabId = -2;
    if (!browser) return;
    this.init();
  }

  // 在页面上注入一个事件监听器
  async _injectListener(page: Page) {
    try {
      // let scriptContent = '(async function(){';
      // scriptContent += fs.readFileSync(path.resolve(__dirname, 'scripts', 'finder.js'));
      // scriptContent += `})()`;
      // if (['', 'about:blank', 'chrome://new-tab/', 'chrome://new-tab-page/'].includes(page.url())) {
      //   return;
      // }
      // let count = 3;
      // while (count--) {
      //   try {
      //     await page.evaluate(scriptContent);
      //     break;
      //   } catch (e) {
      //     console.error('inject script to', page.url(), 'Failed', e);
      //     await new Promise((resolve) => setTimeout(resolve, 500));
      //   }
      // }
      await page.evaluate(() => {
        if (window.__hyrpa_getOverflowAutoParent) return;
        var scrollTimer = 0;
        var scrollStartTop = -1;
        var scrollContainer = document.body;
        window.__hyrpa_getOverflowAutoParent = (element) => {
          if (!element) {
            return document.body;
          }
          if (element === document) {
            return document.documentElement;
          }
          const myStyle = getComputedStyle(element);
          if (
            myStyle.overflow === 'auto' ||
            myStyle.overflowX === 'auto' ||
            myStyle.overflowY === 'auto'
          ) {
            return element;
          }
          const parentElement = element.parentElement;
          if (!parentElement) {
            return document.body;
          }
          return window.__hyrpa_getOverflowAutoParent(parentElement);
        };
        window.__hyrpa_getElementXPath = (element) => {
          if (element && element.id) {
            return `//*[@id="${element.id}"]`;
          }
          let path = '';
          for (; element && element.nodeType === 1; element = element.parentNode) {
            let index = 0;
            for (
              let sibling = element.previousSibling;
              sibling;
              sibling = sibling.previousSibling
            ) {
              if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                index++;
              }
            }
            const tagName = element.tagName.toLowerCase();
            const pathIndex = index ? `[${index + 1}]` : '';
            path = `/${tagName}${pathIndex}${path}`;
          }
          return path.length ? path : null;
        };
        document.addEventListener(
          'click',
          (evt) => {
            if (!window.__hyrpa_onMouseClick) return;
            window.__hyrpa_onMouseClick(window.__hyrpa_getElementXPath(evt.target));
          },
          true,
        );
        document.addEventListener(
          'scroll',
          (evt) => {
            // console.log('scroll', evt);
            if (!window.__hyrpa_onScroll) return;
            if (scrollStartTop === -1) {
              scrollContainer = window.__hyrpa_getOverflowAutoParent(evt.target);
              scrollStartTop = scrollContainer.scrollTop ?? 0;
            }
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(() => {
              if (scrollContainer) {
                window.__hyrpa_onScroll(
                  (scrollContainer.scrollTop ?? 0) - scrollStartTop,
                  window.__hyrpa_getElementXPath(scrollContainer),
                );
                scrollStartTop = -1;
              }
            }, 500);
          },
          true,
        );
        document.addEventListener(
          'input',
          (evt) => {
            // console.log('input', evt);
            if (evt.target.tagName === 'SELECT' && window.__hyrpa_onSelect) {
              for (const el of evt.target.children) {
                if (el.value === evt.target.value) {
                  window.__hyrpa_onSelect(
                    el.textContent,
                    window.__hyrpa_getElementXPath(evt.target),
                  );
                  break;
                }
              }
            } else if (evt.target.type === 'checkbox' && window.__hyrpa_onChecked) {
              window.__hyrpa_onChecked(
                evt.target.checked,
                window.__hyrpa_getElementXPath(evt.target),
              );
            } else if (window.__hyrpa_onInput) {
              window.__hyrpa_onInput(evt.target.value, window.__hyrpa_getElementXPath(evt.target));
            }
          },
          true,
        );
        document.addEventListener(
          'keydown',
          (evt) => {
            if (['INPUT', 'TEXTAREA'].includes(evt.target.tagName)) return;
            if (window.__hyrpa_onInput) {
              window.__hyrpa_onKeydown(evt.code);
            }
          },
          true,
        );
      });
      // console.log('inject script to', page.url(), 'Success');
    } catch (e) {
      // console.error('inject script to', page.url(), 'Failed', e);
    }
  }

  async exposeFn(page: Page) {
    page.on('framenavigated', async (frame: Frame & { _isOpened: boolean }) => {
      if (
        frame === page?.mainFrame() &&
        frame.url() !== 'about:blank' &&
        frame.url() !== 'chrome://new-tab/'
      ) {
        if (!frame._isOpened) {
          frame._isOpened = true;
          setTimeout(() => {
            // 如果距离上次点击时间过短，认为时点击链接打开的页面
            if (Date.now() - this.lastClickTimestamp < 1000) return;
            // console.log('Page navigated', frame.url());
            this._reportAction('rpa.tab.GotoPage', { url: frame.url(), waitFor: 'body' });
          }, 200);
        } else {
          // console.log('Page refreshed', frame.url());
          this._reportAction('rpa.tab.RefreshTab', { url: frame.url(), waitFor: 'body' });
        }
      }
      await this._injectListener(frame);
    });
    page.on('frameattached', async (frame: Frame) => {
      await this._injectListener(frame);
    });
    // 鼠标点击
    page.exposeFunction('__hyrpa_onMouseClick', (xpath: string) => {
      // console.log('Mouse clicked in the page');
      this.lastClickTimestamp = Date.now();
      this._reportAction('rpa.event.Click', {
        selector: xpath,
        button: 'left',
        clickCount: 1,
        center: false,
        modifiers: 0,
      });
    });
    page.exposeFunction('__hyrpa_onScroll', (offset: number, xpath: string) => {
      if (offset === 0) return;
      // console.log('Scroll in the page', offset);
      this._reportAction('rpa.tab.ScrollPage', {
        selector: xpath,
        scrollType: 'px',
        val: offset,
      });
    });
    page.exposeFunction('__hyrpa_onInput', (text: string, xpath: string) => {
      // console.log('Type text in the page', text);
      this._reportAction('rpa.event.Type', {
        selector: xpath,
        text,
        clear: true,
        usePaste: false,
      });
    });
    page.exposeFunction('__hyrpa_onKeydown', (code: string) => {
      if (
        [
          'Enter',
          'Escape',
          'Backspace',
          'Tab',
          'Space',
          'ArrowUp',
          'ArrowDown',
          'ArrowLeft',
          'ArrowRight',
        ].includes(code)
      ) {
        // console.log('Keydown in the page', key);
        this._reportAction('rpa.event.Keyboard', { keys: code });
      }
    });
    page.exposeFunction('__hyrpa_onSelect', (text: string, xpath: string) => {
      // console.log('Select in the page', text);
      this._reportAction('rpa.event.Select', { selector: xpath, value: text });
    });
    page.exposeFunction('__hyrpa_onChecked', (checked: boolean, xpath: string) => {
      // console.log('Checked in the page', checked);
      this._reportAction('rpa.event.CheckBox', {
        selector: xpath,
        action: checked ? 'check' : 'uncheck',
      });
    });
    this._injectListener(page);
  }

  async init() {
    try {
      this.browser?.on('targetcreated', async (target) => {
        if (target.type() !== 'page') return;
        let scriptContent = '(async function(){';
        scriptContent += fs.readFileSync(path.resolve(__dirname, 'scripts', 'finder.js'));
        scriptContent += `})()`;
        const page: Page = await target.page();
        await page.evaluateOnNewDocument(scriptContent);
        // 如果距离上次点击时间过短，认为时点击链接打开的页面
        if (Date.now() - this.lastClickTimestamp < 1000) {
          this.exposeFn(await target.page());
          return;
        }
        // console.log('New tab opened:', target.url());
        this._reportAction('rpa.tab.NewTab', {});
        this.lastActiveTabId = -2;
        const newPage = await target.page();
        this.exposeFn(newPage);
      });
      this.browser?.on('targetchanged', async (target) => {
        if (target.type() !== 'page') return;
        const page = await target.page();
        if (page) {
          page.mainFrame()._isOpened = false;
        }
        // console.log('targetchanged:', target.url());
      });
    } catch (e) {
      console.error(e);
    }
  }

  _reportAction(type: string, props: Record<string, any>) {
    if (this.state !== 'recording') return;
    if (this.rpaEditorWin) {
      dispatchMsg('motion-recorder-res', { type, props }, this.rpaEditorWin);
    }
  }

  pause() {
    if (this.state === 'paused') return;
    this.state = 'paused';
    this.wsDispatcher.dispatcher(
      JSON.stringify({ action: 'rpa-recorder-status-update', data: { active: false } }),
    );
  }

  resume() {
    if (this.state === 'recording') return;
    this.state = 'recording';
    this.wsDispatcher.dispatcher(
      JSON.stringify({ action: 'rpa-recorder-status-update', data: { active: true } }),
    );
  }

  stop() {
    if (this.state === 'stopped') return;
    this.state = 'stopped';
    this.browser = null;
    this.rpaEditorWin = undefined;
  }
}
