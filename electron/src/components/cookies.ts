import path from 'path';
import _ from 'lodash';
import fs from 'fs-extra';

import appConfig, { getDynamicPath } from '../configs/app';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import sqlite3, { Database } from 'better-sqlite3';

type Props = {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  shopPolicy: API.ShopSyncPolicyVo;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
};

/**
 * 本地cookie的文件位置是Cookie
 */

/**
 * 浏览器Cookie同步
 */
export default class CookiesSync {
  props: Props;
  shopDataDir: string;
  errorOccur: boolean;
  cookies: any[] | undefined;
  keepLocalFile = true;
  dbConn?: Database;
  filePath: string;
  enabled: boolean;
  constructor(p: Props) {
    this.props = p;
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    this.shopDataDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${this.props.shopId}`,
      this.props.tempShopDataSubDir,
    );
    this.errorOccur = false;
    this.cookies = [];
    const altPath = path.join(this.shopDataDir, 'Default', 'Network', 'Cookies');
    this.filePath = fs.existsSync(altPath)
      ? altPath
      : path.join(this.shopDataDir, 'Default', 'Cookies');
    // 无状态分身不同步cookies，
    this.enabled = this.getEnabled();
    if (p.shopInfo.stateless && !p.shopInfo.statelessSyncPolicyVo?.cookies) {
      this.keepLocalFile = false;
    }
  }

  getEnabled() {
    if (this.props.shopInfo.stateless) {
      return (
        !!this.props.shopInfo.statelessSyncPolicyVo?.cookies && !!this.props.shopPolicy.cookies
      );
    }
    return !!this.props.shopPolicy.cookies;
  }

  static clear(shopId: number) {
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    const shopDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${shopId}`,
    );
    const altPath = path.join(shopDir, 'Default', 'Network', 'Cookies');
    const filePath = fs.existsSync(altPath) ? altPath : path.join(shopDir, 'Default', 'Cookies');
    logger.verbose('[APP] clear cookies data table', filePath);
    const db = sqlite3(filePath);
    try {
      db.exec(`PRAGMA foreign_keys = false`);
      db.exec(`DROP TABLE IF EXISTS cookies`);
      db.exec(
        `CREATE TABLE cookies(creation_utc INTEGER NOT NULL,host_key TEXT NOT NULL,top_frame_site_key TEXT NOT NULL,name TEXT NOT NULL,value TEXT NOT NULL,encrypted_value BLOB NOT NULL,path TEXT NOT NULL,expires_utc INTEGER NOT NULL,is_secure INTEGER NOT NULL,is_httponly INTEGER NOT NULL,last_access_utc INTEGER NOT NULL,has_expires INTEGER NOT NULL,is_persistent INTEGER NOT NULL,priority INTEGER NOT NULL,samesite INTEGER NOT NULL,source_scheme INTEGER NOT NULL,source_port INTEGER NOT NULL,is_same_party INTEGER NOT NULL,last_update_utc INTEGER NOT NULL)
)`,
      );
      db.exec(
        `CREATE UNIQUE INDEX cookies_unique_index ON cookies ( host_key ASC, top_frame_site_key ASC, name ASC, path ASC)`,
      );
      db.exec(`PRAGMA foreign_keys = true`);
    } catch (e) {
      logger.reportError(e, { noNotify: true });
      logger.error('[APP] clear db table fail', e);
      throw e;
    }
  }

  async init() {
    await this.getRemoteData();
    return this;
  }

  async getRemoteData() {
    if (!this.enabled) return;
    try {
      // 拉取 cookies
      let { cookies = [] }: any = await this.props.requestAgent.request(
        `/api/shop/${this.props.shopId}/getCookies`,
        {
          teamId: this.props.teamId,
        },
      );
      // console.log('remote cookies', cookies);
      // 将 cookie 项的非法值 null 转为 undefined，否则写 cookies 时会报错
      this.cookies = cookies.map((c: any) => {
        const keys = Object.keys(c);
        for (let k of keys) {
          if (c[k] === null) {
            c[k] = undefined;
          }
        }
        return c;
      });
    } catch (e) {
      this.errorOccur = true;
      logger.error(`[BROWSER] get remote cookies failed (shopId: ${this.props.shopId})`, e);
    } finally {
      logger.info(`[BROWSER] CookiesSync initialized (shopId: ${this.props?.shopId})`);
    }
  }

  isEnabled() {
    return this.enabled;
  }

  hasException() {
    return this.errorOccur;
  }

  isOnlyDownload() {
    if (process.env.OEM_NAME === 'gg') {
      return true;
    }
    return false;
  }

  exceptionOccur() {
    this.errorOccur = true;
  }

  getCookies() {
    return this.cookies;
  }

  updateTempCookies(cookies: any) {
    this.cookies = cookies;
  }
  getDbConn() {
    if (!this.dbConn) {
      this.dbConn = sqlite3(this.filePath);
    }
    return this.dbConn;
  }

  async tryClearLocalFile() {
    if (!this.keepLocalFile) {
      const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
      const shopDir = path.resolve(
        BROWSER_USER_DATA_DIR_PATH,
        `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${this.props.shopId}`,
      );
      const altPath = path.join(shopDir, 'Default', 'Network', 'Cookies');
      const filePath = fs.existsSync(altPath) ? altPath : path.join(shopDir, 'Default', 'Cookies');
      if (fs.existsSync(filePath)) {
        await fs.rm(filePath).catch((e) => {
          logger.error('[APP] remove cookies file failed', e);
        });
      }
    }
  }

  async upload(automate = false) {
    if (!this.enabled || this.errorOccur || !this.cookies) {
      return;
    }
    if (this.isOnlyDownload()) {
      return;
    }
    const newCookies = _.map(this.cookies, (cookie) =>
      _.pick(cookie, [
        'name',
        'value',
        'domain',
        'path',
        'expires',
        'httpOnly',
        'secure',
        'sameSite',
        'priority',
        'sameParty',
      ]),
    );
    appConfig.DEBUG &&
      logger.info(`[APP] update remote cookies - ${newCookies.length} (automate: ${automate})`);
    await this.props.requestAgent.request(`/api/shop/${this.props.shopId}/setCookies`, {
      data: {
        cookies: newCookies,
      },
      teamId: this.props.teamId,
      method: 'PUT',
    });
    this.cookies = undefined;
  }
}
