import path from 'path';
import os from 'os';
import { app, crashReporter, <PERSON>u, MenuItemConstructorOptions, nativeImage, Tray } from 'electron';
import { autoUpdater } from 'electron-updater';

import protocol from './protocol';
import db from './db';
import logger, { setLevel } from '@e/services/logger';
import {
  getCurrentWindow,
  getMainWindow,
  openLoginWindow,
  openSystemPrefWindow,
} from '../utils/window';
import * as ipc from '@e/utils/ipc';
import AppConfig, { getDynamicPath } from '@e/configs/app';
import i18n from '@e/utils/i18n';
import { isMacPlatform, resolveUrl, useSystemDefaultTitleBar } from '@e/utils/utils';
import KernelManage from '@e/components/kernelManage';
import { dispatchMsg } from '@e/utils/ipc';
import appConfig from '@e/configs/app';

process.on('uncaughtException', (err) => {
  logger.error('[APP] uncaughtException', err);
});

const { LOG_DIR } = getDynamicPath();
app.setAppLogsPath(LOG_DIR);
app.commandLine.appendSwitch('ignore-certificate-errors');
app.commandLine.appendSwitch('disable-renderer-accessibility'); // 禁用残障服务读取DOM树
app.commandLine.appendSwitch('disable-gpu-sandbox');
if (AppConfig.DEBUG) {
  app.commandLine.appendSwitch('remote-debugging-port', '9222');
}
crashReporter.start({ uploadToServer: false });
db.init();
if (db.getDb().get('useBackupEndpoint').value()) {
  db?.changeToEndpoint(2);
}
setLevel(db.getDb().get('LOG_LEVEL').value());
let portalUrl = db.getDb().get('PORTAL_URL').value();
let apiUrl = db.getDb().get('API_URL').value();
logger.info(`[APP] starting (version:${app.getVersion()}, buildNo:${process.env.BUILD_NUMBER})`);
logger.verbose('[APP] PORTAL_URL', portalUrl);
logger.verbose('[APP] API_URL', apiUrl);
protocol.init();
ipc.init();

if (isMacPlatform()) {
  const mode = db.isRuntimeMode() ? 'runtime' : 'app';
  const lang = (process.env.LANG || '').startsWith('zh') ? 'zh' : 'en';
  const arch = os.version().includes('X86_64') ? 'x64' : 'arm64';
  const feedURL = resolveUrl(db.getDlUrl(), `/downloads/mac_${arch}_${mode}_${lang}/`);
  logger.verbose('[APP] FeedURL', feedURL);
  autoUpdater.setFeedURL(feedURL);
}

let tray: Tray | null = null;
let trayMenu: Menu;
app.whenReady().then(async () => {
  logger.info('[APP] ready');
  const isRuntimeMode = db.isRuntimeMode();
  const isRpaExecutor = db.isRpaExecutor();
  const trayIconFileName = isMacPlatform() ? 'logoTemplate.png' : 'tray-logo-16.png';
  tray = new Tray(nativeImage.createFromPath(path.join(__dirname, '..', 'dist', trayIconFileName)));
  let productName = '花漾客户端';
  if (!i18n.isCn()) {
    productName = 'HuaYoung';
  }
  if (db.isRuntimeMode()) {
    productName = '花漾运行时';
    if (!i18n.isCn()) {
      productName = 'HuaYoungRuntime';
    }
  }
  if (appConfig.isOEM) {
    productName = app.getName();
  }
  tray.setToolTip(productName);
  const menuList: MenuItemConstructorOptions[] = [];
  if (!isRuntimeMode && !isRpaExecutor) {
    menuList.push({
      id: 'showWindow',
      label: i18n.t('显示窗口'),
      click: () => {
        if (getCurrentWindow()) {
          getCurrentWindow()?.show();
        } else {
          openLoginWindow();
        }
      },
    });
  }
  if (!isRpaExecutor) {
    if (process.env.OEM_NAME !== 'gg') {
      menuList.push({
        id: 'systemPref',
        visible: isRuntimeMode,
        label: i18n.t('偏好设置'),
        click: () => {
          if (isRuntimeMode) {
            openSystemPrefWindow();
          } else {
            getMainWindow()?.show();
            dispatchMsg('open-system-pref');
          }
        },
      });
    }
  }
  if (!isRuntimeMode && useSystemDefaultTitleBar) {
    // 窗口置顶/取消置顶
    menuList.push({
      id: 'pin',
      type: 'checkbox',
      label: i18n.t('窗口置顶'),
      click: (menuItem) => {
        logger.info('[APP] 窗口置顶', menuItem.checked);
        tray?.setContextMenu(trayMenu);
        getMainWindow()?.setAlwaysOnTop(menuItem.checked);
      },
    });
  }
  menuList.push({
    label: i18n.t('退出'),
    click: () => {
      app.exit();
    },
  });
  trayMenu = Menu.buildFromTemplate(menuList);
  tray.setContextMenu(trayMenu);
  tray.on('double-click', () => {
    if (isRuntimeMode) {
      if (process.env.OEM_NAME === 'gg') {
        return;
      }
      openSystemPrefWindow();
      return;
    }
    if (getCurrentWindow()) {
      getCurrentWindow()?.show();
    } else {
      openLoginWindow();
    }
  });
  KernelManage.getInstance();
});

export function getTrayMenu() {
  return trayMenu;
}
