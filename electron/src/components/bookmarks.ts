import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';
import appConfig, { getDynamicPath } from '../configs/app';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import i18n from '@e/utils/i18n';
import db from '@e/components/db';
import * as process from 'node:process';

type Props = {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  shopPolicy: API.ShopSyncPolicyVo;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
};

function getDataStructure(insertPlaceholder = false) {
  return {
    roots: {
      bookmark_bar: {
        children: insertPlaceholder
          ? [
              {
                children: [
                  {
                    type: 'url',
                    name: '书签同步策略',
                    url: 'https://www.szdamai.com/help/app/bookmark',
                  },
                  {
                    type: 'url',
                    name: '常见问题FAQ',
                    url: 'https://www.szdamai.com/help/faqs',
                  },
                ],
                name: '分身共享书签',
                type: 'folder',
              },
              {
                children: [],
                name: '团队全局书签',
                type: 'folder',
              },
            ]
          : [],
        id: '1',
        name: '书签栏',
        type: 'folder',
      },
      other: {
        children: [],
        id: '2',
        name: '其他书签',
        type: 'folder',
      },
      synced: {
        children: [],
        id: '3',
        name: '移动设备书签',
        type: 'folder',
      },
    },
    version: 1,
  };
}

/**
 * 文件位置Bookmarks
 */

/**
 * 浏览器书签同步
 */
export default class BookmarksSync {
  props: Props;
  shopDataDir: string;
  filePath: string;
  bakFilePath: string;
  errorOccur: boolean;
  rows: any[];
  enabled: boolean;
  originJson: any;
  constructor(p: Props) {
    this.props = p;
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    this.shopDataDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${this.props.shopId}`,
      this.props.tempShopDataSubDir,
    );
    this.errorOccur = false;
    this.filePath = path.join(this.shopDataDir, 'Default', 'Bookmarks');
    this.bakFilePath = path.join(this.shopDataDir, 'Default', 'Bookmarks.bak');
    this.rows = [];
    this.enabled = !!(
      this.props.shopPolicy.userBookmarks ||
      this.props.shopPolicy.shopBookmarks ||
      this.props.shopPolicy.teamBookmarks
    );
    if (process.env.OEM_NAME === 'gg') {
      this.enabled = false;
    }
  }

  async init() {
    await this.writeData();
    return this;
  }

  /**
   * 清理书签
   * @param shopId
   */
  static clear(shopId: number) {
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    const shopDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${shopId}`,
    );
    const filePath = path.join(shopDir, 'Default', 'Bookmarks');
    const bakFilePath = path.join(shopDir, 'Default', 'Bookmarks.bak');
    logger.verbose('[APP] clear bookmarks data table', filePath);
    try {
      // 写文件
      fs.writeJSONSync(filePath, getDataStructure(true));
      fs.removeSync(bakFilePath);
    } catch (e) {
      logger.verbose(e);
    }
  }

  treeShake(nodes: any[]) {
    nodes.forEach((node, idx) => {
      if (node.type === 'folder') {
        nodes[idx] = _.pick(node, ['type', 'name', 'children']);
        if (node.children.length > 0) {
          this.treeShake(node.children);
        }
      } else {
        if (node.type === 'url') {
          nodes[idx] = _.pick(node, ['type', 'name', 'url']);
        }
      }
    });
  }

  async writeData() {
    try {
      const { shopPolicy, shopInfo } = this.props;
      const isCnLang = db.getBrowserLanguage().toLowerCase().startsWith('zh');
      let bookmarks = getDataStructure(!appConfig.isOEM);
      if (!this.enabled) {
        if (fs.existsSync(this.filePath)) {
          bookmarks = await this.getLocalData();
        } else {
          bookmarks = this.transformV104To105(bookmarks);
        }
        this.originJson = bookmarks;
      } else {
        // 获取云端数据（个人、账号），合并数据
        let userBookmarksStr = '';
        let shopBookmarksStr = '';
        let teamBookmarksStr = '';
        if (shopPolicy.userBookmarks) {
          userBookmarksStr = await this.props.requestAgent.request(
            `/api/shop/bookmarks/getUserBookmarks`,
            {
              teamId: this.props.teamId,
            },
          );
        }
        if (shopPolicy.shopBookmarks) {
          shopBookmarksStr = await this.props.requestAgent.request(
            `/api/shop/bookmarks/${this.props.shopId}/bookmarks`,
            {
              teamId: this.props.teamId,
            },
          );
        }
        if (shopPolicy.teamBookmarks) {
          teamBookmarksStr = await this.props.requestAgent.request(
            `/api/shop/bookmarks/getTeamBookmarks`,
            {
              teamId: this.props.teamId,
            },
          );
        }
        let localBookmarks = null;
        // 所有书签未开启同步，使用本地数据
        if (
          [shopPolicy.userBookmarks, shopPolicy.shopBookmarks, shopPolicy.teamBookmarks].includes(
            false,
          )
        ) {
          if (fs.existsSync(this.filePath)) {
            localBookmarks = await this.getLocalData();
          }
        }
        if (userBookmarksStr) {
          const json = JSON.parse(userBookmarksStr);
          if (_.isPlainObject(json)) {
            bookmarks = JSON.parse(userBookmarksStr);
          }
        } else if (localBookmarks) {
          const { userBookmarks, teamBookmarks, shopBookmarks } =
            this.transformV105To104(localBookmarks);
          userBookmarks.roots.bookmark_bar.children.unshift({
            children: teamBookmarks as any,
            name: isCnLang ? '团队全局书签' : 'Team Shared',
            type: 'folder',
          });
          userBookmarks.roots.bookmark_bar.children.unshift({
            children: shopBookmarks as any,
            name: isCnLang ? '分身共享书签' : 'Avatar Shared',
            type: 'folder',
          });
          bookmarks = userBookmarks;
        }
        let shopFolder = bookmarks.roots.bookmark_bar.children.find(
          (item) => ['分身共享书签', 'Avatar Shared'].includes(item.name) && item.type === 'folder',
        );
        let teamFolder = bookmarks.roots.bookmark_bar.children.find(
          (item) => ['团队全局书签', 'Team Shared'].includes(item.name) && item.type === 'folder',
        );
        const localShopFolder = localBookmarks?.roots.bookmark_bar.children.find(
          (item: any) =>
            ['分身共享书签', 'Avatar Shared'].includes(item.name) && item.type === 'folder',
        );
        const localTeamFolder = localBookmarks?.roots.bookmark_bar.children.find(
          (item: any) =>
            ['团队全局书签', 'Team Shared'].includes(item.name) && item.type === 'folder',
        );
        // 处理旧数据，补充文件夹
        if (!shopFolder) {
          bookmarks.roots.bookmark_bar.children.unshift({
            children: [],
            name: '分身共享书签',
            type: 'folder',
          });
        }
        if (!teamFolder) {
          bookmarks.roots.bookmark_bar.children.splice(
            bookmarks.roots.bookmark_bar.children.findIndex(
              (item) =>
                ['分身共享书签', 'Avatar Shared'].includes(item.name) && item.type === 'folder',
            ) + 1,
            0,
            {
              children: [],
              name: '团队全局书签',
              type: 'folder',
            },
          );
        }
        shopFolder = bookmarks.roots.bookmark_bar.children.find(
          (item) => ['分身共享书签', 'Avatar Shared'].includes(item.name) && item.type === 'folder',
        );
        teamFolder = bookmarks.roots.bookmark_bar.children.find(
          (item) => ['团队全局书签', 'Team Shared'].includes(item.name) && item.type === 'folder',
        );
        // 处理存在多个分身共享书签的脏数据
        const shopFoldersIdx: number[] = [];
        bookmarks.roots.bookmark_bar.children.forEach((item, idx) => {
          if (['分身共享书签', 'Avatar Shared'].includes(item.name) && item.type === 'folder') {
            shopFoldersIdx.push(idx);
          }
        });
        if (shopFoldersIdx.length > 1) {
          for (let i = 1; i < shopFoldersIdx.length; i++) {
            // @ts-ignore
            bookmarks.roots.bookmark_bar.children[shopFoldersIdx[i]] = null;
          }
          bookmarks.roots.bookmark_bar.children = bookmarks.roots.bookmark_bar.children.filter(
            (item) => !!item,
          );
        }
        // 填充分身共享书签
        if (shopBookmarksStr && shopBookmarksStr !== '[]') {
          let shopBookmarks = [];
          const json = JSON.parse(shopBookmarksStr);
          if (_.isArray(json)) {
            shopBookmarks = _.uniq(json);
          }
          const shopFolder = bookmarks.roots.bookmark_bar.children.find(
            (item) =>
              ['分身共享书签', 'Avatar Shared'].includes(item.name) && item.type === 'folder',
          );
          if (shopFolder) {
            // @ts-ignore
            shopFolder.children = shopBookmarks;
          }
        } else if (shopFolder) {
          if (localShopFolder) {
            shopFolder.children = localShopFolder.children;
          } else if (userBookmarksStr) {
            shopFolder.children = [
              {
                type: 'url',
                name: '书签同步策略',
                url: 'https://www.szdamai.com/help/app/bookmark',
              },
              {
                type: 'url',
                name: '常见问题FAQ',
                url: 'https://www.szdamai.com/help/faqs',
              },
            ] as any;
          }
        }
        // 填充团队全局书签
        if (teamBookmarksStr && teamBookmarksStr !== '[]') {
          let teamBookmarks = [];
          const json = JSON.parse(teamBookmarksStr);
          if (_.isArray(json)) {
            teamBookmarks = json;
          }
          const teamFolder = bookmarks.roots.bookmark_bar.children.find(
            (item) => ['团队全局书签', 'Team Shared'].includes(item.name) && item.type === 'folder',
          );
          if (teamFolder) {
            // @ts-ignore
            teamFolder.children = teamBookmarks;
          }
        } else if (teamFolder && localTeamFolder) {
          teamFolder.children = localTeamFolder.children;
        }
        bookmarks.roots.bookmark_bar.children.forEach((item) => {
          if (['分身共享书签', 'Avatar Shared'].includes(item.name)) {
            item.name = isCnLang ? '分身共享书签' : 'Avatar Shared';
          } else if (['团队全局书签', 'Team Shared'].includes(item.name)) {
            item.name = isCnLang ? '团队全局书签' : 'Team Shared';
          }
        });
        this.originJson = this.transformV104To105(bookmarks);
      }
      // 写文件
      fs.ensureFileSync(this.filePath);
      fs.writeFileSync(this.filePath, JSON.stringify(this.originJson, null, 2));
      fs.removeSync(this.bakFilePath);
    } catch (e) {
      logger.error('[APP] init bookmark failed', e);
      this.errorOccur = true;
    } finally {
      logger.info(`[BROWSER] BookmarksSync initialized (shopId: ${this.props?.shopId})`);
    }
  }

  hasException() {
    return this.errorOccur;
  }

  /**
   * 获取本地已存储的数据
   */
  async getLocalData() {
    const exists = await fs.pathExists(this.filePath);
    if (!exists) {
      this.errorOccur = true;
      return;
    }
    try {
      const json = await fs.readJson(this.filePath);
      return json;
    } catch (e) {
      logger.reportError(e, { noNotify: true });
      logger.error('[APP] get bookmarks data fail');
      this.errorOccur = true;
    }
  }

  _fillArr(arr: any[], source: Record<string, any> = {}) {
    arr.push({ ..._.pick(source, ['type', 'name', 'url']) });
    if (_.isArray(source.children) && source.children.length > 0) {
      source.children.forEach((item) => {
        this._fillArr(arr, item);
      });
    }
  }

  _isChanged(obj1: any, obj2: any) {
    const arr1: any[] = [];
    const arr2: any[] = [];
    this._fillArr(arr1, obj1.roots?.bookmark_bar);
    this._fillArr(arr1, obj1.roots?.other);
    this._fillArr(arr2, obj2.roots?.bookmark_bar);
    this._fillArr(arr2, obj2.roots?.other);
    return JSON.stringify(arr1) !== JSON.stringify(arr2);
  }

  transformV104To105(_json: any) {
    // 转换为v10.5结构
    const isCnLang = db.getBrowserLanguage().toLowerCase().startsWith('zh');
    const bookmarks = _.cloneDeep(_json);
    let barChildren: any[] = _.cloneDeep(bookmarks.roots.bookmark_bar.children);
    let teamFolderChildren = [];
    const teamFolderIdx = barChildren.findIndex(
      (item: any) => ['团队全局书签', 'Team Shared'].includes(item.name) && item.type === 'folder',
    );
    if (teamFolderIdx !== -1) {
      teamFolderChildren = barChildren[teamFolderIdx].children ?? [];
    }
    const shopFolderIdx = barChildren.findIndex(
      (item: any) =>
        ['分身共享书签', 'Avatar Shared'].includes(item.name) && item.type === 'folder',
    );
    let shopFolderChildren = [];
    if (shopFolderIdx !== -1) {
      shopFolderChildren = barChildren[shopFolderIdx].children ?? [];
    }
    const userChildren: any[] = [];
    barChildren.forEach((item, idx) => {
      if (
        ![teamFolderIdx, shopFolderIdx].includes(idx) &&
        ![
          'https://www.szdamai.com/help/app/bookmark',
          'https://www.szdamai.com/help/faqs',
        ].includes(item.url ?? '')
      ) {
        userChildren.push({
          ...item,
        });
        // @ts-ignore
        barChildren[idx] = null;
      }
    });
    barChildren = barChildren.filter((item) => !!item);
    const results = [];
    if (bookmarks.roots.other?.children?.length > 0) {
      userChildren.push(...bookmarks.roots.other.children);
    }
    // 根据顺序排列书签
    for (let type of (this.props.shopInfo.bookmarkBar ?? 'team,user,shop').split(',')) {
      if (type === 'team') {
        if (teamFolderChildren.length > 0 || this.props.shopPolicy.teamBookmarks) {
          results.push({
            type: 'folder',
            name: isCnLang ? '团队全局书签' : 'Team Shared',
            children: teamFolderChildren,
          });
        }
      } else if (type === 'user') {
        if (userChildren.length > 0 || this.props.shopPolicy.userBookmarks) {
          results.push({
            type: 'folder',
            name: isCnLang ? '个人私有书签' : 'Private',
            children: userChildren,
          });
        }
      } else if (type === 'shop') {
        results.push(...shopFolderChildren);
      }
    }
    // if (shopFolderIdx !== -1) {
    //   barChildren.push(...shopFolderChildren);
    //   barChildren.splice(shopFolderIdx, 1);
    // }
    // barChildren.splice(teamFolderIdx === -1 ? 0 : teamFolderIdx, 0, {
    //   // @ts-ignore
    //   children: userChildren,
    //   name: isCnLang ? '个人私有书签' : 'Private',
    //   type: 'folder',
    // });
    this.treeShake(results);
    const newStructure = getDataStructure();
    newStructure.roots.bookmark_bar.children = results;
    // console.log('newStructure', newStructure);
    newStructure.roots.bookmark_bar.children = newStructure.roots.bookmark_bar.children.filter(
      (item: any) => !(item.type === 'url' && item.url.startsWith('http://szdamai.local/')),
    );
    if (this.props.shopInfo.nameBookmarkEnabled) {
      // 填充检测页
      newStructure.roots.bookmark_bar.children.unshift({
        type: 'url',
        name: this.props.shopInfo.name ?? i18n.t('浏览器检测页'),
        // @ts-ignore
        url: 'http://szdamai.local/',
      });
    }
    return newStructure;
  }

  transformV105To104(_json: any) {
    const json = _.cloneDeep(_json);
    let userBookmarks = getDataStructure();
    let shopBookmarks: any[] = [];
    let teamBookmarks: any[] = [];
    let barChildren: any[] = json.roots?.bookmark_bar?.children ?? [];
    barChildren = barChildren.filter(
      (item: any) => !(item.type === 'url' && item.url.startsWith('http://szdamai.local/')),
    );
    // 还原为 v10.5 以前的结构
    const teamFolderIdx = barChildren.findIndex(
      (item) => ['团队全局书签', 'Team Shared'].includes(item.name) && item.type === 'folder',
    );
    const userFolderIdx = barChildren.findIndex(
      (item) => ['个人私有书签', 'Private'].includes(item.name) && item.type === 'folder',
    );
    barChildren.forEach((item, idx) => {
      if (![teamFolderIdx, userFolderIdx].includes(idx)) {
        shopBookmarks.push({
          ...item,
        });
        // @ts-ignore
        barChildren[idx] = null;
      }
    });
    // barChildren = barChildren.filter((item) => !!item);
    if (json.roots.other?.children?.length > 0) {
      shopBookmarks.push(...json.roots.other.children);
    }
    if (teamFolderIdx !== -1) {
      teamBookmarks = _.cloneDeep(barChildren[teamFolderIdx].children);
    }
    if (userFolderIdx !== -1) {
      userBookmarks.roots.bookmark_bar.children = _.cloneDeep(barChildren[userFolderIdx].children);
    }
    this.treeShake(userBookmarks.roots.bookmark_bar.children);
    this.treeShake(shopBookmarks);
    this.treeShake(teamBookmarks);
    return {
      userBookmarks,
      shopBookmarks,
      teamBookmarks,
    };
  }

  async upload() {
    if (!this.enabled) return;
    const json = await this.getLocalData();
    if (this.errorOccur) return;
    // 比较收藏夹是否有变化
    if (!this._isChanged(this.originJson, json)) {
      return;
    }
    const { userBookmarks, teamBookmarks, shopBookmarks } = this.transformV105To104(json);
    if (this.props.shopPolicy.userBookmarks) {
      await this.props.requestAgent.request(`/api/shop/bookmarks/overrideUserBookmarks`, {
        method: 'POST',
        teamId: this.props.teamId,
        data: userBookmarks,
      });
    }
    if (this.props.shopPolicy.shopBookmarks) {
      await this.props.requestAgent.request(
        `/api/shop/bookmarks/${this.props.shopId}/overrideShopBookmarks`,
        {
          method: 'POST',
          teamId: this.props.teamId,
          data: shopBookmarks,
        },
      );
    }
    if (this.props.shopPolicy.teamBookmarks) {
      await this.props.requestAgent.request(`/api/shop/bookmarks/overrideTeamBookmarks`, {
        method: 'POST',
        teamId: this.props.teamId,
        data: teamBookmarks,
      });
    }
  }
}
