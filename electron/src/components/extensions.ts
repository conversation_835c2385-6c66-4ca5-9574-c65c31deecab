import fs from 'fs-extra';
import path from 'path';
import extract from 'extract-zip';
import Downloader from 'nodejs-file-downloader';
import { getDynamicPath } from '@e/configs/app';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import _ from 'lodash';
import { calcExtensionId } from '@e/utils/utils';
import LevelDbDatabase from '@e/components/levelDbDatabase';

const extensionStateMap: Record<number, 0 | 1 | -1> = {};

interface Props {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  shopPolicy: API.ShopSyncPolicyVo;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
}

/**
 * 浏览器扩展程序
 */
export default class Extensions extends LevelDbDatabase {
  extensions: API.ExtensionsVo[];
  needSyncExtIds: number[];
  result: string[];
  firstInstallExtensionIds: string[];
  private enabled: any;
  keepLocalFile = true;
  private secureFilePath: string;
  constructor(p: Props) {
    super({ ...p, folder: 'HuaYoung Extension Settings' });
    this.extensions = [];
    this.needSyncExtIds = [];
    this.result = [];
    this.firstInstallExtensionIds = [];
    this.enabled = this.getEnabled();
    this.secureFilePath = path.join(this.shopDataDir, 'Default', 'Secure Preferences');
    if (this.props.shopInfo.stateless && !this.props.shopInfo.statelessSyncPolicyVo?.extensions) {
      this.keepLocalFile = false;
    }
  }

  getEnabled() {
    if (this.props.shopInfo.stateless) {
      return !!this.props.shopInfo.statelessSyncPolicyVo?.extensions;
    }
    return true;
  }

  async init() {
    const { teamId, shopId, requestAgent } = this.props;
    try {
      const shopExtensions = await requestAgent.request(`/api/shop/${shopId}/extensions`, {
        teamId,
      });
      const teamExtensions = await requestAgent.request(`/api/shop/teamExtensions`, { teamId });
      this.needSyncExtIds =
        (await requestAgent.request(`/api/extensions/syncExtensionIds`, {
          teamId,
        })) || [];
      this.extensions = _.uniqBy([...shopExtensions, ...teamExtensions], 'id');
      await this.sync();
    } catch (e) {
      logger.error('[APP] browser extensions init failed', e);
    }
    return this;
  }

  /**
   * 同步插件
   */
  async sync() {
    const { EXTENSIONS_DIR } = getDynamicPath();
    await fs.ensureDir(EXTENSIONS_DIR);
    for (let i = 0; i < this.extensions.length; i++) {
      const extension = this.extensions[i];
      const extensionDir = path.join(EXTENSIONS_DIR, String(extension.id));
      if (fs.existsSync(extensionDir)) {
        // 本地以存在该插件
        const manifest = path.join(extensionDir, 'manifest.json');
        if (fs.existsSync(manifest)) {
          const version = fs.readJSONSync(manifest).version;
          // 版本不一致
          if (extension.version?.replace(/^v/, '') !== version) {
            await this.download(extension);
          } else {
            this.result.push(extensionDir);
          }
        } else {
          // 没有 manifest.json 文件
          await this.download(extension);
        }
      } else {
        await this.download(extension);
      }
    }
    this.getFirstInstallExtensionIds(this.result);
    if (this.enabled && this.result.length > 0) {
      // 开启插件设置同步
      await this.downloadExtSettings();
    }
    return {
      paths: this.result,
      firstInstallExtensionIds: this.firstInstallExtensionIds,
    };
  }

  getFirstInstallExtensionIds(paths: string[]) {
    this.firstInstallExtensionIds = [];
    let extensionSettings: Record<string, any> = {};
    if (fs.existsSync(this.secureFilePath)) {
      try {
        const secureJson = fs.readJsonSync(this.secureFilePath);
        extensionSettings = secureJson.extensions.settings || {};
      } catch (e) {
        // ignore
        // logger.error(e);
      }
    }
    for (let i = 0; i < paths.length; i++) {
      const extId = calcExtensionId(paths[i]);
      if (!extensionSettings[extId]) {
        this.firstInstallExtensionIds.push(extId);
      }
    }
  }

  async downloadExtSettings() {
    await super.init();
    if (fs.existsSync(this.getFolderPath())) {
      const { EXTENSIONS_DIR } = getDynamicPath();
      const folders = await fs.readdir(this.getFolderPath());
      for (let i = 0; i < folders.length; i++) {
        const folder = folders[i];
        if (!this.needSyncExtIds.includes(Number(folder))) {
          continue;
        }
        const extId = calcExtensionId(path.join(EXTENSIONS_DIR, folder));
        const realExtDir = path.join(
          this.shopDataDir,
          'Default',
          'Local Extension Settings',
          extId,
        );
        if (fs.existsSync(realExtDir)) {
          await fs.remove(realExtDir);
        }
        await fs.copy(path.join(this.getFolderPath(), folder), realExtDir);
      }
    }
  }

  async tryClearLocalFile() {
    if (!this.keepLocalFile) {
      await super.removeLocalData();
    }
  }

  async uploadExtSettings() {
    if (!this.enabled) return;
    await fs.ensureDir(this.getFolderPath());
    await fs.emptyDir(this.getFolderPath());
    for (let i = 0; i < this.result.length; i++) {
      const extDir = this.result[i];
      const extSqlId = Number(path.basename(extDir));
      if (this.needSyncExtIds.includes(extSqlId)) {
        const extId = calcExtensionId(extDir);
        const realExtDir = path.join(
          this.shopDataDir,
          'Default',
          'Local Extension Settings',
          extId,
        );
        if (fs.existsSync(realExtDir)) {
          await fs.copy(realExtDir, path.join(this.getFolderPath(), path.basename(extDir)));
        }
      }
    }
    const files = await fs.readdir(this.getFolderPath());
    if (files.length > 0) {
      await this.upload();
    }
  }

  /**
   * 下载插件并解压
   * @param extension
   */
  async download(extension: API.ExtensionsVo) {
    const { id, crx } = extension;
    if (!id) return;
    const { EXTENSIONS_DIR } = getDynamicPath();
    const targetPath = path.join(EXTENSIONS_DIR, String(id));
    const _targetPath = path.join(EXTENSIONS_DIR, `${id}_pending`);
    if (extensionStateMap[id] === 0) {
      // 正在下载
      await new Promise((resolve) => {
        // 定时检测插件是否就绪
        const timer = setInterval(() => {
          if (extensionStateMap[id] !== 0) {
            clearInterval(timer);
            if (extensionStateMap[id] === 1) {
              // 成功了
              this.result.push(targetPath);
            }
            resolve(true);
          }
        }, 2 * 1000);
      });
    } else {
      extensionStateMap[id] = 0;
      logger.verbose(`[APP] start download extension ${extension.title} (${extension.id})`);
      const zipFileName = `${id}.zip`;
      const zipFilePath = path.join(EXTENSIONS_DIR, zipFileName);
      try {
        await new Downloader({
          url: crx!,
          directory: EXTENSIONS_DIR,
          fileName: zipFileName,
          cloneFiles: false,
          maxAttempts: 3,
        }).download();
        fs.emptydirSync(_targetPath);
        await extract(zipFilePath, {
          dir: _targetPath,
        });
        await new Promise((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 3 * 1000);
        });
        if (await fs.pathExists(targetPath)) {
          await fs.remove(targetPath);
        }
        await fs.rename(_targetPath, targetPath);
        extensionStateMap[id] = 1;
        this.result.push(targetPath);
      } catch (e) {
        logger.error(`[APP] download extension failed (id: ${id}, url: ${crx})`, e);
        extensionStateMap[id] = -1;
      } finally {
        // 删除 zip 包
        fs.remove(zipFilePath);
      }
    }
  }
}
