import LevelDbDatabase from '@e/components/levelDbDatabase';
import { RequestAgent } from '@e/services/request';
import logger from '@e/services/logger';
import fs from 'fs-extra';

interface Props {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  shopPolicy: API.ShopSyncPolicyVo;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
}

export default class IndexedDBSync extends LevelDbDatabase {
  private enabled: any;
  keepLocalFile = true;
  constructor(props: Props) {
    super({
      ...props,
      folder: 'IndexedDB',
    });
    this.enabled = this.getEnabled();
    if (this.props.shopInfo.stateless && !this.props.shopInfo.statelessSyncPolicyVo?.indexDb) {
      this.keepLocalFile = false;
    }
  }

  getEnabled() {
    if (this.props.shopInfo.stateless) {
      return (
        !!this.props.shopInfo.statelessSyncPolicyVo?.indexDb && !!this.props.shopPolicy.indexDb
      );
    }
    return !!this.props.shopPolicy.indexDb;
  }

  async init(): Promise<IndexedDBSync> {
    if (!this.enabled) return this;
    await super.init();
    logger.info(`[BROWSER] IndexedDBSync initialized (shopId: ${this.props?.shopId})`);
    return this;
  }

  async tryClearLocalFile() {
    if (!this.keepLocalFile) {
      await super.removeLocalData();
    }
  }

  async upload(): Promise<void> {
    if (!this.enabled) return;
    await super.upload();
  }
}
