import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';

import appConfig, { getDynamicPath } from '../configs/app';
import logger from '@e/services/logger';

type Props = {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  rpaFlowId?: number;
  tempShopDataSubDir: string;
};

/**
 * 浏览器偏好配置
 */
export default class Preferences {
  props?: Props;
  shopDataDir: string;
  filePath: string;
  secureFilePath: string;
  errorOccur: boolean;
  per_host_zoom_levels_bak: any;
  enabled: boolean;
  constructor(p: Props) {
    this.props = p;
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    this.shopDataDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${this.props.shopId}`,
      this.props.tempShopDataSubDir,
    );
    this.errorOccur = false;
    this.filePath = path.join(this.shopDataDir, 'Default', 'Preferences');
    this.secureFilePath = path.join(this.shopDataDir, 'Default', 'Secure Preferences');
    this.enabled = true;
  }

  async init() {
    await this.writeData();
    return this;
  }

  async writeData() {
    if (!this.enabled) return;
    try {
      let json: any = {
        // 书签栏默认显示
        bookmark_bar: {
          show_on_all_tabs: !appConfig.isOEM,
          show_tab_groups: false,
        },
        download: {
          directory_upgrade: true,
          prompt_for_download: true,
        },
      };
      if (fs.existsSync(this.filePath)) {
        json = await fs.readJson(this.filePath);
      }
      // 运行RPA时，去掉站点缩放记录
      if (!!this.props?.rpaFlowId && json.partition?.per_host_zoom_levels) {
        this.per_host_zoom_levels_bak = json.partition?.per_host_zoom_levels;
        json.partition.per_host_zoom_levels = {};
      }
      // 去掉插件代理设置
      if (json.extensions) {
        try {
          _.forEach(json.extensions.settings, ({ preferences }: any) => {
            if (preferences.proxy) {
              delete preferences.proxy;
            }
          });
        } catch (e) {
          // ignore
          // logger.error(e);
        }
      }
      json.https_only_mode_auto_enabled = false;
      json.https_only_mode_enabled = false;
      if (json.bookmark_bar) {
        json.bookmark_bar.show_tab_groups = false;
      }
      // json.bookmark_bar = { show_on_all_tabs: true };
      // json.download = { directory_upgrade: true, prompt_for_download: true };
      // 写文件
      await fs.writeJSON(this.filePath, json);
      if (fs.existsSync(this.secureFilePath)) {
        try {
          const secureJson = await fs.readJson(this.secureFilePath);
          _.forEach(secureJson.extensions.settings, ({ preferences }: any) => {
            if (preferences.proxy) {
              delete preferences.proxy;
            }
          });
          // if (!secureJson?.default_search_provider_data?.template_url_data) {
          //   secureJson.default_search_provider_data = {
          //     "template_url_data":
          //       {
          //         "alternate_urls":
          //           [],
          //         "contextual_search_url": "",
          //         "created_by_policy": 0,
          //         "created_from_play_api": false,
          //         "date_created": "0",
          //         "doodle_url": "",
          //         "enforced_by_policy": false,
          //         "favicon_url": "https://www.bing.com/sa/simg/bing_p_rr_teal_min.ico",
          //         "featured_by_policy": false,
          //         "id": "3",
          //         "image_search_branding_label": "",
          //         "image_translate_source_language_param_key": "",
          //         "image_translate_target_language_param_key": "",
          //         "image_translate_url": "",
          //         "image_url": "https://www.bing.com/images/detail/search?iss=sbiupload&FORM=CHROMI#enterInsights",
          //         "image_url_post_params": "imageBin={google:imageThumbnailBase64}",
          //         "input_encodings":
          //           [
          //             "UTF-8"
          //           ],
          //         "is_active": 1,
          //         "keyword": "bing.com",
          //         "last_modified": "0",
          //         "last_visited": "13370518667748122",
          //         "logo_url": "",
          //         "new_tab_url": "https://www.bing.com/chrome/newtab",
          //         "originating_url": "",
          //         "preconnect_to_search_url": false,
          //         "prefetch_likely_navigations": false,
          //         "prepopulate_id": 3,
          //         "safe_for_autoreplace": true,
          //         "search_intent_params":
          //           [],
          //         "search_url_post_params": "",
          //         "short_name": "Microsoft Bing",
          //         "side_image_search_param": "",
          //         "side_search_param": "",
          //         "starter_pack_id": 0,
          //         "suggestions_url": "https://www.bing.com/osjson.aspx?query={searchTerms}&language={language}&PC=U316",
          //         "suggestions_url_post_params": "",
          //         "synced_guid": "485bf7d3-0215-45af-87dc-************",
          //         "url": "https://www.bing.com/search?q={searchTerms}&PC=U316&FORM=CHROMN",
          //         "usage_count": 0
          //       }
          //   };
          // }
          await fs.writeJSON(this.secureFilePath, secureJson);
        } catch (e) {
          // ignore
          // logger.error(e);
        }
      }
    } catch (e) {
      this.errorOccur = true;
    } finally {
      logger.info(`[BROWSER] Preferences initialized (shopId: ${this.props?.shopId})`);
    }
  }

  hasException() {
    return this.errorOccur;
  }

  async restore() {
    if (!this.enabled || this.errorOccur) return;
    try {
      if (!fs.existsSync(this.filePath)) return;
      const json = await fs.readJson(this.filePath);
      // 还原站点缩放记录
      if (!!this.props?.rpaFlowId && json.partition?.per_host_zoom_levels) {
        json.partition.per_host_zoom_levels = this.per_host_zoom_levels_bak;
      }
      // 写文件
      await fs.writeJSON(this.filePath, json);
    } catch (e) {
      logger.error(`[BROWSER] restore preferences failed`, e);
    }
  }
}
