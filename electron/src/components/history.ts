import path from 'path';
import fs from 'fs-extra';
import sqlite3, { Database } from 'better-sqlite3';

import appConfig, { getDynamicPath } from '../configs/app';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';

type Props = {
  teamId: number;
  shopId: number;
  shopInfo: API.ShopDetailVo;
  shopPolicy: API.ShopSyncPolicyVo;
  requestAgent: RequestAgent;
  tempShopDataSubDir: string;
};

/**
 * 历史记录同步
 */
export default class HistorySync {
  props: Props;
  shopDataDir: string;
  dbPath: string;
  dbConn?: Database;
  errorOccur: boolean;
  rows: any[];
  enabled: boolean;
  keepLocalFile = true;
  constructor(p: Props) {
    this.props = p;
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    this.shopDataDir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${this.props.shopId}`,
      this.props.tempShopDataSubDir,
    );
    this.errorOccur = false;
    this.dbPath = path.join(this.shopDataDir, 'Default', 'History');
    this.rows = [];
    this.enabled = this.getEnabled();
    if (this.props.shopInfo.stateless && !this.props.shopInfo.statelessSyncPolicyVo?.history) {
      this.keepLocalFile = false;
    }
  }

  getEnabled() {
    if (this.props.shopInfo.stateless) {
      return (
        !!this.props.shopInfo.statelessSyncPolicyVo?.history && !!this.props.shopPolicy.history
      );
    }
    return !!this.props.shopPolicy.history;
  }

  /**
   * 清除数据
   */
  static clear(shopId: number) {
    const { BROWSER_USER_DATA_DIR_PATH } = getDynamicPath();
    const dir = path.resolve(
      BROWSER_USER_DATA_DIR_PATH,
      `${appConfig.BROWSER_USER_DATA_DIR_PREFIX}${shopId}`,
    );
    const filePath = path.join(dir, 'Default', 'History');

    logger.verbose('[APP] clear History table', filePath);
    const db = sqlite3(filePath);
    try {
      const urlsTable = db
        .prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`)
        .get('urls');
      if (urlsTable) {
        db.exec(`DROP TABLE urls`);
      }
      db.exec(
        `CREATE TABLE urls(id INTEGER PRIMARY KEY AUTOINCREMENT,url LONGVARCHAR,title LONGVARCHAR,visit_count INTEGER DEFAULT 0 NOT NULL,typed_count INTEGER DEFAULT 0 NOT NULL,last_visit_time INTEGER NOT NULL,hidden INTEGER DEFAULT 0 NOT NULL)`,
      );
      const visitsTable = db
        .prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`)
        .get('visits');
      if (visitsTable) {
        db.exec(`DROP TABLE visits`);
      }
      db.exec(
        `CREATE TABLE visits(id INTEGER PRIMARY KEY,url INTEGER NOT NULL,visit_time INTEGER NOT NULL,from_visit INTEGER,transition INTEGER DEFAULT 0 NOT NULL,segment_id INTEGER,visit_duration INTEGER DEFAULT 0 NOT NULL,incremented_omnibox_typed_score BOOLEAN DEFAULT FALSE NOT NULL,opener_visit INTEGER)`,
      );
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] clear db table fail', e);
      throw e;
    }
  }
  async init() {
    // 校验是否需要同步
    if (!this.enabled) return this;
    try {
      // 拉取云端数据
      const histories = await this.props.requestAgent.request(
        `/api/shop/${this.props.shopId}/getHistories`,
        {
          teamId: this.props.teamId,
        },
      );
      if (histories) {
        this.rows = histories;
      }
      await this.writeData();
    } catch (e) {
      this.errorOccur = true;
    } finally {
      logger.info(`[BROWSER] HistorySync initialized (shopId: ${this.props?.shopId})`);
    }
    return this;
  }

  async writeData() {
    try {
      if (!fs.existsSync(this.shopDataDir)) {
        fs.mkdirSync(this.shopDataDir, { recursive: true });
      }
      if (!fs.existsSync(this.dbPath)) {
        await this.createDb();
      } else {
        await this.clearTable();
      }
      await this.repairData();
      await this.insertHistories();
    } catch (e) {
      this.errorOccur = true;
    } finally {
      this.closeDb();
    }
  }

  hasException() {
    return this.errorOccur;
  }

  async getDbConn(readonly = false) {
    if (!this.dbConn) {
      let sqlFilePath = this.dbPath;
      if (readonly) {
        const bakPath = `${this.dbPath}.bak`;
        await fs.copyFile(this.dbPath, bakPath);
        const hasBakPath = await fs.pathExists(bakPath);
        if (hasBakPath) {
          sqlFilePath = bakPath;
        }
      }
      this.dbConn = sqlite3(sqlFilePath, { readonly });
    }
    return this.dbConn;
  }

  closeDb() {
    if (this.dbConn) {
      this.dbConn.close();
      this.dbConn = undefined;
    }
  }

  /**
   * 创建数据库
   */
  async createDb() {
    logger.verbose('[APP] create history database', this.dbPath);
    fs.ensureFileSync(this.dbPath);
    const db = await this.getDbConn();
    try {
      const tableSqls = [
        `DROP TABLE IF EXISTS cluster_keywords`,
        `DROP TABLE IF EXISTS cluster_visit_duplicates`,
        `DROP TABLE IF EXISTS clusters`,
        `DROP TABLE IF EXISTS clusters_and_visits`,
        `DROP TABLE IF EXISTS content_annotations`,
        `DROP TABLE IF EXISTS context_annotations`,
        `DROP TABLE IF EXISTS downloads`,
        `DROP TABLE IF EXISTS downloads_slices`,
        `DROP TABLE IF EXISTS downloads_url_chains`,
        `DROP TABLE IF EXISTS history_sync_metadata`,
        `DROP TABLE IF EXISTS keyword_search_terms`,
        `DROP TABLE IF EXISTS meta`,
        `DROP TABLE IF EXISTS segment_usage`,
        `DROP TABLE IF EXISTS segments`,
        `DROP TABLE IF EXISTS urls`,
        `DROP TABLE IF EXISTS visit_source`,
        `DROP TABLE IF EXISTS visited_links`,
        `DROP TABLE IF EXISTS cluster_keywords`,
        `DROP TABLE IF EXISTS visits`,
        `CREATE TABLE cluster_keywords(cluster_id INTEGER NOT NULL,keyword VARCHAR NOT NULL,type INTEGER NOT NULL,score NUMERIC NOT NULL,collections VARCHAR NOT NULL)`,
        `CREATE TABLE cluster_visit_duplicates(visit_id INTEGER NOT NULL,duplicate_visit_id INTEGER NOT NULL,PRIMARY KEY(visit_id,duplicate_visit_id))WITHOUT ROWID`,
        `CREATE TABLE clusters(cluster_id INTEGER PRIMARY KEY AUTOINCREMENT,should_show_on_prominent_ui_surfaces BOOLEAN NOT NULL,label VARCHAR NOT NULL,raw_label VARCHAR NOT NULL,triggerability_calculated BOOLEAN NOT NULL,originator_cache_guid TEXT DEFAULT "" NOT NULL,originator_cluster_id INTEGER DEFAULT 0 NOT NULL)`,
        `CREATE TABLE clusters_and_visits(cluster_id INTEGER NOT NULL,visit_id INTEGER NOT NULL,score NUMERIC NOT NULL,engagement_score NUMERIC NOT NULL,url_for_deduping LONGVARCHAR NOT NULL,normalized_url LONGVARCHAR NOT NULL,url_for_display LONGVARCHAR NOT NULL, interaction_state INTEGER DEFAULT 0 NOT NULL,PRIMARY KEY(cluster_id,visit_id))WITHOUT ROWID`,
        `CREATE TABLE content_annotations(visit_id INTEGER PRIMARY KEY,visibility_score NUMERIC,floc_protected_score NUMERIC,categories VARCHAR,page_topics_model_version INTEGER,annotation_flags INTEGER NOT NULL,entities VARCHAR,related_searches VARCHAR,search_normalized_url VARCHAR,search_terms LONGVARCHAR,alternative_title VARCHAR,page_language VARCHAR,password_state INTEGER DEFAULT 0 NOT NULL, has_url_keyed_image BOOLEAN DEFAULT false NOT NULL)`,
        `CREATE TABLE context_annotations(visit_id INTEGER PRIMARY KEY,context_annotation_flags INTEGER NOT NULL,duration_since_last_visit INTEGER,page_end_reason INTEGER,total_foreground_duration INTEGER,browser_type INTEGER DEFAULT 0 NOT NULL,window_id INTEGER DEFAULT -1 NOT NULL,tab_id INTEGER DEFAULT -1 NOT NULL,task_id INTEGER DEFAULT -1 NOT NULL,root_task_id INTEGER DEFAULT -1 NOT NULL,parent_task_id INTEGER DEFAULT -1 NOT NULL,response_code INTEGER DEFAULT 0 NOT NULL)`,
        `CREATE TABLE downloads (id INTEGER PRIMARY KEY,guid VARCHAR NOT NULL,current_path LONGVARCHAR NOT NULL,target_path LONGVARCHAR NOT NULL,start_time INTEGER NOT NULL,received_bytes INTEGER NOT NULL,total_bytes INTEGER NOT NULL,state INTEGER NOT NULL,danger_type INTEGER NOT NULL,interrupt_reason INTEGER NOT NULL,hash BLOB NOT NULL,end_time INTEGER NOT NULL,opened INTEGER NOT NULL,last_access_time INTEGER NOT NULL,transient INTEGER NOT NULL,referrer VARCHAR NOT NULL,site_url VARCHAR NOT NULL,embedder_download_data VARCHAR NOT NULL,tab_url VARCHAR NOT NULL,tab_referrer_url VARCHAR NOT NULL,http_method VARCHAR NOT NULL,by_ext_id VARCHAR NOT NULL,by_ext_name VARCHAR NOT NULL,etag VARCHAR NOT NULL,last_modified VARCHAR NOT NULL,mime_type VARCHAR(255) NOT NULL,original_mime_type VARCHAR(255) NOT NULL, by_web_app_id VARCHAR NOT NULL DEFAULT '')`,
        `CREATE TABLE downloads_slices (download_id INTEGER NOT NULL,offset INTEGER NOT NULL,received_bytes INTEGER NOT NULL,finished INTEGER NOT NULL DEFAULT 0,PRIMARY KEY (download_id, offset) )`,
        `CREATE TABLE downloads_url_chains (id INTEGER NOT NULL,chain_index INTEGER NOT NULL,url LONGVARCHAR NOT NULL, PRIMARY KEY (id, chain_index) )`,
        `CREATE TABLE history_sync_metadata (storage_key INTEGER PRIMARY KEY NOT NULL, value BLOB)`,
        `CREATE TABLE keyword_search_terms (keyword_id INTEGER NOT NULL,url_id INTEGER NOT NULL,term LONGVARCHAR NOT NULL,normalized_term LONGVARCHAR NOT NULL)`,
        `CREATE TABLE meta(key LONGVARCHAR NOT NULL UNIQUE PRIMARY KEY, value LONGVARCHAR)`,
        `CREATE TABLE segment_usage (id INTEGER PRIMARY KEY,segment_id INTEGER NOT NULL,time_slot INTEGER NOT NULL,visit_count INTEGER DEFAULT 0 NOT NULL)`,
        `CREATE TABLE segments (id INTEGER PRIMARY KEY,name VARCHAR,url_id INTEGER NON NULL)`,
        `CREATE TABLE urls(id INTEGER PRIMARY KEY AUTOINCREMENT,url LONGVARCHAR,title LONGVARCHAR,visit_count INTEGER DEFAULT 0 NOT NULL,typed_count INTEGER DEFAULT 0 NOT NULL,last_visit_time INTEGER NOT NULL,hidden INTEGER DEFAULT 0 NOT NULL)`,
        `CREATE TABLE visit_source(id INTEGER PRIMARY KEY,source INTEGER NOT NULL)`,
        `CREATE TABLE visited_links(id INTEGER PRIMARY KEY AUTOINCREMENT,link_url_id INTEGER NOT NULL,top_level_url LONGVARCHAR NOT NULL,frame_url LONGVARCHAR NOT NULL,visit_count INTEGER DEFAULT 0 NOT NULL)`,
        `CREATE TABLE visits(id INTEGER PRIMARY KEY AUTOINCREMENT,url INTEGER NOT NULL,visit_time INTEGER NOT NULL,from_visit INTEGER,transition INTEGER DEFAULT 0 NOT NULL,segment_id INTEGER,visit_duration INTEGER DEFAULT 0 NOT NULL,incremented_omnibox_typed_score BOOLEAN DEFAULT FALSE NOT NULL,opener_visit INTEGER,originator_cache_guid TEXT,originator_visit_id INTEGER,originator_from_visit INTEGER,originator_opener_visit INTEGER,is_known_to_sync BOOLEAN DEFAULT FALSE NOT NULL, consider_for_ntp_most_visited BOOLEAN DEFAULT FALSE NOT NULL, external_referrer_url TEXT, visited_link_id INTEGER)`,
        `CREATE INDEX cluster_keywords_cluster_id_index ON cluster_keywords(cluster_id)`,
        `CREATE INDEX clusters_for_visit ON clusters_and_visits(visit_id)`,
        `CREATE INDEX keyword_search_terms_index1 ON keyword_search_terms (keyword_id, normalized_term)`,
        `CREATE INDEX keyword_search_terms_index2 ON keyword_search_terms (url_id)`,
        `CREATE INDEX keyword_search_terms_index3 ON keyword_search_terms (term)`,
        `CREATE INDEX segment_usage_time_slot_segment_id ON segment_usage(time_slot, segment_id)`,
        `CREATE INDEX segments_name ON segments(name)`,
        `CREATE INDEX segments_url_id ON segments(url_id)`,
        `CREATE INDEX segments_usage_seg_id ON segment_usage(segment_id)`,
        `CREATE INDEX urls_url_index ON urls (url)`,
        `CREATE INDEX visited_links_index ON visited_links (link_url_id, top_level_url, frame_url)`,
        `CREATE INDEX visits_from_index ON visits (from_visit)`,
        `CREATE INDEX visits_originator_id_index ON visits (originator_visit_id)`,
        `CREATE INDEX visits_time_index ON visits (visit_time)`,
        `CREATE INDEX visits_url_index ON visits (url)`,
      ];
      tableSqls.forEach((sql) => {
        db.exec(sql);
      });
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] Init history db fail', e);
      throw e;
    }
  }

  /**
   * 清除表中已有的数据
   */
  async clearTable() {
    logger.verbose('[APP] clear History table', this.dbPath);
    const db = await this.getDbConn();
    try {
      db.exec(`DROP TABLE IF EXISTS urls`);
      db.exec(`DROP TABLE IF EXISTS visits`);
      db.exec(
        `CREATE TABLE urls(id INTEGER PRIMARY KEY AUTOINCREMENT,url LONGVARCHAR,title LONGVARCHAR,visit_count INTEGER DEFAULT 0 NOT NULL,typed_count INTEGER DEFAULT 0 NOT NULL,last_visit_time INTEGER NOT NULL,hidden INTEGER DEFAULT 0 NOT NULL)`,
      );
      db.exec(
        `CREATE TABLE visits(id INTEGER PRIMARY KEY AUTOINCREMENT,url INTEGER NOT NULL,visit_time INTEGER NOT NULL,from_visit INTEGER,transition INTEGER DEFAULT 0 NOT NULL,segment_id INTEGER,visit_duration INTEGER DEFAULT 0 NOT NULL,incremented_omnibox_typed_score BOOLEAN DEFAULT FALSE NOT NULL,opener_visit INTEGER,originator_cache_guid TEXT,originator_visit_id INTEGER,originator_from_visit INTEGER,originator_opener_visit INTEGER,is_known_to_sync BOOLEAN DEFAULT FALSE NOT NULL, consider_for_ntp_most_visited BOOLEAN DEFAULT FALSE NOT NULL, external_referrer_url TEXT, visited_link_id INTEGER)`,
      );
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] clear db table fail', e);
      throw e;
    }
  }

  async repairData() {
    const db = await this.getDbConn();
    try {
      const metaTable = db
        .prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`)
        .get('meta');
      if (!metaTable) {
        db.exec(
          `CREATE TABLE meta(key LONGVARCHAR NOT NULL UNIQUE PRIMARY KEY, value LONGVARCHAR)`,
        );
      }
      const v1 = db.prepare(`SELECT * FROM meta WHERE key=?`).get('last_compatible_version');
      if (!v1) {
        db.exec(`INSERT INTO "meta" ("key", "value") VALUES ('last_compatible_version', '16')`);
      }
      const v2 = db.prepare(`SELECT * FROM meta WHERE key=?`).get('version');
      if (!v2) {
        db.exec(`INSERT INTO "meta" ("key", "value") VALUES ('version', '68')`);
      } else {
        db.exec(`UPDATE meta SET value='68' WHERE key='version'`);
      }
    } catch (e) {
      logger.error('[APP] repair db table fail', e);
    }
  }

  /**
   * 插入记录
   */
  async insertHistories() {
    const db = await this.getDbConn();
    const len = this.rows.length;
    logger.verbose(`[APP] insert ${len} rows to history table`);
    try {
      const startTime = new Date().getTime();
      const stmt = db.prepare(
        `INSERT INTO urls (url, title, visit_count, typed_count, last_visit_time, hidden) VALUES (?, ?, ?, ?, ?, ?)`,
      );
      const stmt2 = db.prepare(
        `INSERT INTO visits (url, visit_time, from_visit, transition, segment_id, visit_duration, incremented_omnibox_typed_score, opener_visit, originator_cache_guid, originator_visit_id, originator_from_visit, originator_opener_visit, is_known_to_sync, consider_for_ntp_most_visited, external_referrer_url, visited_link_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      );
      const insertMany = db.transaction((rows) => {
        for (let i = 0; i < len; i++) {
          const rowVo = rows[i];
          const lastVisitTime = !rowVo.lastVisitTime
            ? 0
            : (rowVo.lastVisitTime + 11644473600) * 1000000;
          stmt.run(rowVo.url, rowVo.title, rowVo.visitCount, rowVo.typedCount, lastVisitTime, 0);
          stmt2.run(i + 1, lastVisitTime, 0, 939524096, 0, 8892356, 1, 0, '', 0, 0, 0, 0, 1, '', 0);
        }
      });
      insertMany.deferred(this.rows);
      logger.verbose(`[APP] insert ${len} rows cost: ${new Date().getTime() - startTime}ms`);
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] insert history data fail', e);
      throw e;
    }
  }

  /**
   * 获取本地已存储的数据
   */
  async getLocalData() {
    try {
      const db = await this.getDbConn(true);
      const checkTableStmt = db.prepare<any[]>(
        "SELECT count(*) FROM sqlite_master WHERE type='table' AND name=?",
      );
      if ((checkTableStmt.get('urls') as any['count(*)']) === 0) {
        this.errorOccur = true;
        return;
      }
      const stmt = db.prepare(
        'SELECT url, title, visit_count, typed_count, last_visit_time FROM urls ORDER BY last_visit_time DESC LIMIT 500',
      );
      const result = [];
      for (const row of stmt.iterate() as any) {
        // 过期时间换算参考 https://stackoverflow.com/questions/43518199/cookies-expiration-time-format
        const lastVisitTime = Number(
          Math.max(0, row.last_visit_time / 1000000 - 11644473600).toFixed(0),
        );
        result.push({
          url: row.url,
          title: row.title,
          visitCount: row.visit_count,
          typedCount: row.typed_count,
          lastVisitTime: lastVisitTime,
        });
      }
      return result;
    } catch (e) {
      // logger.reportError(e, { noNotify: true });
      logger.error('[APP] get history fail');
      this.errorOccur = true;
    }
  }

  async tryClearLocalFile() {
    if (!this.keepLocalFile) {
      if (fs.existsSync(this.dbPath)) {
        await fs.rm(this.dbPath).catch((e) => {
          logger.error('[APP] remove History file failed', e);
        });
      }
    }
  }

  async upload() {
    if (!this.enabled) return;
    try {
      const historyItems = await this.getLocalData();
      if (this.errorOccur) return;
      // console.log('history data', historyItems);
      await this.props.requestAgent.request(`/api/shop/${this.props.shopId}/uploadHistories`, {
        method: 'POST',
        teamId: this.props.teamId,
        data: historyItems,
      });
    } finally {
      this.closeDb();
    }
  }
}
