import path from 'path';
import fs from 'fs-extra';
import crypto from 'crypto';
import os from 'os';
import low, { LowdbSync } from 'lowdb';
import FileSync from 'lowdb/adapters/FileSync';
import { app } from 'electron';
import { machineIdSync } from 'node-machine-id';
import { DbScheme } from '../types';
import random from '../utils/random';
import logger from '@e/services/logger';
import { HuaYoungDb } from '@e/components/sqlite/database';

let db: LowdbSync<DbScheme>;
let activePortalUrl = '';
let activeApiUrl = '';
let activeDlUrl = '';
let activeEndpointIdx = 1;
let hyDb: HuaYoungDb | undefined;

export function getHyDb() {
  if (!hyDb) {
    try {
      hyDb = new HuaYoungDb(path.join(getDataSavePath('appData'), app.getName(), 'hy_db.data'));
    } catch (e) {
      logger.error('[APP] init sqlite db error', e);
    }
  }
  return hyDb;
}

export function getRpaExecutorPath() {
  let rpaExecutorPath = '';
  if (os.platform() === 'win32') {
    rpaExecutorPath = process.env.PROGRAMFILES
      ? path.resolve(process.env.PROGRAMFILES, 'RpaExecutor', 'conf')
      : '';
  } else if (os.platform() === 'darwin') {
    rpaExecutorPath = path.join(getDataSavePath('appData'), app.getName(), 'RpaExecutor', 'conf');
  }
  return rpaExecutorPath;
}
const endpointsPath = path.join(getRpaExecutorPath(), 'endpoints');

export function getDataSavePath(name: any) {
  let targetPath = '';
  try {
    targetPath = app.getPath(name);
  } catch (e) {
    targetPath = path.resolve(app.getPath('home'), 'HouYoungData', name);
  }
  return targetPath;
}

function getRunModeFromUUID(uuid: string) {
  const regRes = /@(\w+)$/.exec(uuid);
  if (regRes) {
    return regRes[1];
  }
  return 'normal';
}

/**
 * 本地数据库
 */
function initDbStorage() {
  if (db) {
    return db;
  }
  let db_json = process.env.DB_JSON_FILE || 'db.json';
  const dbFile = path.join(getDataSavePath('appData'), app.getName(), db_json);
  if (fs.existsSync(dbFile)) {
    try {
      fs.readJSONSync(dbFile);
    } catch (e) {
      // 某些杀毒软件会篡改 db.json 为普通2进制文件，如果解析 json 失败则重置
      try {
        fs.rmSync(dbFile);
      } catch (e) {}
    }
  }
  fs.ensureFileSync(dbFile);
  const adapter = new FileSync<DbScheme>(dbFile);
  db = low(adapter);
  let clientUUID = random.nextString(12);
  try {
    clientUUID =
      crypto.createHash('md5').update(machineIdSync()).digest('hex') + random.nextString(6);
  } catch (e) {
    console.error('[APP] get machine id error');
  }
  if (process.env.RUN_MODE && process.env.RUN_MODE !== 'normal') {
    clientUUID = `${clientUUID}-${process.env.RUN_MODE}`;
  }
  let videoRoot = getDataSavePath('videos');
  const uuidFilePath = path.join(getDataSavePath('appData'), app.getName(), 'uuid');
  const dataDir = path.join(getDataSavePath('documents'), 'HuaYoungBrowser');
  const videoDir = path.join(videoRoot, 'HuaYoung');
  const rpaDir = path.join(getDataSavePath('documents'), 'HuaYoungRPA');
  const rpaExecutorPath = getRpaExecutorPath();
  try {
    if (db.get('rpaExecutor').value()) {
      if (!fs.existsSync(path.join(rpaExecutorPath, 'cred'))) return;
      const cred = fs.readFileSync(path.join(rpaExecutorPath, 'cred'), { encoding: 'utf-8' });
      if (cred) {
        const [subject] = cred.trim().split(',');
        const identify = subject.split('-')[1] ?? subject;
        clientUUID = identify;
      }
    }
    if (fs.existsSync(uuidFilePath)) {
      let localUUID = fs.readFileSync(uuidFilePath).toString().trim();
      if (localUUID) {
        if (getRunModeFromUUID(localUUID) !== process.env.RUN_MODE) {
          localUUID =
            localUUID.replace(/@(\w+)$/, '') +
            (process.env.RUN_MODE === 'normal' ? '' : `@${process.env.RUN_MODE}`);
          fs.outputFileSync(uuidFilePath, localUUID, { encoding: 'utf8' });
        }
        clientUUID = localUUID;
      }
    }
  } catch (e) {}
  db.defaults({
    version: app.getVersion(),
    // client uuid
    uuid: clientUUID,
    // 当前登录用户信息
    account: {},
    // http cookies
    cookies: [],
    // 用户默认团队
    defaultTeam: {},
    // 通道默认连接方式
    preferTransitIdMap: {},
    // 录像码率
    recordBps: 2000000,
    // 录像帧率, 0 根据机器性能自动计算
    recordFps: 0,
    // 系统偏好设置
    sysPres: {
      // 开机启动
      autoLaunch: false,
      dataDir,
      videoDir,
      rpaDir,
      appLang: (process.env.LANG || '').startsWith('zh') ? 'zh-CN' : 'en-US',
      lang: 'auto',
      devtools: true,
      gpu: true,
      sandbox: true,
      browserSwitches: '',
      winSyncEvtDelay: 100,
      winSyncPauseKey: 'Ctrl+Shift+S',
      winSyncRunningColor: '#FF0000',
      winSyncPauseColor: '#00FF00',
      preventSleep: false,
      endpoint: '',
      exitOnClose: true,
      fsWatchDepth: 3,
      autoUpdate: 0b01,
      autoInstallTimeRange: '02:00-06:00',
      hideNewVersionAlert: false,
      localFrontendProxyEnabled: true,
      localFrontendProxyMode: 'system',
      localFrontendProxyUrl: '',
      openBrowserIntervalSecMin: 2,
      openBrowserIntervalSecMax: 8,
      autofill: 'password',
    },
    rpaEditorBounds: {
      left: 0,
      top: 0,
      width: 900,
      height: undefined,
    },
    shopTabUrls: {},
    preferences: {},
    appProxy: {
      type: 'direct',
    },
    pptrIgnoreArgs: [],
    useBackupEndpoint: false,
    PORTAL_URL: process.env.PORTAL_URL,
    API_URL: process.env.API_URL,
    DL_URL: process.env.DL_URL || 'https://dl.szdamai.com',
    PORTAL_URL2: process.env.PORTAL_URL2 || 'https://app.thinkoncloud.com',
    API_URL2: process.env.API_URL2 || 'https://api.thinkoncloud.com',
    DL_URL2: process.env.DL_URL2 || 'https://dl.thinkoncloud.com',
    WX_URL: process.env.WX_URL || 'https://wx.szdamai.com',
    // 日志级别
    LOG_LEVEL: 'info',
    TUNNEL_VERBOSE: false,
    RUN_MODE: 'normal',
  }).write();
  const sysPres = db.get('sysPres').value();
  if (db.get('uuid').value() !== clientUUID) {
    db.set('uuid', clientUUID).write();
  }
  try {
    if (!fs.existsSync(uuidFilePath)) {
      fs.outputFile(uuidFilePath, clientUUID, { encoding: 'utf8' });
    }
  } catch (e) {}
  if (!sysPres.rpaDir) {
    sysPres.rpaDir = rpaDir;
  }
  if (typeof sysPres.devtools === 'undefined') {
    sysPres.devtools = true;
  }
  if (typeof sysPres.gpu === 'undefined') {
    sysPres.gpu = true;
  }
  if (typeof sysPres.gpu === 'undefined') {
    sysPres.browserSwitches = '';
  }
  if (typeof sysPres.exitOnClose === 'undefined') {
    sysPres.exitOnClose = true;
  }
  if (typeof sysPres.fsWatchDepth === 'undefined') {
    sysPres.fsWatchDepth = 3;
  }
  if (typeof sysPres.autoUpdate === 'undefined' || sysPres.autoUpdate === true) {
    sysPres.autoUpdate = 0b01;
  } else if (sysPres.autoUpdate === false) {
    sysPres.autoUpdate = 0b00;
  }
  if (typeof sysPres.autoInstallTimeRange === 'undefined') {
    sysPres.autoInstallTimeRange = '02:00-05:59';
  }
  if (typeof sysPres.preventSleep === 'undefined') {
    sysPres.preventSleep = false;
  }
  if (typeof sysPres.sandbox === 'undefined') {
    sysPres.sandbox = true;
  }
  if (typeof sysPres.appLang === 'undefined') {
    sysPres.appLang = (process.env.LANG || 'zh-CN').startsWith('zh') ? 'zh-CN' : 'en-US';
  }
  if (typeof sysPres.winSyncEvtDelay === 'undefined') {
    sysPres.winSyncEvtDelay = 100;
  }
  if (typeof sysPres.winSyncPauseKey === 'undefined') {
    sysPres.winSyncPauseKey = 'Ctrl+Shift+S';
  }
  if (typeof sysPres.winSyncRunningColor === 'undefined') {
    sysPres.winSyncRunningColor = '#FF0000';
  }
  if (typeof sysPres.winSyncPauseColor === 'undefined') {
    sysPres.winSyncPauseColor = '#00FF00';
  }
  if (typeof sysPres.localFrontendProxyEnabled === 'undefined') {
    sysPres.localFrontendProxyEnabled = true;
  }
  if (typeof sysPres.localFrontendProxyMode === 'undefined') {
    sysPres.localFrontendProxyMode = 'system';
  }
  if (typeof sysPres.localFrontendProxyUrl === 'undefined') {
    sysPres.localFrontendProxyUrl = '';
  }
  if (typeof sysPres.openBrowserIntervalSecMin === 'undefined') {
    sysPres.openBrowserIntervalSecMin = 2;
  }
  if (typeof sysPres.openBrowserIntervalSecMax === 'undefined') {
    sysPres.openBrowserIntervalSecMax = 8;
  }
  db.set('sysPres', sysPres).write();
  if (db.get('rpaExecutor').value() && fs.existsSync(endpointsPath)) {
    const apiEndpoints = fs.readFileSync(endpointsPath, { encoding: 'utf-8' });
    if (apiEndpoints.trim()) {
      db.set('API_URL', apiEndpoints.trim()).write();
    }
  }
  if (db.get('RUN_MODE').value() !== process.env.RUN_MODE) {
    db.set('RUN_MODE', process.env.RUN_MODE).write();
    db.set('account', {}).write();
    db.set('cookies', []).write();
  }
  // if (process.env.RUN_MODE !== 'runtime') {
  //   !!db.get('runtimeIdentifier').value() && db.set('runtimeIdentifier', undefined).write();
  //   !!db.get('runtimeSecretKey').value() && db.set('runtimeSecretKey', undefined).write();
  // }
}

function repairRpaDb() {
  if (getHyDb()?.firstRun) {
    let oldDbFile = path.join(getDataSavePath('appData'), app.getName(), 'rpa.db');
    if (fs.existsSync(oldDbFile)) {
      const adapter = new FileSync<any>(oldDbFile);
      let low_db = low(adapter);
      let rpaDbData = low_db.get('rpaDb').value() || {};
      let keys = Object.keys(rpaDbData);
      for (let i = 0; i < keys.length; i++) {
        let key = keys[i];
        let value = rpaDbData[key];
        if (key && key.length > 0 && value && value.length > 0) {
          getHyDb()
            ?.conn.prepare('INSERT INTO rpa_local_db(key, value, update_time) VALUES (?, ?, ?)')
            .run(key, value, Date.now());
        }
      }
      fs.unlink(oldDbFile);
    }
  }
}

export default {
  init: () => {
    initDbStorage();
    repairRpaDb();
    activePortalUrl = db.get('PORTAL_URL').value() || '';
    activeApiUrl = db.get('API_URL').value() || '';
    activeDlUrl = db.get('DL_URL').value() || '';
  },
  getDb: () => {
    initDbStorage();
    return db;
  },
  getJwt: () => {
    return db.get('account').value().jwt;
  },
  getRuntimeJwt: () => {
    return db.get('runtimeJwt').value();
  },
  getLanguage() {
    if (process.env.OEM_NAME === 'gg') {
      return process.env.LANG || app.getPreferredSystemLanguages()[0];
    }
    return this.getSysPres().appLang || process.env.LANG || app.getPreferredSystemLanguages()[0];
  },
  getBrowserLanguage() {
    let lang = this.getSysPres().lang || process.env.LANG || app.getPreferredSystemLanguages()[0];
    if (process.env.OEM_NAME === 'gg') {
      lang = process.env.LANG || app.getPreferredSystemLanguages()[0];
    }
    if (lang === 'auto') {
      // 跟客户端保持一致
      lang = this.getLanguage();
    }
    return lang;
  },
  getUserId: () => {
    const jwt = db.get('account').value().jwt;
    if (!jwt) {
      return null;
    }
    try {
      return Number(
        JSON.parse(Buffer.from(jwt?.split('.')?.[1], 'base64').toString()).sub.split('-')[1],
      );
    } catch (e) {
      return null;
    }
  },
  getCookies: () => {
    return db.get('cookies').value();
  },
  removeSession: async () => {
    const cookies = db.get('cookies').value();
    const newCookies = cookies.filter((v) => !/^session=/.test(v));
    await db.set('cookies', newCookies).write();
  },
  getDeviceIdFromCookies: () => {
    const cookies = db.get('cookies').value();
    const deviceItem = cookies.find((v) => /^toc-device-id=(.+)$/.test(v));
    return /^toc-device-id=(.+)$/.exec(deviceItem ?? '')?.[1];
  },
  getApiUrl: () => {
    return activeApiUrl || 'http://localhost:8000';
  },
  getPortalUrl: () => {
    return activePortalUrl || 'http://localhost:8000';
  },
  getDlUrl: () => {
    return activeDlUrl || 'http://localhost:8000';
  },
  getWxUrl: () => {
    return db.get('WX_URL').value();
  },
  changeToEndpoint: (endpointIdx = 1) => {
    if (process.env.NODE_ENV === 'development') {
      return;
    }
    activeEndpointIdx = endpointIdx;
    const newPortalUrl = db.get(`PORTAL_URL${activeEndpointIdx}`).value();
    const newApiUrl = db.get(`API_URL${activeEndpointIdx}`).value();
    const newDlUrl = db.get(`DL_URL${activeEndpointIdx}`).value();
    activePortalUrl = newPortalUrl || activePortalUrl;
    activeApiUrl = newApiUrl || activeApiUrl;
    activeDlUrl = newDlUrl || activeDlUrl;
    logger.info(
      '[APP] Endpoint change',
      JSON.stringify({ activePortalUrl, activeApiUrl, activeDlUrl }),
    );
  },
  /**
   * 用户偏好设置
   */
  getPreferences: () => {
    const value = db.get('preferences').value();
    return value || {};
  },
  /**
   * 系统偏好设置
   */
  getSysPres: () => {
    const value = db.get('sysPres').value();
    return value || {};
  },
  isRpaExecutor: () => {
    const value = db.get('rpaExecutor').value();
    return !!value;
  },
  isRuntimeMode: () => {
    return (db.get('RUN_MODE').value() || 'normal') === 'runtime';
  },
};
