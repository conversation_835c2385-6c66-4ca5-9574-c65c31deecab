import { app, shell } from 'electron';
import path from 'path';
import logger from '@e/services/logger';

export function createShopShortcut(options: { name: string; location?: string; args?: string }) {
  const { name, location, args } = options;
  const created = shell.writeShortcutLink(
    path.join(location || app.getPath('desktop'), `${name}.lnk`),
    {
      target: process.execPath,
      args,
      description: '花漾快捷方式 - ' + name,
    },
  );
  logger.info(`[APP] Create shortcut（${name}）${created ? 'success' : 'failed'}`);
  return created;
}
