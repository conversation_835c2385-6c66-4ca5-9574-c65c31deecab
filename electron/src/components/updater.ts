import { autoUpdater } from 'electron-updater';
import { powerMonitor } from 'electron';
import moment from 'moment';
import _ from 'lodash';
import logger from '@e/services/logger';
import { dispatchMsg } from '@e/utils/ipc';
import db from '@e/components/db';
import request from '@e/services/request';
import { isLinuxPlatform, isMacPlatform, isWin7Platform, isWinPlatform } from '@e/utils/utils';
import {
  getAboutRuntimeWindow,
  getChromiums,
  getCurrentWindow,
  getSystemPrefWindow,
} from '@e/utils/window';
import path from 'path';
import fs from 'fs-extra';
import { spawn } from 'child_process';
import appConfig, { getDynamicPath } from '@e/configs/app';
import i18n from '@e/utils/i18n';

const AUTO_INSTALL_DELAY = 60;
const assetsUpdaterExeFile = path.resolve(__dirname, '../extra', 'HYUpdater.exe');
let hasNewVersionToInstall = false;
let shouldDispatchNewVersionToInstallMsg = true;

export default class AppUpdater {
  state:
    | 'pending'
    | 'checking'
    | 'notAvailable'
    | 'available'
    | 'downloading'
    | 'downloaded'
    | 'error';
  bytesPerSecond: number;
  percent: number;
  transferred: number;
  total: number;
  autoInstallCheckTimer: any = 0;
  autoInstallTimer: any = 0;
  delayAutoInstallTimer: any = 0;
  hasSpawnLoadingModal: boolean = false;
  constructor() {
    this.state = 'pending';
    this.bytesPerSecond = 0;
    this.percent = 0;
    this.transferred = 0;
    this.total = 1;
    autoUpdater.logger = logger;
    autoUpdater.disableWebInstaller = true;
    // 退出时不自动安装更新
    autoUpdater.autoInstallOnAppQuit = false;
    hasNewVersionToInstall = db.getDb().get('hasNewVersionToInstall').value();

    setInterval(() => {
      this.check();
    }, 10 * 60 * 1000);
    this.startAutoInstallCheckTimer();

    autoUpdater.on('checking-for-update', () => {
      this.state = 'checking';
    });
    autoUpdater.on('update-available', (info: any) => {
      this.state = 'available';
      logger.info('[AppUpdater] update-available', info.version);
    });
    autoUpdater.on('update-not-available', (info: any) => {
      this.state = 'notAvailable';
    });
    autoUpdater.on('error', (err: any) => {
      this.state = 'error';
      logger.error('[AppUpdater] error', err);
    });
    autoUpdater.on('download-progress', (progressObj: any) => {
      logger.info('[AppUpdater] download-progress', progressObj);
      this.state = 'downloading';
      this.bytesPerSecond = progressObj.bytesPerSecond;
      this.percent = Math.min(100, Math.round(progressObj.percent));
      this.transferred = progressObj.transferred;
      this.total = progressObj.total;
      let log_message = 'Download speed: ' + progressObj.bytesPerSecond;
      log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
      log_message = log_message + ' (' + progressObj.transferred + '/' + progressObj.total + ')';
      logger.info(`[AppUpdater] ${log_message}`);
      this.sendStatusToMainWindow();
    });
    autoUpdater.on('update-downloaded', (info: any) => {
      logger.info('[AppUpdater] update-downloaded', info);
      this.state = 'downloaded';
      this.percent = 100;
      if (!db.getDb().get('hasNewVersionToInstall').value()) {
        db.getDb().set('hasNewVersionToInstall', true).write();
        shouldDispatchNewVersionToInstallMsg = false;
      } else if (shouldDispatchNewVersionToInstallMsg) {
        hasNewVersionToInstall = true;
        dispatchMsg('app-new-version-ready-to-install', {}, getCurrentWindow());
      }
      this.sendStatusToMainWindow();
      this.tryAutoInstall();
    });
    powerMonitor.on('resume', () => {
      this.check();
    });
  }

  getVersionConfig() {
    return new Promise((resolve, reject) => {
      request('/api/meta/app/versionConfigs', { forceHttp: true })
        .then((versionConfigsData: API.AppVersionConfig) => {
          const { versionConfigs = [] } = versionConfigsData;
          let targetConfig;
          if (isMacPlatform()) {
            targetConfig = versionConfigs.find((vo) => vo.platfrom === 'macos');
          } else if (isLinuxPlatform()) {
            targetConfig = versionConfigs.find((vo) => vo.platfrom === 'linux');
          } else {
            targetConfig = versionConfigs.find(
              (vo) => vo.platfrom === (isWin7Platform() ? 'windows7' : 'windows'),
            );
          }
          resolve(targetConfig);
        })
        .catch((e) => reject(e));
    });
  }

  async hasNewVersion() {
    try {
      const targetConfig: any = await this.getVersionConfig();
      if (targetConfig && Number(process.env.BUILD_NUMBER) < Number(targetConfig.earlyTryVersion)) {
        return true;
      }
      return false;
    } catch (e) {
      logger.error(e);
    }
    return false;
  }

  async needForceUpdate() {
    try {
      const targetConfig: any = await this.getVersionConfig();
      if (targetConfig && Number(process.env.BUILD_NUMBER) < Number(targetConfig.minBuildNo)) {
        return true;
      }
      return false;
    } catch (e) {
      logger.error(e);
    }
    return false;
  }

  check(force = false) {
    if (!['pending', 'notAvailable', 'downloaded'].includes(this.state)) return;
    const { autoUpdate } = db.getSysPres();
    if (!force && !autoUpdate) return;
    // 先检查最小构建版本
    this.getVersionConfig().then((targetConfig: any) => {
      const { autoUpdate } = db.getSysPres();
      // DAMAI-2231 自动下载/静默安装使用最新版本，手动更新使用最小版本
      if (
        targetConfig &&
        Number(process.env.BUILD_NUMBER) <
          Number(autoUpdate === 0b00 ? targetConfig.minBuildNo : targetConfig.currentBuildNo)
      ) {
        let feedUrl = process.env.UPDATE_URL;
        // 如果是 beta 测试用户，使用 beta 版本
        if (
          feedUrl &&
          targetConfig.currentBuildNo === targetConfig.betaBuildNo &&
          !appConfig.isOEM
        ) {
          feedUrl = feedUrl.replace('downloads/', 'downloads/beta_');
          autoUpdater.setFeedURL(feedUrl);
        }
        logger.info(`[APP] Start check app updates (force:${force}, feedUrl:${feedUrl})`);
        autoUpdater.checkForUpdates();
      }
    });
  }

  startAutoInstallCheckTimer() {
    this.autoInstallCheckTimer = setInterval(() => {
      this.tryAutoInstall();
    }, 60 * 1000);
  }

  // 尝试自动静默安装
  tryAutoInstall() {
    if (this.state !== 'downloaded' || this.autoInstallTimer !== 0) return;
    const { autoUpdate, autoInstallTimeRange = '' } = db.getSysPres();
    // 检查是否开启了自动安装
    if (autoUpdate !== 0b11) return;
    // 检查是否处于允许时间段
    const startDate = moment(autoInstallTimeRange.split('-')[0], 'HH:mm').startOf('minutes');
    const endDate = moment(autoInstallTimeRange.split('-')[1], 'HH:mm').endOf('minutes');
    const current = moment();
    if (!(current.isAfter(startDate) && current.isBefore(endDate))) {
      return;
    }
    // 检查是否有活跃会话
    if (_.some(getChromiums(), (v) => v !== null)) {
      return;
    }
    // 发送即将安装的通知
    dispatchMsg('app-will-start-install', { seconds: AUTO_INSTALL_DELAY });
    logger.info('[APP] dispatch "app-will-start-install" event');
    this.autoInstallTimer = setTimeout(() => {
      this.doInstall();
    }, (AUTO_INSTALL_DELAY + 2) * 1000);
  }

  showLoadingModal() {
    if (appConfig.isOEM) {
      return;
    }
    if (isWinPlatform()) {
      try {
        const { DATA_DIR } = getDynamicPath();
        const updaterExeFileDistPath = path.join(DATA_DIR, 'HYUpdater.exe');
        if (!fs.existsSync(updaterExeFileDistPath)) {
          if (fs.existsSync(assetsUpdaterExeFile)) {
            logger.info('[APP] copy HYUpdater');
            fs.copyFileSync(assetsUpdaterExeFile, updaterExeFileDistPath);
          }
        }
        if (!fs.existsSync(updaterExeFileDistPath)) {
          return;
        }
        if (this.hasSpawnLoadingModal) {
          return;
        }
        logger.info('[APP] spawn HYUpdater');
        const title = db.isRuntimeMode() ? i18n.t('花漾运行时升级中') : i18n.t('花漾客户端升级中');
        const overview = db.isRuntimeMode()
          ? i18n.t('正在为您升级花漾运行时')
          : i18n.t('正在为您升级花漾客户端');
        const detail = db.isRuntimeMode()
          ? i18n.t('这可能需要一点时间，请稍候...')
          : i18n.t('这可能需要一点时间，请稍候...');
        this.hasSpawnLoadingModal = true;
        const proc = spawn(
          updaterExeFileDistPath,
          [`--title=${title}`, `--msg-overview=${overview}`, `--msg-detail=${detail}`],
          { detached: true },
        );
        proc.on('error', (e: Error) => {
          logger.error(`[HYUpdater] process error`, e);
        });
      } catch (e: any) {
        logger.error('[APP] spawn updater.exe failed', e.message);
      }
    }
  }

  doInstall() {
    this.showLoadingModal();
    hasNewVersionToInstall = false;
    db.getDb().set('hasNewVersionToInstall', false).write();
    logger.info('[APP] quitAndInstall(isSilent: true)');
    clearTimeout(this.autoInstallTimer);
    clearInterval(this.autoInstallCheckTimer);
    this.autoInstallTimer = 0;
    autoUpdater.quitAndInstall(true, true);
  }

  getInfo() {
    return {
      state: this.state,
      bytesPerSecond: this.bytesPerSecond,
      percent: this.percent,
      transferred: this.transferred,
      total: this.total,
    };
  }

  hasNewVersionToInstall() {
    return hasNewVersionToInstall;
  }

  sendStatusToMainWindow() {
    // 汇报给渲染进程
    if (db.isRuntimeMode()) {
      const aboutRuntimeWindow = getAboutRuntimeWindow();
      if (aboutRuntimeWindow) {
        dispatchMsg('app-updater-info', this.getInfo(), aboutRuntimeWindow);
      }
      const systemPrefWindow = getSystemPrefWindow();
      if (systemPrefWindow) {
        dispatchMsg('app-updater-info', this.getInfo(), systemPrefWindow);
      }
    } else {
      dispatchMsg('app-updater-info', this.getInfo());
    }
  }

  quitAndInstall() {
    getCurrentWindow()?.hide();
    this.doInstall();
  }

  // 延迟安装
  delayAutoInstall(seconds = 24 * 3600) {
    clearTimeout(this.autoInstallTimer);
    clearInterval(this.autoInstallCheckTimer);
    clearInterval(this.delayAutoInstallTimer);
    this.delayAutoInstallTimer = setTimeout(() => {
      this.startAutoInstallCheckTimer();
    }, seconds * 1000);
  }
}

export const appUpdater = new AppUpdater();
