import sqlite3, {Database} from 'better-sqlite3';
import {version_updates} from './update';

export class HuaYoungDb {
  readonly #filePath: string;
  readonly conn: Database;

  firstRun = true;

  constructor(filePath: string) {
    this.#filePath = filePath;
    this.conn = sqlite3(this.#filePath);
    this.checkUpdate();
  }

  private checkUpdate() {
    let version = 0;
    let exists = this.conn.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?").get('db_version');
    if(exists) {
      this.firstRun = false;
      let row: any = this.conn.prepare("SELECT * FROM db_version WHERE id=?").get(1);
      version = row.version;
    }
    let latest_version = version;
    for(let i = 0; i < version_updates.length; i++) {
      let version_update = version_updates[i];
      latest_version = version_update.version;
      if(version < latest_version) {
        let updates = version_update.updates;
        for(let j = 0; j < updates.length; j++) {
          let sql = updates[j].replaceAll('\n', ' ');
          this.conn.exec(sql);
        }
      }
    }
    if(latest_version > version) {
      this.conn.prepare('update db_version set version=? where id=?').run(latest_version, 1);
    }
  }

}
