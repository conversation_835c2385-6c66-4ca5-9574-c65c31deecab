/**
 * 不同版本之间的更新语句，每次应用启动之后会检查数据库文件版本，并执行对应版本之后的所有 updates 语句
 */
export const version_updates = [
  {
    version: 1, //初始化
    updates: [
      'drop table if exists db_version',
      `create table db_version (
         id integer primary key,
         version integer
     )`,
      'insert into db_version (id, version) values (1, 1)',
    ],
  },
  {
    version: 10003000, //10.3.0，10 003 000，每一占3位，从左到右，依次代表：主版本号，小版本号，bug版本号
    updates: [
      'drop table if exists rpa_local_db',
      `create table rpa_local_db (
         id integer primary key autoincrement,
         key text not null,
         value text not null,
         update_time integer not null
     )`,
      'CREATE INDEX index_key ON rpa_local_db (key)',
    ],
  },
  {
    version: 10005000,
    // 缓存IP/IPPIP的出口IP地址
    updates: [
      'drop table if exists ip_address_cache',
      `create table ip_address_cache (
          id integer primary key autoincrement,
          ip_id integer not null,
          address text not null
      )`,
      'CREATE INDEX index_ip_id ON ip_address_cache (ip_id)',
    ],
  },
  {
    version: 11001000,
    // 手机弹出窗口尺寸、位置
    updates: [
      'drop table if exists popup_mobile_pos',
      `create table popup_mobile_pos (
          id integer primary key autoincrement,
          mobile_id integer not null,
          pos text not null
      )`,
    ],
  },
  {
    version: 12002000,
    // 记录ios手机设置的地址位置
    updates: [
      'drop table if exists ios_geo_location',
      `create table ios_geo_location (
          id integer primary key autoincrement,
          udid text not null,
          lat text not null,
          lon text not null
      )`,
    ],
  },
];
