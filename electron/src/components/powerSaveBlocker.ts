import { powerSaveBlocker } from 'electron';
import db from '@e/components/db';
import logger from '@e/services/logger';

export default class AppPowerSaveBlocker {
  state: 'started' | 'stopped';
  blockId: number;
  constructor() {
    this.state = 'stopped';
    this.blockId = -1;
  }

  init() {
    const { preventSleep = false } = db.getSysPres();
    this.update(preventSleep);
  }

  update(preventSleepState: boolean) {
    try {
      if (this.blockId === -1 && preventSleepState) {
        this.blockId = powerSaveBlocker.start('prevent-display-sleep');
        this.state = 'started';
        logger.info('[APP] powerSaveBlocker started');
      } else if (
        this.blockId !== -1 &&
        powerSaveBlocker.isStarted(this.blockId) &&
        !preventSleepState
      ) {
        powerSaveBlocker.stop(this.blockId);
        this.blockId = -1;
        this.state = 'stopped';
        logger.info('[APP] powerSaveBlocker stopped');
      }
    } catch (e) {
      logger.error('[APP] update preventSleep failed', e);
    }
  }
}

export const appPowerSaveBlocker = new AppPowerSaveBlocker();
