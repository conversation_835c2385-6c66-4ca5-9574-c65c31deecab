import fs from 'fs-extra';
import {waitMilliseconds, waitPromise} from "@e/rpa/utils";
import path from "path";
import {WriteStream} from "fs-extra";

export function writeStream(wStream: WriteStream, buffer: Buffer): Promise<void> {
  return new Promise(async (resolve, reject) => {
    wStream.write(buffer, (err) => {
      if(err) {
        reject(err);
        return;
      }
      wStream.end(()=>{
        resolve();
      });
    });
  });
}

class Lock {
  private readonly lockId: number;
  private readonly _promise!: Promise<any>;
  private readonly releaseFun!: () => any;

  constructor(promise: Promise<any>, releaseFun: () => any) {
    this.lockId = Math.random();
    this._promise = promise;
    this.releaseFun = releaseFun;
  }

  promise() {
    return this._promise;
  }

  private released = false;
  release() {
    if(!this.released) {
      this.released = true;
      waitMilliseconds(500).then(()=>{
        this.releaseFun();
      });
    }
  }

}

class FS {

  locks: Map<string, Lock> = new Map();

  async exists(file: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      fs.access(file, fs.constants.F_OK, (err) => {
        if (err) {
          resolve(false);
        } else {
          resolve(true);
        }
      })
    });
  }

  async stat(file: string, options?: {
    throwIfNoEntry?: boolean;
  }): Promise<fs.Stats> {
    let throwIfNoEntry = options?.throwIfNoEntry || true;
    return new Promise((resolve, reject) => {
      fs.stat(file, (err, stats) => {
        if (err) {
          if(throwIfNoEntry) {
            reject(err);
          } else {
            //@ts-ignore
            resolve(null);
          }
        } else {
          resolve(stats);
        }
      })
    });
  }

  async readFile(file: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      fs.readFile(file, (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
  }

  async copyFile(src: string, dest: string): Promise<void> {
    return new Promise((resolve, reject) => {
      fs.copyFile(src, dest, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(void 0);
        }
      });
    });
  }

  async move(src: string, dest: string): Promise<void> {
    return new Promise((resolve, reject) => {
      fs.move(src, dest, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(void 0);
        }
      });
    });
  }

  async unlink(file: string): Promise<void> {
    return new Promise((resolve, reject) => {
      fs.unlink(file, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(void 0);
        }
      });
    });
  }

  async remove(file: string): Promise<void> {
    return new Promise((resolve, reject) => {
      fs.remove(file, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(void 0);
        }
      });
    });
  }

  async isFile(file: string): Promise<boolean> {
    let stats = await this.stat(file);
    return stats.isFile();
  }

  async isDirectory(file: string): Promise<boolean> {
    let stats = await this.stat(file);
    return stats.isDirectory();
  }

  async mkdirs(dir: string): Promise<void> {
    if(await this.exists(dir)) {
      return;
    }
    let dirname = path.dirname(dir);
    if (dirname == dir) {
      throw '无法在指定路径创建文件或文件夹';
    }
    return new Promise((resolve, reject) => {
      fs.mkdirs(dir, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(void 0);
        }
      });
    });
  }

  async lock(file: string, timeout: number = 30): Promise<Lock> {
    if(this.locks.has(file)) {
      let promise = this.locks.get(file)?.promise();
      if(promise) {
        timeout = timeout || 0;
        timeout = timeout < 0 ? 0 : timeout;
        if(timeout == 0) {
          timeout = 30;//避免无限等待
        }
        try {
          await waitPromise(promise, timeout, 'wait_lock_timeout');
        } catch (e) {
          if(e === 'wait_lock_timeout') {
            //ignore
          } else {
            throw e;
          }
        }
      }
    }
    let resolveFun: (value?: any) => void;
    let promise = new Promise((resolve) => {
      resolveFun = ()=>{
        resolve(void 0);
        this.locks.delete(file);
      };
    });
    let lock = new Lock(promise, ()=>resolveFun());
    this.locks.set(file, lock);
    return lock;
  }

}

let fsutil = new FS();

export default fsutil;
