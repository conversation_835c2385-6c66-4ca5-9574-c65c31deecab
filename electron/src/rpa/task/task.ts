import { SnapshotType } from '../context';
import { waitSeconds } from '../utils';
import { Heartbeat } from '../heartbeat';
import { RpaFlow } from '../rpa';
import { ExitWholeTaskProps } from '../nodes/base';
import { RequestAgent } from '@e/services/request';
import logger from '@e/services/logger';
import low, { LowdbSync } from 'lowdb';
import Memory from 'lowdb/adapters/Memory';
import { Debugger, RpaDebugger } from '@e/rpa/rdb';
import EventEmitter from 'events';
import { LogWatcher } from '@e/rpa/log.watcher';
import { PauseHolder } from '@e/rpa/common';
import { AjaxEventClient } from '@e/utils/AjaxEventClient';
import { TaskItem } from '@e/rpa/task/task_item';
import { ExtraTaskItem } from '@e/rpa/task/extra_item';
import { RpaShopInfo } from '@e/rpa/task/common';
import { popupRpaTaskWin } from '@e/utils/popupTaskWindow';

class Threads {
  threadDelay: number = 0;
  constructor(threadDelay: number) {
    this.threadDelay = threadDelay;
  }
  async execute(next: () => Promise<TaskItem | undefined>) {
    let firstItem = true;
    while (true) {
      let item = await next();
      if (!item) {
        break;
      }
      if (!(await item.shouldExecute())) {
        logger.info(`[RPA] taskId=${item.task?.id}, itemId=${item.id} 不需要执行，跳过...`);
        continue;
      }
      if (!firstItem && this.threadDelay > 0) {
        await waitSeconds(this.threadDelay, false);
      }
      try {
        await item.execute();
      } catch (e) {
        logger.error(`[RPA] 执行taskId=${item.task?.id}, itemId=${item.id} 出现未捕获异常：`, e);
      }
      firstItem = false;
    }
  }
}

export interface TaskConsProps {
  flow: RpaFlow;
  task: API.RpaTaskVo & {
    startNodeNid?: string;
    endNodeNid?: string;
    sId?: string;
    debugFlowIds?: string[];
    breakpoints: any;
    teamName: string;
  };
  _items?: Array<any>;
  requestAgent: RequestAgent;
  ajaxEventClient?: AjaxEventClient;
}

export abstract class Task {
  id: number; //任务id
  teamId: number;
  teamName: string;
  rpaType: 'Browser' | 'Mobile' | 'IOS' | 'Extension';
  creatorId: number;
  planId?: number;
  name!: string; //任务名称
  taskVo: API.RpaTaskVo;

  snapshot: SnapshotType = 'Not'; //默认不截屏
  clientId?: string;
  clientIp?: string;
  concurrent = 1; //并发执行个数
  concurrentDelay = 15; //并发执行间隔，单位秒

  preview: boolean = false; //是否是预览执行
  previewSubflow?: string; //预览的是一个子流程
  console: boolean = false; //是否控制台task
  startNodeNid?: string; //从某节点开始执行
  endNodeNid?: string; //执行到某个节点结束
  runOnCloud: boolean = false;

  flow: RpaFlow;

  items!: Array<TaskItem>;
  extraItems: Array<ExtraTaskItem> = [];

  finished: boolean;
  forceStop: boolean = false;

  heartbeat?: Heartbeat;
  requestAgent: RequestAgent;
  ajaxEventClient?: AjaxEventClient;

  memoryDb!: LowdbSync<any>;
  environments: any;

  ajaxEventHds: any[] = [];
  eventEmitter: EventEmitter;

  //手机流程保存的是手机的信息，但为了兼容统一叫shopsInfo
  declare shopsInfo?: RpaShopInfo[];

  logWatcher?: LogWatcher;

  private rdb?: RpaDebugger;

  paused: PauseHolder;

  /**
   * @param flow
   * @param task
   * @param _items
   * @param shopId 预览使用的店铺id；只有 task.preview==true的时候才有意义
   * @param requestAgent
   */
  constructor({ flow, task, _items, requestAgent, ajaxEventClient }: TaskConsProps) {
    this.taskVo = task;
    this.rpaType = task.rpaType!;
    this.id = task.id!;
    this.teamId = task.teamId!;
    this.teamName = task.teamName!;
    this.planId = task.planId;
    this.creatorId = task.creatorId!;
    this.name = task.name!;
    this.snapshot = task.snapshot!;
    this.preview = task.preview!;
    this.console = !!task.console;
    if (this.preview) {
      this.startNodeNid = task.startNodeNid;
      this.endNodeNid = task.endNodeNid;
      this.previewSubflow = task.sId;
    }
    this.clientId = task.clientId;
    this.clientIp = task.clientIp;
    this.concurrent = task.concurrent || 1;
    this.concurrentDelay = task.concurrentDelay || 15;
    this.finished = false;
    this.runOnCloud = !!task.runOnCloud;
    this.eventEmitter = new EventEmitter({ captureRejections: true });

    this.flow = flow;

    this.paused = new PauseHolder(this.id);
    this.requestAgent = requestAgent;
    this.ajaxEventClient = ajaxEventClient;
    if (this.preview) {
      this.initAsPreview(_items!, task.debugFlowIds || [], task.breakpoints || {});
    } else {
      this.init(_items!);
    }
    this.eventEmitter.on('rpa_evt_exitWholeTask', (prop: ExitWholeTaskProps) => {
      this.onExitWholeTask(prop);
    });

    //@ts-ignore
    const adapter = new Memory();
    this.memoryDb = low(adapter);

    if (!this.preview) {
      if (this.ajaxEventClient) {
        this.ajaxEventClient
          .on('rpa.task.event-chain.dispatch', this.id, (data) => {
            if (this.id == data.taskId) {
              this.dispatchEvent(data.eventName, data.params, data.items);
            }
          })
          .then((hd) => this.ajaxEventHds.push(hd));
        this.ajaxEventClient
          .on('task-item-force-end', this.id, (data) => {
            logger.info(`[RPA] 收到强制结束item事件，taskID=${this.id}, itemIds=${data.itemIds}`);
            this.dispatchEvent('forceEndItem', data, data.itemIds);
          })
          .then((hd) => this.ajaxEventHds.push(hd));
        this.ajaxEventClient
          .on('force-stop-task', this.id, (data) => {
            logger.info(`[RPA] 收到强制停止task事件，taskID=${this.id}`);
            this.stop();
            if (data.stopPostRun) {
              this.stopPostRun();
            }
          })
          .then((hd) => this.ajaxEventHds.push(hd));
        this.ajaxEventClient
          .on('change-pause-status', this.id, (data) => {
            logger.info(`[RPA] 收到切换item暂停状态事件，taskID=${this.id}， paused=${data.paused}, itemId=${data.itemId}`);
            let paused = data.paused;
            let itemId = data.itemId;
            this.changePauseStatus(paused, itemId).then(() => {});
          })
          .then((hd) => this.ajaxEventHds.push(hd));
      }
    }
  }

  private initAsPreview(_items: any[], debugFlowIds: string[] = [], _breakpoints: any = {}) {
    this.items = [];
    let breakpoints = new Map(Object.entries(_breakpoints));
    this.rdb = new RpaDebugger(this.flow);
    this.rdb.setDebugFlowIds(debugFlowIds);
    this.rdb.setBreakpoints(breakpoints);

    const taskItem = this.createTaskItem(true, _items[0]);
    taskItem.rdb = this.rdb;
    this.items.push(taskItem);
    this.prepareExtraSubflow([{ type: 'prerun' }, { type: 'postrun' }]);
  }

  private init(_items: Array<any>) {
    this.refreshItems(_items);
    this.prepareExtraSubflow(_items);
    this.logWatcher = new LogWatcher(this.teamId, this.id, this.requestAgent);
    this.doSubInit();
  }

  //子类初始化
  protected abstract doSubInit(): void;

  protected abstract createTaskItem(preview: boolean, item: any): TaskItem;

  refreshItems(_items: Array<any>) {
    this.items = [];
    for (let i = 0; i < _items.length; i++) {
      let _item = _items[i];
      if (_item.type === 'shop') {
        const taskItem = this.createTaskItem(false, _item);
        this.items.push(taskItem);
      }
    }
  }

  private prepareExtraSubflow(_items: any[]) {
    for (let i = 0; i < _items.length; i++) {
      let _item = _items[i];
      if (_item.type == 'prerun' || _item.type == 'postrun') {
        let nid =
          'prerun' === _item.type ? this.flow.config.prerunNode : this.flow.config.postrunNode;
        if (!!nid && !this.startNodeNid && !this.endNodeNid && !this.previewSubflow) {
          const taskItem = new ExtraTaskItem(this.preview, this, _item.type, {
            ..._item,
            id: _item.id || this.id,
            teamId: this.flow.teamId,
            extraNodeId: nid,
          });
          if (this.preview) {
            taskItem.rdb = this.rdb;
          }
          this.extraItems.push(taskItem);
        }
      }
    }
  }

  getDebugger(): undefined | Debugger {
    if (this.preview) {
      return this.rdb;
    }
    return undefined;
  }

  /**
   * 触发一个事件
   * @param name
   * @param params
   * @param itemIds 如果为空表示触发所有item，否则只触发指定item的事件
   */
  async dispatchEvent(name: string, params: any, itemIds?: number[]) {
    let promises = [];
    for (let i = 0; i < this.items.length; i++) {
      let item = this.items[i];
      if (itemIds && itemIds.length > 0 && itemIds.indexOf(item.id) != -1) {
        promises.push(item.dispatchEvent(name, params));
      } else if (!itemIds) {
        promises.push(item.dispatchEvent(name, params));
      }
    }
    await Promise.all(promises);
  }

  async changePauseStatus(paused: boolean, itemId?: number) {
    if (!this.paused) return;
    if (!!itemId) {
      paused ? this.paused.pauseItem(itemId) : this.paused.resumeItem(itemId);
    } else {
      paused ? this.paused.pauseAll() : this.paused.resumeAll();
    }
    this.logWatcher?.emitPausedStatus(this.paused.toEmitObj());
    for (let i = 0; i < this.items.length; i++) {
      this.items[i].notifyPausedStatus().then(() => {});
    }
  }

  private async executeExtraSubflow(type: string) {
    await this.extraItems.find((item) => item.type === type)?.execute();
  }

  protected async fetchEnvironments() {
    if (!this.environments) {
      const taskReport = await this.requestAgent.request(`/api/rpa/task/${this.id}/environments`, {
        teamId: this.teamId,
      });
      this.environments = taskReport?.environments || {};
    }
  }

  checkEngineVersion() {
    if (typeof this.flow.config.minorVersion === 'number') {
      let engineVersion = parseFloat(process.env.RELEASE_APP_VERSION || '99.0');
      if (engineVersion < this.flow.config.minorVersion) {
        throw new Error(
          `当前客户端版本号 ${engineVersion} 小于此流程要求的最低版本号 ${this.flow.config.minorVersion}，请升级客户端`,
        );
      }
    }
  }

  abstract refreshShopsInfo(): Promise<void>;

  async execute() {
    logger.info(`[RPA] task started (taskID: ${this.id})，预计执行 ${this.items.length} 个item`);
    let index = 0;
    try {
      this.createHeartbeat();

      this.checkEngineVersion();

      await this.logWatcher?.start();
      await this.refreshShopsInfo();
      // 是否要弹出任务详情窗口
      if (this.taskVo.popupTaskLog) {
        popupRpaTaskWin(this.taskVo.teamId, this.taskVo.id);
      }

      //执行初始子流程
      await this.executeExtraSubflow('prerun');
      let next = async () => {
        if (!this.items || index >= this.items?.length) {
          logger.info(`[RPA] no more item to execute, index=${index}`);
          return undefined;
        }
        while (!this.finished && !this.forceStop && this.paused.all) {
          await waitSeconds(1, false);
        }
        return this.items[index++];
      };
      let promises = [];
      //控制台任务一次执行所有item
      let concurrent = this.console
        ? this.items.length
        : Math.min(this.concurrent, this.items.length);
      let threadDelay = concurrent > 1 ? 0 : this.concurrentDelay;
      for (let i = 0; i < concurrent; i++) {
        if (i > 0) {
          await waitSeconds(this.concurrentDelay, false);
        }
        promises.push(new Threads(threadDelay).execute(next));
      }
      await Promise.all(promises);

      //通知所有分身子流程执行完毕
      if (!this.preview) {
        this.requestAgent
          .request('/api/rpa/task/notifyAllItemsEnd', {
            method: 'POST',
            teamId: this.teamId,
            params: {
              rpaTaskId: this.id,
            },
          })
          .then(() => {})
          .catch(() => {});
      }

      await this.executeExtraSubflow('postrun');
    } catch (e) {
      console.error(`执行task (taskID: ${this.id})出现异常`, e);
    } finally {
      logger.info(`[RPA] task finished (taskID: ${this.id})，实际执行 ${index} 个item`);
      await this._onStop();
    }
    return this.forceStop;
  }

  //强行中断流程执行
  stop() {
    this.forceStop = true;
    this.paused.resumeAll();
    this.items.forEach((item) => item.stop());
  }

  //中断清理子流程
  stopPostRun() {
    this.extraItems.forEach((item) => item.stop());
  }

  private async _onStop() {
    this.finished = true;
    this.heartbeat?.stop();
    this.logWatcher?.stop();
    this.logWatcher = undefined;
    for (let hd of this.ajaxEventHds) {
      this.ajaxEventClient?.un(hd);
    }
    if (!this.preview) {
      //结束时发一次心跳是为了同步一个 nodes 状态
      try {
        await this._doHeartbeat();
      } catch (_) {}
      logger.info(`[RPA] notify server mark task(${this.id}) end`);
      await this.requestAgent.request(`/api/rpa/task/${this.id}/markTaskEnd`, {
        method: 'PUT',
        teamId: this.teamId,
      });
    }
    //@ts-ignore
    delete this.flow;
    //@ts-ignore
    delete this.items;
    //@ts-ignore
    delete this.extraItems;
    //@ts-ignore
    delete this.memoryDb;
    this.eventEmitter.removeAllListeners();
    //@ts-ignore
    this.eventEmitter = null;
    //@ts-ignore
    this.paused = undefined;
    logger.info(`[RPA] task stopped (taskID: ${this.id})`);
  }

  /**
   * 创建心跳器
   * @private
   */
  private createHeartbeat() {
    if (this.preview || !!this.heartbeat) {
      return;
    }
    this.heartbeat = new Heartbeat(() => {
      return this._doHeartbeat();
    }, 5000);
    this.heartbeat.start();
  }
  liveItems: any[] = [];
  private async _doHeartbeat() {
    if (this.preview) return;
    let liveItems = this.liveItems && this.liveItems.length > 0 ? this.liveItems : undefined;
    const status = await this.requestAgent.request(`/api/rpa/task/heartbeat/v2/${this.id}`, {
      method: 'PUT',
      teamId: this.teamId,
      data: {
        liveItems: liveItems,
      },
    });
    if (
      ['Ended', 'UnusualEnded', 'Cancelled', 'Ended_Partial_Failed', 'Ended_All_Failed'].includes(
        status,
      ) &&
      !this.finished
    ) {
      this.stop();
      this.stopPostRun();
      this.heartbeat?.stop();
    }
  }

  exit_whole_task_props?: ExitWholeTaskProps;
  private onExitWholeTask(props: ExitWholeTaskProps) {
    this.exit_whole_task_props = props;
    logger.info(`[RPA] task ExitWholeTask node (taskID: ${this.id})`);
    let needCancelItems: any = [];
    this.items.forEach((item) => {
      if (item.releaseInvoked || item.id == props.sourceItem) {
        return;
      }
      if (item.executeTime) {
        //已经开始，但需要强制 interrupt
        if (!!this.exit_whole_task_props?.interrupt) {
          item.stop();
          needCancelItems.push(item.id);
        }
      } else {
        //流程尚未开始
        needCancelItems.push(item.id);
      }
    });
    this.reportMarkItemCancelled(needCancelItems, '遇到强制退出整个流程节点').then(() => {});
    console.log(props);
  }

  async reportMarkItemCancelled(itemIds: number[], errorMsg?: string) {
    if (!this.preview && itemIds.length > 0) {
      this.requestAgent.request('/api/rpa/task/markItemCancelled', {
        method: 'POST',
        teamId: this.teamId,
        data: {
          rpaTaskItemIds: itemIds,
          errorMsg: errorMsg,
        },
      });
    }
  }
}
