所有系统环境变量
  account.name: 账户名称
  account.description: 账户描述
  account.platform: 账户平台
  account.site: 账户平台网站
  task.id : 当前任务的id
  task.name : 当前任务的名称
  task.item.id : 当前任务项的id
  flow.node.nid : 当前流程节点的nid
  prev.result : 上一个节点的返回值
  rpa.while.cycle.times: 当前是第几次循环（只在while节点上下文里有）



文件保存的路径:
如果是oss上，文件所在的目录是

\9898998..,mnz;;l.op/
   ROOT = /rpa/{teamId}/{taskId}/{itemId}/
如果是预览执行，文件所在的目录位于本地
   ROOT = /path/to/root/
   日志文件：
       {ROOT}/task.log
   普通节点截屏文件:
       {ROOT}/snapshot.{nodeId}.png
   数据文件:
       {ROOT}/ 数据文件是由客户自定义的文件名
   截屏节点文件默认名（也属于数据文件）:
       {ROOT}/data.snapshot.{nodeId}.png
