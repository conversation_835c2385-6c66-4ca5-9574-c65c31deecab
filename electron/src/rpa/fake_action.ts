import {Bo<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Input, Page, Point} from 'donkey-puppeteer-core';

import random from '../utils/random';
import {boundingBox, waitMilliseconds, waitPromise, waitRandom} from './utils';
import {RpaSpirit} from '@e/rpa/spirit';

import {mouseTrajectory} from "@e/rpa/ghost_cursor/mouse_cursor";

declare type Track = {
  x: number;
  y: number;
  t: number;
};

export interface Lock {
  promise: Promise<any>;
  release: () => any;
}

const mask_ctrl = 1 << 0,
  mask_shift = 1 << 1,
  mask_alt = 1 << 2;

/**
 * 用来模拟用户操作
 * 借鉴了 https://github.com/kkoooqq/fakebrowser/blob/main/src/core/FakeUserAction.ts
 */
export class FakeAction {
  currPos: Point;
  mobile: boolean = false;

  spirit: RpaSpirit;

  constructor(spirit: RpaSpirit) {
    this.spirit = spirit;
    this.currPos = { x: random.nextInt(0, 1280), y: random.nextInt(0, 700) };
  }

  /**
   * 由服务器端生成鼠标轨迹
   * @param startPos
   * @param endPos
   * @private
   */
  private async fetchMovementTrack(startPos: Point, endPos: Point): Promise<Track[]> {
    let tracks = mouseTrajectory(startPos.x, startPos.y, endPos.x, endPos.y);

    // let tracks = await request(`/api/rpa/task/geneMouseTracks`, {
    //   params: {
    //     x1: Math.floor(startPos.x),
    //     y1: Math.floor(startPos.y),
    //     x2: Math.floor(endPos.x),
    //     y2: Math.floor(endPos.y),
    //   },
    // });
    //@ts-ignore
    return tracks;
  }

  private _lock?: Lock;
  async lock(timeout: number = 30): Promise<Lock> {
    if (this._lock) {
      try {
        await waitPromise(this._lock.promise, timeout);
      } catch (e) {}
    }
    let resolveFun: (value?: any) => void;
    let promise = new Promise((resolve) => {
      resolveFun = resolve;
    });
    this._lock = { promise, release: () => resolveFun() };
    //@ts-ignore
    return this._lock;
  }

  /**
   * Simulate mouse movement
   * @param page
   * @param options
   */
  private async simMouseMove(
    page: Page,
    options: {
      startPos: Point;
      endPos: Point;
      maxPoints?: number;
      cpDelta?: number;
    },
  ) {
    const tracks = await this.fetchMovementTrack(options.startPos, options.endPos);

    let startTime = Date.now();
    for (let n = 0; n < tracks.length; n += 1) {
      const track = tracks[n];
      await this.fitMouseMove(page, track.x, track.y);

      await waitMilliseconds(track.t || 7, false);
      if (Date.now() - startTime > 3000) {
        break;
      }
    }
    // The last pos must correction
    await this.fitMouseMove(page, options.endPos.x, options.endPos.y);
  }

  async simMouseMoveTo(
    page: Page,
    endPos: Point,
    maxPoints?: number,
    cpDelta?: number,
  ): Promise<boolean> {
    // Get the current page of the browser
    // first move to a close position, then finally move to the target position

    await this.simMouseMove(page, {
      startPos: this.currPos,
      endPos: endPos,
      maxPoints,
      cpDelta,
    });

    this.currPos = endPos;

    return true;
  }

  async simMouseMoveToElement(
    page: Page,
    eh: ElementHandle,
    center: boolean | Point = false,
  ): Promise<boolean> {
    await this.adjustElementPositionWithMouse(page, eh, { sim: true });
    // Pause
    await waitRandom(300, 800);

    let box = await boundingBox(eh);
    if (box) {
      const ensureEnter = async () => {
        //确保已经移到位置了
        box = await boundingBox(eh);
        let endPos: Point = this.currPos;
        if (box) {
          if (
            endPos.x > box.x &&
            endPos.x < box.x + box.width &&
            endPos.y > box.y &&
            endPos.y < box.y + box.height
          ) {
            //the position is correction
          } else {
            this.currPos = {
              x: box.x + box.width / 2,
              y: box.y + box.height / 2,
            };
            await this.fitMouseMove(page, this.currPos.x, this.currPos.y);
          }
        }
      };
      let pos;
      if (typeof center != 'boolean') {
        pos = center as Point;
      }
      if (typeof center == 'boolean' && center) {
        const endPos: Point = {
          x: box.x + box.width / 2,
          y: box.y + box.height / 2,
        };

        await this.simMouseMoveTo(page, endPos);
        await ensureEnter();
        return true;
      } else {
        let endPos: Point;
        if (pos) {
          endPos = {
            x: box.x + pos.x,
            y: box.y + pos.y,
          };
        } else {
          // The position of each element click should not be the center of the element
          // size of the clicked element must larger than 10 x 10
          // const endPos: Point = {
          //   x: box.x + box.width / 2 + random.nextInt(0, 5, true),
          //   y: box.y + box.height / 2 + random.nextInt(0, 5, true),
          // };
          let innerSize = await page.evaluate(() => {
            return [window.innerWidth, window.innerHeight];
          });
          const innerWidth = innerSize[0];
          const innerHeight = innerSize[1];
          let offX =
            box.width > 24
              ? random.nextInt(12, box.width - 12)
              : box.width / 2 + random.nextInt(0, 5, true);
          let y = Math.min(innerHeight - 5, box.y + box.height / 2 + random.nextInt(0, 5, true));
          if (box.y >= 0 && box.y + box.height > innerHeight) {
            y = Math.min(
              innerHeight - 5,
              box.y + (innerHeight - box.y) / 2 + random.nextInt(0, 5, true),
            );
          } else if (box.y < 0 && box.y + box.height > innerHeight) {
            y = Math.min(innerHeight - 5, innerHeight / 2 + random.nextInt(0, 5, true));
          } else if (box.y < 0 && box.y + box.height <= innerHeight) {
            y = Math.max(5, (box.y + box.height) / 2 + random.nextInt(0, 5, true));
          }
          endPos = {
            x: Math.min(innerWidth - 5, box.x + offX),
            y,
          };
        }

        await this.simMouseMoveTo(page, endPos);
        await ensureEnter();
      }
      // Pause
      await waitRandom(300, 800);

      return true;
    }

    return false;
  }

  async mouseMoveToElement(
    page: Page,
    eh: ElementHandle,
    center: boolean | Point = false,
  ): Promise<boolean> {
    let pos;
    if (typeof pos != 'boolean') {
      pos = center as Point;
    }
    if (typeof center === 'boolean' && center) {
      await eh.hover();
      return true;
    }
    await eh!.evaluate((eh) => {
      eh.scrollIntoView({
        block: 'center',
        inline: 'center',
      });
    });
    const box = await boundingBox(eh);
    if (box) {
      let x, y;
      if (pos) {
        x = box.x + pos.x;
        y = box.y + pos.y;
      } else {
        x = random.nextInt(box.x + 5, box.x + box.width - 5);
        y = random.nextInt(box.y + 5, box.y + box.height - 5);
      }
      await this.fitMouseMove(page, x, y);
      return true;
    }
    return false;
  }

  async fitMouseDown(page: Page, button: 'left' | 'right' | 'middle') {
    if (this.mobile) {
      await page.touchscreen.touchStart(this.currPos.x, this.currPos.y);
    } else {
      await page.mouse.down({ button });
    }
  }
  async fitMouseUp(page: Page, button: 'left' | 'right' | 'middle') {
    if (this.mobile) {
      await page.touchscreen.touchEnd();
    } else {
      await page.mouse.up({ button });
    }
  }
  async fitMouseMove(page: Page, x: number, y: number) {
    if (this.mobile) {
      try {
        await page.touchscreen.touchMove(x, y);
      } catch (e) {}
    } else {
      await page.mouse.move(x, y);
    }
  }
  async fitMouseElClick(eh: ElementHandle, button: 'left' | 'right' | 'middle') {
    if (this.mobile) {
      await eh.tap();
    } else {
      await eh.click({
        button,
        delay: random.nextInt(30, 80),
        clickCount: 1,
      });
    }
  }

  async simClickElement(
    page: Page,
    eh: ElementHandle,
    options: {
      button: 'left' | 'right' | 'middle';
      clickCount: number;
      modifiers: number;
      center: boolean;
      pos?: { x: number; y: number };
      sim?: boolean;
    } = { button: 'left', modifiers: 0, clickCount: 1, center: true, sim: true },
  ): Promise<void> {
    if (this.mobile) {
      options.modifiers = 0;
      options.clickCount = 1;
    }
    if (options.sim) {
      if (options.pos) {
        await this.adjustElementPositionWithMouse(page, eh, { sim: true });
        let box = await boundingBox(eh);
        if (box) {
          await this.simMouseMoveTo(page, { x: box.x + options.pos.x, y: box.y + options.pos.y });
        }
      } else {
        await this.simMouseMoveToElement(page, eh);
      }
    } else {
      if (options.pos) {
        let box = await boundingBox(eh);
        if (box) {
          await this.fitMouseMove(page, box.x + options.pos.x, box.y + options.pos.y);
        }
      } else {
        await this.mouseMoveToElement(page, eh);
      }
    }
    if (options.modifiers & mask_ctrl) {
      await page.keyboard.down('ControlLeft');
      await waitRandom(30, 80);
    }
    if (options.modifiers & mask_shift) {
      await page.keyboard.down('ShiftLeft');
      await waitRandom(30, 80);
    }
    if (options.modifiers & mask_alt) {
      await page.keyboard.down('AltLeft');
      await waitRandom(30, 80);
    }
    for (let i = 0; i < (options.clickCount || 1); i++) {
      if (options.center) {
        await this.fitMouseElClick(eh, options.button);
        await waitRandom(150, 500);
      } else {
        await this.fitMouseDown(page, options.button);
        await waitRandom(30, 80);
        await this.fitMouseUp(page, options.button);
        await waitRandom(150, 500);
      }
    }
    if (options.modifiers & mask_ctrl) {
      await page.keyboard.up('ControlLeft');
      await waitRandom(30, 80);
    }
    if (options.modifiers & mask_shift) {
      await page.keyboard.up('ShiftLeft');
      await waitRandom(30, 80);
    }
    if (options.modifiers & mask_alt) {
      await page.keyboard.up('AltLeft');
      await waitRandom(30, 80);
    }
  }

  async scrollPage(
    page: Page,
    flag: ElementHandle<Element> | null,
    // number: 滚动偏移量
    target: number | string,
  ) {
    if (typeof target === 'number') {
      return await this.scrollDeltaY(page, flag, target as number);
    } else if (target === 'top' || target === 'bottom') {
      let dy = 20000 * (target === 'top' ? -1 : 1);
      return await this.scrollDeltaY(page, flag, dy);
    }
  }

  private async scrollDeltaY(page: Page, flag: ElementHandle<Element> | null, dy: number) {
    let direction = dy < 0 ? -1 : 1;
    let lastY = undefined;
    for (;;) {
      let deltaY = Math.min(Math.abs(dy), random.nextInt(150, 300));
      if (deltaY == 0) {
        break;
      }
      if (flag) {
        let box = await boundingBox(flag);
        if (box) {
          if (lastY === box.y) {
            break;
          }
          lastY = box.y;
        }
      }
      await page.mouse.wheel({ deltaY: deltaY * direction });
      await waitRandom(100, 400);
      if (dy < 0) {
        dy += deltaY;
      } else {
        dy -= deltaY;
      }
    }
  }

  async scrollIntoView(
    page: Page,
    el: ElementHandle<Element>,
    options?: { top?: number | 'middle'; sim?: boolean },
  ) {
    return this.adjustElementPositionWithMouse(page, el, options);
  }

  private async adjustElementPositionWithMouse(
    page: Page,
    eh: ElementHandle<Element>,
    options?: { top?: number | 'middle'; sim?: boolean },
  ): Promise<BoundingBox | null> {
    let box = null;
    let innerSize = await page.evaluate(() => {
      return [window.innerWidth, window.innerHeight];
    });
    const innerWidth = innerSize[0];
    let innerHeight = innerSize[1];
    let lastY = undefined;
    let posTop = -1; //-1表示不在乎位置
    let sim = true;
    if (options) {
      sim = options.sim !== false;
      if ('middle' == options.top) {
        posTop = innerHeight / 2;
      } else if (typeof options.top == 'number') {
        posTop = options.top as number;
      }
    }
    for (;;) {
      box = await boundingBox(eh);
      if (box) {
        if (typeof lastY === 'number' && Math.abs(lastY - box.y) < 8) {
          break; //两次滚动距离小于8就认为无法滚动
        }
        if (posTop != -1) {
          box.height = 0;
          innerHeight = posTop;
        }
        lastY = box.y;
        // Check the node is in the visible area
        // @ts-ignore
        let deltaX: number = 0;
        let deltaY: number = 0;

        let viewportAdjust = false;

        let topTooSmall = box.y <= 0;
        if (posTop != -1) {
          topTooSmall = box.y <= posTop;
        } else {
          //不在乎位置，检查是不是在可视区域内
          if (box.y >= 0 && box.y < innerHeight) {
            break;
          }
        }
        let bottomTooBig = box.y + box.height >= innerHeight;

        if (topTooSmall && bottomTooBig) {
          //do nothing
        } else if (topTooSmall) {
          // If the top of the node is less than 0
          // deltaY always positive

          // ---------------------
          //     30px           |
          //    [   ]           |
          // ..         Distance to be moved
          // ..                 |
          // ..                 |
          // ---------------------body top

          if (posTop != -1) {
            deltaY = -box.y + posTop;
          } else {
            deltaY = -(box.y - 30) - 0;
          }
          if (sim) {
            if (deltaY > 1000) {
              deltaY = random.nextInt(400, 800);
            } else if (deltaY > 300) {
              deltaY = Math.min(deltaY, random.nextInt(200, 400));
            }
          }

          deltaY = -deltaY;
          viewportAdjust = true;
        } else if (bottomTooBig) {
          // If the bottom is beyond

          if (posTop != -1) {
            deltaY = box.y + box.height - innerHeight;
          } else {
            deltaY = box.y + box.height + 30 - innerHeight;
          }
          if (sim) {
            if (deltaY > 1000) {
              deltaY = random.nextInt(400, 800);
            } else if (deltaY > 300) {
              deltaY = Math.min(deltaY, random.nextInt(200, 400));
            }
          }

          viewportAdjust = true;
        }
        if (viewportAdjust) {
          await page.mouse.wheel({ deltaY });
          if (sim) {
            await waitRandom(50, 150);
          }
        } else {
          break;
        }
      } else {
        break;
      }
    }

    return box;
  }

  async simKeyboardPress(
    page: Page,
    text: KeyInput,
    options = {
      pauseAfterKeyUp: true,
    },
  ): Promise<boolean> {
    await page.keyboard.press(text);

    if (options && options.pauseAfterKeyUp) {
      await waitRandom(300, 1000);
    }

    return true;
  }

  async simKeyboardEnter(
    page: Page,
    options = {
      pauseAfterKeyUp: true,
    },
  ): Promise<boolean> {
    return await this.simKeyboardPress(page, 'Enter', options);
  }

  async simKeyboardEsc(
    page: Page,
    options = {
      pauseAfterKeyUp: true,
    },
  ): Promise<boolean> {
    return await this.simKeyboardPress(page, 'Escape', options);
  }

  async simKeyboardType(
    page: Page,
    eh: ElementHandle,
    text: string,
    aliveChecker: () => boolean = () => true,
    options: { pauseAfterLastKeyUp: boolean; enterModifier?: KeyInput } = {
      pauseAfterLastKeyUp: true,
      enterModifier: undefined,
    },
  ): Promise<boolean> {
    const needsShiftKey = '~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:"ZXCVBNM<>?';

    // TODO: check if shiftKey, alt, ctrl can be fired in mobile browsers
    for (let ch of text) {
      if (!aliveChecker()) {
        return false;
      }
      let needsShift = false;
      if (needsShiftKey.includes(ch)) {
        needsShift = true;
        await page.keyboard.down('ShiftLeft');
        await waitRandom(60, 200);
      }

      // if a Chinese character
      const isCh = ch.match(/^[\u4e00-\u9fa5]/);
      const delay = isCh ? random.nextInt(100, 200) : random.nextInt(60, 100);
      await waitMilliseconds(delay, false);

      if (!!options.enterModifier && ch == '\n') {
        await page.keyboard.down(options.enterModifier);
        await waitRandom(60, 200);
      }
      await eh.type('' + ch);

      if (!!options.enterModifier && ch == '\n') {
        await page.keyboard.up(options.enterModifier);
        await waitRandom(60, 200);
      }

      if (ch == ' ') {
        //每输完空格等待一下
        await waitRandom(60, 200);
      }

      if (needsShift) {
        await waitRandom(60, 200);
        await page.keyboard.up('ShiftLeft');
      }
    }

    if (options && options.pauseAfterLastKeyUp) {
      await waitRandom(300, 1000);
    }

    return true;
  }
}
