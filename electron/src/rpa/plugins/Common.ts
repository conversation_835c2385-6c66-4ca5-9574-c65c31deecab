import { RequestAgent } from '@e/services/request';
import { RpaPlugin } from './RpaPlugins';
import db from '@e/components/db';

export interface Props {
  ctx: string;
  teamId: number;
  requestAgent: RequestAgent;
}

/**
 * 通用插件
 */
export class Common implements RpaPlugin {
  name = '';
  version = '7.5';
  props: Props;

  constructor(props: Props) {
    this.props = props;
    this.name = props.ctx;
  }

  getEnv() {
    return {
      PORTAL_URL: db.getPortalUrl(),
      API_URL: db.getApiUrl(),
    };
  }

  async request(path: string, options: Record<string, any>) {
    return this.props.requestAgent.request(`/api/${this.props.ctx}` + path, {
      teamId: this.props.teamId,
      ...options,
    });
  }
}
