import {RpaPlugin} from '@e/rpa/plugins/RpaPlugins';
import {Common, Props} from '@e/rpa/plugins/Common';

/**
 * tiktok公会相关的接口插件。调用方式为 rpa.gh.push 等
 */
export class TikTokGH extends Common implements RpaPlugin {
  author: string = 'HuaYoung';
  description: string = 'tk公会相关的接口';
  name: string = 'gh';
  version: string = '1.0';

  constructor(props: Props) {
    super(props);
  }

  /**
   * 往公会表里追加一个达人
   * 追加时后台会自动判断数据库里是否有该达人的记录，如果有则不会追加
   * @param name 达人id
   * @param mark 追加时允许指定标记 done表示该达人已经发送过私信，err表示发送失败了。为空表示该达人等待发送私人
   */
  async push(region: string, name: string, mark?: string) {
    let names = [];
    names.push({ region, name, mark });
    return this.batchPush(names);
  }

  /**
   * 批量往公会表里追加达人，具体参数参考 #push
   * @param names: {region: string, name: string, mark?: string}[]
   */
  async batchPush(names: any[]) {
    if (!names || names.length === 0) {
      return;
    }
    for (let creator of names) {
      if (!creator['mark']) {
        delete creator['mark'];
      }
    }
    return this.props.requestAgent.request(`/api/tk/gh/push`, {
      method: 'post',
      teamId: this.props.teamId,
      data: names,
    });
  }

  /**
   * 获取一个未标记的达人(mark为空)，并暂时将其mark标记为done
   * 如果没有未标记的达人，则返回undefined
   */
  async pop(region: string): Promise<string | undefined> {
    return this.props.requestAgent.request(`/api/tk/gh/pop`, {
      teamId: this.props.teamId,
      params: {
        region,
      },
    });
  }

  /**
   * 将某个达人标记为done或者err
   * 因为pop方法会将达人标记为done，所以如果发送私信失败了，需要将其标记为err
   * 但如果发送成功了，可以不用调用该方法
   * @param name
   * @param mark
   */
  async mark(region: string, name: string, mark?: string) {
    let params = {
      region,
      name,
      mark: mark || undefined,
    };
    if (!params['mark']) {
      delete params['mark'];
    }
    return this.props.requestAgent.request(`/api/tk/gh/mark`, {
      method: 'post',
      teamId: this.props.teamId,
      params: params,
    });
  }

  /**
   * 导致所有达人为csv文件。
   * @param dateStr yyyy-MM-dd 格式的日期字符串，比如2024-01-01
   * @return 返回csv文件的内容
   */
  async export(region: string, dateStr?: string): Promise<string> {
    let params: any = {
      region,
    };
    if (dateStr) {
      params['date'] = dateStr;
    }
    return this.props.requestAgent.request(`/api/tk/gh/export`, {
      teamId: this.props.teamId,
      params: params,
    });
  }

  /**
   * 删除某个地区的指定id的达人
   * @param region
   * @param names
   */
  async delete(region: string, names: string[]) {
    if (!names || names.length <= 0) {
      return;
    }
    return this.props.requestAgent.request(`/api/tk/gh/delete`, {
      method: 'post',
      teamId: this.props.teamId,
      params: {
        region,
      },
      data: names,
    });
  }

  /**
   * 统计某个地区的达人数量
   * @param region
   * @param mark 如果不为空则表示只统计有该mark的达人数量，为空表示统计未打标签的达人的数量
   */
  async count(region: string, mark?: string) {
    let params = {
      region,
      mark: mark || undefined,
    };
    if (!params['mark']) {
      delete params['mark'];
    }
    return this.props.requestAgent.request(`/api/tk/gh/count`, {
      teamId: this.props.teamId,
      params: params,
    });
  }

  /**
   * 统计某个区域的总共达人数量
   * @param region
   */
  async countAll(region: string) {
    return this.props.requestAgent.request(`/api/tk/gh/countAll`, {
      teamId: this.props.teamId,
      params: {
        region,
      },
    });
  }

  /**
   * 同步（创建）TK达人
   * @param ghCreatorDocument
   */
  async ghCreatorSync(ghCreatorDocument: API.GhCreatorDocument) {
    return this.props.requestAgent.request(`/api/gh/creator/sync`, {
      method: 'post',
      teamId: this.props.teamId,
      data: ghCreatorDocument,
    });
  }

  /**
   * 批量同步（创建）TK达人
   */
  async ghCreatorSyncBatch(creators: API.GhCreatorDocument[]) {
    return this.props.requestAgent.request(`/api/gh/creator/syncBatch`, {
      method: 'post',
      teamId: this.props.teamId,
      data: {
        creators,
      },
    });
  }

  /**
   * 批量更新达人可用性
   * @param data
   */
  async ghCreatorUpdateAvailable(data: API.GhCreatorUpdateAvailableRequest) {
    return this.props.requestAgent.request(`/api/gh/creator/updateAvailable`, {
      method: 'put',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 批量更新达人状态
   * @param data
   */
  async ghCreatorUpdateStatus(data: API.GhCreatorUpdateStatusRequest) {
    return this.props.requestAgent.request(`/api/gh/creator/updateStatus`, {
      method: 'put',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 批量更新达人状态（用handle）
   * @param data
   */
  async ghCreatorSyncStatus(data: API.GhCreatorSyncStatusRequest) {
    return this.props.requestAgent.request(`/api/gh/creator/syncStatus`, {
      method: 'put',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 批量更新达人是否有新消息（用handle更新）
   * @param data
   */
  async ghCreatorSyncHasNewMsg(data: { hasNewMsg: boolean; handles: string[] }) {
    return this.props.requestAgent.request(`/api/gh/creator/syncHasNewMsg`, {
      method: 'put',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 获取TK达人头像
   * @param creatorId
   */
  async ghCreatorAvatar(creatorId: string) {
    return this.props.requestAgent.request(`/api/gh/creator/avatar?creatorId=${creatorId}`, {
      method: 'get',
      teamId: this.props.teamId,
    });
  }

  /**
   * 同步TK达人头像
   * @param data
   */
  async ghCreatorSyncAvatar(data: API.SyncCreatorAvatarRequest) {
    return this.props.requestAgent.request(`/api/gh/creator/syncAvatar`, {
      method: 'post',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 批量查询达人状态（用handle）
   */
  async ghCreatorStatusList(handles: string[]) {
    return this.props.requestAgent.request(`/api/gh/creator/statusList`, {
      method: 'get',
      teamId: this.props.teamId,
      params: { handles: handles.join(',') },
    });
  }

  /**
   * 批量查询达人是否可用（用handle）
   * handles 为逗号分隔的字符串: handles1,handles2,handles3
   * 返回一个列表 [{ handle: string, id: number, available: boolean, region: string }]，id > 0 表示该达人在数据库中存在，available 为 true 表示该达人在达人列表中可见，region 为达人所在地区
   */
  async ghCreatorAvailableList(handles: string) {
    return this.props.requestAgent.request(`/api/gh/creator/availableList`, {
      method: 'get',
      teamId: this.props.teamId,
      params: { handles },
    });
  }

  /**
   * 同步（创建）直播记录
   * @param data
   */
  async ghLiveSync(data: API.GhLiveDocument) {
    return this.props.requestAgent.request(`/api/gh/live/sync`, {
      method: 'post',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 添加交互记录
   * @param data
   */
  async ghInteraction(data: API.GhInteractionVo) {
    return this.props.requestAgent.request(`/api/gh/interaction`, {
      method: 'post',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 批量添加交互记录
   * @param data
   */
  async ghInteractions(data: API.AddGhInteractionRequest) {
    return this.props.requestAgent.request(`/api/gh/interactions`, {
      method: 'post',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 同步达人私信
   * @param data
   */
  async ghMessageSync(data: API.AddGhMessageRequest) {
    return this.props.requestAgent.request(`/api/gh/message/sync`, {
      method: 'post',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 流程执行时获取任务
   * @param params
   */
  async ghJobsTaskFetchJob(params: { rpaTaskId: number }) {
    return this.props.requestAgent.request(`/api/gh/jobs/task/fetchJob`, {
      method: 'get',
      teamId: this.props.teamId,
      params,
    });
  }

  /**
   * 汇报ghJob执行结果
   * @param data
   */
  async ghJobsTaskReportJob(data: API.ReportGhJobRequest) {
    return this.props.requestAgent.request(`/api/gh/jobs/task/reportJob`, {
      method: 'put',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 批量打标签（用标签名）
   * @param params
   */
  async tagResourcesByTag(params: { resourceType: string; resourceIds: string; tag: string }) {
    return this.props.requestAgent.request(`/api/tag/resources/byTag`, {
      method: 'post',
      teamId: this.props.teamId,
      params,
    });
  }

  /**
   * 批量撤销标签
   * @param params
   */
  async deleteResourcesTag(params: { resourceType: string; resourceIds: string; tag: string }) {
    return this.props.requestAgent.request(`/api/tag/resources/byTag`, {
      method: 'delete',
      teamId: this.props.teamId,
      params,
    });
  }

  /**
   * 同步手机账号
   * @param data
   */
  async rpaSyncMobileAccounts(data: {
    mobileId: number;
    platformType: string;
    area: string;
    accounts: string[];
  }) {
    data.platformType = data.platformType || 'TkVideo';
    data.area = data.area || 'Global';
    return this.props.requestAgent.request(`/api/shop/mobile/account/rpaSyncMobileAccounts`, {
      method: 'post',
      teamId: this.props.teamId,
      data,
    });
  }

  /**
   * 通知有新的tiktok消息了。有主播回复您的消息了
   * 会给相应手机有权限的人发一笔站内信
   * @param data
   */
  async notifyNewTkMessage(data: {
    mobileId: number;
    accounts: string[]
  }) {
    return this.props.requestAgent.request(`/api/shop/mobile/account/notifyNewTkMessage`, {
      method: 'put',
      teamId: this.props.teamId,
      data,
    });
  }

}
