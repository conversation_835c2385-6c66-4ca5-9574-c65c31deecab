import { ipc<PERSON><PERSON><PERSON> } from 'electron';

const io = require('socket.io-client');

//该文件代码跑在 electron 的rpa辅助窗口里
class RemoteCaptcha {
  status: 'preparing' | 'waiting' | 'processing' | 'error' | 'done' = 'preparing';
  msg?: string;
  captchaCode?: string;

  roomName!: string;
  stun!: any;
  rtcConfiguration!: any;
  ioClient: any;

  processing = false;
  options!: any;

  browserWinId?: string;

  pc!: RTCPeerConnection;
  dataChannel!: RTCDataChannel;

  async captcha(data: any) {
    let { roomName, stun, options } = data;
    this.roomName = roomName;
    this.stun = stun;
    this.options = options;
    this.rtcConfiguration = {
      iceServers: [
        {
          urls: this.stun.turn,
          username: this.stun.username,
          credential: this.stun.password,
        },
        {
          urls: 'stun:stun.l.google.com:19302',
        },
      ],
    };

    await this.initSocketIO();
  }

  onPCDisconnected() {
    //todo 如果当前 status 不为done，说明出了问题，怎样提示给用户
    if (this.status == 'error' || this.status == 'done') {
      return;
    }
    nodeLog('远程人工干预页面似乎已经关闭，继续等待...');
    this.processing = false;
    //@ts-ignore
    this.pc = undefined;
    //@ts-ignore
    this.dataChannel = undefined;
  }

  private async initSocketIO() {
    this.ioClient = io.connect(this.stun.signaling, { transports: ['websocket'] });
    await new Promise((resolve, reject) => {
      this.ioClient.on('connect', () => {
        this.ioClient.emit('subscribe-room', { room: this.roomName });
        resolve(true);
      });
    });
    this.ioClient.on('message', (message: any) => {
      let { from, data } = message;
      if (from != this.ioClient.id) {
        this.onSDData(data);
      }
    });

    this.status = 'waiting';
    nodeLog('初始化完成，正在等待远程人工干预页面被打开');
  }

  private async onSDData(data: any) {
    if (data.m) {
      //time,x,y,action,button
      ipcRenderer.send(`rpa-${roomName}-mouse`, data.m);
      return;
    }
    if (data.text) {
      //text
      ipcRenderer.send(`rpa-${roomName}-type`, data.text);
      return;
    }
    switch (data.action) {
      case 'init':
        if(this.processing) {
          this.onPCDisconnected();
        }
        nodeLog('远程人工干预页面已经被打开');
        this.status = 'processing';
        this.processing = true;
        this.sendSDMessage({ action: 'init-ok', stun: this.stun, options: this.options });
        this.initWebrtc();
        break;
      case 'signaling-answer':
        await this.onSignalingAnswer(data);
        break;
      case 'signaling':
        await this.onSignalingData(data);
        break;

      //webrtc datachannel 数据
      case 'captchaDone':
        this.ioClient?.disconnect();
        this.status = 'done';
        break;
      case 'fillCaptcha':
        this.ioClient?.disconnect();
        this.status = 'done';
        this.captchaCode = data.code;
        break;
    }
  }

  private async onChannelData(data: any) {
    if (typeof data == 'string') {
      data = JSON.parse(data);
      await this.onSDData(data);
    }
  }

  private sendSDMessage(message: any) {
    if (this.dataChannel) {
      this.dataChannel.send(JSON.stringify(message));
    } else {
      this.ioClient.emit('publish-message', message);
    }
  }

  private initWebrtc() {
    this.pc = new RTCPeerConnection(this.rtcConfiguration);
    this.pc.onicecandidate = (event: any) => {
      if (event.candidate) {
        this.sendSDMessage({ action: 'signaling', candidate: event.candidate });
      }
    };
    this.pc.onnegotiationneeded = async () => {
      const offer = await this.pc!.createOffer();
      try {
        await this.pc!.setLocalDescription(offer);
      } catch (e) {
        console.error(e);
      }
      this.sendSDMessage({ action: 'signaling-offer', sdp: this.pc.localDescription });
    };
    this.pc.oniceconnectionstatechange = () => {
      let iceConnectionState = this.pc!.iceConnectionState;
      if (
        iceConnectionState === 'failed' ||
        iceConnectionState === 'disconnected' ||
        iceConnectionState === 'closed'
      ) {
        console.log(iceConnectionState);
        this.onPCDisconnected();
      }
    };

    let dataChannel = this.pc.createDataChannel('rpaCaptcha');
    dataChannel.onopen = () => {
      this.dataChannel = dataChannel;
      this.sharePage();
    };
    dataChannel.onmessage = async (event: any) => {
      await this.onChannelData(event.data);
    };
    dataChannel.onclose = (ev) => {
      console.error(ev);
    };
  }

  private async onSignalingAnswer(data: any) {
    await this.pc.setRemoteDescription(new RTCSessionDescription(data.sdp));
  }

  private async onSignalingData(data: any) {
    if (data.candidate) {
      // Add the new ICE candidate to our connections remote description
      await this.pc.addIceCandidate(data.candidate);
    }
  }

  async _attackShopWindow() {
    if (!this.browserWinId) {
      let bw = ipcRenderer.sendSync('get-shop-browserWinId', {
        browserTitle: this.options.browserTitle,
      });
      if (bw.success) {
        this.browserWinId = bw.browserWinId;
      } else {
        console.error(`获取窗口 ${this.options.browserTitle} 失败`);
      }
    }
  }

  private async getShopStream(): Promise<MediaStream> {
    let shopStream = await navigator.mediaDevices.getUserMedia({
      audio: false,
      video: {
        // @ts-ignore
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: this.browserWinId,
          maxFrameRate: 10,
        },
      },
    });
    let preview = document.createElement('video');
    preview.srcObject = shopStream;
    return new Promise(
      (resolve) =>
        (preview.onloadedmetadata = () => {
          preview.srcObject = null;
          resolve(shopStream);
        }),
    );
  }

  private async sharePage() {
    await this._attackShopWindow();
    if (!this.browserWinId) {
      this.status = 'error';
      this.msg = '查找会话窗口失败';
      return;
    }
    let shopStream = await this.getShopStream();
    shopStream.getTracks().forEach((track: any) => {
      try {
        this.pc!.addTrack(track, shopStream);
      } catch (e) {
        console.error(e);
      }
    });
  }

  checkCaptcha() {
    return { status: this.status, msg: this.msg, captchaCode: this.captchaCode };
  }
}

let roomName = 'undefined';
console.log('run here');
let captcha = new RemoteCaptcha();
console.log(captcha);

const nodeLog = (msg: any) => {
  ipcRenderer.send(`rpa-${roomName}-log`, msg);
};

ipcRenderer.on('rpa-channel', async (evt, message) => {
  let cb = message.cb;
  let data = null;
  try {
    switch (message.action) {
      case 'roomName':
        roomName = message.data;
        console.log('roomName=' + roomName);
        break;
      case 'captcha':
        data = await captcha.captcha(message.data);
        break;
      case 'checkCaptcha':
        data = captcha.checkCaptcha();
        break;
      default:
        throw '不支持的action:' + message.action;
    }
    if (cb) {
      ipcRenderer.send(`rpa-${roomName}-cb`, { cb, success: true, data });
    }
  } catch (e) {
    console.error(e);
    if (cb) {
      ipcRenderer.send(`rpa-${roomName}-cb`, { cb, success: false, data: String(e) });
    }
  }
});
