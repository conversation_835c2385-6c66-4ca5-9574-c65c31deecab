import {ipc<PERSON><PERSON><PERSON>} from 'electron';

//todo 参考这个链接转换模型 https://juejin.cn/post/7155685608977858567
//const detCustomModel = 'https://paddlejs.bj.bcebos.com/models/ocr_v2_det_new/model.json';
//const recCustomModel = 'https://paddlejs.bj.bcebos.com/models/ocr_v2_rec_320/model.json';
const detCustomModel = 'https://dl.szdamai.com/downloads/paddle_js/ch_PP-OCRv2_det_fuse_activation/model.json';
const recCustomModel = 'https://dl.szdamai.com/downloads/paddle_js/ch_PP-OCRv2_rec_fuse_activation/model.json';

class OCR {
  initPromise?: Promise<any>;

  async init() {
    if(this.initPromise) {
      return this.initPromise;
    }
    this.initPromise = new Promise(async (resolve, reject) => {
      try {
        const ocr: any = require('@paddlejs-models/ocr');
        //@ts-ignore
        window.ocr = ocr;
        //@ts-ignore
        await window.ocr.init(detCustomModel, recCustomModel).then(()=>{
          ipcRenderer.send(`ocr-helper-ready`);
          resolve(true);
        }).catch((e: any)=>{
          reject(e);
        });
        //@ts-ignore
        //await window.ocr.init();
      } catch (e) {
        reject(e);
      }
    });
  }

  async recognize(image: string) {
    await this.initPromise;

    let img: any = await new Promise((resolve, reject)=>{
      const img = new Image();
      img.src = image;
      img.onload = ()=>{
        resolve(img);
      }
      img.onerror = (e)=>{
        reject('加载图片失败');
      }
    });
    if(img.width > 512 || img.height > 512) {
      throw '为了避免过多消耗本地资源，暂不支持识别宽或高大于512像素的图片';
    }

    //@ts-ignore
    const res = await window.ocr.recognize(img);
    //重新组织一下结果
    let ret = [];
    if(res.text) {
      for(let i = 0; i < res.text.length; i++) {
        let text = res.text[i];
        let points = res.points[i];
        let box = [];
        for(let j = 0; j < points.length; j++) {
          let point = points[j];
          box.push([Math.round(point[0]), Math.round(point[1])]);
        }
        ret.push({
          text: text,
          box: box,
        });
      }
    }
    return ret;
  }

}

const img_ocr = new OCR();
document.addEventListener('DOMContentLoaded', async()=>{
  return img_ocr.init();
});

ipcRenderer.on('recognize-image', async(evt, message)=>{
  let recognizeId = message.recognizeId;
  try {
    let ret = await img_ocr.recognize(message.image);
    ipcRenderer.send('ocr-text-result', {
      recognizeId,
      success: true,
      ret: ret,
    });
  } catch (e: any) {
    console.error(e);
    e = e ?? 'unknown error';
    ipcRenderer.send('ocr-text-result', {
      recognizeId,
      success: false,
      message: e.message || e.toString(),
    });
  }
});
