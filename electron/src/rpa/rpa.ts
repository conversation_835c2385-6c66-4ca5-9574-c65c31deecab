import _ from 'lodash';
import { findNodeClass } from './nodes/index';
import { Header, NodeContext } from '@e/rpa/nodes/base';
import { RpaPlugin } from '@e/rpa/plugins/RpaPlugins';
import { ElementHandle } from 'donkey-puppeteer-core';
import { TaskContext } from '@e/rpa/context';
import { tryUseIframe } from '@e/rpa/utils';

export class RpaFlow {
  id!: number;
  teamId: number;
  rpaType: 'Browser' | 'Mobile';
  name!: string; //流程名称
  description?: string; //描述
  itemPolicy?: string; //流程分身添加策略

  config!: RpaConfig;

  constructor(flow: any) {
    this.id = flow.id;
    this.teamId = flow.teamId;
    this.rpaType = flow.rpaType;
    this.name = flow.name;
    this.description = flow.description;
    this.itemPolicy = flow.itemPolicy;
  }

  parseConfig(json: any) {
    this.config = new RpaConfig(json);
  }
}

export class RpaConfig {
  id?: string;

  minorVersion?: number = 0;

  timeout = 0; //流程执行超时时间
  nodeInterval = 1; //节点间执行间隔
  nodeTimeout = 0; //每个节点执行超时时间
  nodeSim = true; //拟人化操作
  exitOnFail = undefined; //默认的如果遇到节点错误是否不再执行余下的节点

  redirectNodeErr = undefined; //将节点的错误日志重定向到哪里？debug | info | err | none:不输出，默认err

  windowSize: string;
  windowPosition?: string;

  events: RpaEvent[];

  plugins?: RpaPlugin[];

  params: Array<RpaParam> = []; //用户自定义变量

  attachments: Array<Attachment>; //流程附件
  elements: Array<Element>; //流程元素库
  scripts: Array<Script>; //脚本库

  //遇到alert等对话框时处理方式，为空表示取消，否则点确定。如果dialog是prompt，该字符串会当成值传递给prompt
  dialogHandling?: string;

  private _jsonNodes: any;

  prerunNode?: string;
  postrunNode?: string;

  //分身策略，动态添加还是初始时就指定
  itemPolicy: 'manually' | 'dynamic' = 'manually';
  //是否自动打开分身浏览器。兼容历史数据，为空或auto都表示自动打开
  browserPolicy: 'auto' | 'manually' = 'auto';
  //监听到浏览器关闭时的动作。ExitTask表示退出任务，Ignore表示忽略。为空表示ExitTask
  browserExitPolicy: 'ExitTask' | 'Ignore' = 'ExitTask';
  loadVideo = null;
  loadImage = null;
  imageForbiddenSize = 50;
  sidePanel = 'show';
  windowMinimizedPolicy: 'alert' | 'ignore' = 'alert';

  constructor(config: any) {
    this._jsonNodes = JSON.parse(JSON.stringify(config.nodes));
    _.forEach(this._jsonNodes, (n: any) => (n.fid = config.id));
    //转换子流程的节点。有个前提：所有流程的nid都不可以重复
    if (config.subFlows) {
      for (let sid in config.subFlows) {
        let subConfig = config.subFlows[sid];
        subConfig.nodes = subConfig.nodes;
        if (!subConfig.nodes || Object.keys(subConfig.nodes).length == 0) {
          subConfig.nodes = {
            header: {
              type: 'rpa.Header',
            },
          };
        }
        for (let nid in subConfig.nodes) {
          let node = subConfig.nodes[nid];
          node.fid = sid;
          if (nid === 'header') {
            //保存子流程的变量列表
            node.subParams = subConfig.params;
            //子流程第一个节点的nid改为子流程的sid
            this._jsonNodes[sid] = node;
          } else {
            this._jsonNodes[nid] = node;
          }
        }
        if (subConfig.type == 'prerun') {
          this.prerunNode = sid;
        } else if (subConfig.type == 'postrun') {
          this.postrunNode = sid;
        }
      }
    }
    if (typeof config.minorVersion === 'number') {
      this.minorVersion = config.minorVersion;
    }
    this.timeout = typeof config.timeout != 'undefined' ? config.timeout : this.timeout;
    this.nodeInterval =
      typeof config.nodeInterval != 'undefined' ? config.nodeInterval : this.nodeInterval;
    this.nodeTimeout =
      typeof config.nodeTimeout != 'undefined' ? config.nodeTimeout : this.nodeTimeout;
    this.nodeSim = _.isBoolean(config.nodeSim) ? config.nodeSim : this.nodeSim;
    this.exitOnFail = config.exitOnFail;
    this.redirectNodeErr = config.redirectNodeErr;
    this.windowSize = config.windowSize;
    this.windowPosition = config.windowPosition;
    this.dialogHandling = config.dialogHandling;
    this.loadVideo = config.loadVideo;
    this.loadImage = config.loadImage;
    this.imageForbiddenSize = config.imageForbiddenSize || 50;
    this.sidePanel = config.sidePanel || 'show';
    this.windowMinimizedPolicy = config.windowMinimizedPolicy || this.windowMinimizedPolicy;

    this.events = config.events || [];
    this._convertShadows(config.shadows || []);

    if (config.params) {
      for (let i = 0; i < config.params.length; i++) {
        let p = config.params[i];
        let param = new RpaParam(p);
        this.params.push(param);
      }
    }
    this.attachments = (config.attachments ?? []).map((a: any) => new Attachment(a));
    this.elements = (config.elements || []).map((e: any) => new Element(e));
    this.scripts = (config.scripts || []).map((s: any) => new Script(s));
    this.itemPolicy = config.itemPolicy || this.itemPolicy;
    this.browserPolicy = config.browserPolicy || this.browserPolicy;
    this.browserExitPolicy = config.browserExitPolicy || this.browserExitPolicy;
  }

  getNodes(nctx: NodeContext, headerNid?: string, endNid?: string) {
    if (!this._jsonNodes) {
      return new Header();
    }
    let nodes = JSON.parse(JSON.stringify(this._jsonNodes));
    headerNid = headerNid || 'header';
    let subParams = nodes[headerNid]?.subParams;
    let NodeClass = findNodeClass(undefined, headerNid, nodes);
    for (let i in nodes) {
      if (endNid && endNid === i) {
        nodes[i].next = 'rpa.control.ExecuteDebugEnd';
      }
    }
    let header = new NodeClass();
    header.fromJson(nctx, headerNid, nodes);
    //@ts-ignore
    header.subParams = header.subParams || subParams;
    return header;
  }

  private _convertShadows(shadows: DomShadow[]) {
    if (Array.isArray(shadows) && shadows.length > 0) {
      for (let i = 0; i < shadows.length; i++) {
        let shadow: DomShadow = shadows[i];
        let node: any;
        switch (shadow.type) {
          case 'input':
          case 'textarea':
            node = {
              type: 'rpa.event.Type',
              name: `元素映射${shadow.sid}`,
              iframe: shadow.iframe,
              props: {
                selector: shadow.selector,
                text: `{${shadow.sid}_text}`,
                clear: true,
              },
            };
            break;
          case 'button':
            node = {
              type: 'rpa.event.Click',
              name: `元素映射${shadow.sid}`,
              iframe: shadow.iframe,
              props: {
                selector: shadow.selector,
              },
            };
            break;
        }
        if (node) {
          this._jsonNodes[shadow.sid] = node;
          let event = new RpaEvent(`event_${shadow.sid}`, shadow.sid);
          this.events.push(event);
        }
      }
    }
  }
}

export class RpaParam {
  name: string; //不包含{}的变量名称
  label?: string; //如果不为空，ui展示的时候就展示该值而不是name
  type: string; //变量类型
  val: any;
  validVals?: any[]; //可选值，如果不为空ui展示为一个ComboBox
  predefine: boolean; //是否需要用户在执行之前就定义好
  required: boolean; //该输入变量是否必填。只有predefine=true的时候才有意义
  description?: string; //变量描述
  sensitive = false; //是否敏感字段

  constructor({
    name,
    type,
    val,
    predefine,
    required,
    description,
    label,
    validVals,
  }: {
    name: string;
    type: string;
    val: any;
    predefine: boolean;
    required: boolean;
    description?: string;
    label?: string;
    validVals?: any[];
  }) {
    this.name = name;
    this.type = type;
    this.val = val;
    this.label = label;
    this.validVals = validVals;
    if (this.type !== 'string') {
      try {
        this.val = JSON.parse(val);
      } catch (e) {}
    }
    this.predefine = predefine;
    this.required = required;
    this.description = description;
  }
}

//流程附件
export class Attachment {
  key!: string; //附件的key
  name!: string; //附件名称
  size?: number; //附件大小
  type?: string; //附件类型, 如 png, jpg, jpeg, xlsx
  constructor(at: any) {
    Object.assign(this, at);
  }
}

export class Script {
  id!: string;
  name!: string;
  content!: string;
  constructor(s: any) {
    Object.assign(this, s);
  }
}

export type ElementType = 'single' | 'repeat';
export class Element {
  //一个随机的id
  id!: string;
  //元素的类型
  type!: ElementType;

  name!: string;
  description?: string;

  iframe?: string;
  //元素的选择器
  selector!: string;
  //备选的selector，用于定位元素
  backupSelectors?: string[];
  xpath!: string;
  //优先使用何种方式定位元素，可选值: selector|xpath|none
  majorInspect!: string;
  //然后使用何种方式定位元素，可选值: selector|xpath|none
  minorInspect?: string;
  //预览图，可能是个http链接，base64url，或者是指向附件的key
  preview?: string;

  constructor(el: any) {
    Object.assign(this, el);
  }

  async $$(ctx: TaskContext): Promise<Array<ElementHandle>> {
    let frame = await tryUseIframe(ctx, this.iframe);
    if (!frame) {
      return [];
    }
    let result: Array<ElementHandle<any>> = [];
    if ('selector' == this.majorInspect) {
      //@ts-ignore
      result = await frame.$$(this.selector);
    } else if ('xpath' == this.majorInspect) {
      result = await frame.$x(this.xpath);
    }
    if (result.length === 0 && this.minorInspect) {
      if (['selector', 'xpath'].includes(this.minorInspect)) {
        result = await ('selector' == this.minorInspect
          ? frame.$$(this.selector)
          : frame.$x(this.xpath));
      }
    }
    return result;
  }

  async $FromParent(ctx: TaskContext, parent: ElementHandle): Promise<ElementHandle> {
    let xpath = this.xpath;
    if (xpath && xpath.charAt(0) == '/') {
      xpath = '.' + xpath;
    }
    let result: any[] = [];
    if (['selector', 'xpath'].includes(this.majorInspect)) {
      result = await ('selector' == this.majorInspect
        ? parent.$$(this.selector)
        : parent.$x(xpath));
    }
    if (!result || result.length == 0) {
      if (this.minorInspect) {
        if (['selector', 'xpath'].includes(this.minorInspect)) {
          result = await ('selector' == this.minorInspect
            ? parent.$$(this.selector)
            : parent.$x(xpath));
        }
      }
    }
    return (result || [])[0];
  }

  single() {
    return this.type === 'single';
  }
}

class DomShadow {
  sid!: string; // shadowId, 最终会动态生成一个 eventName=sid 的 RpaEvent
  name!: string;
  type!: string;
  iframe?: string;
  selector!: string;
  description?: string;
}

export class RpaEvent {
  name: string; //eventName
  header: string;
  params: Array<RpaParam> = [];
  description?: string;

  constructor(name: string, header: string) {
    this.name = name;
    this.header = header;
  }
}
