import _ from 'lodash';
import moment from 'moment';
import request, { RequestAgent } from '@e/services/request';
import { RpaFlow } from '@e/rpa/rpa';
import db from '@e/components/db';
import { Task } from '@e/rpa/task/task';
import logger from '@e/services/logger';
import { getChromium, getRpaFlowPreviewWindow } from '@e/utils/window';
import { dispatchMsg } from '@e/utils/ipc';
import { AjaxEventClient } from '@e/utils/AjaxEventClient';
import random from '@e/utils/random';
import { RpaPlugin } from '@e/rpa/plugins/RpaPlugins';
import { TikTok } from '@e/rpa/plugins/index';
import { VM } from 'vm2';
import { Common } from '@e/rpa/plugins/Common';
import { TikTokGH } from '@e/rpa/plugins/TikTokGH';
import { CRS } from '@e/rpa/plugins/CRS';
import { createRpaTask } from '@e/rpa/task/factory';
import { portalRpcClient } from '@e/components/backendTask';
import { popupMobileWins } from '@e/utils/ipc/mobile';

const previewFlowData: Record<number, Task | null> = {};
const executeTaskData: Record<number, Task | null> = {};

/**
 * 替换节点ID
 * @param nodes
 */
export function replaceNodesId(nodes: Record<string, any>) {
  const _nodes: any = {};
  const nodeIdMap: Record<string, string> = {};
  _.forEach(nodes, (nodeVo, nodeId) => {
    nodeIdMap[nodeId] = random.nextString(10);
  });
  // header 节点
  nodeIdMap.header = 'header';
  _.forEach(nodes, (nodeVo, nodeId) => {
    const vo = _.cloneDeep(nodeVo);
    if (vo.next && nodeIdMap[vo.next]) {
      vo.next = nodeIdMap[vo.next];
    }
    if (vo.props?.ifNode && nodeIdMap[vo.props?.ifNode]) {
      vo.props.ifNode = nodeIdMap[vo.props?.ifNode];
      if (vo.props?.elseNode && nodeIdMap[vo.props?.elseNode]) {
        vo.props.elseNode = nodeIdMap[vo.props?.elseNode];
      }
    } else if (vo.props?.afterNode && nodeIdMap[vo.props?.afterNode]) {
      vo.props.afterNode = nodeIdMap[vo.props?.afterNode];
    } else if (vo.props?.tryNode && nodeIdMap[vo.props?.tryNode]) {
      vo.props.tryNode = nodeIdMap[vo.props?.tryNode];
      if (vo.props?.catchNode && nodeIdMap[vo.props?.catchNode]) {
        vo.props.catchNode = nodeIdMap[vo.props?.catchNode];
      }
    } else if (vo.props.target && nodeIdMap[vo.props.target]) {
      vo.props.target = nodeIdMap[vo.props.target];
    }
    _nodes[nodeIdMap[nodeId]] = vo;
  });
  return _nodes;
}

async function fetchRefFlow(options: {
  teamId: number;
  flowId: number;
  subFlowId: string;
  refFlowId: number;
  name: string;
  subFlows: Record<string, any>;
  requestAgent: RequestAgent;
  refSubFlowsSource: Record<string, API.RpaConfig>;
  rpaConfig?: API.RpaConfig;
}) {
  const {
    teamId,
    flowId,
    subFlowId,
    refFlowId,
    name,
    subFlows,
    requestAgent,
    refSubFlowsSource,
    rpaConfig,
  } = options;
  try {
    const configData = refSubFlowsSource[refFlowId];
    if (configData) {
      // 被引用流程的主流程
      subFlows[subFlowId] = {
        name,
        nodes: replaceNodesId(configData.nodes ?? {}),
        ..._.pick(configData, ['params']),
      };
      const firstElm = configData.elements?.[0];
      if (firstElm && rpaConfig && !rpaConfig.elements?.some((elm) => elm.id === firstElm.id)) {
        rpaConfig.elements = (rpaConfig.elements ?? []).concat(configData.elements ?? []);
      }
      // 被引用流程的子流程
      const _subFlows = configData?.subFlows ?? {};
      for (const id in _subFlows) {
        const item = _subFlows[id];
        if (item.extra?.refFlowId) {
          await fetchRefFlow({
            teamId,
            flowId,
            subFlowId: id,
            refFlowId: item.extra?.refFlowId,
            name: item.name,
            subFlows,
            requestAgent,
            refSubFlowsSource,
            rpaConfig,
          });
        } else {
          subFlows[id] = item;
        }
      }
    }
  } catch (e) {
    logger.error('[APP] FetchRefFlow failed', e);
  }
}

/**
 * 把 rpaConfig 中引用流程的部分加载完
 * @param rpaConfig
 * @param teamId
 * @param flowId
 */
async function fillConfigRefFlow(
  rpaConfig: API.RpaConfig,
  teamId: number,
  flowId: number,
  requestAgent: RequestAgent,
) {
  const _rpaConfig = { ...rpaConfig };
  const obj: Record<string, any> = { ...(_rpaConfig.subFlows ?? {}) };
  const subFlows = _rpaConfig.subFlows ?? {};
  let refSubFlowsSource: Record<string, API.RpaConfig> = {};
  if (_.some(subFlows, (item) => !!item.extra?.refFlowId)) {
    refSubFlowsSource = await requestAgent.request(`/api/rpa/fetchFlowConfigTree/${_rpaConfig.id}`);
  }
  for (const subFlowId in subFlows) {
    const item = subFlows[subFlowId];
    if (item.extra?.refFlowId && item.extra?.refFlowId !== flowId) {
      await fetchRefFlow({
        teamId,
        flowId,
        subFlowId,
        refFlowId: item.extra?.refFlowId,
        name: item.name,
        subFlows: obj,
        requestAgent,
        refSubFlowsSource,
        rpaConfig: _rpaConfig,
      });
      if (obj[subFlowId]) {
        obj[subFlowId].type = item.type;
      }
    } else {
      obj[subFlowId] = item;
    }
  }
  _rpaConfig.subFlows = obj;
  return _rpaConfig;
}

async function fetchRpaPlugins(
  teamId: number,
  rpaFlowId: number,
  requestAgent: RequestAgent,
): Promise<RpaPlugin[]> {
  let plugins = await requestAgent.request(`/api/rpa/task/detectRpaPlugins`, {
    teamId,
    params: { rpaFlowId: rpaFlowId },
  });
  plugins = plugins || [];
  let result: RpaPlugin[] = [];
  let foundGh = false;
  for (let plugin of plugins) {
    const props = {
      teamId,
      requestAgent,
      ctx: plugin,
    };
    switch (plugin) {
      case 'tk':
        result.push(new TikTok(props));
        break;
      case 'gh':
        foundGh = true;
        result.push(new TikTokGH(props));
        break;
      case 'crs':
        result.push(new CRS(props));
        break;
      default:
        result.push(new Common(props));
    }
  }
  if (!foundGh) {
    //妆容xwh的旧团队流程
    result.push(
      new TikTokGH({
        teamId,
        requestAgent,
        ctx: 'tk/gh',
      }),
    );
  }
  return result;
}

const configCache: Map<string, API.RpaConfig> = new Map();
async function fetchFlowConfig(teamId: number, requestAgent: RequestAgent, rpaFlowConfigId: string) {
  if(!configCache.has(rpaFlowConfigId)) {
    const rpaConfig: API.RpaConfig = await requestAgent.request(
      `/api/rpa/flowConfig/${rpaFlowConfigId}`,
      {
        teamId,
      },
    );
    if(rpaConfig) {
      configCache.set(rpaFlowConfigId, rpaConfig);
    }
  }
  return configCache.get(rpaFlowConfigId)!;
}

/**
 * 任务计划
 */
type ScheduleJobProps = {
  ajaxEventClient: AjaxEventClient;
};
export class ScheduleJob {
  props: ScheduleJobProps;
  constructor(props: ScheduleJobProps) {
    this.props = props;
  }

  _getRequestAgent(evtData: Record<string, any>) {
    const obj: Record<string, any> = _.pick(evtData, ['teamId', 'userId']);
    if (evtData.sscToken) {
      obj.token = evtData.sscToken;
    }
    if (evtData.mstToken) {
      obj.mstToken = evtData.mstToken;
    }
    return new RequestAgent(obj);
  }

  start() {
    // RPA计划（自动调度）
    portalRpcClient.onAppEmit('rpa.plan.schedule.trigger', (data) => {
      const { token, teamId, rpaPlanId, targetAppId } = data;
      logger.info('[RPA] receive plan trigger(schedule)', {
        ...data,
        myAppId: db.getDb().get('uuid').value(),
      });
      if (!targetAppId || targetAppId === db.getDb().get('uuid').value()) {
        // 执行计划
        const requestAgent = this._getRequestAgent(data);
        execPlan(teamId, rpaPlanId, token, {}, requestAgent, this.props.ajaxEventClient);
      }
    });
    // RPA计划（手动执行）
    portalRpcClient.onAppEmit('rpa.plan.run.trigger', (data) => {
      const { token, teamId, rpaPlanId, targetAppId } = data;
      logger.info('[RPA] receive plan trigger(manual)', {
        ...data,
        myAppId: db.getDb().get('uuid').value(),
      });
      if (!targetAppId || targetAppId === db.getDb().get('uuid').value()) {
        const requestAgent = this._getRequestAgent(data);
        requestAgent
          .request(`/api/rpa/plan/manualRunPlan?token=${token}`, { method: 'POST', teamId })
          .then((taskVo: API.RpaTaskVo) => {
            execTask(taskVo, requestAgent, this.props.ajaxEventClient);
          });
      }
    });
    // RPA任务
    portalRpcClient.onAppEmit('rpa.task.run.trigger', (data) => {
      const { token, teamId, rpaFlowId, targetAppId } = data;
      logger.info('[RPA] receive task trigger', {
        ...data,
        myAppId: db.getDb().get('uuid').value(),
      });
      if (!targetAppId || targetAppId === db.getDb().get('uuid').value()) {
        logger.info('[RPA] task trigger', data);
        const requestAgent = this._getRequestAgent(data);
        // 创建任务
        requestAgent
          .request(`/api/rpa/task/runTask?token=${token}`, {
            teamId,
            method: 'POST',
          })
          .then((taskVo: API.RpaTaskVo) => {
            execTask(taskVo, requestAgent, this.props.ajaxEventClient);
          });
      }
    });
    portalRpcClient.onAppEmit('rpa.task.silent.run', async (data) => {
      const { teamId, rpaFlowId, rpaFlowConfigId, shopId, params } = data;
      logger.info('[RPA] receive task silent trigger', {
        ...data,
        myAppId: db.getDb().get('uuid').value(),
      });
      const requestAgent = this._getRequestAgent(data);
      const rpaConfig: API.RpaConfig = await fetchFlowConfig(teamId, requestAgent, rpaFlowConfigId);
      if (params) {
        rpaConfig.params = rpaConfig.params || [];
        for (let param of rpaConfig.params) {
          if (params.hasOwnProperty(param.name!)) {
            param.val = params[param.name!];
          }
        }
      }
      previewTask(teamId, rpaFlowId, shopId, rpaConfig, {}, requestAgent);
    });
    logger.info('[RPA] remote trigger listener started');
    return this;
  }

  stop() {
    portalRpcClient.removeAllListeners('rpa.plan.schedule.trigger');
    portalRpcClient.removeAllListeners('rpa.task.run.trigger');
    portalRpcClient.removeAllListeners('rpa.plan.run.trigger');
    logger.info('[RPA] remote trigger listener stopped');
    return this;
  }
}

/**
 * 执行计划
 */
export async function execPlan(
  teamId: number,
  rpaPlanId: number,
  token = '',
  rewriteParams = {},
  requestAgent: RequestAgent,
  ajaxEventClient: AjaxEventClient,
) {
  let initPlanSuccess = false;
  try {
    let planParams: Record<number, any> = await requestAgent.request(
      `/api/rpa/plan/${rpaPlanId}/params`,
      {
        teamId,
      },
    );
    const planVo: API.RpaPlanVo = await requestAgent.request(`/api/rpa/plan/${rpaPlanId}`);
    if (planVo.itemPolicy === 'dynamic') {
      planParams = {
        0: {
          ...planParams?.[0],
        },
      };
    }
    const date = moment().format('YYYYMMDD');
    const datetime = moment().format('YYYYMMDDHHmmss');
    const index = random.nextString(6);
    const name = planVo.taskNameEl
      ?.replace('{date}', date)
      .replace('{datetime}', datetime)
      .replace('{index}', index);
    const params = { ...planParams, ...rewriteParams };
    const taskVo: API.RpaTaskVo = await requestAgent.request(
      `/api/rpa/plan/run/${rpaPlanId}?token=${token}`,
      {
        teamId,
        method: 'POST',
        data: {
          name,
          params,
        },
      },
    );
    initPlanSuccess = true;
    execTask(taskVo, requestAgent, ajaxEventClient);
  } catch (e) {
    logger.info(`[RPA] execute rpa plan(${rpaPlanId}) failed`, e);
  }
  return initPlanSuccess;
}

/**
 * 执行任务
 * @param rpaTaskVo
 * @param requestAgent
 * @param ajaxEventClient
 */
export async function execTask(
  rpaTaskVo: API.RpaTaskVo,
  requestAgent: RequestAgent,
  ajaxEventClient?: AjaxEventClient,
) {
  const { id: taskId, teamId, flowId } = rpaTaskVo;
  let initTaskSuccess = false;
  try {
    const flowVo: API.RpaFlowVo = await requestAgent.request(`/api/rpa/flow/${rpaTaskVo.flowId}`, {
      teamId,
    });
    const rpaTaskItemVos: API.RpaTaskItemVo[] = await requestAgent.request(
      `/api/rpa/task/${taskId}/items`,
      {
        teamId,
      },
    );
    const rpaConfig: API.RpaConfig = await fetchFlowConfig(teamId!, requestAgent, rpaTaskVo.configId!);
    const _rpaConfig: API.RpaConfig = await fillConfigRefFlow(rpaConfig, teamId!, flowId!, requestAgent);
    let flow = new RpaFlow({
      id: rpaTaskVo.flowId,
      rpaType: rpaTaskVo.rpaType,
      name: flowVo.name,
      teamId: rpaTaskVo.teamId,
      description: flowVo.description,
    });
    flow.parseConfig({ ..._rpaConfig });

    _rpaConfig.plugins = ['{name:"test", version:1, test:()=>{return "a test message";}}']; //for debug
    flow.config.plugins = await parsePlugins({
      teamId: teamId!,
      rpaFlowId: flowId!,
      config: _rpaConfig,
      requestAgent,
    });

    let taskData = {
      ...rpaTaskVo,
      teamName: flowVo.teamName,
      preview: false,
      clientId: db.getDb().get('uuid').value(),
    };

    let task = createRpaTask({
      flow,
      //@ts-ignore
      task: taskData,
      _items: rpaTaskItemVos.filter((itemVo) => itemVo.status === 'NotStart'),
      requestAgent,
      ajaxEventClient,
    });
    executeTaskData[taskId!] = task;
    task.checkEngineVersion();
    task.execute();
    initTaskSuccess = true;
    if (ajaxEventClient) {
      let hd: string;
      ajaxEventClient
        .on('task-progress-changed', taskId, (status) => {
          if (status === 'Ended') {
            ajaxEventClient.un(hd);
          } else if (
            [
              'Ended',
              'UnusualEnded',
              'Cancelled',
              'Ended_Partial_Failed',
              'Ended_All_Failed',
            ].includes(status) &&
            !task.finished
          ) {
            stopExecTask(teamId!, taskId!);
          }
        })
        .then((res) => (hd = res));
    }
  } catch (e) {
    logger.info(`[RPA] execute task(${taskId}) failed`, e);
  }
  return initTaskSuccess;
}

/**
 * 停止执行任务
 * @param teamId
 * @param taskId
 */
export async function stopExecTask(teamId: number, taskId: number) {
  const task = executeTaskData[taskId];
  if (task) {
    try {
      task.stop();
      executeTaskData[taskId] = null;
      return true;
    } catch (e) {
      logger.error(`[RPA] stop task(${taskId}) failed`, e);
      return false;
    }
  }
}

/**
 * 预览
 * @param teamId
 * @param rpaFlowId
 * @param config
 * @param options
 */
export async function previewTask(
  teamId: number,
  rpaFlowId: number,
  shopId: number,
  config: any,
  options: {
    startNodeNid?: string;
    endNodeNid?: string;
    debugFlowIds?: string[];
    breakpoints?: {};
  },
  _requestAgent?: RequestAgent,
) {
  const requestAgent = _requestAgent ?? new RequestAgent();
  const flowVo: API.RpaFlowVo = await requestAgent.request(`/api/rpa/flow/${rpaFlowId}`, {
    teamId,
  });
  let flow = new RpaFlow({
    id: rpaFlowId,
    teamId: teamId,
    name: flowVo.name,
    rpaType: flowVo.rpaType,
    description: flowVo.description,
  });
  const _rpaConfig: API.RpaConfig = await fillConfigRefFlow(config, teamId, rpaFlowId, requestAgent);
  flow.parseConfig(_rpaConfig);
  flow.config.plugins = await parsePlugins({
    teamId,
    rpaFlowId,
    config: _rpaConfig,
    requestAgent,
  });

  let userId = db.getUserId();
  let taskData = {
    id: new Date().getTime(),
    teamId: teamId,
    teamName: flowVo.teamName,
    //@ts-ignore
    creatorId: userId,
    name: `${flowVo.name}_${moment().format('YYYYMMDDHHmmss')}`,
    forceRecord: false,
    snapshot: 'Not',
    rpaType: flowVo.rpaType,
    preview: true,
    clientId: db.getDb().get('uuid'),
    clientIp: '0.0.0.0',
    console: flowVo.console,
    ...options,
  };

  let task = createRpaTask({
    flow,
    //@ts-ignore
    task: taskData,
    _items: [
      {
        id: taskData.id,
        teamId,
        shopId,
      },
    ],
    requestAgent,
  });
  previewFlowData[rpaFlowId] = task;
  // 开始执行
  notifyPreviewStatus(rpaFlowId, 'running');
  task.checkEngineVersion();
  task.execute().then((forceStop: boolean) => {
    notifyPreviewStatus(rpaFlowId, 'stopped');
    if (previewFlowData[rpaFlowId] && !forceStop) {
      // 执行完毕
      previewFlowData[rpaFlowId] = null;
    }
  });
}

export async function runAiAgent(
  teamId: number,
  rpaType: 'Browser' | 'Mobile' | 'IOS',
  shopOrMobileId: number,
  prompt: string,
  pluginJsUrl?: string,
  onStep?: (msg: {
    mobileId: number;
    taskId: number;
    data: { step: number; memory?: string; goal?: string };
  }) => void,
) {
  let flow = new RpaFlow({
    id: shopOrMobileId,
    teamId: teamId,
    name: 'AIAgent',
    rpaType: rpaType,
    description: 'AIAgent',
  });
  flow.parseConfig({
    id: 'AIAgent',
    nodes: {
      header: {
        type: 'rpa.Header',
        next: 'MzNnSnaSJc',
      },
      MzNnSnaSJc: {
        type: 'rpa.tab.NodeScript',
        name: 'RPA引擎脚本',
        script: `(async()=>{\n  let result = await rpa.ai.run${rpaType}(${JSON.stringify(
          prompt,
        )}, ${JSON.stringify(pluginJsUrl)})\n  return result;\n})()`,
        timeout: 600,
      },
    },
    itemPolicy: 'manually',
    browserPolicy: 'auto',
    browserExitPolicy: 'ExitTask',
    sidePanel: 'hide',
    windowMinimizedPolicy: 'alert',
  });
  let userId = db.getUserId();
  let taskData = {
    id: new Date().getTime(),
    teamId: teamId,
    teamName: teamId,
    //@ts-ignore
    creatorId: userId,
    name: `AIAgent`,
    forceRecord: false,
    snapshot: 'Not',
    rpaType: rpaType,
    preview: true,
    clientId: db.getDb().get('uuid'),
    clientIp: '0.0.0.0',
  };
  let task = createRpaTask({
    flow,
    //@ts-ignore
    task: taskData,
    _items: [
      {
        id: taskData.id,
        teamId,
        shopId: shopOrMobileId,
      },
    ],
    requestAgent: new RequestAgent(),
  });
  previewFlowData[shopOrMobileId] = task;
  task.execute().then((forceStop: boolean) => {
    if (previewFlowData[shopOrMobileId] && !forceStop) {
      // 执行完毕
      previewFlowData[shopOrMobileId] = null;
    }
  });
  task.eventEmitter.on('aiStep', (step: { step: number; memory?: string; goal?: string }) => {
    if (rpaType === 'Browser') {
      const chromium = getChromium({ shopId: shopOrMobileId });
      chromium?.wsDispatcher.getClientByIdentify('plugin:aiAgent')?.send(
        JSON.stringify({
          action: 'aiAgent/step',
          data: step,
        }),
      );
    } else {
      onStep?.({
        mobileId: shopOrMobileId,
        taskId: taskData.id,
        data: step,
      });
    }
  });
  let ret = await new Promise((resolve) => {
    task.eventEmitter.on('itemEnd', (ret: any) => {
      resolve(ret);
    });
  });
  return ret;
}

async function parsePlugins(props: {
  teamId: number;
  rpaFlowId: number;
  config: any;
  requestAgent: RequestAgent;
}) {
  const { teamId, rpaFlowId, config, requestAgent } = props;
  let result: any[] = await fetchRpaPlugins(teamId, rpaFlowId, requestAgent);
  // config.plugins = ['{name: "test", version: 1, test: ()=>{return "a test message " + this.name}}', 'class HuaYoung { name="hy"; version=2.2; say(){return "hello, "+this.name} }'];
  if (config.plugins && Array.isArray(config.plugins) && config.plugins.length > 0) {
    for (let i = 0; i < config.plugins.length; i++) {
      let ps = config.plugins[i];
      let vm = new VM();
      let plugin = vm.run(`(()=>{return ${ps}})()`);
      if (
        typeof plugin === 'function' &&
        !!plugin.prototype &&
        plugin.prototype.constructor === plugin
      ) {
        plugin = new plugin();
      }
      result.push(plugin);
    }
  }
  return result;
}

export async function stopPreview(rpaFlowId: number, stopPostRun = false) {
  const task = previewFlowData[rpaFlowId];
  if (task) {
    previewFlowData[rpaFlowId] = null;
    task.stop();
    if (stopPostRun) {
      task.stopPostRun();
      notifyPreviewStatus(rpaFlowId, 'stopped');
    }
  }
}

export async function runDebugCommand(rpaFlowId: number, command: string, args: []) {
  const task = previewFlowData[rpaFlowId];
  if (task && task.getDebugger()) {
    let debug = task.getDebugger()!;
    switch (command) {
      case 'setBreakpoints':
      case 'setSkipAll':
      case 'addBreakpoint':
      case 'removeBreakpoint':
      case 'resume':
      case 'stepNext':
        //@ts-ignore
        return await debug[command].apply(debug, args);
        break;
      case 'getParams':
      case 'runNodeScript':
        //@ts-ignore
        let ret = await debug[command].apply(debug, args);
        return JSON.parse(JSON.stringify(ret));
    }
  }
}

export async function dispatchEvent(rpaFlowId: number, data: { eventName: string; params: any }) {
  const task = previewFlowData[rpaFlowId];
  if (task) {
    await task.dispatchEvent(data.eventName, data.params);
  }
}

function notifyPreviewStatus(rpaFlowId: number, status = 'running') {
  const win = getRpaFlowPreviewWindow(rpaFlowId);
  if (win) {
    dispatchMsg('rpa-flow-preview-status', { status }, win);
  }
}
