import fs from 'fs-extra';
import path from 'path';
import {BoundingBox, ElementHandle, Page, Target} from 'donkey-puppeteer-core';

import random from '../utils/random';
import {UnSerializableParam} from '@e/rpa/params/un_serializable';
import {TaskContext} from '@e/rpa/context';
import {Element} from '@e/rpa/rpa';

import moment from 'moment';
import crypto from 'crypto';
import safePromiseRace from '@e/rpa/safePromiseRace';
import fsutil from '@e/rpa/fs.utils';

const acorn = require('acorn');
const escodegen = require('escodegen');
const estraverse = require('estraverse');

export const noop = () => {};

export const waitMilliseconds = async (milliseconds: number, jitter = false) => {
  if (milliseconds && milliseconds > 0) {
    if (jitter) {
      //加上 (1/4 * x, 1000) 毫秒的抖动
      milliseconds += random.nextInt(0, Math.min(milliseconds / 4, 1000), random.nextBoolean());
    }
    if (milliseconds > Math.pow(2, 31) - 1) {
      throw new Error('等待时长超过限制');
    }
    return new Promise((resolve) => {
      setTimeout(() => resolve(1), milliseconds);
    });
  }
};

export const waitSeconds = async (seconds: number, jitter = false) => {
  if (seconds && seconds > 0) {
    return waitMilliseconds(1000 * seconds, jitter);
  }
};

export const delayExec = async (fn: () => any, milliseconds: number) => {
  if (milliseconds > 0) {
    await waitMilliseconds(milliseconds, false);
  }
  return fn();
};

/**
 * 等待某个区间的随机毫秒数
 * @param min, max
 */
export const waitRandom = async (min = 0, max = 1000) => {
  let milliseconds = random.nextInt(min, max);
  return waitMilliseconds(milliseconds, false);
};

/**
 * 等待一个promise
 * @param promise
 * @param seconds 超时时间 0表示永不超时
 */
export const waitPromise = async (promise: Promise<any>, seconds: number, err?: string) => {
  if (seconds && seconds > 0) {
    let timerHandler: any = 0;
    const timeoutPromise = new Promise((_, reject) => {
      timerHandler = setTimeout(reject, 1000 * seconds, err || 'timeout');
    });
    let race = safePromiseRace([promise, timeoutPromise]);
    ((r, timer) => {
      Promise.resolve(r)
        .catch(() => {})
        .finally(() => clearTimeout(timer));
    })(race, timerHandler);
    return race;
  }
  return promise;
};

export const deleteFolder = async (filePath: string) => {
  await fsutil.remove(filePath);
};

export const mkdirsSync = (dir: string) => {
  if (fs.existsSync(dir)) {
    return true;
  } else {
    let dirname = path.dirname(dir);
    if (dirname == dir) {
      throw '无法在指定路径创建文件或文件夹';
    }
    fs.mkdirsSync(dir);
  }
};

export const parseParamName = (paramName: string) => {
  if (/^\{([^{}]+)\}$/.test(paramName)) {
    paramName = RegExp.$1;
  }
  return paramName;
};

export const saveToParam = (params: any, saveTo: string, val: any, mustDefine = true) => {
  if (saveTo) {
    let key = parseParamName(saveTo);
    if (mustDefine && !params.hasOwnProperty(key)) {
      throw `不能赋值到一个未声明的变量 {${key}}`;
    }
    const propDescriptor = Object.getOwnPropertyDescriptor(params, key);
    let writeable = propDescriptor?.writable ?? true;
    if (writeable) {
      params[key] = val;
    }
  }
};

const getParamValue = async (paramName: string, params: any, asStr = false) => {
  //判断是不是一个合法的变量名，如果不是合法的变量名，直接原样返回
  if (/[\s=:,"'\\]/.test(paramName.trim())) {
    //包含空格,回车符等，不是变量名
    return `{${paramName}}`;
  }

  let tiers = paramName.trim().split('.');

  let val = params[tiers[0]];
  if (val && val instanceof UnSerializableParam) {
    return (val as UnSerializableParam).attr(tiers.slice(1).join('.'), asStr);
  }

  //普通变量
  if (tiers.length > 1) {
    val = params;
    /*
     * 下面的逻辑，如果遇到如下params {'a.b.c':1, a:{b:{c:2}}}
     * {a.b.c} 获取到的是1，即优先参数名当一个整体获取，其次才尝试递归对象获取
     */
    for (let i = 0; i < tiers.length; i++) {
      let wholeKey = tiers.slice(i).join('.');
      if (val.hasOwnProperty(wholeKey)) {
        val = val[tiers.slice(i).join('.')];
        break;
      }
      val = val[tiers[i]];
      if (typeof val == 'undefined') {
        break;
      }
    }
  }
  return !!asStr ? String(val) : val;
};

/**
 * 表达式替换，不支持执行 #js> 脚本
 * @param str
 * @param params
 */
export const evalParams = async (str: any, params: any, forceStr = false) => {
  if (str && typeof str === 'string') {
    if (!forceStr && /^\{([^{}]+)\}$/.test(str)) {
      //如果是单个变量，结果不转成字符串
      let paramName = parseParamName(str);
      return getParamValue(paramName, params);
    }
    let reg = /\{([^{}]+)\}/g;
    while (reg.test(str)) {
      let paramName = RegExp.$1;
      let val = await getParamValue(paramName, params, true);
      str = str.replace('{' + paramName + '}', val);
      reg.lastIndex -= paramName.length + 2 - val.length;
    }
  }
  return str;
};

export function comfortInteger(value: any) {
  if(typeof value === 'number') {
    return Math.floor(value);
  }
  if(typeof value === 'string' && /^[0-9]+(.[0-9]{2})?$/.test(value)) {
    return Math.floor(parseFloat(value));
  }
  return value;
}

/**
 * @desc Second-order Bessel curves
 * @param {number} t Current Percentage
 * @param {Array} p1 Starting point coordinates
 * @param {Array} p2 End point coordinates
 * @param {Array} cp Control Points
 */
export const twoBezier = (t: number, p1: number[], cp: number[], p2: number[]): number[] => {
  const [x1, y1] = p1;
  const [cx, cy] = cp;
  const [x2, y2] = p2;
  let x = (1 - t) * (1 - t) * x1 + 2 * t * (1 - t) * cx + t * t * x2;
  let y = (1 - t) * (1 - t) * y1 + 2 * t * (1 - t) * cy + t * t * y2;
  return [x, y];
};

/**
 * @desc Third-order Bessel curves
 * @param {number} t Current Percentage
 * @param {Array} p1 Starting point coordinates
 * @param {Array} p2 End point coordinates
 * @param {Array} cp1 First Control Points
 * @param {Array} cp2 Second Control Points
 */
export const threeBezier = (
  t: number,
  p1: number[],
  cp1: number[],
  cp2: number[],
  p2: number[],
): number[] => {
  const [x1, y1] = p1;
  const [x2, y2] = p2;
  const [cx1, cy1] = cp1;
  const [cx2, cy2] = cp2;
  let x =
    x1 * (1 - t) * (1 - t) * (1 - t) +
    3 * cx1 * t * (1 - t) * (1 - t) +
    3 * cx2 * t * t * (1 - t) +
    x2 * t * t * t;
  let y =
    y1 * (1 - t) * (1 - t) * (1 - t) +
    3 * cy1 * t * (1 - t) * (1 - t) +
    3 * cy2 * t * t * (1 - t) +
    y2 * t * t * t;
  return [x, y];
};

export const boundingBoxNew = async (eh: ElementHandle): Promise<BoundingBox | null> => {
  try {
    const model = await eh.boxModel();

    if (!model) {
      return null;
    }
    let leftPoint = model.content[0];
    return { x: leftPoint.x, y: leftPoint.y, width: model.width, height: model.height };
  } catch (ignored: any) {
    return null;
  }
};

export const boundingBox = async (eh?: ElementHandle | null): Promise<BoundingBox | null> => {
  if (!eh) {
    return null;
  }

  let box = await eh.boundingBox();
  if (!box) {
    box = await boundingBoxNew(eh);
  }

  return box;
};

export function getSprintTab(page: Page) {
  //__sprint_tab__ 是 SelectTab 时缓存到 page 的属性
  //@ts-ignore
  return page.__sprint_tab__;
}

export function isNormalPage(t: Target) {
  //@ts-ignore
  if(t.type() === 'page' && t.__proto__.constructor.name != 'OtherTarget' && !(t.url().startsWith('devtools://devtools'))) {
    return true;
  } else {
    return false;
  }
}

export async function getTargetPage(target: Target): Promise<Page | null> {
  return new Promise(async (resolve) => {
    let page = null;
    try {
      page = await waitPromise(target.page(), 2);
    } catch (e) {}
    resolve(page);
  });
}

/**
 * 尝试优先展示元素库的元素名称
 * @param expression
 * @param elements
 */
export function prefElementName(expression: any, elements: Array<Element>) {
  if (typeof expression === 'string' && /^element:\/\/([^\s]+)/.test(expression)) {
    let elementId = RegExp.$1;
    let element = (elements || []).find((e) => e.id === elementId);
    if (element) {
      return element.name + ' (' + element.selector + ')';
    } else {
      return '未知元素 ' + elementId;
    }
  }
  return expression;
}

export async function tryUseIframe(ctx: TaskContext, iframes?: string) {
  if (!iframes) {
    return ctx.page;
  }
  let waitEntry = ctx.page;
  let frames = (await ctx.evalParams(iframes)).split('>>');
  for (let i = 0; i < frames.length; i++) {
    await waitMilliseconds(688, false);
    let iframeSelector = frames[i].trim();
    let isXpath = /^\//.test(iframeSelector);
    let iframe = null;
    if (isXpath) {
      let ar = await waitEntry.$x(iframeSelector);
      iframe = await ar[0];
    } else {
      //@ts-ignore
      iframe = await waitEntry.$(iframeSelector);
    }
    if (iframe) {
      //@ts-ignore
      waitEntry = await iframe.contentFrame(); //fixme 某些网站 iframe.contentFrame() 返回空
      if (waitEntry) {
        //@ts-ignore
        waitEntry.__handler = iframe;
      }
    }
  }
  return waitEntry;
}

export async function getBoundingClientRect(ctx: TaskContext, el: ElementHandle, iframes?: string) {
  let rect = await el.evaluate((el) => el.getBoundingClientRect().toJSON());
  let ret = { x: rect.x, y: rect.y, width: rect.width, height: rect.height };
  if (iframes) {
    let waitEntry = ctx.page;
    let frames = (await ctx.evalParams(iframes)).split('>>');
    for (let i = 0; i < frames.length; i++) {
      let iframeSelector = frames[i].trim();
      let isXpath = /^\//.test(iframeSelector);
      let iframe = null;
      if (isXpath) {
        let ar = await waitEntry.$x(iframeSelector);
        iframe = await ar[0];
      } else {
        iframe = await waitEntry.$(iframeSelector);
      }
      if (iframe) {
        //@ts-ignore
        rect = await iframe.evaluate((el) => el.getBoundingClientRect().toJSON());
        ret.x += rect.x;
        ret.y += rect.y;
        //@ts-ignore
        waitEntry = await iframe.contentFrame(); //fixme 某些网站 iframe.contentFrame() 返回空
        if (waitEntry) {
          //@ts-ignore
          waitEntry.__handler = iframe;
        }
      } else {
        break;
      }
    }
  }
  return {
    x: Math.floor(ret.x),
    y: Math.floor(ret.y),
    width: Math.floor(ret.width),
    height: Math.floor(ret.height),
  };
}

export async function walkFiles(dir: string): Promise<string[]> {
  const subdirs: any = fs.readdirSync(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir: string) => {
      const res = path.resolve(dir, subdir);
      let stat = await fsutil.stat(res);
      return stat.isDirectory() ? walkFiles(res) : res;
    }),
  );
  //@ts-ignore
  return files.reduce((a, f) => a.concat(f), []);
}

/**
 * 子流程上下文处理
 * @param ctx
 * @param subParams
 * @param subParamsDefine
 */
export async function prepareSubParams(
  ctx: TaskContext,
  subParams: any[] = [],
  subParamsDefine?: any[],
): Promise<object> {
  //尝试将字符串转换成对象
  const convertValue = (p: any, v: any) => {
    if (v && typeof v == 'string') {
      v = v.trim();
      if (p.type != 'string') {
        //未显式声明为字符串
        try {
          v = JSON.parse(v);
        } catch (_) {}
      }
    }
    return v;
  };

  let userParamsObj: any = {};
  let predefines: any = {};
  for (let i = 0; i < subParams.length; i++) {
    let p = subParams[i];
    let v = convertValue(p, p.val);
    userParamsObj[p.name] = v;
    if (!!p.predefine) {
      // 是输入变量
      predefines[p.name] = p;
    }
  }
  if (subParamsDefine && subParamsDefine.length > 0) {
    for (let i = 0; i < subParamsDefine.length; i += 2) {
      let key = subParamsDefine[i];
      let p = predefines[key];
      if (p) {
        let val = await ctx.evalParams(subParamsDefine[i + 1]);
        if (val === subParamsDefine[i + 1]) {
          //不是 {} 也不是 #js>
          val = convertValue(p, val);
        }
        userParamsObj[key] = val;
      }
    }
  }
  return userParamsObj;
}

export class CacheRequest {
  value: any;
  private readonly request: () => any;
  life: number;

  time: number = 0;

  constructor(life: number, request: () => any) {
    this.life = life;
    this.request = request;
  }

  async get(): Promise<any> {
    if (Date.now() - this.time >= this.life) {
      this.value = await this.request();
      this.time = Date.now();
    }
    return this.value;
  }
}

export const allow_modules: any = {
  queryString: require('querystring'),
  moment: moment,
  lodash: require('lodash'),
  crypto: require('crypto'),
  axios: require('axios'),
  'form-data': require('form-data'),
  'socks-proxy-agent': require('socks-proxy-agent'),
  'https-proxy-agent': require('https-proxy-agent'),
};

export function compileScriptForNode(ctx: TaskContext, script: string) {
  if (!script) {
    return 'void(0)';
  }
  //脚本很短，不包含require，没必要编译也没必要缓存
  if (script.length < 'let a=require("a.js")'.length) {
    return `with(ctx.params){\n${script}\n}`;
  }
  const md5Hash = 'node_' + crypto.createHash('md5').update(script).digest('hex');
  let wrapScript = ctx.scriptCache.get(md5Hash);
  if (!wrapScript) {
    wrapScript = compileScript(ctx, true, script);
    wrapScript = `with(ctx.params){\n${wrapScript}\n\n}`;
    ctx.scriptCache.set(md5Hash, wrapScript);
  }
  return wrapScript!;
}

export function compileScriptForBrowser(ctx: TaskContext, script: string) {
  if (!script) {
    return '(()=>{})()';
  }
  if (typeof script === 'function') {
    return script;
  }
  const md5Hash = 'browser_' + crypto.createHash('md5').update(script).digest('hex');
  let wrapScript = ctx.scriptCache.get(md5Hash);
  if (!wrapScript) {
    wrapScript = compileScript(ctx, false, script);
    ctx.scriptCache.set(md5Hash, wrapScript);
  }
  return wrapScript!;
}

function compileScript(ctx: TaskContext, isNode: boolean, script: string): string {
  let ast = acorn.parse(script, { ecmaVersion: 12 });
  let ft = isNode ? false : functionType(ast);
  let modules: any = { index: 1, indexes: {}, contents: [] };
  let compiledScript = handleRequires(ctx, ast, modules);
  let keys = Object.keys(modules.indexes);
  let __rpa_modules__ = `
const __rpa_require__ = [];
const __rpa_node_require__ = (id)=>{
  if(typeof window === 'object') throw '模块 ' + id + ' 不存在';
  return rpa.require(id);
}
  `;
  if (keys.length > 0) {
    for (let i = 1; i < modules.contents.length; i++) {
      __rpa_modules__ += `__rpa_require__[${i}] = ${modules.contents[i]}\n`;
    }
    __rpa_modules__ += '\n\n';
  }
  if (isNode || !ft) {
    compiledScript = __rpa_modules__ + compiledScript;
  } else {
    ast = acorn.parse(compiledScript, { ecmaVersion: 12 });
    let modulesAst = acorn.parse(__rpa_modules__, { ecmaVersion: 12 });
    if ('ArrowFunctionExpression' == ft) {
      ast.body[0].expression.body.body = modulesAst.body.concat(ast.body[0].expression.body.body);
    } else if ('FunctionDeclaration' == ft) {
      ast.body[0].body.body = modulesAst.body.concat(ast.body[0].body.body);
    }
    compiledScript = escodegen.generate(ast);
  }
  return compiledScript;
}

function handleRequires(ctx: TaskContext, ast: any, modules: any): string {
  estraverse.replace(ast, {
    enter: (node: any) => {
      if (node.__visited) {
        return estraverse.VisitorOption.Skip;
      }
      if (
        node.type === 'CallExpression' &&
        node.callee.type === 'Identifier' &&
        node.callee.name === 'require'
      ) {
        let id = node.arguments[0].value || '';
        let childFun = undefined;
        let m = ctx.flow.config.scripts.find((s: any) => s.name === id);
        if (m) {
          //代码片段
          if (!modules.indexes[id]) {
            modules.indexes[id] = modules.index++;
            //代码片段
            let scriptContent = ctx.scriptCache.get(id);
            if (!scriptContent) {
              ctx.scriptCache.set(id, 'void(0)'); //先占位，避免无限递归调用
              scriptContent = '(()=>{module = {};\n';
              scriptContent += m.content || '';
              scriptContent += '\nreturn module.exports;\n})()';
              let scriptAst = acorn.parse(scriptContent, { ecmaVersion: 12 });
              scriptContent = handleRequires(ctx, scriptAst, modules);
              ctx.scriptCache.set(id, scriptContent);
            }
            modules.contents[modules.indexes[id]] = scriptContent;
          }
          childFun = acorn.parse(`__rpa_require__[${modules.indexes[id]}]`, {
            ecmaVersion: 2020,
            //@ts-ignore
          }).body[0].expression;
          childFun.__visited = true;
        } else if (allow_modules[id]) {
          // require 内置模块
          node.callee.name = '__rpa_node_require__';
        } else {
          childFun = acorn.parse(`(()=>{throw "模块 '${id}' 不存在";})()`, {
            ecmaVersion: 2020,
            //@ts-ignore
          }).body[0].expression;
          childFun.callee.__visited = true;
        }
        return childFun;
      }
    },
  });
  return escodegen.generate(ast);
}

function functionType(ast: any) {
  let body = ast.body;
  let statementType = body ? body[0].type : '';
  if ('ExpressionStatement' == statementType) {
    statementType = body[0].expression?.type || '';
  }
  switch (statementType) {
    case 'ArrowFunctionExpression':
    case 'FunctionDeclaration':
      return statementType;
  }
  return undefined;
}
