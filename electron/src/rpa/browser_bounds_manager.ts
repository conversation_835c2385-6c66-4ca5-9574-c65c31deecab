import { screen } from 'electron';

interface PosPlaceholder {
  left: number;
  top: number;
  width: number;
  height: number;
}

export interface BrowserBoundsHolder {
  value: () => PosPlaceholder;
  release: () => void;
}

/**
 * 用于管理 RPA task item 对应分身得窗口位置
 */
export class BrowserBoundsManager {
  rpaTaskVo: API.RpaTaskVo;
  posPlaceholders: PosPlaceholder[];
  placeholdersFlag: boolean[] = [];

  constructor(rpaTaskVo: API.RpaTaskVo) {
    this.rpaTaskVo = rpaTaskVo;
    this.posPlaceholders = this.calc();
    for (let i = 0; i < this.posPlaceholders.length; i++) {
      this.placeholdersFlag[i] = false;
    }
  }

  getTargetScreenBounds(displayId?: number) {
    let workArea = screen.getPrimaryDisplay().workArea;
    if (displayId) {
      const targetDisplay = screen.getAllDisplays().find((v) => v.id === displayId);
      if (targetDisplay) {
        workArea = targetDisplay.workArea;
      }
    }
    return workArea;
  }

  /**
   * 计算各个窗口位置
   */
  calc() {
    const posPlaceholders = [];
    const layoutConfig = JSON.parse(this.rpaTaskVo.layoutConfig ?? '{}');
    const concurrent = this.rpaTaskVo.concurrent ?? 1;
    const {
      layout,
      displayId,
      columns = 3,
      left = 0,
      top = 0,
      hs = 60,
      vs = 60,
      sizeMethod = 'auto',
      width = 640,
      height = 400,
    } = layoutConfig;
    const containerBounds = this.getTargetScreenBounds(displayId);
    let browserWidth = containerBounds.width;
    let browserHeight = containerBounds.height;
    if (layout === 'grid') {
      // 宫格布局
      browserWidth = Math.floor((containerBounds.width - left - hs * (columns - 1)) / columns);
      browserHeight = Math.floor(
        (containerBounds.height - top - vs * (Math.ceil(concurrent / columns) - 1)) /
          Math.ceil(concurrent / columns),
      );
    } else {
      // 对角线布局
      browserWidth = Math.floor(containerBounds.width - left - hs * (concurrent - 1));
      browserHeight = Math.floor(containerBounds.height - top - vs * (concurrent - 1));
    }
    const _width = sizeMethod === 'auto' ? browserWidth : width;
    const _height = sizeMethod === 'auto' ? browserHeight : height;
    let _left = 0;
    let _top = 0;
    let prevRight = containerBounds.x ?? 0 + left;
    let prevBottom = containerBounds.y ?? 0 + top;
    for (let index = 0; index < concurrent; index++) {
      if (layout === 'grid') {
        _left = prevRight + (index % columns === 0 ? 0 : hs);
        _top = prevBottom;
        if (index % columns === columns - 1) {
          //  代表下一个是每行的开始
          prevBottom = Math.min(
            containerBounds.y + containerBounds.height - 20,
            _top + _height + vs,
          );
          prevRight = Math.min(containerBounds.x + containerBounds.width - 20, left);
        } else {
          prevRight = _left + _width;
        }
      } else {
        _left = prevRight + (index === 0 ? 0 : hs);
        _top = prevBottom + (index === 0 ? 0 : vs);
        prevRight = _left;
        prevBottom = _top;
      }
      posPlaceholders.push({
        width: _width,
        height: _height,
        left: _left,
        top: _top,
      });
    }
    return posPlaceholders;
  }

  alloc(): BrowserBoundsHolder {
    const containerBounds = this.getTargetScreenBounds();
    const emptyIdx = this.placeholdersFlag.findIndex((v) => !v);
    if (emptyIdx !== -1) {
      this.placeholdersFlag[emptyIdx] = true;
    }
    return {
      value: () => this.posPlaceholders[emptyIdx] ?? this.posPlaceholders[0] ?? containerBounds,
      release: () => {
        if (emptyIdx !== -1) {
          this.placeholdersFlag[emptyIdx] = false;
        }
      },
    };
  }
}
