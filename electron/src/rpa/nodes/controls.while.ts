import {Node, NodeContext} from '@e/rpa/nodes/base';
import {SubFlowTaskContext, TaskContext} from '@e/rpa/context';
import {WalkDog} from '@e/rpa/dog';
import {findNodeClass} from '@e/rpa/nodes/index';
import {comfortInteger, saveToParam, waitSeconds} from '@e/rpa/utils';
import {ElementHandle} from 'donkey-puppeteer-core';
import {rpaLogger} from '@e/services/logger';
import {Condition} from '@e/rpa/node_condition';
import {ConditionNode} from '@e/rpa/nodes/controls';
import {ElementParam} from '@e/rpa/params/element_param';

//退出循环
class Break extends ConditionNode {
  type = 'rpa.control.Break';
  declare props: {
    type: 'always' | 'count' | 'conditions'; //always 直接退出； count 循环次数；conditions 条件
    runCount?: string; //循环固定次数退出，也是使用 breaks 的机制来实现
    relation: string; // 多个条件之间的关系 and | or

    breaks: Array<any>;
  };

  breaks: Array<Condition> = [];

  getConditions(): Array<Condition> {
    return this.breaks;
  }

  async exec(ctx: TaskContext): Promise<any> {
    let type = this.props.type || 'always';
    let flag = false;
    let whileNode: WhileNodes | undefined;
    let parent: TaskContext | undefined = ctx.parent;
    while (!!parent) {
      let node = parent.current.node;
      if (node instanceof WhileNodes) {
        whileNode = node as WhileNodes;
        break;
      }
      if (parent instanceof SubFlowTaskContext) {
        //不跨子流程
        break;
      }
      parent = parent.parent;
    }
    if (!whileNode) {
      throw '未能找到对应的循环节点';
    }
    if (type === 'always') {
      flag = true;
    } else if (type === 'count') {
      flag = await new Condition({
        operator: '>=',
        left: 'cons ' + whileNode.cycleTimes,
        right: this.props.runCount!,
      }).test(ctx, this);
    } else {
      //条件
      let and = this.props.type == 'conditions' ? this.props.relation !== 'or' : true;
      flag = await this.test(ctx, and);
    }
    if (flag) {
      this._affect(ctx);
    } else {
      this.log(ctx, '给定的条件未满足');
    }
  }

  _affect(ctx: TaskContext) {
    this.log(ctx, '满足退出循环的条件...');
    ctx.breakNode = this;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    if ('conditions' == this.props.type) {
      for (let i = 0; i < this.props.breaks?.length; i++) {
        this.breaks.push(new Condition(this.props.breaks[i]));
      }
    }
  }
}

//继续循环
class Continue extends Break {
  type = 'rpa.control.Continue';
  //@ts-ignore
  declare props: {
    type: 'always' | 'count' | 'conditions'; //always 直接continue； count 循环次数；conditions 条件
    runCount?: string; //循环固定次数退出，也是使用 continues 的机制来实现
    relation: string; // 多个条件之间的关系 and | or

    continues: Array<any>;
  };

  continues: Array<Condition> = [];

  getConditions(): Array<Condition> {
    return this.continues;
  }

  _affect(ctx: TaskContext) {
    this.log(ctx, '满足循环continue的条件...');
    ctx.continueNode = this;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    if ('conditions' == this.props.type) {
      for (let i = 0; i < this.props.continues.length; i++) {
        this.continues.push(new Condition(this.props.continues[i]));
      }
    }
  }
}

interface WhileProps {
  indexParam?: string; //计数器变量
  afterNode?: string; //循环体
}
export abstract class WhileNodes extends Node {
  declare props: WhileProps;

  afterNode?: Node;

  cycleTimes = 0; //循环次数
  _keepRun = true;

  //短时间之内循环次数过多，可能会造成客户端假死
  protected isDangerCycle = false;

  protected keepRun(ctx: TaskContext) {
    return ctx.keepRun && this._keepRun;
  }

  protected meetBreak(ctx: TaskContext): boolean {
    if (ctx.breakNode) {
      ctx.breakNode = undefined;
      this._keepRun = false;
      this.log(ctx, '循环被Break强制中断');
      return true;
    }
    return false;
  }

  protected meetContinue(ctx: TaskContext): boolean {
    if (ctx.continueNode) {
      ctx.continueNode = undefined;
      return true;
    }
    return false;
  }

  whileCtx: TaskContext | undefined;
  protected initSubContext(ctx: TaskContext) {
    this.whileCtx?.doDestroy();
    if (this.afterNode) {
      this.whileCtx = ctx.createWhileNodeTaskContext(this.nid);
    }
  }
  /**
   * 执行循环 nodes
   * @param ctx
   * @protected
   */
  protected async execAfterNode(ctx: TaskContext) {
    if (ctx.destroyed) {
      return;
    }
    if (this.afterNode && this.whileCtx) {
      if (this.props.indexParam) {
        saveToParam(ctx.params, this.props.indexParam, this.cycleTimes);
      }
      this.dog = new WalkDog(this.whileCtx, this.afterNode);
      await this.dog.walk();
      this.execRet = this.dog?.popRet().val;
    }
    await this.dangerCheck();
  }

  protected async dangerCheck() {
    if (this.isDangerCycle) {
      await waitSeconds(1);
    } else if (Date.now() - this.execTime < 60 * 1000 && this.cycleTimes > 4096) {
      //60秒之内执行了超过1024次，认为是无意义循环，减缓其执行速度
      this.isDangerCycle = true;
    }
  }

  protected isEmptyWhile(): boolean {
    if (this.afterNode) {
      let node: Node | undefined = this.afterNode;
      while (node) {
        if (!node.disabled) {
          return false;
        }
        node = node.next;
      }
    }
    return true;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    if (this.props.afterNode) {
      nctx.tiers.push(nid);
      let NodeClass = findNodeClass(this, this.props.afterNode, nodes);
      this.afterNode = new NodeClass();
      this.afterNode!.fromJson(nctx, this.props.afterNode, nodes);
      nctx.tiers.pop();
    }
  }

  gc() {
    super.gc();
    this.whileCtx?.doDestroy();
    this.whileCtx = undefined;
    this.afterNode = undefined;
  }

  release(ctx: TaskContext) {
    super.release(ctx);
    this.whileCtx?.doDestroy();
    this.whileCtx = undefined;
  }
}

//无限循环
class InfiniteWhile extends WhileNodes {
  type = 'rpa.control.InfiniteWhile';

  declare props: {
    loopTimes?: string; //循环次数，如果为空，则表示无限循环
  } & WhileProps;

  async exec(ctx: TaskContext): Promise<any> {
    this.cycleTimes = 0;
    this.isDangerCycle = false;
    this._keepRun = true;
    let end = Number.MAX_SAFE_INTEGER;
    if (typeof this.props.loopTimes !== 'undefined') {
      end = ~~(await ctx.evalParams(this.props.loopTimes));
    }
    if (this.isEmptyWhile()) {
      this.error(ctx, '检测到空循环，跳过执行。');
      return;
    }
    this.initSubContext(ctx);
    for (; this.cycleTimes < end && this.keepRun(ctx); this.cycleTimes++) {
      this.log(ctx, `正在执行第 ${this.cycleTimes + 1} 次循环`);
      // 执行循环 nodes
      await this.execAfterNode(ctx);
      if (this.afterNode && this.dog) {
        if (!this.dog.alive) {
          //子链已经被杀
          return;
        }
        if (this.meetBreak(ctx) || ctx.returnNode || this.whileCtx?.returnNode) {
          break;
        }
        if (this.meetContinue(ctx)) {
          continue;
        }
      }
    }
    this.log(ctx, `循环执行结束，共执行完整循环 ${this.cycleTimes} 次`);
  }
}

//@deprecated 循环分支，保留只为了旧有流程不报错
class While extends Node {
  type = 'rpa.control.While';

  static compatibleClass(nodeJson: any): any {
    let props = nodeJson.props || {};
    props.loopTimes = undefined;
    if (!!props.array) {
      return ArrayWhile;
    } else {
      return InfiniteWhile;
    }
  }

  async exec(ctx: TaskContext): Promise<any> {
    //已经改用其它节点实现
  }
}

//遍历数组循环
class ArrayWhile extends WhileNodes {
  type = 'rpa.control.ArrayWhile';

  declare props: {
    array: string;
    itemParam: string;
  } & WhileProps;

  async exec(ctx: TaskContext): Promise<any> {
    let array = await ctx.evalParams(this.props.array);
    if (!Array.isArray(array)) {
      throw '待遍历对象不是一个数组';
    }
    this.cycleTimes = 0;
    this.isDangerCycle = false;
    this._keepRun = true;
    if (this.isEmptyWhile()) {
      this.error(ctx, '检测到空循环，跳过执行。');
      return;
    }
    this.initSubContext(ctx);
    for (; this.cycleTimes < array.length && this.keepRun(ctx); this.cycleTimes++) {
      if (!!this.props.itemParam) {
        saveToParam(ctx.params, this.props.itemParam, array[this.cycleTimes]);
      }
      this.log(ctx, `正在执行第 ${this.cycleTimes + 1} 次循环`);
      // 执行循环 nodes
      await this.execAfterNode(ctx);
      if (this.afterNode && this.dog) {
        if (!this.dog.alive) {
          //子链已经被杀
          return;
        }
        if (this.meetBreak(ctx) || ctx.returnNode || this.whileCtx?.returnNode) {
          break;
        }
        if (this.meetContinue(ctx)) {
          continue;
        }
      }
    }
    this.log(ctx, `循环执行结束，共执行完整循环 ${this.cycleTimes} 次`);
  }
}

//次数循环
class NumberWhile extends WhileNodes {
  type = 'rpa.control.NumberWhile';

  declare props: {
    start: number;
    end: number;
    step: number;
    itemParam: string;
  } & WhileProps;

  async exec(ctx: TaskContext): Promise<any> {
    let start = comfortInteger(await ctx.evalParams(this.props.start));
    let end = comfortInteger(await ctx.evalParams(this.props.end));
    let step = comfortInteger(await ctx.evalParams(this.props.step));
    if (typeof start !== 'number' || typeof end !== 'number' || typeof step !== 'number') {
      throw '循环参数不是数字';
    }
    if (step === 0) {
      throw '递增值不能为0';
    }
    this.cycleTimes = 0;
    this.isDangerCycle = false;
    this._keepRun = true;
    if (this.isEmptyWhile()) {
      this.error(ctx, '检测到空循环，跳过执行。');
      return;
    }
    this.initSubContext(ctx);
    for (let i = start; i <= end && this.keepRun(ctx); this.cycleTimes++, i += step) {
      if (!!this.props.itemParam) {
        saveToParam(ctx.params, this.props.itemParam, i);
      }
      this.log(ctx, `正在执行第 ${this.cycleTimes + 1} 次循环`);
      // 执行循环 nodes
      await this.execAfterNode(ctx);
      if (this.afterNode && this.dog) {
        if (!this.dog.alive) {
          //子链已经被杀
          return;
        }
        if (this.meetBreak(ctx) || ctx.returnNode || this.whileCtx?.returnNode) {
          break;
        }
        if (this.meetContinue(ctx)) {
          continue;
        }
      }
    }
    this.log(ctx, `循环执行结束，共执行完整循环 ${this.cycleTimes} 次`);
  }
}

//键值对循环
class MapWhile extends WhileNodes {
  type = 'rpa.control.MapWhile';

  declare props: {
    target: string; //要遍历的对象
    keyParam: string; //用来保存遍历对象的每一次key的变量
    valueParam: string; //用来保存遍历对象的每一次value的变量
  } & WhileProps;

  async exec(ctx: TaskContext): Promise<any> {
    let target = await ctx.evalParams(this.props.target);
    let keys = Object.keys(target);
    this.cycleTimes = 0;
    this.isDangerCycle = false;
    this._keepRun = true;
    if (this.isEmptyWhile()) {
      this.error(ctx, '检测到空循环，跳过执行。');
      return;
    }
    this.initSubContext(ctx);
    for (let key of keys) {
      if (!this.keepRun(ctx)) {
        break;
      }
      let value = target[key];
      saveToParam(ctx.params, this.props.keyParam, key);
      saveToParam(ctx.params, this.props.valueParam, value);
      // 执行循环 nodes
      await this.execAfterNode(ctx);
      this.cycleTimes++;
      if (this.afterNode && this.dog) {
        if (!this.dog.alive) {
          //子链已经被杀
          return;
        }
        if (this.meetBreak(ctx) || ctx.returnNode || this.whileCtx?.returnNode) {
          break;
        }
        if (this.meetContinue(ctx)) {
          continue;
        }
      }
    }
  }
}

declare interface Element {
  selector: string; //拟提取元素的 selector 或 xpath
  saveTo: string; //保存至变量
}

//遍历当前页面的一组元素
class ElementsLoop extends WhileNodes {
  type = 'rpa.control.ElementsLoop';
  isPageNode = true;

  declare props: {
    mode: 'parent' | 'self';
    selector: string; //当 mode==parent，为父元素的selector，否则为子循环元素本身的selector
    saveTo: string; //循环元素保存到哪个变量
    elements: Array<string>; // [selector1, saveTo1, selector2, saveTo2, ...] 这种形式
  } & WhileProps;
  elements!: Array<Element>; //拟提取元素列表

  async exec(ctx: TaskContext): Promise<void> {
    this.cycleTimes = 0;
    this.isDangerCycle = false;
    this._keepRun = true;
    let selector = this.props.selector;
    let items;
    if (this.props.mode == 'parent') {
      let parent = await this.$(ctx, selector);
      const childNodesHandle = await parent!.evaluateHandle((element) =>
        Array.from(element.children),
      );
      const childNodes = await childNodesHandle.getProperties();
      items = Array.from(childNodes.values());
    } else {
      items = await this.$$(ctx, selector);
    }
    this.initSubContext(ctx);
    for (; this.keepRun(ctx) && this.cycleTimes < items.length; this.cycleTimes++) {
      this.log(ctx, `正在遍历第 ${this.cycleTimes + 1} / ${items.length} 个元素`);

      //@ts-ignore
      let item: ElementHandle = items[this.cycleTimes];
      let itemParam = await this.saveElementToParam(ctx, item, this.props.saveTo);
      this.debug(ctx, `遍历第${this.cycleTimes + 1}个循环元素`);
      if (!(await item.isIntersectingViewport())) {
        //如果当前元素不在可视区，将其移到屏幕中间
        await ctx.fakeAction.scrollIntoView(ctx.page, item, { top: 'middle' });
      }
      //* 提取所有的子元素
      for (let j = 0; j < this.elements.length; j++) {
        let element = this.elements[j];
        let subSelector = String(element.selector);
        let elHd;
        try {
          if (/^element:\/\/([^\s]+)/.test(subSelector)) {
            let elementId = RegExp.$1;
            let element = (ctx.flow.config.elements || []).find((e) => e.id === elementId);
            if (element) {
              elHd = await element.$FromParent(ctx, item);
            }
          } else {
            elHd = (await (itemParam! as ElementParam).$(subSelector))?.element;
          }
        } catch (e) {
          rpaLogger.error('[RPA] select element failed', e);
        }
        //@ts-ignore
        let elParam = await this.saveElementToParam(ctx, elHd, element.saveTo);
        if (!elParam) {
          this.debug(ctx, `第${j + 1}个拟提取元素未找到`);
        }
      }
      // 执行循环 nodes
      await this.execAfterNode(ctx);
      if (this.afterNode && this.dog) {
        if (!this.dog.alive) {
          //子链已经被杀
          return;
        }
        if (this.meetBreak(ctx) || ctx.returnNode || this.whileCtx?.returnNode) {
          break;
        }
        if (this.meetContinue(ctx)) {
          continue;
        }
      }
    } //整个遍历循环结束

    this.log(ctx, `遍历结束，共遍历 ${this.cycleTimes} 个元素`);
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    this.elements = new Array<Element>();
    for (let i = 0; i + 1 < this.props.elements.length; i += 2) {
      this.elements.push({
        selector: this.props.elements[i],
        saveTo: this.props.elements[i + 1],
      });
    }
  }
}

export const NodeTypes = {
  'rpa.control.Break': Break,
  'rpa.control.Continue': Continue,

  'rpa.control.While': While,
  'rpa.control.InfiniteWhile': InfiniteWhile,
  'rpa.control.ArrayWhile': ArrayWhile,
  'rpa.control.MapWhile': MapWhile,
  'rpa.control.NumberWhile': NumberWhile,
  'rpa.control.ElementsLoop': ElementsLoop,
};
