import type { LogFunctions } from 'electron-log';
import { Header, Node, PageNode } from '@e/rpa/nodes/base';
import { MobileScreenShot, Return, ScreenShot, WaitSelector } from '@e/rpa/nodes';
import { waitMilliseconds, waitPromise, waitSeconds } from '@e/rpa/utils';
import { rpaLogger } from '@e/services/logger';
import { ReturnValue, SubFlowTaskContext, TaskContext } from '@e/rpa/context';
import moment from 'moment';
import safePromiseRace from '@e/rpa/safePromiseRace';
import { rpa_types } from '@e/rpa/vm/vm.types';
import { ProtocolError } from 'donkey-puppeteer-core';

export class WalkDog {
  ctx: TaskContext;
  header: Node;
  rpaLogger: LogFunctions;

  /**
   * 执行结果。
   * 一定是 walk() 返回后才有结果
   */
  ret!: ReturnValue;
  popRet() {
    let ret = this.ret;
    //@ts-ignore
    this.ret = undefined;
    return ret;
  }

  //子链
  parent?: WalkDog;
  child?: WalkDog;

  //如果是false,说明该dog是被kill的
  alive = true;

  constructor(ctx: TaskContext, header: Node) {
    this.ctx = ctx;
    this.header = header;
    this.parent = ctx.parent?.current.dog;
    this.rpaLogger = rpaLogger.scope(`${ctx.item?.shopId}-${ctx.item?.id}`);
    if (this.parent) {
      this.parent.child = this;
    }
  }

  async walk() {
    let ctx = this.ctx;
    ctx.current = { node: new Header(), dog: this };
    if (this.parent) {
      //不将其节点带入
      //@ts-ignore
      ctx.current.ret = this.parent.ctx.params.prevResult;
    }
    try {
      let node: Node | undefined = this.header;
      if (node && node.type == 'rpa.Header') {
        node = node.next;
      }
      while (node && ctx.keepRun && this.alive) {
        if (ctx.breakNode || ctx.continueNode) {
          //如果有 breakNode 或 continueNode，跳出此执行链，直到遇到第一个循环节点来处理这个breakNode
          break;
        }
        if (!ctx.keepRun || !this.alive) {
          return;
        }

        if (node.disabled) {
          node = node.next;
          continue;
        }

        ctx.prev = ctx.current || undefined;
        ctx.current = {
          node: node,
          dog: this,
          ret: undefined,
        };
        if (ctx.prev?.ret) {
          // @ts-ignore
          ctx.params['prevResult'] = ctx.prev?.ret; //将上个节点返回值塞到 params 里
        } else {
          // @ts-ignore
          ctx.params['prevResult'] = { success: true };
        }
        // @ts-ignore
        ctx.params['flowNodeNid'] = node.nid; //将当前节点的nid塞到 params 里

        if (ctx.preview) {
          //如果有断点，等待调试
          await ctx.rdb?.meetNode(ctx, node);
          //有可能调试断点放过之后已经结束预览了
          if (!ctx.keepRun || !this.alive) {
            return;
          }
        }

        await this.waitInterval(ctx, node);
        if (!ctx.keepRun || !this.alive) {
          return;
        }
        node.walkLog && ctx.walkLog && ctx.log(`开始执行...`);
        await ctx.onBeforeExec(node);

        //执行节点之前先检查是否需要暂停
        await this.pauseIfNeeded(ctx);
        if (!ctx.keepRun || !this.alive) {
          return;
        }

        //执行当前节点的逻辑
        await this._execNode(node);

        //执行节点之后先检查是否需要暂停
        await this.pauseIfNeeded(ctx);
        if (!ctx.keepRun || !this.alive) {
          return;
        }

        if (node.logMsg && node.logMsg.trim().length > 0) {
          await ctx.nodeLog(
            node._owner || node,
            node.logLevel || 'info',
            await ctx.evalParams(node.logMsg, true),
          );
        }
        if (!ctx.keepRun || !this.alive) {
          return;
        }

        let success = ctx.currentSuccess();
        if (!success) {
          let tryCatch = ctx.currentTryCatch();
          if (
            tryCatch &&
            !ctx.returnNode &&
            !ctx.anchorNode &&
            node.type !== 'rpa.control.TryCatchCatcher'
          ) {
            tryCatch.log(ctx, '捕捉到异常，尝试执行异常处理机制节点');
            //@ts-ignore
            ctx.meetAnchor(tryCatch.catchNode);
          }
        }
        if (node.walkLog && ctx.walkLog && !ctx.returnNode && !ctx.anchorNode) {
          if (success) {
            await ctx.log('执行成功');
          }
        }
        await (success ? ctx.onNodeSuccess(node) : ctx.onNodeFailed(node));
        await ctx.onAfterExec(node, ctx.current?.ret!);

        //截屏
        if (ctx.snapshot == 'Node' || (!success && ctx.snapshot == 'OnFail')) {
          if (node.failShots >= 10) {
            await ctx.log('该节点已经截屏已超过10次，不再截屏');
          } else {
            const screenshot: ScreenShot | MobileScreenShot =
              'Mobile' === ctx.rpaType
                ? new MobileScreenShot().setOwner(node)
                : new ScreenShot().setOwner(node);
            const screenshotType = !!ctx.headless ? 'element' : 'window';
            screenshot.props = {
              type: screenshotType,
              selector: 'body',
              filename: `work_dir://snapshot.${moment().format('YYYYMMDDHHmmss')}_${node.runs}_${
                node.name || node.nid
              }.png`,
            };
            screenshot.fileType = 'Screenshot';
            await ctx.log('正在截屏...');
            try {
              try {
                await ctx.insurePage();
                await ctx.tryHandleDialog();
              } catch (ignore) {}
              await waitPromise(screenshot.exec(ctx), 15);
              await ctx.log('截屏成功');
              node.failShots = node.failShots++;
            } catch (e) {
              await ctx.log('截屏失败:' + e);
            }
          }
        }

        if (!success && !ctx.returnNode && !ctx.anchorNode) {
          //碰到 returnNode 不打印结束也不打印异常
          let exitOnFail = node.exitOnFail || 'none';
          switch (exitOnFail) {
            case 'none':
              await ctx.log('根据配置，当前节点执行失败流程仍将继续执行');
              break;
            case 'exit':
              await ctx.log('根据配置，当前节点执行失败将结束整个流程执行');
              ctx.keepRun = false;
              ctx.meetExit(ctx.current?.ret!);
              break;
            case 'return':
              await ctx.log('根据配置，当前节点执行失败将结束当前子流程执行');
              ctx.meetReturn(new Return());
              break;
          }
        }

        node.runs++;
        if (!ctx.keepRun || !this.alive) {
          return;
        }

        if (ctx.returnNode) {
          //碰到了Return节点，当前子流程结束执行
          return;
        }

        //如果有next，执行next
        node = node.next;
        if (ctx.anchorNode) {
          //被goto接管
          node = ctx.anchorNode;
          ctx.anchorNode = undefined;
        }
      }
    } finally {
      // 当前dog结束时，释放当前dog的锁
      if (ctx instanceof SubFlowTaskContext) {
        ctx.suspendLock.resume(ctx);
      }
      if (ctx.parent && ctx.parent.current) {
        ctx.parent.current.childRet = ctx.current?.ret;
      }
      let success = ctx.currentSuccess();
      let val = ctx.current?.ret?.val;
      let errMsg = ctx.current?.ret?.error;
      ctx.destroy();
      this.parent = undefined;
      // @ts-ignore
      this.ctx = undefined;
      this.ret = {
        success,
        val,
        error: errMsg,
      };
    }
  }

  kill() {
    if (this.child) {
      this.child.kill();
      this.child = undefined;
    }
    this.alive = false;
  }

  private async pauseIfNeeded(ctx: TaskContext) {
    //检查item是不是被暂停了
    while (ctx.keepRun && this.alive && ctx.paused) {
      await waitSeconds(1);
    }

    //检查是不是被其它线程暂停了
    if (ctx.suspendLock?.needWait(ctx.threadId)) {
      ctx.log('当前线程被暂停，等待...');
      // 其它线程暂停了所有其它线程
      await ctx.suspendLock.promise();
      ctx.log('当前线程恢复执行...');
    }
  }

  async _execNode(node: Node) {
    let ctx = this.ctx;
    let ret: ReturnValue = {
      success: true,
      val: undefined,
    };
    let lock;
    let isExist = node.type == 'rpa.control.Exit';
    try {
      if (node.isPageNode) {
        let sprintTab = await ctx.insurePage();
        if (!!sprintTab.crashed && (node as PageNode).caseCrash) {
          if (!ctx.sys.ignore_ERROR_PAGE_CRASHED) {
            throw rpa_types.ERROR_PAGE_CRASHED;
          }
        }
        await ctx.tryHandleDialog();
      } else if (node.isMobileNode) {
        if (!ctx.device?.isConnected()) {
          throw '手机已经断开连接';
        }
        ctx.device?.kickInUse();
      }
      if (node.mkNode) {
        lock = await ctx.fakeAction.lock();
      }
      node.execTime = Date.now();
      node.execRet = undefined;
      let execPromise = undefined;
      if ('Mobile' === ctx.rpaType && node.isPageNode) {
        throw '手机流程不支持执行浏览器流程节点';
      } else if ('Browser' === ctx.rpaType && node.isMobileNode) {
        throw '浏览器流程不支持执行手机流程节点';
      }
      if (node.isPageNode) {
        execPromise = this.preparePremiseSelector(ctx, node as PageNode).then(() => node.exec(ctx));
      } else {
        execPromise = node.exec(ctx);
      }
      let promises = [waitPromise(execPromise, node.timeout || ctx.nodeTimeout)];
      if (!isExist) {
        promises.push(ctx.keepRunWatchPromise || Promise.resolve());
      }
      await safePromiseRace(promises);
      ret.val = node.execRet;
      ret = ctx.current?.childRet ?? ret;
    } catch (e) {
      let message = typeof e === 'string' ? e : new String(e).toString();
      if (
        e instanceof ProtocolError &&
        message.indexOf(
          "Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.",
        ) != -1
      ) {
        message =
          '遇到无法恢复的错误，这很可能是操作系统过于繁忙或内存不足导致，具体异常信息为：' +
          message;
      }
      if (this.alive) {
        // 有可能当前节点还未执行完成，但是dog已经被kill了，这时候不处理异常
        this.rpaLogger.error(`${node.name}(${node.nid})`, e);
        ret.success = false;
        if (message == 'timeout') {
          ret.error = '等待超时';
        } else {
          ret.error = message;
        }
        ret.node = {
          type: node.type,
          name: node.name,
          props: JSON.parse(JSON.stringify(node.props)),
        };
        let redirectNodeErr = node.redirectNodeErr || 'err';
        let printFun = (ctx: TaskContext, msg: any) => {};
        switch (redirectNodeErr) {
          case 'err':
            printFun = node.error;
            break;
          case 'info':
          case 'log':
            printFun = node.log;
            break;
          case 'debug':
            printFun = node.debug;
            break;
        }
        await printFun.call(node, ctx, `执行失败：${ret.error}`);
      }
    } finally {
      node.execRet = undefined;
      lock?.release();
    }
    ctx.current && (ctx.current.ret = ret);
    node.killDog(); //有子链的节点由于超时等原因执行到这里，如果不killDog，子链不会停止运行
    try {
      node.release(ctx);
    } catch (_) {}
  }

  async preparePremiseSelector(ctx: TaskContext, node: PageNode): Promise<any> {
    let premiseSelector = (node as PageNode).premiseSelector;
    if (!!premiseSelector && premiseSelector.length > 0) {
      premiseSelector = premiseSelector.filter((ps) => !!ps);
      let waits = [];
      for (let i = 0; i < premiseSelector.length; i++) {
        let ps = premiseSelector[i];
        if (!!ps && !(await node.$(ctx, ps, false))) {
          waits.push(ps);
        }
      }
      if (waits.length > 0) {
        node.debug(ctx, '等待相关元素出现...');
        let waitProps: any = { selector: waits[0], waitForVisible: false, relation: 'all' };
        waitProps.extraSelector = [];
        for (let i = 1; i < premiseSelector.length; i++) {
          waitProps.extraSelector.push(premiseSelector[i]);
          waitProps.extraSelector.push(false);
        }
        return new WaitSelector()
          .setOwner(node)
          ._useProps(waitProps)
          .exec(ctx)
          .then(() => {
            node.debug(ctx, '已等到相关元素');
          });
      }
    }
  }

  async waitInterval(ctx: TaskContext, node: Node) {
    let interval = node.interval;
    if (typeof interval === 'undefined' || interval === null) {
      interval = ctx.nodeInterval;
    }
    interval = interval * 1000;
    if (interval === 0) {
      //避免While节点执行过快
      interval = 10;
    }
    await waitMilliseconds(interval, false);
  }
}
