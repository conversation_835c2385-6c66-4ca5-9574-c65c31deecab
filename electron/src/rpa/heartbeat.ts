
export class Heartbeat {
  fun: (times: number) => void;
  interval: number;

  private _times: number = 0;
  private _interval: any;

  private lastFire = 0;

  constructor(fun: (times: number) => void, interval: number) {
    this.fun = fun;
    this.interval = interval;
    if (interval <= 0) {
      throw 'interval must > 0';
    }
  }

  start() {
    this.stop();
    this._interval = setInterval(() => {
      let diff = Date.now() - this.lastFire;
      if(diff >= this.interval) {
        this.lastFire = Date.now();
        this.fun(this._times++);
      } else {
        //ignore current fire
      }
    }, this.interval);
  }

  stop() {
    clearInterval(this._interval);
    this._times = 0;
    this.lastFire = 0;
  }
}
