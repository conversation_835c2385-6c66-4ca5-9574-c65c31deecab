import path from 'path';
import chokidar from 'chokidar';
import wcmatch from 'wildcard-match';
import _ from 'lodash';
import request from '@e/services/request';
import logger from '@e/services/logger';
import db from '@e/components/db';
import fsutil from "@e/rpa/fs.utils";
import {portalRpcClient} from "@e/components/backendTask";

type FileWatcherProps = {
};

/**
 * 监听本地文件变更
 */
export class FileWatcher {
  props: FileWatcherProps;
  private fsWatchers: chokidar.FSWatcher[];
  constructor(props: FileWatcherProps) {
    this.props = props;
    this.fsWatchers = [];
  }

  start() {
    this._loadTriggers();
    portalRpcClient.onAppEmit('rpa.plan.trigger-changed', (evt) => {
        logger.info('[RPA] receive rpa.plan.trigger-changed event', evt);
        this._loadTriggers();
      });
    return this;
  }

  stop() {
    this._removeTriggers();
    portalRpcClient.removeAllListeners('rpa.plan.trigger-changed');
    return this;
  }

  _removeTriggers() {
    this.fsWatchers.forEach((fw) => {
      fw.close();
    });
    this.fsWatchers = [];
  }

  async _loadTriggers() {
    const triggers: API.RpaFileTriggerVo[] = await request(`/api/rpa/trigger/fileTriggers`, {});
    this._removeTriggers();
    for(let trigger of triggers) {
      if (trigger.diskType === 'LocalDisk' && trigger.realPath) {
        const dir = path.resolve(trigger.realPath);
        if (!(await fsutil.exists(dir))) {
          return;
        }
        const stat = await fsutil.stat(dir);
        const cwd = stat.isDirectory() ? dir : path.dirname(dir);
        const watcher = chokidar.watch(dir, {
          ignored: /(^|[\/\\])\../,
          ignoreInitial: true,
          ignorePermissionErrors: true,
          usePolling: false,
          depth: db.getDb().get('sysPres').value()?.fsWatchDepth || 3,
          cwd,
        });
        const callback = _.debounce(
          (eventType: string, p: string) => {
            const dirname = path.dirname(p);
            if (!trigger.includeSub && dirname !== '.') {
              return;
            }
            let matched = true;
            try {
              if (trigger.fileExt && !wcmatch(trigger.fileExt)(p)) {
                matched = false;
              }
            } catch (e) {
              // 通配符无效
            }
            if (!matched) return;
            logger.info(
              `[RPA] local file change(${eventType}) detected: ${path.resolve(
                cwd,
                p,
              )}, triggerId: ${trigger.id}`,
            );
            request(
              `/api/rpa/trigger/fileEvent?triggerId=${
                trigger.id
              }&eventType=${eventType}&file=${path.resolve(cwd, p)}`,
              { method: 'POST', teamId: trigger.teamId },
            );
          },
          5000,
          { leading: true, trailing: false },
        );
        if (trigger.watchCreate) {
          watcher.on('add', (p) => callback('Created', p));
          watcher.on('addDir', (p) => callback('Created', p));
        }
        if (trigger.watchDelete) {
          watcher.on('unlink', (p) => callback('Deleted', p));
          watcher.on('unlinkDir', (p) => callback('Deleted', p));
        }
        if (trigger.watchModify) {
          watcher.on('change', (p) => callback('Modified', p));
        }
        this.fsWatchers.push(watcher);
      }
    }
  }
}
