import logger from '@e/services/logger';
import { openShopWindow } from '@e/utils/window';
import { getAjaxEventClientIns, portalRpcClient } from '@e/components/backendTask';
import ShopSession from '@e/utils/shop-session';

/**
 * 任务计划
 */
type OpenAPiListenerProps = {};
export class OpenApiListener {
  props: OpenAPiListenerProps;
  constructor(props: OpenAPiListenerProps) {
    this.props = props;
  }

  start() {
    // 打开会话
    portalRpcClient.onAppEmit('openapi-shop-openSession', (data) => {
      const { accountId, ghost = false, ...restProps } = data;
      logger.info('[OPEN_API] receive openSession event', {
        ...data,
      });
      if (ghost) {
        // 不打开浏览器窗口，只创建通道
        this.createGhostSession(data);
      } else {
        openShopWindow(
          {
            shopId: accountId,
            ghost,
            ...restProps,
          },
          () => {},
        );
      }
    });
    logger.info('[RPA] OpenAPI listener started');
    return this;
  }

  async createGhostSession(data: any) {
    const { accountId, ghost = false, ...restProps } = data;
    const shopSession = new ShopSession({
      shopId: accountId,
      ghost,
      ...restProps,
    });
    try {
      await shopSession.init();
      await shopSession.openSession();
      // 监听会话关闭事件
      let ajaxEventClient = getAjaxEventClientIns();
      await ajaxEventClient?.once('shop-session-closed', shopSession.sessionId, (data) => {
        const { fromOpenapi = false } = data;
        logger.info(
          `[SESSION] received "shop-session-closed" event(by OpenAPI: ${fromOpenapi}), start closing tunnel (session:${shopSession.sessionId})`,
        );
        shopSession.tunnelRouter?.close();
      });
      await shopSession.initTunnelRouter();
      shopSession.tunnelRouter?.startHeartbeat();
      shopSession.sessionOpenReport();
    } catch (e) {
      shopSession.closeSession();
      logger.error('[OPEN_API] createGhostSession error', e);
    }
  }

  stop() {
    portalRpcClient.removeAllListeners('openapi-shop-openSession');
    logger.info('[RPA] OpenAPI listener stopped');
    return this;
  }
}
