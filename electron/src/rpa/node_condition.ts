import {TaskContext} from './context';
import {<PERSON><PERSON>, Node} from '@e/rpa/nodes/base';
import {UnSerializableParam} from '@e/rpa/params/un_serializable';
import {ElementParam} from '@e/rpa/params/element_param';

const comfortable = async (input: any, basicVal = true): Promise<any> => {
  if (typeof input != 'undefined') {
    //尽量尝试使用数字比较
    if (typeof input === 'string' && /^[0-9]+(.[0-9]{2})?$/.test(input)) {
      //@ts-ignore
      return parseFloat(input);
    }
    if ('false' === input || 'true' === input) {
      //@ts-ignore
      return 'true' === input;
    }
    //todo 日期格式?
  }
  if (basicVal && input instanceof UnSerializableParam) {
    input = await input.textContent();
    return comfortable(input, basicVal);
  }
  return input;
};

/**
 * 用来定义条件
 * 操作符目前支持 == === < <= > >= contains
 */

export class Condition {
  operator: string;
  iframe?: string;
  left: string;
  right: string;
  expression?: string;

  constructor({
    operator,
    left,
    right,
    iframe,
    expression,
  }: {
    operator: string;
    left: string;
    right: string;
    iframe?: string;
    expression?: string;
  }) {
    this.operator = operator;
    this.left = left;
    this.right = right;
    this.iframe = iframe;
    this.expression = expression;
  }

  async test(ctx: TaskContext, node: Node) {
    if (this.expression) {
      return Boolean(await ctx.evalParams(this.expression));
    }
    let basicVal = !(this.operator == 'isExist' || this.operator == 'isNotExist' || this.operator == 'isVisible');
    let left = await this._val(ctx, node, this.left);
    let right = await this._val(ctx, node, this.right);
    // 如果不是显式的字符串比较操作
    if (
      !['isEmpty', 'isNotEmpty', 'startsWith', 'notStartsWith', 'endsWith', 'notEndsWith', 'isTrue', 'isFalse'].includes(
        this.operator,
      )
    ) {
      left = await comfortable(left, basicVal);
      right = await comfortable(right, basicVal);
    }
    if (['contains', 'notContains'].includes(this.operator)) {
      if (typeof left == 'undefined' || left == null) {
        return false;
      }
      if (Array.isArray(left)) {
        return 'contains' === this.operator
          ? left.indexOf(right) != -1
          : left.indexOf(right) === -1;
      }
      return 'contains' === this.operator
        ? ('' + left).indexOf('' + right) != -1
        : ('' + left).indexOf('' + right) === -1;
    }
    switch (this.operator) {
      case '==':
        return left == right;
      case '===':
        return left === right;
      case '!=':
      case '!==':
        return left != right;
      case '<':
        return left < right;
      case '<=':
        return left <= right;
      case '>':
        return left > right;
      case '>=':
        return left >= right;
      case 'isExist':
        return typeof left != 'undefined' && left != null && left !== '';
      case 'isNotExist':
        return typeof left == 'undefined' || left == null || left === '';
      case 'isTrue':
        if(typeof left == 'string') {
          if('true' === left) {
            return true;
          } else if('false' === left) {
            return false;
          }
        }
        return !!left;
      case 'isFalse':
        if(typeof left == 'string') {
          if('true' === left) {
            return false;
          } else if('false' === left) {
            return true;
          }
        }
        return !left;
      case 'isEmpty':
        return left === '';
      case 'isNotEmpty':
        return left !== '';
      case 'startsWith':
        return typeof left === 'string' && left.startsWith(String(right));
      case 'notStartsWith':
        return typeof left === 'string' && !left.startsWith(String(right));
      case 'endsWith':
        return typeof left === 'string' && left.endsWith(String(right));
      case 'notEndsWith':
        return typeof left === 'string' && !left.endsWith(String(right));
      case 'isVisible':
        if(left === undefined || left === null) {
          return false;
        }
        if(left instanceof ElementParam) {
          return await (left as ElementParam).visible();
        }
        throw '是否可见只适用于网页元素';
    }
    return false;
  }

  async _val(ctx: TaskContext, node: Node, expr: string = '', basicVal = true) {
    //sele #searchButton | cons 1234 | para
    let result: any = '';
    if (expr) {
      let type = expr.substr(0, 4);
      expr = expr.substr(5);
      switch (type) {
        case 'cons': //常量
          result = expr;
          break;
        case 'para': //变量
          result = await ctx.evalParams(expr);
          break;
        case 'sele': //selector
          await ctx.insurePage();
          let temp = await ctx.evalParams(expr);
          if(temp && temp instanceof ElementParam) {
            result = temp;
          } else {
            let toolNode = new Header();
            toolNode._iframe = this.iframe;
            let el = undefined;
            try {
              //刷新的过程有概率出现异常
              el = await toolNode.$(ctx, expr, false);
            } catch (ignore) {}
            if (el) {
              result = new ElementParam('', el, toolNode.iframe);
            } else {
              //@ts-ignore
              result = undefined;
            }
          }
          break;
      }
    }
    return result;
  }

  toJson() {
    return {
      operator: this.operator,
      left: this.left,
      right: this.right,
    };
  }
}
