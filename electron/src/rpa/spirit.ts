import { MessageDispatcher } from '@e/components/messageDispatcher';

export interface MouseEventOptions {
  x?: number;
  y?: number;
  button?: 'left' | 'right' | 'middle';
}

export class RpaSpirit {
  ws!: MessageDispatcher;

  readonly mouse = Object.freeze({
    down: async (options: MouseEventOptions) => {
      return this.run('rpa/mouse/down', options);
    },

    up: async (options: MouseEventOptions) => {
      return this.run('rpa/mouse/up', options);
    },
  });

  async run(fun: string, data?: any): Promise<any> {
    if (this.ws) {
      return this.ws.rpaSpirit(fun, data);
    } else {
      throw '当前节点无法访问浏览器（请确保当前节点不位于初始/清理子流程）';
    }
  }

  appendLog(log: string) {
    if (this.ws) {
      this.ws.appendLogToRpaExt(log);
    }
  }

  toggleSidePanel(open: boolean) {
    if (this.ws) {
      const client = this.ws.getClientByIdentify('plugin:rpa');
      client?.send(
        JSON.stringify({
          action: open ? 'open-side-panel' : 'close-side-panel',
        }),
      );
    }
  }
}
