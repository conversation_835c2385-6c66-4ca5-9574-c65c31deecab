import {RequestAgent} from "@e/services/request";
import {portalRpcClient} from "@e/components/backendTask";
import {ChatRoom} from "@e/ws/interface";

/**
 * 通过LogWatcher其它客户端能够实时拿到流程执行的执行日志，该类是日志观察服务的Server
 * 客户端观察流程：
 * 3. 使用 watchItem 来观察某个具体的item的日志更新消息
 * 4. 为了避免不必要的数据发送，客户端可以使用 leaveItem 来取消观察某个item的日志更新消息
 * 5. 可以通过 fullLog 来获取某个item的完整日志
 */
export class LogWatcher {
  teamId: number;
  taskId: number;
  requestAgent!: RequestAgent;

  token!: string;

  room!: ChatRoom;

  clients: Map<string, number[]> = new Map();
  fullLogs: Map<number, string[]> = new Map();

  pausedStatusCache: any = [];

  constructor(teamId: number, taskId: number, requestAgent: RequestAgent) {
    this.teamId = teamId;
    this.taskId = taskId;
    this.requestAgent = requestAgent;
  }

  async emitLog(itemId: number, message: string) {
    let data = {
      action: 'emit-log',
      itemId,
      message,
    };
    if(!this.fullLogs.has(itemId)) {
      this.fullLogs.set(itemId, []);
    }
    let fullLog = this.fullLogs.get(itemId)!;
    fullLog.push(message);
    if(fullLog.length > 1024) {
      fullLog.shift();
    }

    this.clients.forEach((items, clientId: string) => {
      if(items.indexOf(itemId) >= 0) {
        this.room.sendMessage(data, clientId);
      }
    });
  }

  /**
   * item结束，清理日志，防止内存泄漏
   * @param itemId
   */
  async emitItemEnd(itemId: number) {
    this.fullLogs.delete(itemId);
  }

  async emitPausedStatus(items: any) {
    this.pausedStatusCache = items;
    let data = {
      action: 'emit-paused-status',
      items,
    };
    this.clients.forEach((items, clientId: string) => {
      this.room.sendMessage(data, clientId);
    });
  }

  async start() {
    if(!!this.room) {
      return;
    }
    this.room = portalRpcClient.joinChatRoom(`task_log_watch_${this.taskId}`);
    this.room.on('users-change', (event: any)=>{
      const fromUser = event.fromUser;
      if(fromUser != this.room.currentUser) {
        const eventName = event.event;
        if(eventName == 'join') {
          this.onUserJoin(fromUser);
        } else if(eventName == 'leave') {
          this.onUserLeave(fromUser);
        }
      }
    });
    this.room.on('message', (event: any)=>{
      const {fromUser, data} = event;
      this.onMessage(fromUser, data);
    });
  }

  async onUserJoin(fromUser: string) {
    if(!this.clients.has(fromUser)) {
      this.clients.set(fromUser, []);
      setTimeout(()=>{
        this.room.sendMessage({
          action: 'emit-watch-ready',
        }, fromUser);
      }, 1000);
      await this.emitPausedStatus(this.pausedStatusCache);
    }
  }

  async onUserLeave(fromUser: string) {
    if(this.clients.has(fromUser)) {
      this.clients.delete(fromUser);
    }
  }

  async onMessage(from: string, data: any) {
    let action = data.action || 'noop';
    switch (action) {
      case 'watchItem':
        if(this.clients.has(from)) {
          let items = this.clients.get(from)!;
          if(items.indexOf(data.itemId) == -1) {
            this.clients.get(from)!.push(data.itemId);
          }
        }
        break;

      case 'leaveItem':
        if(this.clients.has(from)) {
          let items = this.clients.get(from)!;
          let index = items.indexOf(data.itemId);
          if(index >= 0) {
            items.splice(index, 1);
          }
        }
        break;

      case 'fullLog':
        if(this.clients.has(from)) {
          let itemId = data.itemId;
          let fullLog = this.fullLogs.get(itemId) || [];
          this.room.sendMessage({
            action: 'emit-full-log',
            itemId,
            message: fullLog.join('\n'),
          }, from)
        }
        break;
    }
  }

  stop() {
    this.room?.leave();
  }

}
