import { BrowserWindow, ipcMain } from 'electron';
import appConfig from '@e/configs/app';
import path from 'path';
import logger from '@e/services/logger';
import db from '@e/components/db';

export class PauseHolder {
  taskId: number;

  all = false;

  pausedItems: Map<number, any> = new Map<number, any>();

  constructor(taskId: number) {
    this.taskId = taskId;
  }

  toEmitObj() {
    if (this.all) {
      return 'all';
    }
    return Array.from(this.pausedItems.keys());
  }

  pauseAll() {
    this.all = true;
    this.pausedItems.clear();
  }

  resumeAll() {
    this.all = false;
    this.pausedItems.clear();
  }

  pauseItem(item: number) {
    this.pausedItems.set(item, true);
  }

  resumeItem(item: number) {
    this.pausedItems.delete(item);
  }

  isItemPaused(item: number) {
    if (this.all) {
      return true;
    }
    return this.pausedItems.has(item);
  }
}

class RecognizerCallback {
  recognizeId: number;

  promise: Promise<any>;
  resolve!: (value: any) => void;
  reject!: (reason?: any) => void;

  constructor(recognizeId: number) {
    this.recognizeId = recognizeId;
    this.promise = new Promise((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
      setTimeout(() => {
        reject('recognize timeout');
      }, 60000);
    });
  }

  async waitFor() {
    return this.promise;
  }
}

let recognize_id_seed = 1;
let _textRecognizer: TextRecognizer | undefined;
export class TextRecognizer {
  initPromise?: Promise<any>;
  initResolve?: (value: any) => void;
  helperWindow!: BrowserWindow;

  recognizerCallbacks: Map<number, RecognizerCallback> = new Map<number, RecognizerCallback>();

  constructor() {
    const showHelper = db.getDb().get('SHOW_OCR_HELPER').value();
    this.initPromise = new Promise(async (resolve, reject) => {
      this.initResolve = resolve;
      try {
        this.helperWindow = new BrowserWindow({
          title: 'OCR Helper',
          show: !!showHelper,
          width: 400,
          height: 300,
          maximizable: false,
          fullscreenable: false,
          webPreferences: {
            devTools: appConfig.DEBUG,
            preload: path.join(__dirname, 'helper/ocr_helper.js'),
            nodeIntegration: true,
            // enableRemoteModule: true,
            webSecurity: false,
          },
        });
        if (showHelper) {
          this.helperWindow.webContents.openDevTools();
        }
        await this.helperWindow.loadFile(path.join(__dirname, './helper/index.html'));
      } catch (e) {
        reject(e);
      }
    });
    ipcMain.on(`ocr-helper-ready`, (evt) => {
      this.initResolve!(true);
    });
    ipcMain.on(`ocr-text-result`, (evt, result: any) => {
      let recognizeId = result.recognizeId;
      let callback = this.recognizerCallbacks.get(recognizeId);
      if (callback) {
        if (result.success) {
          callback.resolve!(result.ret);
        } else {
          callback.reject(result.message);
        }
      }
    });
  }

  async waitReady() {
    return this.initPromise;
  }

  currentJobPromise?: Promise<any>;
  async recognize(image: string) {
    this.heartbeat();
    if (this.currentJobPromise) {
      await this.currentJobPromise.catch(() => {});
    }
    this.currentJobPromise = new Promise(async (resolve, reject) => {
      await this.waitReady();
      let recognizeId = recognize_id_seed++;
      let callback = new RecognizerCallback(recognizeId);
      this.recognizerCallbacks.set(recognizeId, callback);
      this.helperWindow.webContents.send('recognize-image', {
        image: image,
        recognizeId: recognizeId,
      });
      callback
        .waitFor()
        .then((ret) => {
          resolve(ret);
        })
        .catch((e) => {
          reject(e);
        })
        .finally(() => {
          this.recognizerCallbacks.delete(recognizeId);
        });
    });
    try {
      return await this.currentJobPromise;
    } finally {
      this.currentJobPromise = undefined;
    }
  }

  idleTimeout: any;
  heartbeat() {
    clearTimeout(this.idleTimeout || 0);
    //ocr服务会占用2.5G左右的内存，5分钟未使用就销毁
    this.idleTimeout = setTimeout(() => {
      logger.info('destroy text ocr window for idle 5 minutes');
      this.destroy();
    }, 5 * 60 * 1000);
  }

  destroy() {
    this.helperWindow?.destroy();
    _textRecognizer = undefined;
  }
}

export const getTextRecognizer = () => {
  if (!_textRecognizer) {
    logger.info('init text ocr window');
    _textRecognizer = new TextRecognizer();
    _textRecognizer.heartbeat();
  }
  return _textRecognizer;
};
