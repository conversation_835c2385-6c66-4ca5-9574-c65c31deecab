const acorn = require('acorn');

/**
 * 包装脚本以通过puppeteer在浏览器里执行
 * @param script
 * @param params
 * @param args
 */
export const wrapper_script = (script, params, args) => {
  if (!script) {
    return 'undefined';
  }
  let isFun = false;
  if (typeof script === 'function') {
    isFun = true;
    script = script.toString();
  } else {
    script = script.toString().trim();
    let body = acorn.parse(script, { ecmaVersion: 12 })?.body;
    let statementType = body ? body[0].type : '';
    if ('ExpressionStatement' == statementType) {
      statementType = body[0].expression?.type || '';
    }
    switch (statementType) {
      case 'ArrowFunctionExpression':
      case 'FunctionDeclaration':
        isFun = true;
    }
  }
  let obj = {
    params: params,
    args: args,
    script: script,
  };
  let evalExpression = '';
  if (isFun) {
    evalExpression = `let ret = (new Function('return ' + script)())(ctx)`;
  } else {
    evalExpression = `let ret = eval('with(ctx.params){'+script+'}');`;
  }
  let result = `
((ctx) => {
  return new Promise(async (resolve, reject) =>{
    let script = ctx.script;
    delete ctx.script;
    try {
      ${evalExpression};
      if(ret instanceof Promise) {
        ret = await ret;
      }
      for (let key in ctx.params) {
        let val = ctx.params[key];
        if (val && !!val.__un_serializable_param__) {
          val = {};
        }
        try {
          JSON.stringify(val);
        } catch(_ignore){
          val = {};
        }
        ctx.params[key] = val;
      }
      resolve([ctx.params, ret]);
    } catch (e) {
      reject(e);
    }
  });
})((
  (ctx) => {
        try{
          for (let key in ctx.params) {
            let val = ctx.params[key];
            if (val && !!val.__un_serializable_param__) {
              let fun = eval(val.fun);
              val = fun();
              val.__un_serializable_param__ = true;
            }
            ctx.params[key] = val;
          }
        } catch(_ignore) {}
        return ctx;
      }
)(${JSON.stringify(obj)}))
`;
  return result;
};

export const scripts = {
  'rpa.tab.ScrollPage': (type, val) => {
    if (type === 'dom') {
      document.querySelector(val).scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
      let target = null;
      if (type === 'px') {
        target = window.scrollY + val;
      } else if (type === 'pos') {
        target = 'top' == val ? 0 : new Date().getTime();
      }
      window.scroll({
        top: target,
        behavior: 'smooth',
      });
    }
    return val;
  },

  'rpa.control.Manual.confirm': (options) => {
    console.log('options', options);
    let div = document.getElementById('rpa_manual_div');
    if (div) {
      div.remove();
    }
    div = document.createElement('div');
    div.setAttribute('id', 'rpa_manual_div');
    div.setAttribute(
      'style',
      'position:fixed;z-index:2147483647;left:50%;top:50%;width:450px;height:240px;margin-left: -187px;margin-top: -100px;line-height: 30px;text-align:left;border:1px solid #ddd;border-radius:5px;color:#333;background: rgba(255,255,255,0.8);box-shadow: 0 0 20px rgb(0 0 0 / 30%)',
    );
    const headDiv = document.createElement('div');
    headDiv.setAttribute(
      'style',
      'padding: 0 10px; margin-bottom: 24px; border-bottom: 1px solid #ddd; font-size: 14px; cursor: move;',
    );
    headDiv.innerText = '人工干预';
    headDiv.addEventListener('mousedown', (e) => {
      if (!e.target) return;
      const rect = e.target.getBoundingClientRect();
      const x = e.pageX - rect.x;
      const y = e.pageY - rect.y;
      function handleMouseMove(e) {
        if (div) {
          div.style.right = 'auto';
          div.style.left = e.pageX - x + 'px';
          div.style.top = e.pageY - y + 'px';
          div.style.marginLeft = '0px';
          div.style.marginTop = '0px';
        }
      }
      function removeListener() {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', removeListener);
      }
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', removeListener);
    });
    const icon = document.createElement('img');
    icon.setAttribute(
      'src',
      'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAYAAADimHc4AAAAAXNSR0IArs4c6QAABGxJREFUeF7tnc1y4yAMx+17+0TtTNu3ym27t7zVpjPtE7X3NGJNBjtgJBAfIvKpdTDY/x+SELbxPHW8PR++X5fTez1P88vmVO1v7u4T/DNP5w9n5+nz+Gj297jNPZ0UCH6e5j9WdOZzO1kwn8fHd+a6k6trDsAR3dejky8sduA8nf9CmdYwmgBoJXoISksYVQH0JrwPCMCoaRVVAEgQfgujFoiiACQKvwFhAndJiygG4Onw82+apqqBNRZ4U38vaQ3sAJZeD+IPt5UAwQrg+fD97ozjhwOwJHmsQZoFwAC+ntpZTl/HhzfqQf5RV2YtI7ucmDSXAP2WO82RZQH3LL6FkxsXkgHcg7+PWQAHhCQAKv4tmlRLIANQ8cN2kRITSADU58edEhUCCcDT4eccPwUtQYGABjDS1EKFLoLOE1AA1O/TkWGDchSAik8X3xmeRhO1KAD1++kA4Miv48Ouxrs/au/PE385ejceBAGo+Czim0r2RkVBAL2OemIm3anLDFqBF0DPvV8ogKAVeAF02ouMOUsFcLk967WCGwA9937hALxWcAOg594vHYDPClYAeu/9AwC4sQIFwDfaxNa0igUrAL27nxEsYHsNVwAS3A+2i/Vezk3MFEAbWlc3dAUgwf200apMqzafMQDU/ZQRea9W64YUQH3tbYvGDRkAvU68tdOmSssrAHqzvYrm60YgDsz6qEkD5ZcmIQ4AAFGPlO/d3JDWmUQCEDwdfWNq8OSEOAsYCQDMjs7SRkAKoF3MMi2PCEDUEFQBqAWwKgAxQC2AVVJaZQqAphd7aQXALimtQh2G0vRiL60A2CUlVaiJGEku/sIKgF9TfI06F4TXqkhJCwBWKhSzvMxImbACKNKv8ZWa+wHLPWEx2fBIFmBuSS4AxCwvNhCA1U15BYD3HCwl7XvE9rkgMYF4FAtYAZAUB0YBsHo0UVIcGAGAu4yB+3S0CDc0LAApbmgEAO41bN+Q6X40JB3AdhWV7Tti3buhoQFICMbSAWzP3/eidtdWIBmAbxGn0FIF3cYCyQB85x5arKNbK5AKILSEmbjlaoQ+nm4m3nyTSHsLNnVrBSyzYRUr2VvAb3fJMmlPTlfUFN1UbPVEXbQPLWVawVjMigKQ9tpPmkxljor1fmg1CgAKSXuPrIyctFox4qMBSMiQafIULx0c9WxbRlnAYgU6KkJyi/l9txo0AIWAU5+ycjrJBdnmNSiHQWD9frIFOBBEvdyN67t5pVLET7IAhXALKlX8LAA6PP0Pgurzk0dBIQO955iQK362BbhQ7mzeCD5zC9+UPOVFDmQmjG3kHjLmHH/v05GUB2BAjAyBw+Wwx4Cd2DDSUBU9tYDppNl5ALaRxRpeBH9Zm83XhzRjd0G+hgS6peLCW52qABCUvFUTvgkAFwT83dHnz6sL3xSA66bAPTWC0Uz0akEYG6w9llEicJukiSuBol5b0yCcerIwzeGOoC7vMwMY2GD/dltlpZcx+wcsigeFODLW1GuIHfcL5GLdciwenVYAAAAASUVORK5CYII=',
    );
    icon.setAttribute(
      'style',
      'float: left; margin-left: 32px; margin-right: 16px; width: 48px; height: 48px;',
    );
    const contentDiv = document.createElement('div');
    contentDiv.setAttribute('style', 'padding: 0 20px; font-size: 14px;');
    contentDiv.innerText = options?.subject || '人工操作是否已完成？';
    const descDiv = document.createElement('div');
    descDiv.setAttribute(
      'style',
      'padding: 0 20px; margin-left: 78px; font-size: 12px; color: #999;',
    );
    descDiv.innerText = options?.content || '完成后，RPA将继续执行后续流程';
    const btn = document.createElement('button');
    btn.setAttribute(
      'style',
      'position: absolute; right: 32px; bottom: 24px; height: 32px; padding: 4px 15px; line-height: 1; font-size: 14px; border-radius: 3px;text-align: center;color: #fff; border: 0; background: #0F7CF4;cursor: pointer;',
    );
    btn.innerText = '确认完成';
    div.appendChild(headDiv);
    div.appendChild(icon);
    div.appendChild(contentDiv);
    div.appendChild(descDiv);
    div.appendChild(btn);
    btn.addEventListener('click', (e) => {
      window.__hy_rpa_manual_finished__ = true;
      //@ts-ignore
      document.getElementById('rpa_manual_div').remove();
    });
    document.body.appendChild(div);
  },

  'rpa.util.el2selector': (node) => {
    let root = [];
    function calcOut() {
      let output = '';
      for (let i = root.length - 1; i >= 0; i--) {
        let elt = root[i];
        if (output) {
          output += ' > ';
        }
        output += elt.name;
        output += elt.attrs;
        if (typeof elt.nth === 'number') {
          output += `:nth-child(${elt.nth})`;
        }
      }
      return output;
    }

    function nthChild(input) {
      const parent = input.parentNode;
      if (!parent) {
        return null;
      }
      let child = parent.firstChild;
      if (!child) {
        return null;
      }
      let i = 0;
      while (child) {
        if (child.nodeType === 1) {
          i++;
        }
        if (child === input) {
          break;
        }
        child = child.nextSibling;
      }
      return i;
    }

    function retrieveNodeNameAndAttributes(node) {
      let output = '';
      let nodeName = node.nodeName.toLowerCase();
      let id = false;
      if (nodeName !== 'body' && node.hasAttributes()) {
        let attrs = node.attributes;
        for (let i = 0; i < attrs.length; i++) {
          attrs[i].value = attrs[i].value ? attrs[i].value.trim() : '';
          if (attrs[i].value && attrs[i].value.length < 24) {
            if (attrs[i].name === 'id') {
              let idStr = '#' + CSS.escape(attrs[i].value);
              output = idStr;
              if (document.querySelectorAll(idStr).length <= 1) {
                id = true;
              }
              break;
            } else if (attrs[i].name === 'class') {
              let classes = attrs[i].value.split(/\s+\b/).join('.');
              output += '.' + classes;
            } else {
              //output += '[' + attrs[i].name + "='" + attrs[i].value + "']";
            }
          }
        }
      }
      let elt = { name: nodeName, attrs: output };
      if (!id) {
        elt.nth = nthChild(node);
      }
      root.push(elt);
      if (nodeName === 'body' || id) {
        return;
      } else {
        let selector = calcOut();
        if (document.querySelectorAll(selector).length <= 1) {
          return;
        }
        retrieveNodeNameAndAttributes(node.parentNode);
      }
    }
    try {
      retrieveNodeNameAndAttributes(node);
    } catch (e) {
      return '#can_not_determine_selector';
    }
    return calcOut();
  },

  'rpa.util.findScrollFlag': () => {
    if (document.body['___rpa_scroll_invoke___']) {
      return;
    }
    document.body.addEventListener('mousemove', (e) => {
      window['___rpa_scroll_flag___'] = e.target;
    });
    document.body['___rpa_scroll_invoke___'] = true;
  },

  'rpa.data.SearchNode': (searchScope, text, filterFun, searchType, nodeType) => {
    // console.log('===>>>>>'+text);
    // console.log('===>>>>>'+filterFun);
    nodeType = nodeType || '*';
    if (filterFun) {
      filterFun = eval(filterFun);
    }

    function contains(nodeType, text) {
      function removeRelatives(nodes) {
        for (let i = nodes.length - 1; i >= 0; i--) {
          let element = nodes[i];
          if (element) {
            let parent = element.parentElement;
            while (parent) {
              for (let j = 0; j < nodes.length; j++) {
                if (nodes[j] === parent) {
                  nodes[j] = undefined;
                }
              }
              parent = parent.parentElement;
            }
          } // else already removed
        }
        return [...new Set(nodes.filter((node) => !!node))];
      }
      let elements = searchScope.querySelectorAll('*');
      elements = [].filter.call(elements, function (element) {
        if (
          [
            'HTML',
            'HEAD',
            'TITLE',
            'BODY',
            'DOCUMENT',
            'SCRIPT',
            'STYLE',
            'FORM',
            'TABLE',
            'TBODY',
            'TR',
          ].indexOf(element.nodeName) != -1
        ) {
          return false;
        }
        return RegExp(text).test(element.textContent);
      });
      elements = removeRelatives(elements);
      //如果5层以内是a标签，则返回a标签
      for (let i = 0; i < elements.length; i++) {
        let element = elements[i];
        if (element.nodeName === 'EM') {
          element = element.parentElement;
          elements[i] = element;
        }
        let parent = element.parentElement;
        let count = 0;
        while (parent && count < 5) {
          if (parent.nodeName === 'A') {
            elements[i] = parent;
            break;
          }
          parent = parent.parentElement;
          count++;
        }
      }
      elements = removeRelatives(elements);
      nodeType = nodeType && nodeType != '*' ? nodeType.toUpperCase() : undefined;
      return elements.filter((element) => {
        if (element) {
          if (!nodeType || element.nodeName == nodeType) {
            let visible = element.checkVisibility({
              checkOpacity: true, // Check CSS opacity property too
              checkVisibilityCSS: true, // Check CSS visibility property too
            });
            visible = visible && element.offsetWidth;
            if (visible && !element.offsetHeight) {
              let children = Array.from(element.children);
              let childHasHeight = false;
              for (let child of children) {
                if (child.offsetHeight) {
                  childHasHeight = true;
                  break;
                }
              }
              visible = childHasHeight;
            }
            return visible;
          }
        }
        return false;
      });
    }
    function getElementXPath(element) {
      if (element && !!element.id) {
        // 如果元素有id，则直接返回id路径
        return `//*[@id="${element.id}"]`;
      } else {
        if (element.parentElement) {
          // 如果没有id，则递归查找父节点，拼接成完整的xpath路径
          let xpathIndex = 1;
          let siblings = Array.from(element.parentElement.childNodes).filter(
            (n) => n.tagName == element.tagName,
          );
          if (siblings.length == 1) {
            return getElementXPath(element.parentElement) + `/${element.tagName.toLowerCase()}`;
          } else {
            for (let i = 0; i < siblings.length; i++) {
              let sibling = siblings[i];
              if (sibling === element) {
                return (
                  getElementXPath(element.parentElement) +
                  `/${element.tagName.toLowerCase()}[${xpathIndex}]`
                );
              }
              if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                xpathIndex++;
              }
            }
          }
        } else {
          // 如果没有父节点
          return `/${element.tagName.toLowerCase()}`;
        }
      }
    }

    let elements = contains(nodeType, text);
    if (filterFun) {
      let fes = [];
      for (let i = 0; i < elements.length; i++) {
        if (filterFun(elements[i])) {
          fes = [elements[i]];
          break;
        }
      }
      elements = fes;
    }
    if (elements && elements.length > 0) {
      if ('first' == searchType || 'custom' == searchType) {
        return getElementXPath(elements[0]);
      } else {
        return elements.map((element) => getElementXPath(element));
      }
    }
    return undefined;
  },
};
