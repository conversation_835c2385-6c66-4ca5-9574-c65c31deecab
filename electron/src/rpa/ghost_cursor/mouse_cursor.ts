import random from '../../utils/random';

const sqrt3 = Math.sqrt(3);
const sqrt5 = Math.sqrt(5);

/**
 * @param startX
 * @param startY
 * @param destX
 * @param destY
 * @param G_0 (重力因子)：控制鼠标从当前点向目标点移动的“拉力”大小。增大 G_0 会使鼠标移动趋向目标的速度加快，路径趋直；减小它则增加轨迹的随机性，使得鼠标在趋近目标时的拉力变弱，增加路径的起伏。
 * @param W_0 (随机扰动幅度上限)：确定扰动大小的最大值。随机扰动用于模拟手动操作中随机的小偏移。增加 W_0 会导致更大的抖动效果，从而使轨迹看起来更不规则。
 * @param M_0 (速度上限)：限制鼠标速度的最大值。较低的 M_0 会导致更慢且更分散的移动，增加模拟人类的慢速手动操作。较高的值则会加快移动速度，使得轨迹更流畅。
 * @param D_0 (扰动触发距离)：控制目标距离在 D_0 范围内时的扰动大小。越接近目标点，D_0 内的扰动就会减小，使得鼠标移动更平滑。
 * @param skipRate 建议1-4，值越大，生成的轨迹速度越快
 */
export function mouseTrajectory(
  startX: number,
  startY: number,
  destX: number,
  destY: number,
  G_0 = 7,
  W_0 = 3,
  M_0 = 25,
  D_0 = 12,
  skipRate = 1,    // 控制生成点的稀疏程度
  overshootDist = 5 // 超出距离
) {
  if(Math.abs(startX-destX) < 450 && Math.abs(startY-destY) < 10) {
    //水平短距离移动，通常是拖滑块验证码，速度慢点
    M_0 = 12
  }
  const result = [];
  let currentX = startX, currentY = startY;
  let vX = 0, vY = 0, W_x = 0, W_y = 0;

  const totalDistance = Math.hypot(destX - startX, destY - startY);
  const minEasingFactor = 0.2;
  let stepCounter = 0;

  while (Math.hypot(destX - startX, destY - startY) >= 0.5) {
    const dist = Math.hypot(destX - startX, destY - startY);
    const W_mag = Math.min(W_0, dist);

    const progress = (totalDistance - dist) / totalDistance;
    const easingFactor = Math.max(Math.sin(progress * Math.PI), minEasingFactor);

    if (dist >= D_0) {
      W_x = W_x / sqrt3 + (2 * Math.random() - 1) * W_mag / sqrt5;
      W_y = W_y / sqrt3 + (2 * Math.random() - 1) * W_mag / sqrt5;
    } else {
      W_x /= sqrt3;
      W_y /= sqrt3;
      if (M_0 < 3) {
        M_0 = Math.random() * 3 + 3;
      } else {
        M_0 /= sqrt5;
      }
    }

    vX += (W_x + G_0 * (destX - startX) / dist) * easingFactor;
    vY += (W_y + G_0 * (destY - startY) / dist) * easingFactor;

    const v_mag = Math.hypot(vX, vY);
    if (v_mag > M_0 * easingFactor) {
      const v_clip = (M_0 / 2 + Math.random() * M_0 / 2) * easingFactor;
      vX = (vX / v_mag) * v_clip;
      vY = (vY / v_mag) * v_clip;
    }

    startX += vX;
    startY += vY;

    const moveX = Math.round(startX);
    const moveY = Math.round(startY);

    if ((currentX !== moveX || currentY !== moveY) && stepCounter % skipRate === 0) {
      currentX = moveX;
      currentY = moveY;
      result.push({ x: currentX, y: currentY });
    }

    stepCounter++;
  }

  // 计算一个更明显的超出点
  const overshootAngle = Math.atan2(destY - startY, destX - startX);
  const overshootX = destX + random.nextInt(-overshootDist, overshootDist);
  const overshootY = destY + random.nextInt(-overshootDist, overshootDist);
  let overshootDone = false;

  // 移动到超出点
  while (!overshootDone) {
    const dist = Math.hypot(overshootX - startX, overshootY - startY);
    if (dist < 0.5) overshootDone = true;

    vX = (overshootX - startX) * 0.2;
    vY = (overshootY - startY) * 0.2;

    startX += vX;
    startY += vY;

    const moveX = Math.round(startX);
    const moveY = Math.round(startY);

    if (currentX !== moveX || currentY !== moveY) {
      currentX = moveX;
      currentY = moveY;
      result.push({ x: currentX, y: currentY });
    }
  }

  // 回到目标点
  let returnDone = false;
  while (!returnDone) {
    const dist = Math.hypot(destX - startX, destY - startY);
    if (dist < 0.5) returnDone = true;

    vX = (destX - startX) * 0.2;
    vY = (destY - startY) * 0.2;

    startX += vX;
    startY += vY;

    const moveX = Math.round(startX);
    const moveY = Math.round(startY);

    if (currentX !== moveX || currentY !== moveY) {
      currentX = moveX;
      currentY = moveY;
      result.push({ x: currentX, y: currentY });
    }
  }

  return result;
}



