const str_seed = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
const FONT_LIST = [
  'Andale Mono',
  'Arial',
  'Arial Black',
  'Arial Hebrew',
  'Arial MT',
  'Arial Narrow',
  'Arial Rounded MT Bold',
  'Arial Unicode MS',
  'Apple Chancery',
  'Apple Color Emoji',
  'Apple SD Gothic Neo',
  'AppleGothic',
  'Apple Symbols',
  'Apple Braille',
  'Bitstream Vera Sans Mono',
  'Book Antiqua',
  'Bookman Old Style',
  'Calibri',
  'Cambria',
  'Cambria Math',
  'Century',
  'Century Gothic',
  'Century Schoolbook',
  'Comic Sans',
  'Comic Sans MS',
  'Consolas',
  'Courier',
  'Courier New',
  'Garamond',
  'Geneva',
  'Georgia',
  'Helvetica',
  'Helvetica Neue',
  'Impact',
  'Lucida Bright',
  'Lucida Calligraphy',
  'Lucida Console',
  'Lucida Fax',
  'LUCIDA GRANDE',
  'Lucida Handwriting',
  'Lucida Sans',
  'Lucida Sans Typewriter',
  'Lucida Sans Unicode',
  'Microsoft Sans Serif',
  'Lucida Grande',
  'MS Serif',
  'Tahoma',
  'Trebuchet MS',
  'MYRIAD',
  'MYRIAD PRO',
  'Palatino',
  'Palatino Linotype',
  'Segoe Print',
  'Segoe Script',
  'Segoe UI',
  'Segoe UI Light',
  'Segoe UI Semibold',
  'Monaco',
  'Monotype Corsiva',
  'MS Gothic',
  'MS Outlook',
  'MS PGothic',
  'MS Reference Sans Serif',
  'MS Sans Serif',
  'Segoe UI Symbol',
  'Times',
  'Times New Roman',
  'Times New Roman PS',
  'Verdana',
  'Wingdings',
  'Wingdings 2',
  'Webdings',
  'Wingdings 3',
  'Microsoft Himalaya',
  'Microsoft JhengHei',
  'Microsoft Tai Le',
  'Microsoft Yi Baiti',
  'Microsoft YaHei',
];

/**
 * 浏览器字体检测
 */
class FontDetector {
  constructor() {
    this.baseFonts = ['monospace', 'sans-serif', 'serif'];
    const testString = 'mmmmmmmmmmlli';
    const testSize = '72px';
    this.h = document.getElementsByTagName('body')[0];
    this.s = document.createElement('span');
    this.s.style.fontSize = testSize;
    this.s.innerHTML = testString;
    this.defaultWidth = {};
    this.defaultHeight = {};
    for (var index in this.baseFonts) {
      this.s.style.fontFamily = this.baseFonts[index];
      this.h.appendChild(this.s);
      this.defaultWidth[this.baseFonts[index]] = this.s.offsetWidth;
      this.defaultHeight[this.baseFonts[index]] = this.s.offsetHeight;
      this.h.removeChild(this.s);
    }
  }

  detect(font) {
    let detected = false;
    const { h, s, baseFonts, defaultWidth, defaultHeight } = this;
    for (let index in this.baseFonts) {
      s.style.fontFamily = font + ',' + baseFonts[index];
      h.appendChild(s);
      var matched =
        s.offsetWidth != defaultWidth[baseFonts[index]] ||
        s.offsetHeight != defaultHeight[baseFonts[index]];
      h.removeChild(s);
      detected = detected || matched;
    }
    return detected;
  }

  getAvailableFonts() {
    const availableFonts = [];
    for (let f of FONT_LIST) {
      if (this.detect(f)) {
        availableFonts.push(f);
      }
    }
    return availableFonts;
  }
}

function findIP(onNewIP) {
  var myPeerConnection =
    window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
  var pc = new myPeerConnection({ iceServers: [{ urls: 'stun:stun.l.google.com:19302' }] }),
    noop = function () {},
    localIPs = {},
    ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g,
    key;

  function ipIterate(ip) {
    if (['127.0.0.1', '0.0.0.0'].includes(ip)) {
      return;
    }
    if (!localIPs[ip]) onNewIP(ip);
    localIPs[ip] = true;
  }

  if (!pc || !pc.createDataChannel || !pc.createOffer) {
    return;
  }
  pc.createDataChannel('');

  pc.createOffer(function (sdp) {
    sdp.sdp.split('\n').forEach(function (line) {
      if (line.indexOf('candidate') < 0) return;
      line.match(ipRegex).forEach(ipIterate);
    });
    pc.setLocalDescription(sdp, noop, noop);
  }, noop);

  pc.onicecandidate = function (ice) {
    if (
      !ice ||
      !ice.candidate ||
      !ice.candidate.candidate ||
      !ice.candidate.candidate.match(ipRegex)
    )
      return;
    console.log(ice.candidate.candidate);
    ice.candidate.candidate.match(ipRegex).forEach(ipIterate);
  };
}

const random = {
  /**
   * 获取 [min, max] 区间的随机数
   * @param min include
   * @param max include
   */
  nextInt: function (min, max) {
    if (max <= min) {
      return min;
    }
    return min + Math.floor(Math.random() * (max - min + 1));
  },

  nextFloat: function (min, max, bitLen = 4) {
    const base = Math.pow(10, bitLen);
    return this.nextInt(min * base, max * base) / base;
  },

  /**
   * 获取随机boolean值
   */
  nextBoolean: function () {
    return !!this.nextInt(0, 1);
  },

  /**
   * 生成一个随机字符串，长度 len
   * @param len
   */
  nextString: function (len = 16, seed = str_seed) {
    if (len <= 0 || !seed) {
      return '';
    }
    let result = '';
    while (len-- > 0) {
      result += seed.charAt(this.nextInt(0, seed.length - 1));
    }
    return result;
  },
};

const agent = await FingerprintJS.load();
const { components: cmp } = await agent.get();
function getBrowserType(userAgent) {
  const isOpera = userAgent.indexOf('Opera') > -1;
  // @ts-ignore
  const isIE = window.ActiveXObject || 'ActiveXObject' in window;
  const isEdge = userAgent.indexOf('Edge/') > -1 || userAgent.indexOf('Edg/') > -1;
  const isFF = userAgent.indexOf('Firefox') > -1;
  const isSafari = userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1;
  const isChrome = userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Safari') > -1 && !isEdge;
  if (isIE) {
    return 'IE';
  }
  if (isFF) {
    return 'Firefox';
  }
  if (isOpera) {
    return 'Opera';
  }
  if (isSafari) {
    return 'Safari';
  }
  if (isChrome) {
    return 'Chrome';
  }
  if (isEdge) {
    return 'Edge';
  }
  return 'Unknown';
}

const result = {};

result.userAgent = navigator.userAgent;
result.platform = cmp.platform.value;
result.browser = getBrowserType(result.userAgent);

result.computerName = `Localhost-${random.nextString(6, '0123456789')}${random.nextString(4)}`;
result.mac = 'unknown';

result.location_type = 'Auto';
result.location = 'unknown';
result.timezone_type = 'Auto';
result.timezone = cmp.timezone.value;
result.lang_type = 'Auto';
result.lang = navigator.language;
result.webrtcPublicIp_type = 'Auto';
result.webrtcPublicIp = '0.0.0.0';
result.webrtcInnerIp_type = 'Original';
result.webrtcInnerIp = '0.0.0.0';

result.screenSize_type = 'Auto';
result.screenSize = `${window.screen.width || '1920'}x${window.screen.height || '1080'}`;
result.cpu = cmp.hardwareConcurrency.value;
result.mem = cmp.deviceMemory.value ?? 2; // 不是 https 协议下，读取不到内存大小，给个默认值
result.touchSupported_type = 'Assign';
result.touchSupported = !!cmp.touchSupport.value?.maxTouchPoints;
result.colorGamut = cmp.colorGamut.value;
result.colorDepth = window.screen.colorDepth;

result.webgl_type = 'Assign';
try {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl');
  const debugInfo = gl?.getExtension('WEBGL_debug_renderer_info');
  result.webglVendor = gl?.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
  result.webglRenderer = gl?.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
} catch (e) {
  console.log('get webgl debug info failed', e);
}
result.batteryType = 'Assign';
try {
  // @ts-ignore
  navigator.getBattery().then((battery) => {
    result.batteryLevel = battery.level * 100;
    result.batteryCharging = battery.charging;
    result.batteryChargingTime = battery.chargingTime / 60;
    result.batteryDischargingTime = battery.dischargingTime / 60;
  });
} catch (e) {
  result.batteryType = 'Original';
  console.log('get battery info failed', e);
}
// canvas 噪音模式 - 文字坐标偏移
result.canvasMode = 'fillText';
result.canvasTX = random.nextFloat(-1, 1);
result.canvasTY = random.nextFloat(-1, 1);
result.canvasRX = random.nextFloat(-1, 1);
result.canvasRY = random.nextFloat(-1, 1);
// canvas 偏移量范围 0x000000 ~ 0x0f0f0f
result.canvas = Number(
  `0x${random
    .nextString(3, '0123456789ABCDEF')
    .split('')
    .map((n) => `0${n}`)
    .join('')}`,
);
// webgl噪声
result.webglDX = random.nextFloat(-1, 1);
// ClientRects 噪声
result.rectDX = random.nextFloat(-1, 1);
result.audio = -1000 + Math.floor(Math.random() * 2000);
result.mediaType = 'Original';
result.videoInputs = [];
result.audioInputs = [];
result.audioOutputs = [];
try {
  const devices = await navigator.mediaDevices.enumerateDevices();
  devices.forEach((device) => {
    let ar = [];
    switch (device.kind) {
      case 'videoinput':
        ar = result.videoInputs;
        break;
      case 'audioinput':
        ar = result.audioInputs;
        break;
      case 'audiooutput':
        ar = result.audioOutputs;
        break;
      default:
    }
    ar.push({
      deviceId: '',
      groupId: '',
      label: '',
    });
  });
} catch (e) {
  console.log('get media devices failed', e);
}
result.pluginType = 'Original';
result.plugins = [
  {
    description: 'Portable Document Format',
    filename: 'internal-pdf-viewer',
    name: 'PDF Viewer',
  },
  {
    description: 'Portable Document Format',
    filename: 'internal-pdf-viewer',
    name: 'Chrome PDF Viewer',
  },
  {
    description: 'Portable Document Format',
    filename: 'internal-pdf-viewer',
    name: 'Chromium PDF Viewer',
  },
  {
    description: 'Portable Document Format',
    filename: 'internal-pdf-viewer',
    name: 'Microsoft Edge PDF Viewer',
  },
  {
    description: 'Portable Document Format',
    filename: 'internal-pdf-viewer',
    name: 'WebKit built-in PDF',
  },
];

const availableFonts = new FontDetector().getAvailableFonts();
result.fonts = JSON.stringify(availableFonts).replace(/[[\]]/g, '');

result.ports_type = 'Original';
result.ports = [];
result.headers = [];
// 获取经纬度
result.location = await new Promise((resolve) => {
  let timer = 0;
  navigator.geolocation.getCurrentPosition((position) => {
    clearTimeout(timer);
    resolve(`${position.coords.longitude},${position.coords.latitude}`);
  });
  timer = setTimeout(() => {
    resolve('unknown');
  }, 3000);
});
// 获取 webrtc publicIP
result.webrtcPublicIp = await new Promise((resolve) => {
  let timer = 0;
  findIP((ip) => {
    clearTimeout(timer);
    resolve(ip);
  });
  timer = setTimeout(() => {
    resolve('0.0.0.0');
  }, 3000);
});

return result;
