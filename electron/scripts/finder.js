var Limit;
(function (Limit) {
  Limit[(Limit['All'] = 0)] = 'All';
  Limit[(Limit['Two'] = 1)] = 'Two';
  Limit[(Limit['One'] = 2)] = 'One';
})(Limit || (Limit = {}));
let config;
let rootDocument;
function finder(input, options) {
  if (input.nodeType !== Node.ELEMENT_NODE) {
    throw new Error(`Can't generate CSS selector for non-element node type.`);
  }
  if ('html' === input.tagName.toLowerCase()) {
    return 'html';
  }
  const defaults = {
    root: document.body,
    includeRoot: true,
    batchSelector: false,
    idName: (name) => true,
    className: (name) => true,
    tagName: (name) => true,
    attr: (name, value) => false,
    seedMinLength: 1,
    seedMaxLength: 3,
    optimizedMinLength: 2,
    threshold: 1000,
    maxNumberOfTries: 10000,
  };
  config = Object.assign(Object.assign({}, defaults), options);
  rootDocument = findRootDocument(config.root, defaults);
  if (config.batchSelector) {
    const stack = getStack(input);
    const paths = sort(combinations(stack));
    const selectors = [];
    const selectorsCount = [];
    paths.forEach((path) => {
      const cssSelector = selector(path);
      const count = rootDocument.querySelectorAll(cssSelector).length;
      if (!selectorsCount.includes(count)) {
        selectorsCount.push(count);
        selectors.push(cssSelector);
      }
    });
    return selectors;
  } else {
    let path = bottomUpSearch(input, Limit.All, () =>
      bottomUpSearch(input, Limit.Two, () => bottomUpSearch(input, Limit.One)),
    );
    if (path) {
      const optimized = sort(optimize(path, input));
      if (optimized.length > 0) {
        path = optimized[0];
      }
      return selector(path);
    } else if (rootDocument === document.body) {
      let treePath = '';
      let el = input;
      while (el.parentNode && el !== document.documentElement) {
        treePath = ` > :nth-child(${index(el)})` + treePath;
        el = el.parentNode;
      }
      treePath = ':root' + treePath;
      return treePath;
      // throw new Error(`Selector was not found.`);
    } else {
      return '';
    }
  }
}
function findRootDocument(rootNode, defaults) {
  if (rootNode.nodeType === Node.DOCUMENT_NODE) {
    return rootNode;
  }
  if (rootNode === defaults.root) {
    return rootNode.ownerDocument;
  }
  return rootNode;
}
function getStack(input) {
  let stack = [];
  let current = input;
  let i = 0;
  while (current && current !== (config.includeRoot ? config.root.parentElement : config.root)) {
    let level = maybe(id(current)) ||
      maybe(...attr(current)) ||
      maybe(...classNames(current)) ||
      maybe(tagName(current)) || [any()];
    const nth = index(current);
    if (nth) {
      level = level.concat(level.filter(dispensableNth).map((node) => nthChild(node, nth)));
    }
    for (let node of level) {
      node.level = i;
    }
    stack.push(level);
    current = current.parentElement;
    i++;
    if (i > config.seedMaxLength) {
      break;
    }
  }
  return stack;
}
function bottomUpSearch(input, limit, fallback) {
  let path = null;
  let stack = [];
  let current = input;
  let i = 0;
  while (current && current !== (config.includeRoot ? config.root.parentElement : config.root)) {
    let level = maybe(id(current)) ||
      maybe(...attr(current)) ||
      maybe(...classNames(current)) ||
      maybe(tagName(current)) || [any()];
    const nth = index(current);
    if (limit === Limit.All) {
      if (nth) {
        level = level.concat(level.filter(dispensableNth).map((node) => nthChild(node, nth)));
      }
    } else if (limit === Limit.Two) {
      level = level.slice(0, 1);
      if (nth) {
        level = level.concat(level.filter(dispensableNth).map((node) => nthChild(node, nth)));
      }
    } else if (limit === Limit.One) {
      const [node] = (level = level.slice(0, 1));
      if (nth && dispensableNth(node)) {
        level = [nthChild(node, nth)];
      }
    }
    for (let node of level) {
      node.level = i;
    }
    stack.push(level);
    if (stack.length >= config.seedMinLength) {
      path = findUniquePath(stack, fallback);
      if (path) {
        break;
      }
    }
    current = current.parentElement;
    i++;
  }
  if (!path && rootDocument === document.body) {
    path = findUniquePath(stack, fallback);
  }
  return path;
}
function findUniquePath(stack, fallback) {
  const paths = sort(combinations(stack));
  if (paths.length > config.threshold) {
    return fallback ? fallback() : null;
  }
  for (let candidate of paths) {
    if (unique(candidate)) {
      return candidate;
    }
  }
  return null;
}
function selector(path) {
  let node = path[0];
  let query = node.name;
  for (let i = 1; i < path.length; i++) {
    const level = path[i].level || 0;
    if (node.level === level - 1) {
      query = `${path[i].name} > ${query}`;
    } else {
      query = `${path[i].name} ${query}`;
    }
    node = path[i];
  }
  return query;
}
function penalty(path) {
  return path.map((node) => node.penalty).reduce((acc, i) => acc + i, 0);
}
function unique(path) {
  switch (rootDocument.querySelectorAll(selector(path)).length) {
    case 0:
      // throw new Error(`Can't select any node with this selector: ${selector(path)}`);
      return false;
    case 1:
      return true;
    default:
      return false;
  }
}
function id(input) {
  const elementId = input.getAttribute('id');
  if (elementId && config.idName(elementId) && !/\d{5}/.test(elementId)) {
    return {
      name: '#' + cssesc(elementId, { isIdentifier: true }),
      penalty: 0,
    };
  }
  return null;
}
function attr(input) {
  const attrs = Array.from(input.attributes).filter((attr) => config.attr(attr.name, attr.value));
  return attrs.map((attr) => ({
    name: '[' + cssesc(attr.name, { isIdentifier: true }) + '="' + cssesc(attr.value) + '"]',
    penalty: 0.5,
  }));
}
function classNames(input) {
  const names = Array.from(input.classList).filter(config.className);
  return names.map((name) => ({
    name: '.' + cssesc(name, { isIdentifier: true }),
    penalty: 1,
  }));
}
function tagName(input) {
  const name = input.tagName.toLowerCase();
  if (config.tagName(name)) {
    return {
      name,
      penalty: 2,
    };
  }
  return null;
}
function any() {
  return {
    name: '*',
    penalty: 3,
  };
}
function index(input) {
  const parent = input.parentNode;
  if (!parent) {
    return null;
  }
  let child = parent.firstChild;
  if (!child) {
    return null;
  }
  let i = 0;
  while (child) {
    if (child.nodeType === Node.ELEMENT_NODE) {
      i++;
    }
    if (child === input) {
      break;
    }
    child = child.nextSibling;
  }
  return i;
}
function nthChild(node, i) {
  return {
    name: node.name + `:nth-child(${i})`,
    penalty: node.penalty + 1,
  };
}
function dispensableNth(node) {
  return node.name !== 'html' && !node.name.startsWith('#');
}
function maybe(...level) {
  const list = level.filter(notEmpty);
  if (list.length > 0) {
    return list;
  }
  return null;
}
function notEmpty(value) {
  return value !== null && value !== undefined;
}
function* combinations(stack, path = []) {
  if (stack.length > 0) {
    for (let node of stack[0]) {
      yield* combinations(stack.slice(1, stack.length), path.concat(node));
    }
  } else {
    yield path;
  }
}
function sort(paths) {
  return Array.from(paths).sort((a, b) => penalty(a) - penalty(b));
}
function* optimize(
  path,
  input,
  scope = {
    counter: 0,
    visited: new Map(),
  },
) {
  if (path.length > 2 && path.length > config.optimizedMinLength) {
    for (let i = 1; i < path.length - 1; i++) {
      if (scope.counter > config.maxNumberOfTries) {
        return; // Okay At least I tried!
      }
      scope.counter += 1;
      const newPath = [...path];
      newPath.splice(i, 1);
      const newPathKey = selector(newPath);
      if (scope.visited.has(newPathKey)) {
        return;
      }
      if (unique(newPath) && same(newPath, input)) {
        yield newPath;
        scope.visited.set(newPathKey, true);
        yield* optimize(newPath, input, scope);
      }
    }
  }
}
function same(path, input) {
  return rootDocument.querySelector(selector(path)) === input;
}
const regexAnySingleEscape = /[ -,\.\/:-@\[-\^`\{-~]/;
const regexSingleEscape = /[ -,\.\/:-@\[\]\^`\{-~]/;
const regexExcessiveSpaces = /(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g;
const defaultOptions = {
  escapeEverything: false,
  isIdentifier: false,
  quotes: 'single',
  wrap: false,
};
function cssesc(string, opt = {}) {
  if (CSS.escape) {
    return CSS.escape(string);
  }
  const options = Object.assign(Object.assign({}, defaultOptions), opt);
  if (options.quotes != 'single' && options.quotes != 'double') {
    options.quotes = 'single';
  }
  const quote = options.quotes == 'double' ? '"' : "'";
  const isIdentifier = options.isIdentifier;
  const firstChar = string.charAt(0);
  let output = '';
  let counter = 0;
  const length = string.length;
  while (counter < length) {
    const character = string.charAt(counter++);
    let codePoint = character.charCodeAt(0);
    let value = void 0;
    // If it’s not a printable ASCII character…
    if (codePoint < 0x20 || codePoint > 0x7e) {
      if (codePoint >= 0xd800 && codePoint <= 0xdbff && counter < length) {
        // It’s a high surrogate, and there is a next character.
        const extra = string.charCodeAt(counter++);
        if ((extra & 0xfc00) == 0xdc00) {
          // next character is low surrogate
          codePoint = ((codePoint & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;
        } else {
          // It’s an unmatched surrogate; only append this code unit, in case
          // the next code unit is the high surrogate of a surrogate pair.
          counter--;
        }
      }
      value = '\\' + codePoint.toString(16).toUpperCase() + ' ';
    } else {
      if (options.escapeEverything) {
        if (regexAnySingleEscape.test(character)) {
          value = '\\' + character;
        } else {
          value = '\\' + codePoint.toString(16).toUpperCase() + ' ';
        }
      } else if (/[\t\n\f\r\x0B]/.test(character)) {
        value = '\\' + codePoint.toString(16).toUpperCase() + ' ';
      } else if (
        character == '\\' ||
        (!isIdentifier &&
          ((character == '"' && quote == character) || (character == "'" && quote == character))) ||
        (isIdentifier && regexSingleEscape.test(character))
      ) {
        value = '\\' + character;
      } else {
        value = character;
      }
    }
    output += value;
  }
  if (isIdentifier) {
    if (/^-[-\d]/.test(output)) {
      output = '\\-' + output.slice(1);
    } else if (/\d/.test(firstChar)) {
      output = '\\3' + firstChar + ' ' + output.slice(1);
    }
  }
  // Remove spaces after `\HEX` escapes that are not followed by a hex digit,
  // since they’re redundant. Note that this is only possible if the escape
  // sequence isn’t preceded by an odd number of backslashes.
  // output = output.replace(regexExcessiveSpaces, function ($0, $1, $2) {
  //   if ($1 && $1.length % 2) {
  //     // It’s not safe to remove the space, so don’t.
  //     return $0;
  //   }
  //   // Strip the space.
  //   return ($1 || '') + $2;
  // });
  if (!isIdentifier && options.wrap) {
    return quote + output + quote;
  }
  return output;
}

const overlayCls = '__huayoung-selector-finder-overlay__';
const containerCls = '__huayoung-selector-finder-container__';
const formCls = '__huayoung-selector-form__';
const MAX_HIGHLIGHT_OVERLAY = 100;
const modeNameMap = {
  single: '单个',
  container: '容器',
  repeat: '循环',
  child: '子',
};
/**
 * 元素选择
 */
class SelectorFinder {
  constructor() {
    this.containerIdx = 0;
    this.activeContainerTimer = 0;
    this.throttleTimer = 0;
    this.currentElm = null;
    this.active = false;
    this.mode = 'single';
    this.root = document;
    this.containerElements = [];
    this.selector = '';
    this.containerSelector = '';
    // 预览blink效果
    this.previewTimer = 0;
    // 顺序预览
    this.previewQueueTimer = 0;
    this.elements = [];
    this.debug = false;
    this._handleMouseOver = this._handleMouseOver.bind(this);
    this._handleOverlayClick = this._handleOverlayClick.bind(this);
    this._handleContainerIdxChange = this._handleContainerIdxChange.bind(this);
    this._handleControlBtnClick = this._handleControlBtnClick.bind(this);
  }

  _log(...args) {
    if (!this.debug) return;
    console.log.apply(this, args);
  }
  _handleMouseOver(e) {
    this.active = true;
    clearTimeout(this.throttleTimer);
    this.throttleTimer = setTimeout(() => {
      const elm = e.target;
      if (!elm || !elm.getBoundingClientRect) return;
      this.currentElm = elm;
      if (this.mode === 'container') {
        this.drawContainer(elm);
        this._cleanOverlay();
        const selector = this.finderSelector(elm);
        if (selector) {
          this.root.querySelectorAll(selector + ' > *').forEach((item, idx) => {
            if (idx < MAX_HIGHLIGHT_OVERLAY) {
              this.highlight(item, false);
            }
          });
        }
        this.root.addEventListener('click', this._handleOverlayClick, true);
      } else {
        this.highlight(elm);
      }
    }, 50);
  }
  _handleOverlayClick(e) {
    e.stopPropagation();
    e.preventDefault();
    try {
      this.selector = this.finderSelector(this.currentElm);
      if (this.selector) {
        // this._renderSelectorList();
        // 将获取到的选择器发送给元素捕获器
        // content -> background -> node.js -> rpa selector picker window
        chrome.runtime.sendMessage({ action: 'selector-finder-res', data: this.selector });
        this.stop();
      }
    } catch (e) {
      console.error(e);
    }
    this.pause();
  }
  _handleControlBtnClick(e) {
    var _a, _b;
    const { direction } = e.target.dataset;
    const selector = this._getSelector();
    if (!selector) return;
    const currentElm = this.root.querySelector(selector);
    if (!currentElm) return;
    let newElm = null;
    if (direction === 'up') {
      if (
        currentElm.parentElement !== this.root &&
        ((_a = currentElm.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) !== 'HTML'
      ) {
        newElm = currentElm.parentElement;
      }
    } else if (direction === 'down') {
      if (currentElm.children.length > 0) {
        newElm = currentElm.children[0];
      }
    } else if (direction === 'prev') {
      if (currentElm.previousElementSibling) {
        newElm = currentElm.previousElementSibling;
      }
    } else if (direction === 'next') {
      if (currentElm.nextElementSibling) {
        newElm = currentElm.nextElementSibling;
      }
    }
    if (newElm) {
      try {
        const res = this.finderSelector(newElm);
        if (res) {
          this.selector = res;
          this._renderSelectorList();
          (_b = document.getElementById('__huayoung-action-preview__')) === null || _b === void 0
            ? void 0
            : _b.click();
        }
      } catch (e) {
        console.error(e);
      }
    }
  }
  finderSelector(elm) {
    let selector = '';
    try {
      if (!elm) return '';
      if (this.mode === 'child') {
        // 确保选中的元素在指定的父容器下
        let el = elm;
        let found = false;
        while (el.parentNode && el !== document.documentElement) {
          el = el.parentNode;
          if (el === this.root) {
            found = true;
            break;
          }
        }
        if (!found) return '';
      }
      selector = finder(elm, {
        idName: () => this.mode !== 'repeat',
        root: this.root === document ? document.body : this.root,
        includeRoot: this.mode !== 'child',
        batchSelector: this.mode === 'repeat',
      });
      // console.log('selector', selector);
    } catch (e) {
      console.error('Finder selector error', e);
    }
    return selector;
  }
  finderXpath(element) {
    if (!element) return '';
    // if (element && element.id) {
    //     return `//*[@id="${element.id}"]`;
    // }
    let path = '';
    let foundRoot = false;
    for (; element && element.nodeType === 1; element = element.parentNode) {
      if (element === this.root) {
        foundRoot = true;
        break;
      }
      let index = 0;
      for (let sibling = element.previousSibling; sibling; sibling = sibling.previousSibling) {
        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
          index++;
        }
      }
      const tagName = element.tagName.toLowerCase();
      const pathIndex =
        index ||
        (element.nodeType === 1 &&
          element.parentElement &&
          element.parentElement.childElementCount > 1)
          ? `[${index + 1}]`
          : '';
      path = `/${tagName}${pathIndex}${path}`;
    }
    if (!foundRoot && this.root !== document) {
      return '';
    }
    return path.length ? path : '';
  }
  highlight(elm, clearOverlay = true, once = false) {
    if (!elm) return;
    let overlayElm = null;
    if (!clearOverlay) {
      overlayElm = document.createElement('div');
      overlayElm.className = overlayCls;
      document.body.append(overlayElm);
    } else {
      overlayElm = document.querySelector(`.${overlayCls}`);
      if (!overlayElm) {
        overlayElm = document.createElement('div');
        overlayElm.className = overlayCls;
        // this.root.addEventListener('click', this._handleOverlayClick, true);
        document.body.append(overlayElm);
      }
    }
    const { height, width, top, left } = elm.getBoundingClientRect();
    overlayElm.style.display = 'block';
    overlayElm.style.top = `${top}px`;
    overlayElm.style.left = `${left}px`;
    overlayElm.style.width = `${width}px`;
    overlayElm.style.height = `${height}px`;
    overlayElm.style.opacity = 0;
    this.previewTimer = setTimeout(() => {
      overlayElm.style.opacity = 0.3;
      this.previewTimer = setTimeout(() => {
        overlayElm.style.opacity = 0;
        if (once) {
          overlayElm.remove();
          return;
        }
        this.previewTimer = setTimeout(() => {
          overlayElm.style.opacity = 0.3;
          this.previewTimer = setTimeout(() => {
            overlayElm.remove();
          }, 300);
        }, 500);
      }, 500);
    }, 300);
  }

  selectByXPath(xpath, contextNode = document) {
    const elements = [];
    const result = document.evaluate(
      xpath,
      contextNode,
      null,
      XPathResult.ORDERED_NODE_ITERATOR_TYPE,
      null,
    );

    let node;
    while ((node = result.iterateNext())) {
      elements.push(node);
    }

    return elements;
  }
  async highlightSelector(selector, previewMode) {
    this._cleanOverlay();
    if (!selector) return 0;
    // 绘制蒙层
    let elements;
    if (/^\//.test(selector)) {
      // xpath
      elements = this.selectByXPath(selector, this.root);
    } else {
      // css selector
      elements = this.root.querySelectorAll(selector);
    }
    // 预览的是子元素
    if (['childParallel', 'childQueue'].includes(previewMode)) {
      const children = [];
      elements.forEach((elm) => {
        children.push(...elm.children);
      });
      elements = children;
    }
    for (let i = 0; i < elements.length; i++) {
      const elm = elements[i];
      if (i < MAX_HIGHLIGHT_OVERLAY) {
        if (['queue', 'childQueue'].includes(previewMode)) {
          await elm.scrollIntoViewIfNeeded();
          await this._sleep(100);
          this.highlight(elm, false, true);
          await new Promise((resolve) => {
            this.previewQueueTimer = setTimeout(() => {
              resolve();
            }, 1000);
          });
        } else {
          this.highlight(elm, false);
        }
      }
    }
    return elements.length;
  }

  _sleep(timeout) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, timeout);
    });
  }

  previewSelector(element, opt) {
    this._log('previewSelector', element, opt);
    const [majorInspect, minorInspect] = element.rule ?? ['selector', 'xpath'];
    let count = 0;
    const doHighlight = async () => {
      clearTimeout(this.previewQueueTimer);
      if (opt.mode === 'child') {
        const containerElements = this._getElements(opt.containerElement);
        for (let i = 0; i < containerElements.length; i++) {
          const containerElm = containerElements[i];
          const childElm = this._getElement(element, containerElm);
          if (!childElm) {
            continue;
          }
          if (opt.previewMode === 'queue') {
            await childElm.scrollIntoViewIfNeeded();
            await this._sleep(100);
            this.highlight(childElm, false, true);
            await new Promise((resolve) => {
              this.previewQueueTimer = setTimeout(() => {
                resolve();
              }, 1000);
            });
          } else {
            this.highlight(childElm, false);
          }
        }
      } else {
        count =
          this.highlightSelector(element[majorInspect], opt.previewMode) ||
          (minorInspect !== 'none'
            ? this.highlightSelector(element[minorInspect], opt.previewMode)
            : 0);
      }
    };
    doHighlight();
    // 返回元素信息
    return {
      count,
    };
  }
  /**
   * 设置激活的父元素
   * @param index
   */
  setActiveContainer(index = 0) {
    this.containerIdx = index;
    const elm = document.querySelectorAll(this.containerSelector)[this.containerIdx];
    if (elm) {
      this._cleanOverlay();
      this.root.removeEventListener('mouseover', this._handleMouseOver, true);
      this.root = elm;
      this.drawContainer(this.root);
      const picker = document.getElementById('__huayoung-container-picker__');
      if (picker) {
        picker.value = `${index + 1}`;
      }
      this.root.addEventListener('mouseover', this._handleMouseOver, true);
      clearInterval(this.activeContainerTimer);
      this.activeContainerTimer = setInterval(() => {
        this.drawContainer(this.root);
      }, 1000);
    } else {
      // 未找到父元素
      const placeholder = document.getElementById('__huayoung-container-picker-value__');
      if (placeholder) {
        placeholder.innerHTML = '<span style="color: red">未找到父元素</span>';
      }
    }
  }
  _handleContainerIdxChange(e) {
    const index = Number(e.target.value);
    const elm = document.querySelectorAll(this.containerSelector)[index - 1];
    if (elm) {
      this.setActiveContainer(index - 1);
    } else {
      e.target.value = `${this.containerIdx + 1}`;
    }
  }
  drawContainer(elm) {
    if (!elm) return false;
    let containerElm = document.querySelector(`.${containerCls}`);
    if (!containerElm) {
      containerElm = document.createElement('div');
      containerElm.className = containerCls;
      document.body.append(containerElm);
    }
    const { height, width, top, left } = elm.getBoundingClientRect();
    containerElm.style.display = 'block';
    containerElm.style.top = `${top - 2}px`;
    containerElm.style.left = `${left - 2}px`;
    containerElm.style.width = `${width + 2}px`;
    containerElm.style.height = `${height + 2}px`;
    return true;
  }

  async setContainer(containerElement, idx = 0) {
    const elements = this._getElements(containerElement);
    this.containerElements = elements;
    if (elements[idx]) {
      this.root = elements[idx];
      this.mode = 'child';
      if (this.root) {
        try {
          await this.root.scrollIntoViewIfNeeded();
          await this._sleep(100);
        } catch {}
      }
      // 绘制容器
      this.drawContainer(this.root);
      clearInterval(this.activeContainerTimer);
      this.activeContainerTimer = setInterval(() => {
        this.drawContainer(this.root);
      }, 1000);
    }
  }

  /**
   * 获取所有父容器下的子元素个数
   * @param selector
   * @param xpath
   * @returns {number}
   */
  countAllContainerChildren(selector, xpath) {
    let count = 0;
    if (this.containerElements.length === 0) return count;
    this.containerElements.forEach((containerElm) => {
      if (containerElm.querySelector(selector)) {
        count++;
      } else if (this.selectByXPath(xpath, containerElm).length > 0) {
        count++;
      }
    });
    return count;
  }

  /**
   * 清除蒙层
   */
  _cleanOverlay() {
    try {
      document.querySelectorAll(`.${overlayCls}`).forEach((overlay) => {
        overlay.remove();
      });
    } catch (e) {
      console.error('[RPA] selector finder error', e);
    }
  }
  _getSelector() {
    const inputElm = document.getElementById('__huayoung-selector-input__');
    if (inputElm && inputElm.value.trim()) {
      return inputElm.value.trim();
    }
    return '';
  }
  _countElements() {
    const selector = this._getSelector();
    if (selector) {
      const descEl = document.getElementById('__huayoung-selector-desc__');
      if (descEl) {
        if (this.mode === 'container') {
          const count = this.root.querySelectorAll(selector + ' > *').length;
          descEl.innerText = `在该容器下共找到${count}个直接子元素`;
        } else {
          const count = this.root.querySelectorAll(selector).length;
          descEl.innerText = `共找到${count}个元素`;
        }
      }
    }
  }
  _getElement(element, root = this.root) {
    this._log('_getElement', element);
    const [majorInspect, minorInspect] = element.rule ?? ['selector', 'xpath'];
    let res;
    if (majorInspect === 'selector') {
      res = root.querySelector(element[majorInspect]);
    } else if (majorInspect === 'xpath') {
      res = this.selectByXPath(element[majorInspect], root)[0];
    }
    if (!res && minorInspect !== 'none') {
      if (minorInspect === 'selector') {
        res = root.querySelector(element[minorInspect]);
      } else if (minorInspect === 'xpath') {
        res = this.selectByXPath(element[minorInspect], root)[0];
      }
    }
    return res;
  }
  _getElements(element, root = this.root) {
    this._log('_getElements', element);
    const [majorInspect, minorInspect] = element.rule ?? ['selector', 'xpath'];
    let res = [];
    if (majorInspect === 'selector') {
      res = root.querySelectorAll(element[majorInspect]);
    } else if (majorInspect === 'xpath') {
      res = this.selectByXPath(element[majorInspect], root);
    }
    if (!res.length === 0 && minorInspect !== 'none') {
      if (minorInspect === 'selector') {
        res = root.querySelectorAll(element[minorInspect]);
      } else if (minorInspect === 'xpath') {
        res = this.selectByXPath(element[minorInspect], root);
      }
    }
    return res;
  }
  start(mode = 'single', containerSelector = '', parentMode = 'self') {
    var _a, _b;
    if (!this.active) {
      this.mode = mode;
      // this._renderForm();
      if (mode === 'child' && containerSelector) {
        if (parentMode === 'parent') {
          this.containerSelector = containerSelector + ' > *';
        } else {
          this.containerSelector = containerSelector;
        }
      }
    }
  }

  getNearbyElements(element, opt) {
    const { direction, containerElement, containerIdx, allChildren, childIdx } = opt;
    if (containerElement) {
      const elements = this._getElements(containerElement);
      if (elements[containerIdx]) {
        this.root = elements[containerIdx];
      }
    }
    let currentElm = this._getElement(element);
    if (!currentElm) {
      this.root = document;
      return;
    }
    let newElm = null;
    let _a;
    let message;
    if (direction === 'up') {
      if (
        currentElm.parentElement !== this.root &&
        ((_a = currentElm.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) !== 'HTML'
      ) {
        newElm = currentElm.parentElement;
      } else {
        message = '已没有上级元素';
      }
    } else if (direction === 'down') {
      if (currentElm.children.length > 0) {
        if (currentElm.children.length > 1 && !containerElement) {
          // 有多个子元素
          if (allChildren) {
            // 所有子元素
            const newSelector = element.selector ? `${element.selector} > *` : '';
            const newXpath = element.xpath ? `${element.xpath}/*` : '';
            this.previewSelector(
              { selector: newSelector, xpath: newXpath },
              {
                ...opt,
                previewMode: 'parallel',
              },
            );
            newElm = currentElm.children[0];
            let innerText = '';
            if (['INPUT', 'TEXTAREA', 'SELECT'].includes(newElm.tagName)) {
              innerText = newElm.value || newElm.placeholder || '';
            } else {
              innerText = newElm.innerText || '';
            }
            return {
              selector: newSelector,
              xpath: newXpath,
              innerText: innerText.trim().substring(0, 10),
              tagName: newElm.tagName,
              elementCount: this.elements.length,
              resultCount: currentElm.children.length,
              href: window.location.href,
              documentTitle: document.title,
            };
          } else if (typeof childIdx !== 'undefined') {
            // 指定子元素
            newElm = currentElm.children[childIdx];
          } else {
            return {
              needConfirmChildren: true,
              totalChildrenCount: currentElm.children.length,
            };
          }
        } else {
          newElm = currentElm.children[0];
        }
      } else {
        message = '已没有下级元素';
      }
    } else if (direction === 'prev') {
      if (currentElm.previousElementSibling) {
        newElm = currentElm.previousElementSibling;
      } else {
        message = '已没有前一元素';
      }
    } else if (direction === 'next') {
      if (currentElm.nextElementSibling) {
        newElm = currentElm.nextElementSibling;
      } else {
        message = '已没有后一元素';
      }
    }
    if (newElm) {
      this.elements = [newElm];
      try {
        const selector = this.finderSelector(newElm);
        const xpath = this.finderXpath(newElm);
        this.previewSelector({ selector, xpath }, opt);
        let innerText = '';
        if (['INPUT', 'TEXTAREA', 'SELECT'].includes(newElm.tagName)) {
          innerText = newElm.value || newElm.placeholder || '';
        } else {
          innerText = newElm.innerText || '';
        }
        this.root = document;
        return {
          selector,
          xpath,
          innerText: innerText.trim().substring(0, 10),
          tagName: newElm.tagName,
          count: 1,
          href: window.location.href,
          documentTitle: document.title,
          fullSelector: this.finderSelector(newElm),
        };
      } catch (e) {
        console.error(e);
      }
    }
    this.root = document;
    return { count: 0, message };
  }

  /**
   * 修改最后一个元素，再重现计算相似元素
   * @param direction
   * @returns {{xpath: string, resultCount: number, selector: string, elementCount: number, href: string, documentTitle: string}|{message: string}}
   */
  changeLastElementAndGetSimilarElement(direction) {
    const currentElm = this.elements[this.elements.length - 1];
    let newElm = null;
    let _a;
    let message;
    if (direction === 'up') {
      if (
        currentElm.parentElement !== this.root &&
        ((_a = currentElm.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) !== 'HTML'
      ) {
        newElm = currentElm.parentElement;
      } else {
        message = '已没有上级元素';
      }
    } else if (direction === 'down') {
      if (currentElm.children.length > 0) {
        newElm = currentElm.children[0];
      } else {
        message = '已没有下级元素';
      }
    } else if (direction === 'prev') {
      if (currentElm.previousElementSibling) {
        newElm = currentElm.previousElementSibling;
      } else {
        message = '已没有前一元素';
      }
    } else if (direction === 'next') {
      if (currentElm.nextElementSibling) {
        newElm = currentElm.nextElementSibling;
      } else {
        message = '已没有后一元素';
      }
    }
    if (newElm) {
      this.highlight(newElm, true, true);
      return this.getSimilarElement(newElm, true);
    }
    return {
      message,
    };
  }

  getClosestCommonAncestor(elements) {
    if (elements.length === 0) {
      return null;
    }
    if (elements.length < 2) {
      return elements[0].parentElement;
    }

    // Get the ancestors of each element, starting from the immediate parent
    const ancestors = elements.map((el) => {
      let ancestor = el.parentElement;
      const allAncestors = [ancestor];

      while (ancestor !== null) {
        ancestor = ancestor.parentElement;
        allAncestors.push(ancestor);
      }

      return allAncestors;
    });

    // Find the first ancestor that is common to all elements
    for (let i = 0; i < ancestors[0].length; i++) {
      const ancestor = ancestors[0][i];
      let isCommonAncestor = true;

      for (let j = 1; j < ancestors.length; j++) {
        if (!ancestors[j].includes(ancestor)) {
          isCommonAncestor = false;
          break;
        }
      }

      if (isCommonAncestor) {
        return ancestor;
      }
    }

    return elements[0].parentElement;
  }
  getSimilarElement(domNode, replaceLastElm = false) {
    if (replaceLastElm) {
      this.elements[this.elements.length - 1] = domNode;
    } else {
      this.elements.push(domNode);
    }
    const commonAncestor = this.getClosestCommonAncestor([...new Set(this.elements)]);
    this._log('commonAncestor', commonAncestor);
    const commonAncestorSelector = this.finderSelector(commonAncestor);
    const commonAncestorXpath = this.finderXpath(commonAncestor);
    this._log('commonAncestorSelector', commonAncestorSelector);
    this._log('commonAncestorXpath', commonAncestorXpath);

    const selectorAndXpathList = this.elements.map((el) => {
      let selector = '';
      let xpath = '';
      while (el && el !== commonAncestor) {
        let nodeName = el.nodeName.toLowerCase();
        let index = Array.prototype.indexOf.call(el.parentElement.children, el);
        let nth = index + 1;
        if (
          [...el.parentElement.children].filter((c) => c.nodeName.toLowerCase() === nodeName)
            .length > 1
        ) {
          selector = selector
            ? `${nodeName}:nth-child(${nth}) > ${selector}`
            : `${nodeName}:nth-child(${nth})`;
          xpath = xpath ? `${nodeName}[${nth}]/${xpath}` : `${nodeName}[${nth}]`;
        } else {
          selector = selector ? `${nodeName} > ${selector}` : nodeName;
          xpath = xpath ? `${nodeName}/${xpath}` : nodeName;
        }
        el = el.parentElement;
      }

      return { selector: selector.trim(), xpath: xpath.trim() };
    });

    this._log('selectorAndXpathList', selectorAndXpathList);
    // 处理 css selector
    const selectors = selectorAndXpathList.map((s) => s.selector);
    const selectorArr = selectors.map((s) => s.split(' > '));
    // 遍历所有的selectorArr，找到第一个差异项的 index
    let index = 0;
    const minLen = Math.min(...selectorArr.map((s) => s.length));
    const char = selectorArr[0][index];
    while (index < minLen) {
      if (selectorArr.every((s) => s[index] === char)) {
        index++;
      } else {
        break;
      }
    }
    const firstTagName = selectorArr[0][0].split(':')[0];
    this._log('firstTagName', firstTagName);
    const childSelectors = new Set();
    selectorArr.forEach((arr) => {
      const s = arr[index];
      if (s) {
        const tagName = s.split(':')[0];
        if (firstTagName === tagName) {
          arr[index] = s.split(':')[0];
        }
      }
      const childSelector = arr.join(' > ');
      this._log('childSelector', childSelector);
      if (childSelectors.has((cs) => childSelector.includes(cs))) {
        // 如果当前这个选择器更精确，替换已有的选择器
        childSelectors.delete(cs);
      } else if (childSelectors.has((cs) => cs.includes(childSelector))) {
        // 已经存在更精确的选择器，跳过
      } else {
        // 找不到匹配的选择器，添加
        childSelectors.add(childSelector);
      }
    });
    // 处理 xpath
    const xpaths = selectorAndXpathList.map((s) => s.xpath);
    const xpathArr = xpaths.map((s) => s.split('/'));
    const childXpaths = new Set();
    xpathArr.forEach((arr) => {
      const s = arr[index];
      if (s) {
        const tagName = s.split('[')[0];
        if (firstTagName === tagName) {
          arr[index] = s.split('[')[0];
        }
      }
      const childXpath = arr.join('/');
      if (childXpaths.has((cx) => childXpath.includes(cx))) {
        // 如果当前这个选择器更精确，替换已有的选择器
        childXpaths.delete(cx);
      } else if (childXpaths.has((cx) => cx.includes(childXpath))) {
        // 已经存在更精确的选择器，跳过
      } else {
        // 找不到匹配的选择器，添加
        childXpaths.add(childXpath);
      }
    });

    const selector = [...childSelectors].map((cs) => `${commonAncestorSelector} > ${cs}`).join(',');
    const xpath = [...childXpaths].map((cx) => `${commonAncestorXpath}/${cx}`).join('|');
    return {
      selector,
      xpath,
      elementCount: this.elements.length,
      resultCount: document.querySelectorAll(selector).length || this.selectByXPath(xpath).length,
      href: window.location.href,
      documentTitle: document.title,
    };
  }

  stop() {
    var _a, _b, _c;
    this.mode = 'single';
    clearInterval(this.previewTimer);
    clearTimeout(this.previewQueueTimer);
    clearInterval(this.activeContainerTimer);
    this._cleanOverlay();
    (_a = document.querySelector(`.${containerCls}`)) === null || _a === void 0
      ? void 0
      : _a.remove();
    this.active = false;
    this.root = document;
    this.containerElements = [];
  }
}

window.__hyrpa_selectorFinder = new SelectorFinder();
