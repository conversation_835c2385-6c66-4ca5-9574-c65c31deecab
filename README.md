# 说明

当前项目使用 [Ant Design Pro V5](https://beta-pro.ant.design/) 脚手架.

NodeJs 版本需要用 v20.11.1

### 本地开发

```bash
npm install yarn -g
yarn config set registry https://registry.npmmirror.com
yarn global add node-gyp
APPIUM_SKIP_CHROMEDRIVER_INSTALL=1 yarn --registry=https://registry.npmmirror.com

# 启动
npm start

# 连接测试服务器
vim config/mockConfig.ts
# 将useMock改为false，热更新结束后即生效，无需重启
# backendServer为后台api器地址，可以随时修改
```

如果直接通过官方源进行 `npm install` ，发现安装 `electron` 依赖失败时，可以尝试加入环境变量:

```
ELECTRON_MIRROR="https://registry.npmmirror.com/electron/"
```

启动失败可以尝试删除缓存目录：

```
rm -rf node_modules/
rm -rf src/.umi*
```

### 构建 Windows 安装包

需要在 Windows 系统下执行 package.bat

可以在构建服务器 Windows 节点中执行以下代码进行测试：

```bat
cd C:\jenkins\workspace\browser-build-installationPackage
set path="c:\jenkins\tools\jenkins.plugins.nodejs.tools.NodeJSInstallation\nodeJs16\";%path%
set SIGN_URL=http://localhost:3000
package.bat
```

## 从 Swagger 文档自动更新 Api 接口

```
npm run downloadApiJsonFromSwagger
```

## electron 桌面应用

通过 `electron` 来构建桌面应用程序。`electron` 在渲染模型上分为 `main` 和 `render`。其中 `render` 可以根据运行环境决定显示本地文件还是 webpack-dev-server 。

### 本地调试

1. 启动 webpack-dev-server

```bash
npm start
```

2. 打包浏览器端的静态资源

```bash
yarn build:browser
```

3. 启动 electron

一键启动：

```bash
npm run electron:start
```

分开启动（适合对客户端相关的代码进行调试）：

```bash
# 先启动编译客户端入口文件 electron/main.js 的 script
npm run electron:watch
# 设置 Run/Debug Configurations
Node interpreter: 选择 node_modules/.bin/electron.cmd
Working directory: 选择项目根目录
JavaScript file: 选择 electron/main.js
```

执行上述操作后，如果 `electron/src` 下的文件有修改，会自动编译 `electron/main.js`，文件更新后需要 ReRun/ReDebug 才能生效。

### 构建本地调试应用

```bash
npm run electron:pack
```

### 构建分发

1. 编译 `render` 所需要的静态文件

```bash
npm run build:electron
```

2. 打包

打包工具 `electron-builder` 要求 nodejs 版本 `>= 14.0.0`，所以建议使用 `v14.17.1 TLS` 版本进行打包。打包时，会下载 `electron` 安装包，这个文件比较大，如果下载失败可以挂上代理再试试，或者从[淘宝镜像](https://registry.npmmirror.com/electron/)中下载到对应的缓存目录（`/Users/<USER>/Library/Caches/electron`）.

```bash
npm run electron:dist
```

### 其它说明

#### 1. `main` 进程依赖项的安装

为了优化安装包的体积，`electron` 主进程所需的第三方依赖使用独立的 `package.json` 来管理，所以如果 `main.js` 需要引入第三方依赖，需要在 `electron/package.json` 下添加依赖项。

### 图标的更新

#### 1.单色图标

库：https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2655923&keyword=&project_type=&page= 选择 FontClass 格式下载，解压缩后使用 iconfont.css、iconfont.woff2、iconfont.woff、iconfont.ttf 文件覆盖 src\assets\fonts 目录下对应的文件

#### 2.彩色图标

库：https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2677509&keyword=&project_type=&page= 选择 Symbol 格式下载，解压缩后使用 iconfont.js、iconfont.json 文件覆盖 src\assets\fonts 目录下对应的文件

## Troubleshooting

1. 调试 electron 客户端时，如果报 `better-sqlite3` 的错误（其它 native 库同理），尝试重新 build 一下：

```
.\node_modules\.bin\electron-rebuild -f -m .\node_modules\better-sqlite3
```
