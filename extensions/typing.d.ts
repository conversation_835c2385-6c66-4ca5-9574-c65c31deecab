declare module '*.css';
declare module '*.less';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';

interface Window {}

declare const window: Window;

interface ShopInfo {
  /** 访问店铺次数 */
  accessCount?: number;
  /** 店铺账号 */
  account?: string;
  /** 创建时间 */
  createTime?: string;
  /** 创建者 */
  creatorId?: number;
  /** 绑定的指纹Id */
  fingerprintId?: number;
  /** 授权给的用户ID */
  grantUserIdList?: number[];
  /** id */
  id?: number;
  /** 绑定IP */
  ip?: string;
  /** IP绑定次数 */
  ipBindCount?: number;
  /** IP绑定时间 */
  ipBindTime?: string;
  /** 绑定IP-ID */
  ipId?: number;
  /** 绑定的IP */
  ipVo: {
    city?: string;
    cloudProvider?: string;
    cloudRegion?: string;
    country?: string;
    createTime?: string;
    id?: number;
    importType?: 'Platform' | 'User';
    ip: string;
    latitude?: number;
    longitude?: number;
    pipeType?: 'Proxy' | 'Tunnel';
    postalCode?: string;
    region?: string;
    regionName?: string;
    shops?: any[];
    status?: 'NORMAL' | 'NOT_VALID';
    timezone: string;
  };
  /** 店铺名称 */
  name: string;
  /** 平台区域 */
  platformArea: string;
  /** 平台ID */
  platformId?: number;
  /** 平台登录URL */
  platformLoginUrl: string;
  /** 平台名称 */
  platformName: 'Amazon' | 'Shopee' | 'Wish' | 'eBay';
  /** 团队ID */
  teamId: number;
  locale: string;
  recordPerception?: boolean;
  monitorPerception?: boolean;
  /**
   * 录像策略
   */
  recordPolicy: 'Chosen' | 'Disabled' | 'Forced';
  securityPolicyEnabled: boolean;
  //是否允许跳过敏感操作
  allowSkip: boolean;
  parentShopId?: number;
}

interface RecordInfo {
  /** 所属会话id */
  sessionId: number;
  /** 要录像窗口标题 */
  windowName: string;
  /** 录像缓存目录 */
  cachePath: string;
  wsUrl?: string;
  mimeType?: string;
  /** 帧率默认值10 */
  fps?: number;
  /** videoBitsPerSecond，默认值2000000 */
  bps?: number;
}

interface RecordEvent {
  name: string;
  data: any;
}

type RecordState = 'started' | 'Pause' | 'ForceStop' | 'Overrun' | 'error';

type RecorderPopupAction =
  | 'getEnv'
  | 'getRecordState'
  | 'getSessionStartTime'
  | 'getRecordDuration'
  | 'getPauseRemain'
  | 'stopRecord'
  | 'startRecord'
  | 'getWatchingUsers'
  | 'getLanguage';
type RecorderBgAction =
  | 'recordState'
  | 'sessionStartTime'
  | 'recordDuration'
  | 'pauseRemain'
  | 'watchingUsers'
  | 'heartbeat';
