const API_URL = 'http://szdamai.local';
let shopInfo: ShopInfo | any = null;
let language = 'zh';
let windowId: number | undefined;
let messageDispatcher: MessageDispatcher | null = null;
let cacheMessages: any[] = [];
let waiting = false;

chrome.windows.getCurrent().then((window) => {
  console.info('window.id', window.id);
  windowId = window.id;
});

//functions
const sleep = (delay: number) => new Promise((resolve) => setTimeout(resolve, delay || 0));

// keepalive
setInterval(chrome.runtime.getPlatformInfo, 25 * 1000);
// win7 v109内核只支持通过 OffScreen 方式保活
async function createOffscreen() {
  await chrome.offscreen
    .createDocument({
      url: 'assets/offscreen.html',
      reasons: ['BLOBS'],
      justification: 'keep service worker running',
    })
    .catch(() => {});
}
self.onmessage = (e) => {}; // keepAlive

export function donkeyRequest(path: string, options: Record<string, any> = {}): Promise<any> {
  const { method = 'GET', params = {}, data = {}, ...restOpts } = options;
  return new Promise((resolve, reject) => {
    fetch(`${API_URL}/donkeyRequest`, {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        path,
        options: {
          method,
          params,
          data,
          ...restOpts,
        },
      }),
    }).then(async (res) => {
      const { success, message: msg, data: resData } = await res.json();
      if (success) {
        resolve(resData);
      } else {
        reject(new Error(msg));
      }
    });
  });
}

chrome.action.onClicked.addListener(async (tab) => {
  const ctxs = await chrome.runtime.getContexts({ contextTypes: ['SIDE_PANEL'] });
  console.log('@@@ctxs', ctxs);
  if (ctxs.length) {
    // 关闭
    try {
      chrome.sidePanel.setOptions({ enabled: false });
    } catch (e) {
      console.error(e);
    }
  } else {
    // 打开
    try {
      chrome.sidePanel.setOptions({ enabled: true });
      chrome.sidePanel.open({ windowId });
    } catch (e) {
      console.error(e);
    }
  }
});

chrome.runtime.onMessage.addListener(
  async (msg: { action: string; data: { [k: string]: any } }, sender, sendResponse) => {
    if (typeof msg !== 'object') return;
    const { action, data = {} } = msg;
    console.log('onMessage', msg);
    switch (action) {
      case 'hello':
        sendResponse('hi');
        break;
      case 'getEnv':
        if (!shopInfo) {
          sendResponse({});
        } else {
          sendResponse({
            shopInfo,
            cacheMessages,
            waiting,
          });
        }
        break;
      case 'getLanguage':
        sendResponse(language);
        break;
      case 'open-side-panel':
        try {
          chrome.sidePanel.setOptions({ enabled: true });
          chrome.sidePanel.open({ windowId });
        } catch (e) {
          console.error(e);
        }
        break;
      case 'close-side-panel':
        try {
          chrome.sidePanel.setOptions({ enabled: false });
        } catch (e) {
          console.error(e);
        }
        break;
      case 'update-cache-messages':
        cacheMessages = data.messages;
        break;
      case 'update-waiting-state':
        waiting = data.waiting;
        break;
    }
    return true;
  },
);

async function doFetch(url: string): Promise<Response> {
  try {
    const res = await fetch(url);
    return res;
  } catch (e) {
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        try {
          const res = await doFetch(url);
          resolve(res);
        } catch (e) {
          reject(e);
        }
      }, 1000);
    });
  }
}

const rpcInvoke = async (fun: string, data: any): Promise<any> => {
  if (fun && typeof fun === 'string') {
    if (fun == 'ping') {
      return 'hello ' + data;
    }
  }
};

/**
 * 通过 websocket 来进行消息传递
 */
export class MessageDispatcher {
  private wsClient: MessageClient;
  constructor(wsClient: MessageClient) {
    this.wsClient = wsClient;
    this.wsClient.addMessageListener((msg: string) => {
      this.onMessage(msg);
    });
  }

  private onMessage(msg: string) {
    console.log('receive message', msg);
    if (/^[A-Za-z0-9-_\s]+$/.test(msg)) {
    } else {
      try {
        const payload = JSON.parse(msg);
        const { action, data } = payload;
        switch (action) {
          case 'open-side-panel':
            try {
              chrome.sidePanel.setOptions({ enabled: true });
              chrome.sidePanel.open({ windowId });
            } catch (e) {
              console.error(e);
            }
            break;
          case 'close-side-panel':
            try {
              chrome.sidePanel.setOptions({ enabled: false });
            } catch (e) {
              console.error(e);
            }
            break;
          case 'rpc':
            let fun = payload.fun;
            rpcInvoke(fun, data)
              .then((ret) => {
                let requestId = payload.requestId;
                this.send({
                  requestId,
                  action: 'rpc-callback',
                  success: true,
                  data: ret,
                });
              })
              .catch((err) => {
                let requestId = payload.requestId;
                this.send({
                  requestId,
                  action: 'rpc-callback',
                  success: false,
                  data: String(err),
                });
              });
            break;
          case 'aiAgent/step':
            console.log('@@@aiAgent/step', data);
            chrome.runtime.sendMessage({ action: 'aiAgent/step', data });
            break;
        }
      } catch (e) {
        console.error('JSON parse failed', e);
      }
    }
  }

  send(payload: any) {
    try {
      this.wsClient.send(typeof payload === 'string' ? payload : JSON.stringify(payload));
    } catch (e) {
      console.log('Websocket send msg failed', e);
    }
  }
}

class MessageClient {
  identify: string;
  wsUrl: string;
  ws!: WebSocket;
  connectTimer: any;

  messageListeners: any[] = [];

  constructor(identify: string, wsUrl: string) {
    this.identify = identify;
    this.wsUrl = wsUrl;
    this.reconnect();
    setInterval(() => {
      this.healthCheck();
    }, 1000);
  }

  send(msg: string) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(msg);
    }
  }

  addMessageListener(listener: (msg: string) => void) {
    this.messageListeners.push(listener);
  }

  healthCheck() {
    if (
      !this.ws ||
      (this.ws.readyState !== WebSocket.OPEN && this.ws.readyState !== WebSocket.CONNECTING)
    ) {
      this.connectTimer = setTimeout(() => {
        clearTimeout(this.connectTimer);
        this.reconnect();
      }, 10);
    }
  }

  private reconnect() {
    if (this.ws) {
      switch (this.ws.readyState) {
        case WebSocket.CONNECTING:
        case WebSocket.OPEN:
          return;
      }
      this.ws.close();
    }

    try {
      console.log(`${new Date().toString()} [ws] connecting to ${this.wsUrl}`);
      this.ws = new WebSocket(this.wsUrl);
      this.ws.addEventListener('open', () => {
        console.log(`${new Date().toString()} [ws] opened`);
        this.ws.send('identify=' + this.identify);
      });

      this.ws.addEventListener('message', (evt) => {
        this.messageListeners.forEach((listener) => {
          listener(evt.data);
        });
      });

      this.ws.addEventListener('close', () => {
        console.log(`[ws] closed`);
        this.connectTimer = setTimeout(() => {
          clearTimeout(this.connectTimer);
          this.reconnect();
        }, 10);
      });
    } catch (e) {
      this.connectTimer = setTimeout(() => {
        clearTimeout(this.connectTimer);
        this.reconnect();
      }, 10);
    }
  }
}

const doInit = async () => {
  console.log(`${new Date().toString()} doInit`);
  await new Promise(async (resolve) => {
    const res = await doFetch(`${API_URL}/api/getEnv`);
    const resObj = await res.json();
    shopInfo = resObj.shopInfo;
    language = resObj.language;
    if (resObj.isWin7) {
      createOffscreen();
    }
    console.log(new Date().toString(), JSON.stringify(Object.keys(resObj), null, 2));

    messageDispatcher = new MessageDispatcher(new MessageClient('plugin:aiAgent', resObj.wsUrl));
    resolve(true);
  });
};

//@ts-ignore
doInit().then(() => {});
