@import '../widgets/variables';
:global(#root) {
  display: flex;
  flex-direction: column;
}

.wrap {
  position: absolute;
  z-index: 1001;
  overflow: hidden;
  background-color: white;
  border-radius: 16px 16px 0 0;
  box-shadow: rgba(0, 0, 0, 0.16) 0 5px 40px;
  &.zoom-out {
    transform: translate(0, 0) scale(0) !important;
    transition: all 0.2s;
  }
}

.resize-wrap {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  height: 58px;
  padding: 0 16px;
  color: white;
  font-size: 16px;
  background: linear-gradient(
    90deg,
    rgba(72, 64, 219, 1) 0%,
    rgba(15, 124, 244, 1) 64%,
    rgba(6, 177, 251, 1) 100%
  );
  background-color: @primary-color;
  cursor: grab;
  :global {
    .iconfont {
      cursor: pointer;
    }
    .ant-select-selector {
      border-radius: 15px !important;
    }
  }
}

.body {
  display: flex;
  flex: 1;
  flex-direction: column-reverse;
  padding: 16px 16px 0;
  overflow: auto;
}

.conversation-item-wrap {
  margin-bottom: 24px;
  &:before {
    display: table;
    content: ' ';
  }
  &:after {
    display: table;
    clear: both;
    content: ' ';
  }
  .conversation-item {
    position: relative;
    width: calc(100% - 48px);
    text-align: left;
    &.user {
      position: relative;
      float: right;
      padding-left: 48px;
      .create-time {
        right: 0;
      }
      :global(.dm-iconFontIcon) {
        position: absolute;
        top: 50%;
        left: -30px;
        margin-top: -8px;
      }
    }
    &.assistant {
      float: left;
      padding-right: 48px;
    }
    .chat-bubble {
      position: relative;
      display: inline-block;
      width: auto;
      max-width: 100%;
      padding: 10px 12px;
      word-wrap: break-word;
      background-color: #f5f5f5;
      border-radius: 10px;
      &.user {
        float: right;
        color: white;
        background-color: @primary-color;
        &::selection,
        ::selection {
          color: @primary-color;
          background: white;
        }
        .link-underlined {
          color: inherit;
          text-decoration: underline;
        }
      }
      &.assistant {
      }
    }
    .create-time {
      position: absolute;
      top: -20px;
      color: @text-muted;
      font-size: 12px;
    }
    .extra-content-row {
      clear: both;
      margin-top: 8px;
      :global {
        .ant-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          max-width: 120px;
          font-size: 13px;
          > span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

.ctx-menu {
  :global {
    .dm-iconFontIcon {
      color: @primary-color;
    }
    .ant-dropdown-menu-item-disabled {
      .dm-iconFontIcon {
        color: #ccc;
      }
    }
  }
  .keymap-text {
    float: right;
    color: #999;
  }
}

.empty-wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  padding: 0 10px;
  display: flex;
  justify-content: center;
  :global {
    .ant-empty-description {
      text-align: left;
      text-indent: 1em;
    }
  }
}

.footer {
  flex: 0 0 auto;
  padding: 4px;
  border-top: 1px solid @border-color;
  &.pending {
    max-height: 41px;
  }

  .record-btn-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
  }
}
