import React from 'react';
import ReactDOM from 'react-dom';
import { MemoryRouter, Route, Switch } from 'react-router-dom';
import 'antd/dist/antd.css';

import './global.less';
import SidePanel from './SidePanel';
import I18N from './I18N';

function App() {
  return (
    <MemoryRouter>
      <Route path="/" component={SidePanel} />
    </MemoryRouter>
  );
}

const timer = setTimeout(() => {
  console.log('timeout, try to reload');
  chrome.runtime.reload();
}, 2000);

chrome.runtime.sendMessage({ action: 'getLanguage' }, (language) => {
  console.log('@@@language', language);
  clearTimeout(timer);
  I18N.setLang(language);
  ReactDOM.render(<App />, document.getElementById('root'));
});
