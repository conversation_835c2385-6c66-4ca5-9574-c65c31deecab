import React, {
  FC,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import styles from './sidePanel.less';
import {
  Button,
  Col,
  Dropdown,
  Input,
  Menu,
  message,
  Row,
  Select,
  Spin,
  Tooltip,
  Typography,
} from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import { EllipsisOutlined } from '@ant-design/icons';
import _ from 'lodash';
import classNames from 'classnames';
import moment from 'moment/moment';
import Lottie from 'react-lottie';
import I18N from './I18N';
import MarkdownView from '../widgets/MarkdownView';
import aiLoadingImg from '../assets/lottie-assets/ai-orb.json';
import recordingImg from '../assets/lottie-assets/recording-bubble.json';
import { API_URL } from '../utils';
import useAudioRecorder from '../useAudioRecorder';

export type GlobalContextInterface = {
  shopInfo?: any;
  cacheMessages?: any[];
  waiting?: boolean;
};

type Props = {};

const PAGE_SIZE = 15;

interface ConversationProps {
  type: 'helper' | 'rpa_editor' | 'shop';
  resourceId?: number;
  emptyView?: ReactNode;
}

interface AiChatProps {
  type: 'helper' | 'rpa_editor' | 'shop';
  resourceId?: number;
  top?: number;
  left?: number;
  bottom?: number;
  right?: number;
  visible: boolean;
  title?: ReactNode;
  emptyView?: ReactNode;
  changeVisible: (visible: boolean) => void;
}

type AiConversationMsgDto = {
  content?: string;
  conversation?: number;
  createTime?: string;
  extraInfo?: string;
  id?: number;
  role?: 'assistant' | 'hy' | 'system' | 'user';
  /** 该句话是否触发了敏感词 */
  sensitive?: boolean;
  teamId?: number;
  type?: 'action' | 'error' | 'pre_action' | 'rpa_script' | 'text';
  userId?: number;
};

type AiPlugin = {
  name: string;
  label: string;
  url: string;
};

const Conversation: FC<ConversationProps> = (props) => {
  const { type, resourceId, emptyView } = props;
  const ctx = useContext(GlobalContext);
  const [aiPluginList, setAiPluginList] = useState<AiPlugin[]>([]);
  const [aiPlugin, setAiPlugin] = useState('');
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState<AiConversationMsgDto[]>(ctx.cacheMessages ?? []);
  const [inputText, setInputText] = useState('');
  const [waiting, setWaiting] = useState(ctx.waiting ?? false);
  const [recording, setRecording] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const messagesRef = useRef(messages);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const scrollRef = useRef<InfiniteScroll>(null);
  const { startRecording, stopRecording } = useAudioRecorder({
    onTextChange: (text) => {
      setInputText(text.replaceAll(/花样/g, I18N.t('花漾')));
      textAreaRef.current?.focus();
    },
  });

  // 加载场景插件列表
  useEffect(() => {
    setAiPluginList([
      {
        name: 'iWorker',
        label: 'iWorker',
        url: 'https://dl.szdamai.com/scripts/iWorker.fillForm.js',
      },
    ]);
    const lastPlugin = localStorage.getItem('aiPlugin');
    if (lastPlugin) {
      setAiPlugin(lastPlugin);
    }
  }, []);

  // 加载历史消息
  const loadHistory = useCallback(() => {
    //
  }, [messages]);

  useEffect(() => {
    loadHistory();
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    messagesRef.current = messages;
    chrome.runtime.sendMessage({
      action: 'update-cache-messages',
      data: {
        messages,
      },
    });
  }, [messages]);

  useEffect(() => {
    chrome.runtime.sendMessage({
      action: 'update-waiting-state',
      data: {
        waiting,
      },
    });
  }, [waiting]);

  useEffect(() => {
    chrome.runtime.onMessage.addListener((msg, sender) => {
      console.log('onMessage', msg, sender);
      if (typeof msg !== 'object') return;
      const { action, data } = msg;
      if (action === 'aiAgent/step') {
        const textToAppend = `- ${data.goal}`;
        const arr = [...messagesRef.current];
        if (arr[0]?.role !== 'assistant') {
          arr.unshift({
            content: '',
            createTime: new Date().toString(),
            id: msg.taskId,
            role: 'assistant',
            type: 'text',
          });
        }
        const target = arr[0];
        if (target.content) {
          target.content += '\n';
        }
        target.content += textToAppend;
        setMessages(arr);
      }
    });
  }, []);

  const stopCurrentJob = useCallback(() => {
    fetch(`${API_URL}/api/aiAgent/stopTask`).then(() => {
      setWaiting(false);
    });
  }, []);

  const sendMsg = useCallback(() => {
    const userMsg = inputText.trim();
    if (!userMsg || waiting) return;
    setWaiting(true);
    setInputText('');
    // 立即显示自己的消息
    const newMessages: AiConversationMsgDto[] = [
      {
        temp: true,
        id: Date.now(),
        createTime: new Date().toString(),
        role: 'user',
        type: 'text',
        content: userMsg,
      } as AiConversationMsgDto,
    ].concat(messages);
    setMessages(newMessages);
    scrollRef.current!.getScrollableTarget()!.scrollTop = 0;

    // 分身的 AI Agent 对话
    fetch(`${API_URL}/api/aiAgent/prompt`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: userMsg,
        pluginJsUrl: aiPluginList.find((plugin) => plugin.name === aiPlugin)?.url,
      }),
    })
      .then((res) => res.json())
      .then((json) => {
        const { success, data, message } = json;
        let content: any = '';
        if (json.success) {
          content = data.errMsg ?? data.val;
        } else {
          content = message;
        }
        if (typeof content === 'object') {
          if (content.content) {
            content = content.content;
          } else {
            content = JSON.stringify(content);
          }
        }
        const arr = [...messagesRef.current];
        arr.unshift({
          content: content,
          createTime: new Date().toString(),
          id: Date.now(),
          role: 'assistant',
          type: 'text',
        });
        setMessages(arr);
        if (scrollRef.current?.getScrollableTarget()) {
          scrollRef.current.getScrollableTarget()!.scrollTop = 0;
        }
        setWaiting(false);
      });
  }, [inputText, messages, type, waiting, resourceId, aiPluginList, aiPlugin]);

  const doStopRecording = useCallback(
    async (force = false) => {
      if (!force) {
        if (!recording) return;
      }
      setRecording(false);
      stopRecording();
    },
    [recording, stopRecording],
  );

  const doStartRecording = useCallback(async () => {
    if (recording) return;
    try {
      await startRecording();
      setRecording(true);
      const listener = () => {
        doStopRecording(true);
        document.removeEventListener('mouseup', listener);
      };
      document.addEventListener('mouseup', listener);
    } catch (e: any) {
      console.error(e);
      if (e.message === 'Permission denied') {
        message.error(I18N.t('麦克风已被禁用'));
      } else {
        message.error(e.message);
      }
      setRecording(false);
    }
  }, [doStopRecording, recording, startRecording]);

  const geneContent = useCallback((msgDto: AiConversationMsgDto) => {
    const { role, content } = msgDto;
    // @ts-ignore
    let actionObj: { code: string; name: string; arguments?: any } = {};
    switch (msgDto.type) {
      default:
        if (role === 'user') {
          return content;
        }
        if (!content) {
          return <EllipsisOutlined />;
        }
        return <MarkdownView text={content} />;
    }
  }, []);

  const messageListCon = useMemo(() => {
    const con = messages.map((msgDto, idx) => {
      const { role, id, createTime, sensitive } = msgDto;
      return (
        <div key={id} className={styles.conversationItemWrap}>
          <div
            className={classNames(
              styles.conversationItem,
              { [styles.user]: role === 'user' },
              { [styles.assistant]: role === 'assistant' },
            )}
          >
            <div
              className={classNames(
                styles.chatBubble,
                { [styles.user]: role === 'user' },
                { [styles.assistant]: role === 'assistant' },
              )}
            >
              {geneContent(msgDto)}
            </div>
            <div className={styles.createTime}>
              {moment(new Date(createTime!)).format('MM-DD HH:mm:ss')}
            </div>
          </div>
          <div />
        </div>
      );
    });
    if (waiting) {
      con.unshift(
        <Row key="waiting" wrap={false} align="middle" justify="center">
          <Col>
            <Lottie
              width={42}
              height={42}
              isClickToPauseDisabled
              options={{
                animationData: aiLoadingImg,
              }}
            />
          </Col>
          <Col>
            <Typography.Text type="secondary" style={{ fontSize: 12 }}>
              {I18N.t('正在执行您的指令...')}

              <a style={{ marginLeft: 8 }} onClick={stopCurrentJob}>
                {I18N.t('停止执行')}
              </a>
            </Typography.Text>
          </Col>
        </Row>,
      );
    } else if (messages.length === 0) {
      // 还没有产生过消息
      con.unshift(
        <div key="empty" className={styles.emptyWrap}>
          <Row gutter={8}>
            <Col flex="0 0 auto">
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="13844"
                width="70"
                height="70"
              >
                <path
                  d="M359.381333 543.146667l13.141334-48.384a3183.786667 3183.786667 0 0 0 37.632-144.554667h2.389333c13.141333 47.786667 25.088 98.56 38.826667 144.554667l13.141333 48.384H359.381333z m155.306667 182.186666h92.586667L465.109333 283.306667H361.173333L219.605333 725.333333h89.6l31.061334-113.493333h143.36l31.061333 113.493333z m144.554667 0h88.405333V283.306667h-88.405333V725.333333z"
                  fill="#0F7CF4"
                  p-id="13845"
                ></path>
                <path
                  d="M512 0c282.752 0 512 229.248 512 512s-229.248 512-512 512S0 794.752 0 512 229.248 0 512 0z m0 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667S747.648 85.333333 512 85.333333z"
                  fill="#262626"
                  p-id="13846"
                ></path>
              </svg>
            </Col>
            <Col>
              <div style={{ fontSize: 16, marginBottom: 12 }}>{I18N.t('小漾同学')}</div>
              <div style={{ color: '#999' }}>{I18N.t('需要我做什么，您一句话的事儿')}</div>
            </Col>
          </Row>
        </div>,
      );
    }
    return con;
  }, [geneContent, messages, waiting]);

  return (
    <>
      <Row className={styles.header} align="middle">
        <Col flex="0 0 auto">{I18N.t('场景选择：')}</Col>
        <Col flex="1">
          <Select
            value={aiPlugin}
            showSearch
            onSelect={(v) => {
              setAiPlugin(v);
              localStorage.setItem('aiPlugin', v);
            }}
            style={{ width: '100%' }}
          >
            <Select.Option value="">{I18N.t('通用场景')}</Select.Option>
            {aiPluginList.map((plugin) => (
              <Select.Option key={plugin.name} value={plugin.name}>
                {plugin.label}
              </Select.Option>
            ))}
          </Select>
        </Col>
      </Row>
      <Dropdown
        destroyPopupOnHide
        trigger={['contextMenu']}
        overlay={
          <Menu className={styles.ctxMenu}>
            <Menu.Item
              key="clean"
              disabled={waiting}
              icon={
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="11229"
                  width="14"
                  height="14"
                >
                  <path
                    d="M682.666667 42.666667a85.333333 85.333333 0 0 1 85.333333 85.333333v85.333333h213.333333a42.666667 42.666667 0 0 1 0 85.333334h-42.666666v554.666666a128 128 0 0 1-128 128H213.333333a128 128 0 0 1-128-128V298.666667H42.666667a42.666667 42.666667 0 1 1 0-85.333334h213.333333V128a85.333333 85.333333 0 0 1 78.933333-85.12L341.333333 42.666667z m170.666666 256H170.666667v554.666666a42.666667 42.666667 0 0 0 37.674666 42.368L213.333333 896h597.333334a42.666667 42.666667 0 0 0 42.368-37.674667L853.333333 853.333333V298.666667zM341.333333 384a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 0 1-85.333333 0v-341.333333a42.666667 42.666667 0 0 1 42.666666-42.666667z m170.666667 0a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 0 1-85.333334 0v-341.333333a42.666667 42.666667 0 0 1 42.666667-42.666667z m170.666667 0a42.666667 42.666667 0 0 1 42.666666 42.666667v341.333333a42.666667 42.666667 0 0 1-85.333333 0v-341.333333a42.666667 42.666667 0 0 1 42.666667-42.666667z m0-256H341.333333v85.333333h341.333334V128z"
                    fill="#878787"
                    p-id="11230"
                  ></path>
                </svg>
              }
              onClick={() => {
                // 清空
                setMessages([]);
              }}
            >
              {I18N.t('清空')}
            </Menu.Item>
          </Menu>
        }
      >
        <div id="ai-chat-scroller" className={styles.body}>
          <InfiniteScroll
            inverse
            ref={scrollRef}
            next={loadHistory}
            style={{ display: 'flex', flexDirection: 'column-reverse', overflow: 'visible' }}
            loader={
              <div className={styles.conversationItemWrap} style={{ textAlign: 'center' }}>
                <Spin />
              </div>
            }
            dataLength={messages.length}
            hasMore={hasMore}
            endMessage={
              messages.length > PAGE_SIZE ? (
                <Typography.Paragraph type="secondary" style={{ textAlign: 'center' }}>
                  {I18N.t('-没有更多消息啦-')}
                </Typography.Paragraph>
              ) : null
            }
            scrollableTarget="ai-chat-scroller"
          >
            {messageListCon}
          </InfiniteScroll>
        </div>
      </Dropdown>
      <Row className={classNames(styles.footer, { [styles.pending]: loading })} align="bottom">
        {false && (
          <Col onMouseDown={doStartRecording} style={{ marginRight: 6 }}>
            <div className={styles.recordBtnWrap}>
              {recording ? (
                <Lottie
                  options={{ animationData: recordingImg }}
                  style={{ position: 'absolute', width: '130%', height: '130%' }}
                />
              ) : (
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="24809"
                  width="20"
                  height="32"
                >
                  <path
                    d="M170.670507 543.737883C170.670507 714.743959 323.500787 853.323733 512 853.323733s341.329493-138.579774 341.329493-309.58585a31.359647 31.359647 0 0 0-17.493136-27.477024 38.143571 38.143571 0 0 0-34.986273 0 31.359647 31.359647 0 0 0-17.535803 27.477024c0 135.934471-121.470633 246.098565-271.314281 246.098565s-271.314281-110.164094-271.314281-246.098565a31.359647 31.359647 0 0 0-17.493137-27.477024 38.143571 38.143571 0 0 0-34.986273 0 31.359647 31.359647 0 0 0-17.535802 27.477024z m305.78856 302.289933v142.590395c0 12.629191 6.741257 24.319726 17.749133 30.634322a35.711598 35.711598 0 0 0 35.5836 0 35.370269 35.370269 0 0 0 17.749133-30.634322v-142.590395A35.455601 35.455601 0 0 0 512 810.657547a35.455601 35.455601 0 0 0-35.540933 35.412935zM382.380125 1023.98848h259.23975c14.677168 0 28.202349-8.106575 35.540934-21.333093 7.338584-13.226518 7.338584-29.439669 0-42.666187a40.746208 40.746208 0 0 0-35.540934-21.333093H382.380125c-14.677168 0-28.202349 8.106575-35.540934 21.333093-7.338584 13.226518-7.338584 29.439669 0 42.666187 7.338584 13.226518 20.863765 21.333093 35.540934 21.333093z"
                    fill="#262626"
                    p-id="24810"
                  ></path>
                  <path
                    d="M512 0c117.801341 0 213.330933 104.361493 213.330933 233.128044v259.069085C725.330933 620.963681 629.801341 725.325173 512 725.325173s-213.330933-104.361493-213.330933-233.128044V233.128044C298.669067 104.361493 394.198659 0 512 0z m0 85.332373c-70.697871 0-127.99856 66.68725-127.99856 148.904992v256.850444c0 53.162069 24.405059 102.356182 63.99928 128.937216a112.638733 112.638733 0 0 0 127.99856 0c39.594221-26.581034 63.99928-75.775148 63.99928-128.979883V234.280031C639.99856 152.019623 582.697871 85.332373 512 85.332373z"
                    fill="#0F7CF4"
                    p-id="24811"
                  ></path>
                </svg>
              )}
            </div>
          </Col>
        )}
        <Col flex="1">
          <Input.TextArea
            ref={textAreaRef}
            value={inputText}
            autoSize={{ minRows: 1, maxRows: 5 }}
            placeholder={I18N.t('有问题请问我...')}
            onChange={(e) => setInputText(e.target.value)}
            onPressEnter={(e) => {
              if (e.ctrlKey) {
                setInputText(`${inputText}\n`);
              } else if (!e.shiftKey) {
                e.preventDefault();
                sendMsg();
              }
            }}
          />
        </Col>
        <Col style={{ marginLeft: 8 }}>
          <Button disabled={waiting} type="primary" onClick={sendMsg}>
            {I18N.t('发送')}
          </Button>
        </Col>
      </Row>
    </>
  );
};

export const GlobalContext = React.createContext<GlobalContextInterface>({});

/**
 *
 * @param props
 * @constructor
 */
const SidePanel: React.FC<Props> = (props) => {
  const [env, setEnv] = useState<GlobalContextInterface | null>(null);

  const getEnv = useCallback(() => {
    chrome.runtime.sendMessage(
      {
        action: 'getEnv',
      },
      (res) => {
        console.log('getEnv', res);
        setEnv(res);
        if (_.isEmpty(res)) {
          setTimeout(() => {
            getEnv();
          }, 1000);
        }
      },
    );
  }, []);

  useEffect(() => {
    getEnv();
  }, []);

  if (!env) return null;

  return (
    <GlobalContext.Provider value={env}>
      <Conversation type="shop" />
    </GlobalContext.Provider>
  );
};

export default SidePanel;
