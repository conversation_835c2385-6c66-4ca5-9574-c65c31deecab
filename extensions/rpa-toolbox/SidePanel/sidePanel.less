.wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  flex: 0 0 auto;
  height: 40px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border-bottom: 1px solid #ddd;

  .view-switch-wrap {
    height: 100%;
    display: flex;
    align-items: center;
    > a {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      min-width: 65px;
      padding: 0 10px;
      &.active:after {
        display: block;
      }
      &:after {
        display: none;
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        height: 2px;
        width: 100%;
        background: #1890ff;
      }
    }
  }

  a {
    display: flex;
    padding: 4px;
  }
}

@keyframes textGradient {
  0% {
    background-position: 0% 0%;
  }
  33% {
    background-position: 33% 33%;
  }
  66% {
    background-position: 66% 66%;
  }
  100% {
    background-position: 100% 100%;
  }
}

.fixed-banner {
  flex: 0 0 auto;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  background: #ecf9ff;

  .rpa-flow-text {
    overflow: hidden;
    max-width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    background-image: linear-gradient(90deg, #e3419e, #fe4b83, #29B3FF, #58F2BD);
    background-position: 0% 0%;
    background-size: 400% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: 1.75s linear infinite alternate-reverse;
    animation-name: textGradient;
  }
}

.body {
  flex: 1;
  overflow: auto;
  padding: 0 8px;
}

.empty-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.footer {
  flex: 0 0 auto;
  padding: 4px 8px;
  border-top: 1px solid #ddd;
}

.log-row {
  white-space: pre;
  font-size: 12px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  &.logWrap {
    white-space: pre-wrap;
    word-break: break-all;
  }
}
