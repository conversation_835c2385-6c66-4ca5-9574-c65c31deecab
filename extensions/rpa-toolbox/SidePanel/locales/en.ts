export default {
  '等待调度中...': 'Scheduling...',
  '确定要中断流程的执行吗？': 'Stop current RPA task?',
  流程一旦中断无法恢复: 'Once the process is interrupted, it cannot be resumed',
  中断流程成功: 'RPA task has bean interrupted',
  停止: 'Stop',
  暂停: 'Pause',
  恢复: 'Resume',
  确定: 'OK',
  取消: 'Cancel',
  流程执行: 'Run',
  流程说明: 'Readme',
  控制台: 'Console',
  日志: 'Logs',
  '正在等待控制台输出...': 'Waiting for console output...',
  '可设置日志输出的Level，并可自定义日志输出的格式':
    'Set the log output level and customize the log output format',
  格式化: 'Format',
  自动换行: 'Word Wrap',
  请选择: 'Select',
  年月日: 'YYYY-MM-DD',
  时分秒: 'HH:mm:ss',
  节点ID: 'Node ID',
  节点名称: 'Node Name',
  节点类型: 'Node Type',
  日志内容格式化: 'Logs format',
  控制台内容格式化: 'Console format',
  可设置控制台输出内容的格式: 'Set the console output format',
  流程恢复成功: 'Resumed success',
  流程暂停成功: 'Paused success',
};
