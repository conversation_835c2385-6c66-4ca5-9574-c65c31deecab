body {
  width: 100%;
  height: 100%;
  margin: 0;
  font-size: 14px;
  overflow: auto;
}

* {
  box-sizing: border-box;
}

:global {
  #root {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .ant-tooltip {
    .ant-tooltip-arrow-content,
    .ant-tooltip-inner {
      font-size: 12px;
      background-color: #0f7cf4;
      &::selection,
      ::selection {
        color: #0f7cf4;
        background: white;
        text-decoration-color: #0f7cf4;
      }
      .link-underlined {
        color: inherit;
        text-decoration: underline;
      }
    }
  }
}

*::-webkit-scrollbar-track-piece {
  background: none;
}
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: #fff;
}
*::-webkit-scrollbar-button {
  display: none;
}
*::-webkit-scrollbar-thumb {
  background: #e1e1e1;
  border: 1px solid #fff;
  border-radius: 5px;
}
*::-webkit-scrollbar-track {
  border: 1px solid #fff;
}
