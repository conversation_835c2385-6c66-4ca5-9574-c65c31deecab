import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { convertLinksToHTML, donkeyRequest, sendMessage } from '../Popup/utils';
import styles from './sidePanel.less';
import {
  Alert,
  Button,
  Checkbox,
  Col,
  Form,
  message,
  Modal,
  Row,
  Select,
  Space,
  Tooltip,
} from 'antd';
import I18N from './I18N';
import { LoadingOutlined } from '@ant-design/icons';
import _ from 'lodash';
import { RpaContextInterface } from '../Popup/Popup';

type Props = {};

/**
 *
 * @param props
 * @constructor
 */
const SidePanel: React.FC<Props> = (props) => {
  const [rpaTaskItemVo, setRpaTaskItemVo] = useState<any>({});
  const [env, setEnv] = useState<RpaContextInterface | null>(null);
  const [view, setView] = useState<'console' | 'log'>('console');
  const [paused, setPaused] = useState(false);
  const [mouseTrackVisible, setMouseTrackVisible] = useState(true);
  const [logSettingsModalVisible, setLogSettingsModalVisible] = useState(false);
  const [consoleSettingsModalVisible, setConsoleSettingsModalVisible] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [consoleLogs, setConsoleLogs] = useState<string[]>([]);
  const [logLevel, setLogLevel] = useState(0);
  const [logFormat, setLogFormat] = useState('{time}');
  const [consoleFormat, setConsoleFormat] = useState('{time}');
  const [logWrap, setLogWrap] = useState(false);
  const [consoleWrap, setConsoleWrap] = useState(true);
  const [finishing, setFinishing] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);
  const [changingPauseStatus, setChangingPauseStatus] = useState(false);
  const [logLimit, setLogLimit] = useState(1000);
  const autoScrollRef = useRef(autoScroll);
  // const logsRef = useRef(logs);
  const [form] = Form.useForm();
  const [consoleForm] = Form.useForm();
  const taskIdRef = useRef();
  const taskItemIdRef = useRef();
  const pausedRef = useRef(false);

  useEffect(() => {
    pausedRef.current = paused;
  }, [paused]);

  const waitPausedStatus = useCallback((v) => {
    return new Promise((resolve) => {
      const maxWaitCount = 10;
      let count = 0;
      const timer = setInterval(() => {
        count++;
        if (pausedRef.current === v || count >= maxWaitCount) {
          clearInterval(timer);
          resolve(true);
        }
      }, 1000);
    });
  }, []);

  useEffect(() => {
    autoScrollRef.current = autoScroll;
  }, [autoScroll]);

  useEffect(() => {
    document.addEventListener('click', (e) => {
      if (e.target?.nodeName === 'A' && e.target?.dataset?.url) {
        fetch(`http://szdamai.local/api/openExternalUrl?url=${e.target?.dataset?.url}`);
      }
    });
  }, []);

  const loadItemData = useCallback(() => {
    if (!taskItemIdRef.current) return;
    donkeyRequest(`/api/rpa/task/findItem/${taskItemIdRef.current}`).then((res) => {
      setRpaTaskItemVo(res);
    });
  }, []);

  const changePausedStatus = useCallback((paused) => {
    if (!taskItemIdRef.current) return Promise.resolve();
    return donkeyRequest(
      `/api/rpa/task/changePauseStatus/${taskIdRef.current}?rpaTaskItemId=${taskItemIdRef.current}&paused=${paused}`,
      {
        method: 'PUT',
      },
    );
  }, []);

  const checkRpaTask = useCallback(() => {
    sendMessage('getEnv', {}, (res) => {
      console.log('getEnv', res);
      setEnv(res);
      setPaused(res.paused);
      setMouseTrackVisible(res.mouseTrackVisible);
      taskIdRef.current = res.currentTaskId;
      taskItemIdRef.current = res.currentTaskItemId;
      if (taskItemIdRef.current) {
        loadItemData();
      }
    });
  }, []);

  // useEffect(() => {
  //   logsRef.current = logs;
  // }, [logs]);

  useEffect(() => {
    const onMessageListener = (msg: any) => {
      const { action, data } = msg;
      switch (action) {
        case 'rpa-task-started':
          checkRpaTask();
          setPaused(false);
          setFinishing(false);
          break;
        case 'task-log':
          setLogs((logs) => [...logs, data]);
          if (typeof data === 'string' && data.startsWith('print')) {
            setConsoleLogs((consoleLogs) => [...consoleLogs, data]);
          }
          break;
        case 'task-state-update':
          loadItemData();
          if (data?.finished) {
            setPaused(false);
            setFinishing(false);
          }
          break;
        case 'task-item-state-update':
          loadItemData();
          break;
        case 'paused-status-update':
          setPaused(data.paused);
          break;
        case 'clear-task-log':
          setLogs([]);
          setConsoleLogs([]);
          break;
      }
    };
    checkRpaTask();
    sendMessage('getCurrentTaskConsoleLogs', {}, (res) => {
      setConsoleLogs(res || []);
    });
    sendMessage('getCurrentTaskLogs', {}, (res) => {
      setLogs(res || []);
      chrome.runtime.onMessage.addListener(onMessageListener);
    });
    return () => {
      chrome.runtime.onMessage.removeListener(onMessageListener);
    };
  }, []);

  const isConsole = useMemo(() => {
    return view === 'console';
  }, [view]);

  const logContent = useMemo(() => {
    let logArr = isConsole ? [...consoleLogs] : [...logs];
    if (isConsole) {
      logArr = logArr.filter((t) => t.startsWith('print ') || t.startsWith('printError '));
    } else {
      if (logLevel > 0) {
        // remove log
        logArr = logArr.filter(
          (t) =>
            !t.startsWith('log ') &&
            !t.startsWith('debug ') &&
            !t.startsWith('print ') &&
            !t.startsWith('printError '),
        );
      }
      if (logLevel > 1) {
        // remove info
        logArr = logArr.filter((t) => !t.startsWith('info '));
      }
    }
    return logArr.slice(-logLimit).map((row, idx) => {
      // {level} {datetime} {node.nid} {node.type} {node.name} {content}
      const regRes =
        /^(\w+)\s(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})\s(\S+)\s(\S+)\s(\S*)\s([\w\W]*)/.exec(row);
      const c = /^(err|error|printError)\s/.test(row) ? '#f5222d' : '#333';
      let t = row.replace(/^(log|debug|info|err|error|print|printError)\s/, '');
      if (regRes) {
        const [, , datetime, nodeId, nodeType, nodeName, content] = regRes;
        t = '';
        const format = isConsole ? consoleFormat : logFormat;
        if (format.includes('date')) {
          t += `${datetime.split(' ')[0]} `;
        }
        if (format.includes('time')) {
          t += `${datetime.split(' ')[1]} `;
        }
        if (format.includes('node.nid') && nodeId !== 'undefined') {
          t += `${nodeId} `;
        }
        if (format.includes('node.type') && nodeType !== 'undefined') {
          t += `${nodeType} `;
        }
        if (format.includes('node.name') && nodeName !== 'undefined') {
          t += `${nodeName} `;
        }
        if (content) {
          t += content;
        }
      }
      if (isConsole) {
        return (
          <div
            key={`${logLimit}-${idx}`}
            className={`${styles.logRow} ${consoleWrap ? styles.logWrap : ''}`}
            style={{
              color: c,
            }}
            dangerouslySetInnerHTML={{ __html: convertLinksToHTML(t) }}
          />
        );
      }
      return (
        <div
          key={`${logLimit}-${idx}`}
          className={`${styles.logRow} ${
            (isConsole ? consoleWrap : logWrap) ? styles.logWrap : ''
          }`}
          style={{
            color: c,
          }}
        >
          {t}
        </div>
      );
    });
  }, [
    consoleLogs,
    logs,
    isConsole,
    logLevel,
    logFormat,
    consoleFormat,
    logWrap,
    consoleWrap,
    logLimit,
  ]);

  useEffect(() => {
    const container = document.getElementById('log-container');
    if (autoScrollRef.current && container) {
      setTimeout(() => {
        container.scrollTop = container.scrollHeight - container.offsetHeight + 20;
      }, 100);
    }
  }, [logContent]);

  const isUnfinished = useMemo(() => {
    return ['Scheduling', 'Scheduled', 'NotStart', 'Running'].includes(rpaTaskItemVo.status!);
  }, [rpaTaskItemVo.status]);

  const consoleStatusText = useMemo(() => {
    if (isUnfinished) {
      if (paused) {
        return (
          <Row
            align="middle"
            gutter={4}
            className={`${styles.logRow} ${logWrap ? styles.logWrap : ''}`}
          >
            <Col>
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="77517"
                width="16"
                height="16"
              >
                <path
                  d="M512 170.666667c235.648 0 426.666667 191.018667 426.666667 426.666666s-191.018667 426.666667-426.666667 426.666667S85.333333 832.981333 85.333333 597.333333 276.352 170.666667 512 170.666667z m0 85.333333a341.333333 341.333333 0 1 0 0 682.666667 341.333333 341.333333 0 0 0 0-682.666667z m0 85.333333a42.666667 42.666667 0 0 1 42.368 37.674667L554.666667 384l-0.042667 170.666667H682.666667a42.666667 42.666667 0 0 1 0 85.333333h-170.666667a42.666667 42.666667 0 0 1-42.666667-42.666667V384a42.666667 42.666667 0 0 1 42.666667-42.666667zM225.706667 33.621333l2.304 2.773334a50.005333 50.005333 0 0 1-5.845334 67.413333l-132.352 121.173333a42.112 42.112 0 0 1-59.477333-2.602666l-2.346667-2.773334a50.048 50.048 0 0 1 5.888-67.413333l132.266667-121.173333a42.154667 42.154667 0 0 1 59.52 2.602666zM855.04 28.672l2.773333 2.346667 132.352 121.173333c19.114667 17.578667 21.674667 46.848 5.845334 67.413333a42.112 42.112 0 0 1-59.050667 7.68l-2.773333-2.304-132.352-121.173333a50.048 50.048 0 0 1-5.845334-67.413333 42.112 42.112 0 0 1 59.050667-7.68z"
                  fill="#878787"
                  p-id="77518"
                ></path>
              </svg>
            </Col>
            <Col style={{ color: '#999' }}>{I18N.t('流程暂停运行')}</Col>
          </Row>
        );
      }
      return (
        <Row
          align="middle"
          gutter={4}
          className={`${styles.logRow} ${logWrap ? styles.logWrap : ''}`}
        >
          <Col>
            <LoadingOutlined />
          </Col>
          <Col style={{ color: '#999' }}>{I18N.t('正在等待控制台输出...')}</Col>
        </Row>
      );
    }
  }, [isUnfinished, paused]);

  const loadMoreRow = useMemo(() => {
    let logArr = isConsole ? consoleLogs : logs;
    if (logArr.length > logLimit) {
      return (
        <a
          onClick={() => {
            setLogLimit(logLimit + 500);
          }}
        >
          <Row align="middle" gutter={4} className={styles.logRow}>
            <Col>
              <span className="anticon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="10884"
                  width="16"
                  height="16"
                >
                  <path
                    d="M694.858499 33.920639l1.405092-1.064464 10.98527 6.046156 4.683642 1.958614-0.468364 0.425786 67.870232 37.213665-237.418076 186.068327-51.264592-67.997967 120.582495-99.633841A425.870802 425.870802 0 0 0 175.953533 248.857233C31.186413 434.159146 64.057065 701.680267 249.358978 846.532543a423.784453 423.784453 0 0 0 212.339301 87.371214l-9.665334 84.646187a508.558375 508.558375 0 0 1-255.130759-104.913583C-25.443077 739.915818-64.870828 418.788284 108.849715 196.44302 250.636335 14.973178 490.523967-44.721969 694.858499 33.920639z m74.512487 815.975611l52.031006 67.487024a510.261517 510.261517 0 0 1-242.272032 100.059627l-11.623948-84.390715a425.232124 425.232124 0 0 0 201.907553-83.155936z m163.075903-274.631741l84.305557 12.43294a508.600953 508.600953 0 0 1-102.273712 237.375498l-67.444446-51.988428 8.387977-11.070426a423.57156 423.57156 0 0 0 74.46991-171.591616l2.554714-15.157968z m11.751683-336.200346a511.453717 511.453717 0 0 1 75.960159 223.835514l-84.731343 8.30282a426.381745 426.381745 0 0 0-59.524833-180.660849l68.296017-51.520063z"
                    fill="currentColor"
                    p-id="10885"
                  ></path>
                </svg>
              </span>
            </Col>
            <Col>{I18N.t('加载更多')}</Col>
          </Row>
        </a>
      );
    }
    return null;
  }, [isConsole, consoleLogs, logs, logLimit]);

  const isRunning = useMemo(
    () => ['Running'].includes(rpaTaskItemVo?.status),
    [rpaTaskItemVo?.status],
  );

  const footerAction = useMemo(() => {
    return (
      <Row wrap={false} gutter={8}>
        {paused ? (
          <Col flex={8}>
            <Button
              block
              disabled={!isRunning}
              icon={
                changingPauseStatus ? (
                  <LoadingOutlined />
                ) : (
                  <span className="anticon">
                    <svg
                      className="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                    >
                      <path
                        d="M239.872 104.917333a132.053333 132.053333 0 0 1 130.56-2.901333l8.021333 4.693333 451.328 284.842667c40.746667 25.6 65.365333 71.338667 65.365334 120.448a142.592 142.592 0 0 1-57.856 115.285333l-7.424 5.077334L378.453333 917.034667c-89.216 56.448-201.728-8.021333-206.805333-112.896l-0.170667-7.381334V227.072l41.770667-0.042667-41.813333-0.042666A142.378667 142.378667 0 0 1 232.533333 109.568l7.338667-4.693333zM333.824 177.493333a48.469333 48.469333 0 0 0-51.2-0.682666c-15.104 8.96-25.216 25.386667-27.136 44.032l-0.341333 6.272v569.6c0 43.008 40.789333 69.034667 74.368 52.053333l4.309333-2.474667 451.413333-284.672c16.085333-10.24 26.282667-29.056 26.282667-49.621333 0-18.602667-8.234667-35.712-21.632-46.336l-4.693333-3.285333L333.866667 177.493333z"
                        fill="currentColor"
                      ></path>
                    </svg>
                  </span>
                )
              }
              style={{ backgroundColor: '#52c41a', color: 'white', border: 'none' }}
              onClick={() => {
                setChangingPauseStatus(true);
                changePausedStatus(false)
                  .then(() => waitPausedStatus(false))
                  .finally(() => {
                    setChangingPauseStatus(false);
                  });
              }}
            >
              {I18N.t('恢复')}
            </Button>
          </Col>
        ) : (
          <Col flex={8}>
            <Button
              block
              type="primary"
              disabled={!isRunning}
              icon={
                changingPauseStatus ? (
                  <LoadingOutlined />
                ) : (
                  <span className="anticon">
                    <svg
                      className="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                    >
                      <path
                        d="M844.8 42.666667c28.288 0 51.2 20.992 51.2 46.933333v844.8c0 25.941333-22.912 46.933333-51.2 46.933333h-153.6c-28.288 0-51.2-20.992-51.2-46.933333V89.6c0-25.941333 22.912-46.933333 51.2-46.933333h153.6z m-512 0C361.088 42.666667 384 63.658667 384 89.6v844.8c0 25.941333-22.912 46.933333-51.2 46.933333H179.2c-28.288 0-51.2-20.992-51.2-46.933333V89.6C128 63.658667 150.912 42.666667 179.2 42.666667h153.6z"
                        fill="currentColor"
                      ></path>
                    </svg>
                  </span>
                )
              }
              onClick={() => {
                setChangingPauseStatus(true);
                changePausedStatus(true)
                  .then(() => waitPausedStatus(true))
                  .finally(() => {
                    setChangingPauseStatus(false);
                  });
              }}
            >
              {I18N.t('暂停')}
            </Button>
          </Col>
        )}
        <Col flex={8}>
          <Button
            type="primary"
            danger
            block
            disabled={!isRunning}
            icon={
              <span className="anticon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="31958"
                  width="16"
                  height="16"
                >
                  <path
                    d="M332.629333 127.957333c1.834667 4.778667 3.541333 11.477333 5.162667 20.053334l2.389333 14.378666L341.333333 170.666667l1.536 12.970666c1.578667 15.616 1.322667 24.448-0.682666 26.496l-0.853334 0.426667a384 384 0 1 0 341.376 0L682.666667 128h24.96A469.418667 469.418667 0 0 1 981.333333 554.666667c0 259.2-210.133333 469.333333-469.333333 469.333333S42.666667 813.866667 42.666667 554.666667C42.666667 365.312 154.794667 202.112 316.330667 127.957333h16.298666zM512 0a42.666667 42.666667 0 0 1 42.666667 42.666667v426.666666a42.666667 42.666667 0 0 1-85.333334 0V42.666667a42.666667 42.666667 0 0 1 42.666667-42.666667z"
                    fill="currentColor"
                    p-id="31959"
                  ></path>
                </svg>
              </span>
            }
            loading={isRunning && finishing}
            onClick={() => {
              Modal.confirm({
                centered: true,
                title: I18N.t('确定要中断流程的执行吗？'),
                content: I18N.t('流程一旦中断无法恢复'),
                okText: I18N.t('确定'),
                cancelText: I18N.t('取消'),
                onOk: () => {
                  setFinishing(true);
                  return donkeyRequest(`/api/rpa/task/markItemEnd`, {
                    method: 'POST',
                    data: {
                      rpaTaskItemId: taskItemIdRef.current,
                      force: true,
                    },
                  })
                    .then(() => {
                      message.success(I18N.t('中断流程成功'));
                      loadItemData();
                      sendMessage('clearCurrentTaskItemId', {});
                    })
                    .catch((err) => {
                      setFinishing(false);
                      message.error(err.message);
                    });
                },
              });
            }}
          >
            {I18N.t('停止')}
          </Button>
        </Col>
        <Col flex={8}>
          <Button
            type="primary"
            ghost
            block
            icon={
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                style={{ verticalAlign: 'text-top', marginRight: 4 }}
              >
                <path
                  d="M512.047361 1023.997867a511.972695 511.972695 0 1 1 362.00736-149.965335A508.559543 508.559543 0 0 1 512.047361 1023.997867z"
                  fill="#0B5BFF"
                  opacity=".951"
                ></path>
                <path
                  d="M512.047361 896.004693a383.979521 383.979521 0 1 1 271.516186-112.463335A381.419658 381.419658 0 0 1 512.047361 896.004693z"
                  fill="#0BA6FF"
                  opacity=".951"
                ></path>
                <path
                  d="M512.047361 768.011519a255.986347 255.986347 0 1 1 181.025012-74.961335A254.279772 254.279772 0 0 1 512.047361 768.011519z"
                  fill="#0BE6FF"
                  opacity=".951"
                ></path>
                <path
                  d="M512.047361 640.018346a127.993174 127.993174 0 1 1 0-255.986348 127.993174 127.993174 0 0 1 0 255.986348z"
                  fill="#4652FF"
                  opacity=".951"
                ></path>
              </svg>
            }
            onClick={() => {
              setMouseTrackVisible(!mouseTrackVisible);
              if (mouseTrackVisible) {
                // 隐藏鼠标轨迹时顺便清空鼠标轨迹
                chrome.runtime.sendMessage({ action: 'clearMouseTrack' });
              }
              chrome.runtime.sendMessage({
                action: mouseTrackVisible ? 'hideMouseTrack' : 'showMouseTrack',
              });
            }}
          >
            {I18N.t('轨迹')}
          </Button>
        </Col>
      </Row>
    );
  }, [rpaTaskItemVo.status, finishing, paused, changingPauseStatus, mouseTrackVisible, isRunning]);

  return (
    <div className={styles.wrap}>
      <Row className={styles.header} align="middle">
        <Col flex="1" style={{ height: '100%' }}>
          <div className={styles.viewSwitchWrap}>
            <a
              className={view === 'console' ? styles.active : ''}
              onClick={() => setView('console')}
            >
              {I18N.t('控制台')}
            </a>
            <a className={view === 'log' ? styles.active : ''} onClick={() => setView('log')}>
              {I18N.t('日志')}
            </a>
          </div>
        </Col>
        <Col>
          <Space>
            <a
              style={{ display: 'flex' }}
              onClick={() => {
                isConsole ? setConsoleSettingsModalVisible(true) : setLogSettingsModalVisible(true);
              }}
            >
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="11453"
                width="16"
                height="16"
              >
                <path
                  d="M768 0a85.333333 85.333333 0 0 1 85.333333 85.333333v85.333334h85.333334a42.666667 42.666667 0 0 1 42.666666 42.666666v256a42.666667 42.666667 0 0 1-37.674666 42.368L938.666667 512H512v128h42.666667a42.666667 42.666667 0 0 1 42.666666 42.666667v213.333333a128 128 0 0 1-256 0v-213.333333a42.666667 42.666667 0 0 1 42.666667-42.666667h42.666667v-170.666667a42.666667 42.666667 0 0 1 42.666666-42.666666h426.666667V256h-42.666667v42.666667a85.333333 85.333333 0 0 1-85.333333 85.333333H128a85.333333 85.333333 0 0 1-85.333333-85.333333V85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333h640z m-256 725.333333h-85.333333v170.666667a42.666667 42.666667 0 0 0 37.674666 42.368L469.333333 938.666667a42.666667 42.666667 0 0 0 42.368-37.674667L512 896v-170.666667z m256-640H128v213.333334h640V85.333333z"
                  fill="#0f7cf4"
                  p-id="11454"
                ></path>
              </svg>
            </a>
            <Tooltip title={`${I18N.t('自动滚动：')}${autoScroll ? I18N.t('开') : I18N.t('关')}`}>
              <a onClick={() => setAutoScroll((prevState) => !prevState)}>
                {autoScroll ? (
                  <svg
                    className="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="46539"
                    width="16"
                    height="16"
                  >
                    <path
                      d="M85.333333 0h853.333334a85.333333 85.333333 0 0 1 85.333333 85.333333v853.333334a85.333333 85.333333 0 0 1-85.333333 85.333333H85.333333a85.333333 85.333333 0 0 1-85.333333-85.333333V85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333z"
                      fill="#0F7CF4"
                      p-id="46540"
                    ></path>
                    <path
                      d="M896 85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v768a42.666667 42.666667 0 0 1-37.674667 42.368L896 938.666667H128a42.666667 42.666667 0 0 1-42.666667-42.666667v-85.333333a42.666667 42.666667 0 0 1 42.666667-42.666667h640V128a42.666667 42.666667 0 0 1 42.666667-42.666667h85.333333z m-256 0a42.666667 42.666667 0 0 1 42.666667 42.666667v512a42.666667 42.666667 0 0 1-42.666667 42.666667H128a42.666667 42.666667 0 0 1-42.666667-42.666667V128a42.666667 42.666667 0 0 1 42.666667-42.666667h512z"
                      fill="#FFFFFF"
                      p-id="46541"
                    ></path>
                    <path
                      d="M768 768v170.666667H256v-170.666667h512z m170.666667-512v512h-170.666667V256h170.666667z"
                      fill="#9CCBFF"
                      p-id="46542"
                    ></path>
                    <path
                      d="M853.333333 213.333333a42.666667 42.666667 0 1 0 0-85.333333 42.666667 42.666667 0 0 0 0 85.333333z m0 682.666667a42.666667 42.666667 0 1 0 0-85.333333 42.666667 42.666667 0 0 0 0 85.333333zM170.666667 896a42.666667 42.666667 0 1 0 0-85.333333 42.666667 42.666667 0 0 0 0 85.333333z"
                      fill="#0F7CF4"
                      p-id="46543"
                    ></path>
                  </svg>
                ) : (
                  <svg
                    className="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="46783"
                    width="16"
                    height="16"
                  >
                    <path
                      d="M85.333333 0h853.333334a85.333333 85.333333 0 0 1 85.333333 85.333333v853.333334a85.333333 85.333333 0 0 1-85.333333 85.333333H85.333333a85.333333 85.333333 0 0 1-85.333333-85.333333V85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333z"
                      fill="#CCCCCC"
                      p-id="46784"
                    ></path>
                    <path
                      d="M896 85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v768a42.666667 42.666667 0 0 1-37.674667 42.368L896 938.666667H128a42.666667 42.666667 0 0 1-42.666667-42.666667v-85.333333a42.666667 42.666667 0 0 1 42.666667-42.666667h640V128a42.666667 42.666667 0 0 1 42.666667-42.666667h85.333333z m-256 0a42.666667 42.666667 0 0 1 42.666667 42.666667v512a42.666667 42.666667 0 0 1-42.666667 42.666667H128a42.666667 42.666667 0 0 1-42.666667-42.666667V128a42.666667 42.666667 0 0 1 42.666667-42.666667h512z"
                      fill="#FFFFFF"
                      p-id="46785"
                    ></path>
                    <path
                      d="M768 768v170.666667H256v-170.666667h512z m170.666667-512v512h-170.666667V256h170.666667z"
                      fill="#E6E6E6"
                      p-id="46786"
                    ></path>
                    <path
                      d="M853.333333 213.333333a42.666667 42.666667 0 1 0 0-85.333333 42.666667 42.666667 0 0 0 0 85.333333z m0 682.666667a42.666667 42.666667 0 1 0 0-85.333333 42.666667 42.666667 0 0 0 0 85.333333zM170.666667 896a42.666667 42.666667 0 1 0 0-85.333333 42.666667 42.666667 0 0 0 0 85.333333z"
                      fill="#999999"
                      p-id="46787"
                    ></path>
                  </svg>
                )}
              </a>
            </Tooltip>
            <a
              style={{ display: 'flex' }}
              title={I18N.t('复制')}
              onClick={() => {
                // 复制
                message.success('复制成功');
                navigator.clipboard.writeText(
                  document.querySelector<HTMLDivElement>('#log-container')?.innerText || '',
                );
              }}
            >
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="10706"
                width="16"
                height="16"
              >
                <path
                  d="M725.333333 213.333333a85.333333 85.333333 0 0 1 85.333334 85.333334v640a85.333333 85.333333 0 0 1-85.333334 85.333333H128a85.333333 85.333333 0 0 1-85.333333-85.333333V298.666667a85.333333 85.333333 0 0 1 85.333333-85.333334h597.333333z m0 85.333334H128v640h597.333333V298.666667z m170.666667-298.666667a128 128 0 0 1 127.786667 120.490667L1024 128v640a42.666667 42.666667 0 0 1-85.034667 4.992L938.666667 768V128a42.666667 42.666667 0 0 0-37.674667-42.368L896 85.333333H298.666667A42.666667 42.666667 0 0 1 293.674667 0.298667L298.666667 0h597.333333z"
                  fill="#0f7cf4"
                  p-id="10707"
                ></path>
              </svg>
            </a>
            <a
              style={{ display: 'flex' }}
              title={I18N.t('清空')}
              onClick={() => {
                // 清空
                if (isConsole) {
                  setConsoleLogs([]);
                  sendMessage('clearTaskConsoleLogs');
                } else {
                  setLogs([]);
                  sendMessage('clearTaskLogs');
                }
              }}
            >
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="12032"
                width="16"
                height="16"
              >
                <path
                  d="M85.333333 981.333333V469.333333H0V298.666667a128 128 0 0 1 128-128h256V85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333h85.333334a85.333333 85.333333 0 0 1 85.333333 85.333333v85.333334h256a128 128 0 0 1 128 128v170.666666h-85.333333v512H85.333333z m768-512H170.666667v426.666667h85.333333v-213.333333a42.666667 42.666667 0 0 1 85.333333 0v213.333333h128v-213.333333a42.666667 42.666667 0 0 1 85.333334 0v213.333333h128v-213.333333a42.666667 42.666667 0 0 1 85.333333 0v213.333333h85.333333V469.333333z m42.666667-213.333333H128a42.666667 42.666667 0 0 0-42.368 37.674667L85.333333 298.666667v85.333333h853.333334V298.666667a42.666667 42.666667 0 0 0-37.674667-42.368L896 256z m-341.333333-170.666667h-85.333334v85.333334h85.333334V85.333333z"
                  fill="#0f7cf4"
                  p-id="12033"
                ></path>
              </svg>
            </a>
          </Space>
        </Col>
      </Row>
      {isRunning && (
        <div className={styles.fixedBanner}>
          <div className={styles.rpaFlowText}>
            {finishing
              ? I18N.t('正在停止“{{flowName}}”RPA流程', { flowName: env?.currentFlowName ?? '--' })
              : I18N.t('RPA流程“{{flowName}}”正在执行', { flowName: env?.currentFlowName ?? '--' })}
          </div>
        </div>
      )}
      <div id="log-container" className={styles.body}>
        {loadMoreRow}
        {logContent}
        {view === 'console' && consoleStatusText}
      </div>
      {!!footerAction && <div className={styles.footer}>{footerAction}</div>}
      <Modal
        title={I18N.t('日志内容格式化')}
        visible={logSettingsModalVisible}
        onOk={() => {
          const values = form.getFieldsValue(true);
          setLogLevel(values.logLevel);
          setLogFormat(values.logFormat);
          setLogWrap(values.logWrap);
          setLogSettingsModalVisible(false);
        }}
        onCancel={() => setLogSettingsModalVisible(false)}
        okText={I18N.t('确定')}
        cancelText={I18N.t('取消')}
      >
        <Form form={form} requiredMark={false} initialValues={{ logLevel, logFormat, logWrap }}>
          <Alert
            showIcon
            message={I18N.t('可设置日志输出的Level，并可自定义日志输出的格式')}
            style={{ marginBottom: 24 }}
          />

          <Form.Item label="Level" name="logLevel">
            <Select>
              <Select.Option value={0}>Debug</Select.Option>
              <Select.Option value={1}>Info</Select.Option>
              <Select.Option value={2}>Error</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label={I18N.t('格式化')} name="logFormat">
            <FormatSelector />
          </Form.Item>
          <Form.Item name="logWrap" valuePropName="checked">
            <Checkbox>{I18N.t('自动换行')}</Checkbox>
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={I18N.t('控制台内容格式化')}
        visible={consoleSettingsModalVisible}
        onOk={() => {
          const values = consoleForm.getFieldsValue(true);
          setConsoleFormat(values.consoleFormat);
          setConsoleWrap(values.consoleWrap);
          setConsoleSettingsModalVisible(false);
        }}
        onCancel={() => setConsoleSettingsModalVisible(false)}
        okText={I18N.t('确定')}
        cancelText={I18N.t('取消')}
      >
        <Form
          form={consoleForm}
          requiredMark={false}
          initialValues={{ consoleFormat, consoleWrap }}
        >
          <Alert
            showIcon
            message={I18N.t('可设置控制台输出内容的格式')}
            style={{ marginBottom: 24 }}
          />
          <Form.Item label={I18N.t('格式化')} name="consoleFormat">
            <FormatSelector />
          </Form.Item>
          <Form.Item name="consoleWrap" valuePropName="checked">
            <Checkbox>{I18N.t('自动换行')}</Checkbox>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

function FormatSelector({ value, onChange }: any) {
  return (
    <Select
      mode="multiple"
      allowClear
      style={{ width: '100%' }}
      placeholder={I18N.t('请选择')}
      value={value ? value.split(' ') : []}
      onChange={(values) => {
        onChange(values.length > 0 ? values.join(' ') : '');
      }}
    >
      <Select.Option value="{date}">{I18N.t('年月日')}</Select.Option>
      <Select.Option value="{time}">{I18N.t('时分秒')}</Select.Option>
      <Select.Option value="{node.nid}">{I18N.t('节点ID')}</Select.Option>
      <Select.Option value="{node.name}">{I18N.t('节点名称')}</Select.Option>
      <Select.Option value="{node.type}">{I18N.t('节点类型')}</Select.Option>
    </Select>
  );
}

export default SidePanel;
