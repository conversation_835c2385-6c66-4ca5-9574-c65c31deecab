import {<PERSON>Handler} from "./common";
import Tab = chrome.tabs.Tab;

async function currentTab(): Promise<{tabId: number}> {
  let tabs = await new Promise<Tab[]>((resolve, reject) => {
    chrome.tabs.query({active: true}, (tabs) => {
      resolve(tabs);
    });
  });
  console.log(tabs);
  return {tabId: tabs[0].id!};
}


class RpaEvents extends EventHandler {
  readonly prefix = 'rpa/events/';



  async run(fun: string, data: any): Promise<any> {

    //default throw an error
    return super.run(fun, data);
  }

}

class RpaMouseEvents extends EventHandler {
  readonly prefix = 'rpa/mouse/';

  async down(options: any) {
    //todo 目前没有好的实现机制
  }

  async up(options: any) {
    //todo 目前没有好的实现机制
  }

}

export default [new RpaEvents(), new RpaMouseEvents()];
