import Tab = chrome.tabs.Tab;
import WINDOW_ID_CURRENT = chrome.windows.WINDOW_ID_CURRENT;
import { EventHandler } from './common';

class RpaTabs extends EventHandler {
  readonly prefix = 'rpa/tabs/';

  async closeTab(tab: number | 'others' | 'right') {
    let tabs = await new Promise<Tab[]>((resolve, reject) => {
      chrome.tabs.query({ windowType: 'normal' }, (tabs) => {
        resolve(tabs);
      });
    });
    let closingTabs: Tab[] = [];
    if ('others' === tab || 0 === tab) {
      for (let i = 0; i < tabs.length; i++) {
        let t = tabs[i];
        if ('others' === tab && !t.active) {
          closingTabs.push(t);
        } else if (0 === tab && t.active) {
          closingTabs.push(t);
        }
      }
    } else if ('right' === tab) {
      // 右侧的标签页
      let activeIndex = tabs.findIndex((t) => t.active);
      if (activeIndex !== -1) {
        for (let i = activeIndex + 1; i < tabs.length; i++) {
          closingTabs.push(tabs[i]);
        }
      }
    } else if (typeof tab === 'number') {
      if (tab < 0) {
        let index = tabs.length + tab;
        if (index < 0) {
          throw 'index超出范围';
        }
        closingTabs.push(tabs[index]);
      } else if (tab > 0) {
        let index = tab - 1;
        if (index >= tabs.length) {
          throw 'index超出范围';
        }
        closingTabs.push(tabs[index]);
      }
    }
    if (closingTabs.length > 0 && tabs.length == 1) {
      throw '会话浏览器最后一个标签页不可关闭';
    }
    await new Promise((resolve, reject) => {
      let tabIds: number[] = closingTabs.map((t) => t.id!);
      chrome.tabs.remove(tabIds, resolve);
    });
    return closingTabs.map((tab) => ({
      id: tab.id,
      position: tab.index + 1,
      title: tab.title,
      url: tab.url,
      active: tab.active,
    }));
  }

  async getTabs() {
    let tabs = await new Promise<Tab[]>((resolve, reject) => {
      chrome.tabs.query({ windowType: 'normal' }, (tabs) => {
        resolve(tabs);
      });
    });
    const tabsZoom = await Promise.all(tabs.map((tab) => chrome.tabs.getZoom(tab.id!)));
    return tabs.map((tab, idx) => ({
      id: tab.id,
      position: tab.index + 1,
      title: tab.title,
      url: tab.url,
      active: tab.active,
      zoom: tabsZoom[idx],
    }));
  }

  async getPopups() {
    let tabs = await new Promise<Tab[]>((resolve, reject) => {
      chrome.tabs.query({ windowType: 'popup' }, (tabs) => {
        resolve(tabs);
      });
    });
    return tabs.map((tab, idx) => ({
      id: tab.id,
      title: tab.title,
      url: tab.url,
      popup: true,
    }));
  }

  async checkCrashed(tab: Tab) {
    let crashed = false;
    if (tab.url && /https?:\/\//.test(tab.url)) {
      //判断当前tab是否已经crash了
      try {
        await chrome.tabs.sendMessage(tab.id!, 'rpa.crash.check');
      } catch (e) {
        console.log(`tab [url=${tab.url}] could not send message, crashed maybe.`, e);
        crashed = true;
      }
    }
    return crashed;
  }

  async current() {
    let tabs = await new Promise<Tab[]>((resolve, reject) => {
      chrome.tabs.query({ windowType: 'normal', active: true }, (tabs) => {
        resolve(tabs);
      });
    });
    let tab = tabs[0];
    let crashed = await this.checkCrashed(tab);
    let zoom = await chrome.tabs.getZoom(tab.id!);
    return {
      id: tab.id,
      position: tab.index + 1,
      title: tab.title,
      url: tab.url,
      active: tab.active,
      crashed: crashed,
      zoom: zoom,
    };
  }

  async newTab() {
    await new Promise<Tab>((resolve, reject) => {
      chrome.tabs.create(
        {
          active: true,
          url: 'http://szdamai.tab/newTab',
        },
        (tab) => resolve(tab),
      );
    });
  }

  async refreshTab() {
    let tabs = await new Promise<Tab[]>((resolve, reject) => {
      chrome.tabs.query({ windowType: 'normal', active: true }, (tabs) => {
        resolve(tabs);
      });
    });
    if (tabs.length > 0) {
      await new Promise((resolve, reject) => {
        chrome.tabs.reload(tabs[0].id!, {}, () => resolve(void 0));
      });
    }
  }

  async selectTab(tab: number) {
    let tabs = await new Promise<Tab[]>((resolve, reject) => {
      chrome.tabs.query({ windowType: 'normal' }, (tabs) => {
        resolve(tabs);
      });
    });
    let index = 0;
    if (tab < 0) {
      index = tabs.length + tab;
    } else {
      index = tab - 1;
    }
    if (index < 0 || index >= tabs.length) {
      throw 'index超出范围';
    }
    let target = tabs[index];
    await new Promise((resolve) => {
      chrome.windows.update(WINDOW_ID_CURRENT, { focused: true }, (win) => {
        chrome.tabs.update(target.id!, { active: true }, () => {
          resolve(void 0);
        });
      });
    });
    let crashed = await this.checkCrashed(target);
    return {
      id: target.id,
      position: target.index + 1,
      title: target.title,
      url: target.url,
      active: target.active,
      crashed: crashed,
    };
  }

  async setCurrentTabZoom(zoom: number) {
    let tabs = await new Promise<Tab[]>((resolve, reject) => {
      chrome.tabs.query({ windowType: 'normal', active: true }, (tabs) => {
        resolve(tabs);
      });
    });
    if (tabs.length > 0) {
      await new Promise((resolve, reject) => {
        chrome.tabs.setZoom(tabs[0].id!, zoom, () => resolve(void 0));
      });
    }
  }

  async run(fun: string, data: any): Promise<any> {
    //default throw an error
    return super.run(fun, data);
  }
}

export const tabs = new RpaTabs();
