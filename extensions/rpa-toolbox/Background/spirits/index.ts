import {<PERSON>Handler} from "./common";

import events from "./events";
import {tabs} from "./tabs";

class RpaSpirit {

  handlers: EventHandler[] = [];
  constructor() {
    this.handlers = this.handlers.concat(events);
    this.handlers.push(tabs);
  }

  async run(fun: string, data: any): Promise<any> {
    for(let handler of this.handlers) {
      if(fun && fun.startsWith(handler.prefix)) {
        //首先尝试直接函数调用
        let m = fun.substring(handler.prefix.length);
        //@ts-ignore
        let method = handler[m];
        if(method) {
          return method.call(handler, data);
        }
        //不是直接函数调用，交由handler自己处理
        return handler.run(fun, data);
      }
    }
    throw `不支持的调用${fun}`;
  }

}

export const rpaSpirit = new RpaSpirit();
