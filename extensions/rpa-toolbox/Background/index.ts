import { rpaSpirit } from './spirits';

const API_URL = 'http://szdamai.local';
let shopInfo: ShopInfo | any = null;
let sscToken = '';
let deviceId: string = '';
let language = 'zh';
let currentFlowName: string | undefined;
let currentTaskId: number | undefined;
let currentTaskName: string | undefined;
let currentTaskItemId: number | undefined;
let currentTaskLogs: string[] = [];
let currentTaskConsoleLogs: string[] = [];
let windowId: number | undefined;
let paused = false;
let consoleVisible = false;
let mouseTrackVisible = false;
let hasAuth = true;
let messageDispatcher: MessageDispatcher | null = null;
const LOG_ROW_LIMIT = 10000; // 限制日志行数
const CONSOLE_ROW_LIMIT = 5000; // 限制控制台日志行数

chrome.windows.getCurrent().then((window) => {
  console.info('window.id', window.id);
  windowId = window.id;
});
setInterval(() => {
  if (windowId) {
    chrome.windows.get(windowId).then((window) => {
      if (window.state === 'minimized') {
        if (messageDispatcher) {
          messageDispatcher.send('window-minimized');
        }
      }
    });
  }
}, 3000);

//functions
const sleep = (delay: number) => new Promise((resolve) => setTimeout(resolve, delay || 0));

// keepalive
setInterval(chrome.runtime.getPlatformInfo, 25 * 1000);
// win7 v109内核只支持通过 OffScreen 方式保活
async function createOffscreen() {
  await chrome.offscreen
    .createDocument({
      url: 'assets/offscreen.html',
      reasons: ['BLOBS'],
      justification: 'keep service worker running',
    })
    .catch(() => {});
}
self.onmessage = (e) => {}; // keepAlive

export function donkeyRequest(path: string, options: Record<string, any> = {}): Promise<any> {
  const { method = 'GET', params = {}, data = {}, ...restOpts } = options;
  return new Promise((resolve, reject) => {
    fetch(`${API_URL}/donkeyRequest`, {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        path,
        options: {
          method,
          params,
          data,
          ...restOpts,
        },
      }),
    }).then(async (res) => {
      const { success, message: msg, data: resData } = await res.json();
      if (success) {
        resolve(resData);
      } else {
        reject(new Error(msg));
      }
    });
  });
}

chrome.runtime.onMessage.addListener(
  async (msg: { action: string; data: { [k: string]: any } }, sender, sendResponse) => {
    if (typeof msg !== 'object') return;
    const { action, data = {} } = msg;
    console.log('onMessage', msg);
    switch (action) {
      case 'hello':
        sendResponse('hi');
        break;
      case 'getEnv':
        if (!shopInfo) {
          sendResponse({});
        } else {
          sendResponse({
            shopInfo,
            sscToken,
            currentFlowName,
            currentTaskId,
            currentTaskName,
            currentTaskItemId,
            paused,
            consoleVisible,
            mouseTrackVisible,
            hasAuth,
          });
        }
        break;
      case 'getLanguage':
        sendResponse(language);
        break;
      case 'getCurrentTaskId':
        sendResponse(currentTaskId);
        break;
      case 'getCurrentTaskLogs':
        sendResponse(currentTaskLogs);
        break;
      case 'getCurrentTaskConsoleLogs':
        sendResponse(currentTaskConsoleLogs);
        break;
      case 'runTask':
        fetch(
          `${API_URL}/api/runRpaTask?flowId=${data.rpaFlow.flowId}&bizCode=${
            data.rpaFlow.bizCode ?? ''
          }`,
        );
        // runTask(data.rpaFlow, data.paramValues).then((res) => {
        //   currentTaskLogs = [];
        //   currentTaskConsoleLogs = [];
        //   // 通知侧边栏清空日志
        //   chrome.runtime.sendMessage({ action: 'clear-task-log' });
        //   chrome.runtime.sendMessage({ action: 'task-state-update' });
        //   sendResponse(res);
        // });
        break;
      case 'clearCurrentTaskId':
        currentTaskId = undefined;
        break;
      case 'clearCurrentTaskItemId':
        currentTaskId = undefined;
        break;
      case 'clearTaskLogs':
        currentTaskLogs = [];
        break;
      case 'clearTaskConsoleLogs':
        currentTaskConsoleLogs = [];
        break;
      case 'open-side-panel':
        try {
          consoleVisible = true;
          chrome.sidePanel.setOptions({ enabled: true });
          chrome.sidePanel.open({ windowId });
        } catch (e) {
          console.error(e);
        }
        break;
      case 'close-side-panel':
        try {
          consoleVisible = false;
          chrome.sidePanel.setOptions({ enabled: false });
        } catch (e) {
          console.error(e);
        }
        break;
      case 'clearMouseTrack':
        fetch(`${API_URL}/api/${action}`);
        break;
      case 'showMouseTrack':
        mouseTrackVisible = true;
        fetch(`${API_URL}/api/${action}`);
        break;
      case 'hideMouseTrack':
        mouseTrackVisible = false;
        fetch(`${API_URL}/api/${action}`);
        break;
    }
    return true;
  },
);

async function doFetch(url: string): Promise<Response> {
  try {
    const res = await fetch(url);
    return res;
  } catch (e) {
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        try {
          const res = await doFetch(url);
          resolve(res);
        } catch (e) {
          reject(e);
        }
      }, 1000);
    });
  }
}

const rpcInvoke = async (fun: string, data: any): Promise<any> => {
  if (fun && typeof fun === 'string') {
    if (fun == 'ping') {
      return 'hello ' + data;
    }
    if (fun.startsWith('rpa/')) {
      return rpaSpirit.run(fun, data);
    }
  }
};

/**
 * 通过 websocket 来进行消息传递
 */
export class MessageDispatcher {
  private wsClient: MessageClient;
  constructor(wsClient: MessageClient) {
    this.wsClient = wsClient;
    this.wsClient.addMessageListener((msg: string) => {
      this.onMessage(msg);
    });
  }

  private onMessage(msg: string) {
    console.log('receive message', msg);
    if (/^[A-Za-z0-9-_\s]+$/.test(msg)) {
    } else {
      try {
        const payload = JSON.parse(msg);
        const { action, data } = payload;
        switch (action) {
          case 'task-log':
            // 限制日志行数
            if (currentTaskLogs.length >= LOG_ROW_LIMIT) {
              currentTaskLogs.shift();
            }
            currentTaskLogs.push(data);
            if (typeof data === 'string' && data.startsWith('print')) {
              // 限制控制台日志行数
              if (currentTaskConsoleLogs.length >= CONSOLE_ROW_LIMIT) {
                currentTaskConsoleLogs.shift();
              }
              currentTaskConsoleLogs.push(data);
            }
            try {
              chrome.runtime.sendMessage(payload);
            } catch (e) {
              console.error('@@@ sendMessage', e);
            }
            break;
          case 'task-state-update':
            if (data?.started) {
              paused = false;
              currentFlowName = data?.flowName;
              currentTaskId = data?.taskId;
              currentTaskName = data?.taskName;
              currentTaskItemId = data?.taskItemId;
              currentTaskLogs = [];
              currentTaskConsoleLogs = [];
              chrome.runtime.sendMessage({
                action: 'rpa-task-started',
                data: {
                  id: data?.taskId,
                },
              });
              chrome.runtime.sendMessage({ action: 'clear-task-log' });
              fetch(`${API_URL}/api/clearMouseTrack`);
            } else if (data?.finished) {
              paused = false;
              currentTaskId = undefined;
              currentTaskItemId = undefined;
              fetch(`${API_URL}/api/clearMouseTrack`);
            }
            chrome.runtime.sendMessage(payload);
            break;
          case 'task-item-state-update':
            chrome.runtime.sendMessage({ action: 'task-item-state-update' });
            break;
          case 'paused-status-update':
            paused = data.paused;
            chrome.runtime.sendMessage(payload);
            break;
          case 'open-side-panel':
            try {
              consoleVisible = true;
              chrome.sidePanel.setOptions({ enabled: true });
              chrome.sidePanel.open({ windowId });
            } catch (e) {
              console.error(e);
            }
            break;
          case 'close-side-panel':
            try {
              consoleVisible = false;
              chrome.sidePanel.setOptions({ enabled: false });
            } catch (e) {
              console.error(e);
            }
            break;
          case 'rpc':
            let fun = payload.fun;
            rpcInvoke(fun, data)
              .then((ret) => {
                let requestId = payload.requestId;
                this.send({
                  requestId,
                  action: 'rpc-callback',
                  success: true,
                  data: ret,
                });
              })
              .catch((err) => {
                let requestId = payload.requestId;
                this.send({
                  requestId,
                  action: 'rpc-callback',
                  success: false,
                  data: String(err),
                });
              });
            break;
        }
      } catch (e) {
        console.error('JSON parse failed', e);
      }
    }
  }

  send(payload: any) {
    try {
      this.wsClient.send(typeof payload === 'string' ? payload : JSON.stringify(payload));
    } catch (e) {
      console.log('Websocket send msg failed', e);
    }
  }
}

class MessageClient {
  identify: string;
  wsUrl: string;
  ws!: WebSocket;
  connectTimer: any;

  messageListeners: any[] = [];

  constructor(identify: string, wsUrl: string) {
    this.identify = identify;
    this.wsUrl = wsUrl;
    this.reconnect();
    setInterval(() => {
      this.healthCheck();
    }, 1000);
  }

  send(msg: string) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(msg);
    }
  }

  addMessageListener(listener: (msg: string) => void) {
    this.messageListeners.push(listener);
  }

  healthCheck() {
    if (
      !this.ws ||
      (this.ws.readyState !== WebSocket.OPEN && this.ws.readyState !== WebSocket.CONNECTING)
    ) {
      this.connectTimer = setTimeout(() => {
        clearTimeout(this.connectTimer);
        this.reconnect();
      }, 10);
    }
  }

  private reconnect() {
    if (this.ws) {
      switch (this.ws.readyState) {
        case WebSocket.CONNECTING:
        case WebSocket.OPEN:
          return;
      }
      this.ws.close();
    }

    try {
      console.log(`${new Date().toString()} [ws] connecting to ${this.wsUrl}`);
      this.ws = new WebSocket(this.wsUrl);
      this.ws.addEventListener('open', () => {
        console.log(`${new Date().toString()} [ws] opened`);
        this.ws.send('identify=' + this.identify);
      });

      this.ws.addEventListener('message', (evt) => {
        this.messageListeners.forEach((listener) => {
          listener(evt.data);
        });
      });

      this.ws.addEventListener('close', () => {
        console.log(`[ws] closed`);
        this.connectTimer = setTimeout(() => {
          clearTimeout(this.connectTimer);
          this.reconnect();
        }, 10);
      });
    } catch (e) {
      this.connectTimer = setTimeout(() => {
        clearTimeout(this.connectTimer);
        this.reconnect();
      }, 10);
    }
  }
}

const doInit = async () => {
  console.log(`${new Date().toString()} doInit`);
  await new Promise(async (resolve) => {
    const res = await doFetch(`${API_URL}/api/getEnv`);
    const resObj = await res.json();
    shopInfo = resObj.shopInfo;
    sscToken = resObj.sscToken;
    language = resObj.language;
    deviceId = resObj.deviceId;
    hasAuth =
      resObj.grantedFunctionCodes.includes('RPA_LIST') &&
      resObj.grantedFunctionCodes.includes('RPA_RUN');
    if (resObj.isWin7) {
      createOffscreen();
    }
    console.log(new Date().toString(), JSON.stringify(Object.keys(resObj), null, 2));

    messageDispatcher = new MessageDispatcher(new MessageClient('plugin:rpa', resObj.wsUrl));
    resolve(true);
  });
};

//@ts-ignore
doInit().then(() => {});
