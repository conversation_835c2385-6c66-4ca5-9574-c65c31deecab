/**
 * 向 background 发送事件
 * @param action
 * @param data
 * @param responseCallback
 */
export function sendMessage(
  action: string,
  data: { [k: string]: any } = {},
  responseCallback = (res: any) => {},
) {
  console.log('sendMessage', action, data);
  chrome.runtime.sendMessage({ action, data }, responseCallback);
}

/**
 * 由客户端代发请求
 * @param path
 * @param options
 */
export function donkeyRequest(path: string, options: Record<string, any> = {}): Promise<any> {
  const { method = 'GET', params = {}, data = {}, ...restOpts } = options;
  return new Promise((resolve, reject) => {
    fetch(`http://szdamai.local/donkeyRequest`, {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        path,
        options: {
          method,
          params,
          data,
          ...restOpts,
        },
      }),
    }).then(async (res) => {
      const { success, message: msg, data: resData } = await res.json();
      if (success) {
        resolve(resData);
      } else {
        reject(new Error(msg));
      }
    });
  });
}

/**
 * 由客户端代发请求，无需鉴权，可向任何 domain 发起
 * @param url
 * @param options
 */
export function rawRequest(url: string, options: Record<string, any> = {}) {
  const { method = 'GET', params = {}, data = {}, ...restOpts } = options;
  return new Promise((resolve, reject) => {
    fetch(`http://szdamai.local/rawRequest`, {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url,
        options: {
          method,
          params,
          data,
          ...restOpts,
        },
      }),
    }).then(async (res) => {
      const { success, message: msg, data: resData } = await res.json();
      if (success) {
        resolve(resData);
      } else {
        reject(new Error(msg));
      }
    });
  });
}

/**
 * 把对象的值的首末空格去掉
 * @param map
 * @param exclude
 */
export const trimValues = (map: Record<string, any>, exclude: string[] = ['password']) => {
  const obj: any = {};
  Object.keys(map).forEach((key: string) => {
    const value = map[key];
    if (!exclude.includes(key) && typeof value === 'string') {
      obj[key] = value.trim();
    } else {
      obj[key] = value;
    }
  });
  return obj;
};

export function escapeHtml(str: string) {
  return str.replace(/[&<>"']/g, (match) => {
    return (
      {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;',
      }[match] ?? match
    );
  });
}

export function convertLinksToHTML(input: string) {
  const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;

  const result = input.replace(linkRegex, function (match, p1, p2) {
    const linkText = escapeHtml(p1);
    const linkUrl = escapeHtml(p2);
    let openInExternalBrowser = false;
    try {
      const urlObj = new URL(linkUrl);
      if (urlObj.searchParams.get('__openInExternalBrowser')) {
        openInExternalBrowser = true;
      }
    } catch (e) {}
    if (openInExternalBrowser) {
      return `<a data-url="${linkUrl}">${linkText}</a>`;
    }
    return '<a href="' + linkUrl + '" target="_blank">' + linkText + '</a>';
  });

  return result;
}
