import React, { FC, HTMLProps } from 'react';

interface Props extends HTMLProps<HTMLImageElement> {
  fileName: string;
}

const Icon: FC<Props> = (props) => {
  const { fileName, style = {}, className = '' } = props;

  const url = `/assets/images/${fileName}.png`;
  const src = chrome.runtime.getURL(url);
  const src2x = chrome.runtime.getURL(url.replace('.png', '@2x.png'));
  return (
    <img
      className={className}
      srcSet={`${src} 1x,${src2x} 2x`}
      src={src}
      alt=""
      style={{ pointerEvents: 'none', userSelect: 'none', ...style }}
    />
  );
};

export default Icon;
