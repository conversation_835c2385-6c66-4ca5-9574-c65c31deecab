.wrap {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji";
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  .octicon {
    display: inline-block;
    fill: currentColor;
    vertical-align: text-bottom;
    display: inline-block;
    overflow: visible !important;
    vertical-align: text-bottom;
    fill: currentColor;
  }
  h1 {
    &:hover {
      .anchor {
        .octicon-link {
          &:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
          }
          visibility: visible;
        }
        text-decoration: none;
      }
    }
    margin: .67em 0;
    font-weight: 600;
    padding-bottom: .3em;
    font-size: 2em;
    border-bottom: 1px solid hsla(210,18%,87%,1);
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    .octicon-link {
      color: #24292f;
      vertical-align: middle;
      visibility: hidden;
    }
    tt {
      padding: 0 .2em;
      font-size: inherit;
    }
    code {
      padding: 0 .2em;
      font-size: inherit;
    }
  }
  h2 {
    &:hover {
      .anchor {
        .octicon-link {
          &:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
          }
          visibility: visible;
        }
        text-decoration: none;
      }
    }
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    font-weight: 600;
    padding-bottom: .3em;
    font-size: 1.5em;
    border-bottom: 1px solid hsla(210,18%,87%,1);
    .octicon-link {
      color: #24292f;
      vertical-align: middle;
      visibility: hidden;
    }
    tt {
      padding: 0 .2em;
      font-size: inherit;
    }
    code {
      padding: 0 .2em;
      font-size: inherit;
    }
  }
  h3 {
    &:hover {
      .anchor {
        .octicon-link {
          &:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
          }
          visibility: visible;
        }
        text-decoration: none;
      }
    }
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    font-weight: 600;
    font-size: 1.25em;
    .octicon-link {
      color: #24292f;
      vertical-align: middle;
      visibility: hidden;
    }
    tt {
      padding: 0 .2em;
      font-size: inherit;
    }
    code {
      padding: 0 .2em;
      font-size: inherit;
    }
  }
  h4 {
    &:hover {
      .anchor {
        .octicon-link {
          &:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
          }
          visibility: visible;
        }
        text-decoration: none;
      }
    }
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    font-weight: 600;
    font-size: 1em;
    .octicon-link {
      color: #24292f;
      vertical-align: middle;
      visibility: hidden;
    }
    tt {
      padding: 0 .2em;
      font-size: inherit;
    }
    code {
      padding: 0 .2em;
      font-size: inherit;
    }
  }
  h5 {
    &:hover {
      .anchor {
        .octicon-link {
          &:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
          }
          visibility: visible;
        }
        text-decoration: none;
      }
    }
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    font-weight: 600;
    font-size: .875em;
    .octicon-link {
      color: #24292f;
      vertical-align: middle;
      visibility: hidden;
    }
    tt {
      padding: 0 .2em;
      font-size: inherit;
    }
    code {
      padding: 0 .2em;
      font-size: inherit;
    }
  }
  h6 {
    &:hover {
      .anchor {
        .octicon-link {
          &:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
          }
          visibility: visible;
        }
        text-decoration: none;
      }
    }
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    font-weight: 600;
    font-size: .85em;
    color: #57606a;
    .octicon-link {
      color: #24292f;
      vertical-align: middle;
      visibility: hidden;
    }
    tt {
      padding: 0 .2em;
      font-size: inherit;
    }
    code {
      padding: 0 .2em;
      font-size: inherit;
    }
  }
  details {
    display: block;
    margin-top: 0;
    margin-bottom: 16px;
    summary {
      cursor: pointer;
    }
    &:not([open]) {
      >* {
        &:not(summary) {
          display: none !important;
        }
      }
    }
  }
  figcaption {
    display: block;
  }
  figure {
    display: block;
    margin: 1em 40px;
  }
  summary {
    display: list-item;
    h1 {
      display: inline-block;
      padding-bottom: 0;
      border-bottom: 0;
      .anchor {
        margin-left: -40px;
      }
    }
    h2 {
      display: inline-block;
      padding-bottom: 0;
      border-bottom: 0;
      .anchor {
        margin-left: -40px;
      }
    }
    h3 {
      display: inline-block;
      .anchor {
        margin-left: -40px;
      }
    }
    h4 {
      display: inline-block;
      .anchor {
        margin-left: -40px;
      }
    }
    h5 {
      display: inline-block;
      .anchor {
        margin-left: -40px;
      }
    }
    h6 {
      display: inline-block;
      .anchor {
        margin-left: -40px;
      }
    }
  }
  [hidden] {
    display: none !important;
  }
  a {
    background-color: transparent;
    color: #0969da;
    text-decoration: none;
    transition: 80ms cubic-bezier(0.33, 1, 0.68, 1);
    transition-property: color,background-color,box-shadow,border-color;
    &:hover {
      text-decoration: underline;
    }
    &:focus {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
      &:not(:focus-visible) {
        outline: solid 1px transparent;
      }
    }
    &:focus-visible {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
    }
    &:not([class]) {
      &:focus {
        outline-offset: 0;
      }
      &:focus-visible {
        outline-offset: 0;
      }
    }
    &:not([href]) {
      color: inherit;
      text-decoration: none;
    }
  }
  abbr[title] {
    border-bottom: none;
    text-decoration: underline dotted;
  }
  b {
    font-weight: 600;
  }
  strong {
    font-weight: 600;
  }
  dfn {
    font-style: italic;
  }
  mark {
    background-color: #fff8c5;
    color: #24292f;
  }
  small {
    font-size: 90%;
  }
  sub {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
    bottom: -0.25em;
  }
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
    top: -0.5em;
  }
  img {
    border-style: none;
    max-width: 100%;
    box-sizing: content-box;
    background-color: #ffffff;
  }
  code {
    font-family: monospace;
    font-size: 1em;
    font-family: ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;
    font-size: 12px;
    padding: .2em .4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(175,184,193,0.2);
    border-radius: 6px;
    br {
      display: none;
    }
  }
  kbd {
    font-family: monospace;
    font-size: 1em;
    display: inline-block;
    padding: 3px 5px;
    font: 11px ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;
    line-height: 10px;
    color: #24292f;
    vertical-align: middle;
    background-color: #f6f8fa;
    border: solid 1px rgba(175,184,193,0.2);
    border-bottom-color: rgba(175,184,193,0.2);
    border-radius: 6px;
    box-shadow: inset 0 -1px 0 rgba(175,184,193,0.2);
  }
  pre {
    font-family: monospace;
    font-size: 1em;
    margin-top: 0;
    margin-bottom: 0;
    font-family: ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;
    font-size: 12px;
    word-wrap: normal;
    margin-top: 0;
    margin-bottom: 16px;
    padding: 16px;
    overflow: auto;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 6px;
    code {
      font-size: 100%;
      display: inline;
      max-width: auto;
      padding: 0;
      margin: 0;
      overflow: visible;
      line-height: inherit;
      word-wrap: normal;
      background-color: transparent;
      border: 0;
    }
    >code {
      padding: 0;
      margin: 0;
      word-break: normal;
      white-space: pre;
      background: transparent;
      border: 0;
    }
    tt {
      display: inline;
      max-width: auto;
      padding: 0;
      margin: 0;
      overflow: visible;
      line-height: inherit;
      word-wrap: normal;
      background-color: transparent;
      border: 0;
    }
  }
  samp {
    font-family: monospace;
    font-size: 1em;
    font-family: ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;
    font-size: 12px;
    font-size: 85%;
  }
  hr {
    box-sizing: content-box;
    overflow: hidden;
    background: transparent;
    border-bottom: 1px solid hsla(210,18%,87%,1);
    height: .25em;
    padding: 0;
    margin: 24px 0;
    background-color: #d0d7de;
    border: 0;
    &::before {
      display: table;
      content: "";
    }
    &::after {
      display: table;
      clear: both;
      content: "";
    }
  }
  input {
    font: inherit;
    margin: 0;
    overflow: visible;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    &::-webkit-outer-spin-button {
      margin: 0;
      -webkit-appearance: none;
      appearance: none;
    }
    &::-webkit-inner-spin-button {
      margin: 0;
      -webkit-appearance: none;
      appearance: none;
    }
  }
  [type=button] {
    -webkit-appearance: button;
  }
  [type=reset] {
    -webkit-appearance: button;
  }
  [type=submit] {
    -webkit-appearance: button;
  }
  [type=checkbox] {
    box-sizing: border-box;
    padding: 0;
  }
  [type=radio] {
    box-sizing: border-box;
    padding: 0;
  }
  [type=number] {
    &::-webkit-inner-spin-button {
      height: auto;
    }
    &::-webkit-outer-spin-button {
      height: auto;
    }
  }
  [type=search] {
    &::-webkit-search-cancel-button {
      -webkit-appearance: none;
    }
    &::-webkit-search-decoration {
      -webkit-appearance: none;
    }
  }
  &::-webkit-input-placeholder {
    color: inherit;
    opacity: .54;
  }
  &::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
  }
  table {
    border-spacing: 0;
    border-collapse: collapse;
    display: block;
    width: max-content;
    max-width: 100%;
    overflow: auto;
    margin-top: 0;
    margin-bottom: 16px;
    th {
      font-weight: 600;
      padding: 6px 13px;
      border: 1px solid #d0d7de;
    }
    td {
      padding: 6px 13px;
      border: 1px solid #d0d7de;
    }
    tr {
      background-color: #ffffff;
      border-top: 1px solid hsla(210,18%,87%,1);
      &:nth-child(2n) {
        background-color: #f6f8fa;
      }
    }
    img {
      background-color: transparent;
    }
  }
  td {
    padding: 0;
  }
  th {
    padding: 0;
  }
  [role=button] {
    transition: 80ms cubic-bezier(0.33, 1, 0.68, 1);
    transition-property: color,background-color,box-shadow,border-color;
    &:focus {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
      &:not(:focus-visible) {
        outline: solid 1px transparent;
      }
    }
    &:focus-visible {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
    }
  }
  input[type=radio] {
    transition: 80ms cubic-bezier(0.33, 1, 0.68, 1);
    transition-property: color,background-color,box-shadow,border-color;
    &:focus {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
      outline-offset: 0;
      &:not(:focus-visible) {
        outline: solid 1px transparent;
      }
    }
    &:focus-visible {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
      outline-offset: 0;
    }
  }
  input[type=checkbox] {
    transition: 80ms cubic-bezier(0.33, 1, 0.68, 1);
    transition-property: color,background-color,box-shadow,border-color;
    &:focus {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
      outline-offset: 0;
      &:not(:focus-visible) {
        outline: solid 1px transparent;
      }
    }
    &:focus-visible {
      outline: 2px solid #0969da;
      outline-offset: -2px;
      box-shadow: none;
      outline-offset: 0;
    }
  }
  p {
    margin-top: 0;
    margin-bottom: 10px;
    margin-top: 0;
    margin-bottom: 16px;
  }
  blockquote {
    margin: 0;
    padding: 0 1em;
    color: #57606a;
    border-left: .25em solid #d0d7de;
    margin-top: 0;
    margin-bottom: 16px;
    &:first-child {
      margin-top: 0;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  ul {
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 16px;
    ol {
      list-style-type: lower-roman;
      margin-top: 0;
      margin-bottom: 0;
      ol {
        list-style-type: lower-alpha;
      }
    }
    ul {
      ol {
        list-style-type: lower-alpha;
      }
      margin-top: 0;
      margin-bottom: 0;
    }
  }
  ol {
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 16px;
    list-style-type: initial;
    ol {
      list-style-type: lower-roman;
      margin-top: 0;
      margin-bottom: 0;
      ol {
        list-style-type: lower-alpha;
      }
    }
    ul {
      ol {
        list-style-type: lower-alpha;
      }
      margin-top: 0;
      margin-bottom: 0;
    }
  }
  dd {
    margin-left: 0;
  }
  tt {
    font-family: ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;
    font-size: 12px;
    padding: .2em .4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(175,184,193,0.2);
    border-radius: 6px;
    br {
      display: none;
    }
  }
  &::placeholder {
    color: #6e7781;
    opacity: 1;
  }
  &::before {
    display: table;
    content: "";
  }
  &::after {
    display: table;
    clear: both;
    content: "";
  }
  >* {
    &:first-child {
      margin-top: 0 !important;
    }
    &:last-child {
      margin-bottom: 0 !important;
    }
  }
  .absent {
    color: #cf222e;
  }
  .anchor {
    float: left;
    padding-right: 4px;
    margin-left: -20px;
    line-height: 1;
    &:focus {
      outline: none;
    }
  }
  dl {
    margin-top: 0;
    margin-bottom: 16px;
    padding: 0;
    dt {
      padding: 0;
      margin-top: 16px;
      font-size: 1em;
      font-style: italic;
      font-weight: 600;
    }
    dd {
      padding: 0 16px;
      margin-bottom: 16px;
    }
  }
  ul.no-list {
    padding: 0;
    list-style-type: none;
  }
  ol.no-list {
    padding: 0;
    list-style-type: none;
  }
  ol[type="1"] {
    list-style-type: decimal;
  }
  ol[type=a] {
    list-style-type: lower-alpha;
  }
  ol[type=i] {
    list-style-type: lower-roman;
  }
  div {
    >ol {
      &:not([type]) {
        list-style-type: decimal;
      }
    }
  }
  li {
    >p {
      margin-top: 16px;
    }
  }
  li+li {
    margin-top: .25em;
  }
  img[align=right] {
    padding-left: 20px;
  }
  img[align=left] {
    padding-right: 20px;
  }
  .emoji {
    max-width: none;
    vertical-align: text-top;
    background-color: transparent;
  }
  span.frame {
    display: block;
    overflow: hidden;
    >span {
      display: block;
      float: left;
      width: auto;
      padding: 7px;
      margin: 13px 0 0;
      overflow: hidden;
      border: 1px solid #d0d7de;
    }
    span {
      img {
        display: block;
        float: left;
      }
      span {
        display: block;
        padding: 5px 0 0;
        clear: both;
        color: #24292f;
      }
    }
  }
  span.align-center {
    display: block;
    overflow: hidden;
    clear: both;
    >span {
      display: block;
      margin: 13px auto 0;
      overflow: hidden;
      text-align: center;
    }
    span {
      img {
        margin: 0 auto;
        text-align: center;
      }
    }
  }
  span.align-right {
    display: block;
    overflow: hidden;
    clear: both;
    >span {
      display: block;
      margin: 13px 0 0;
      overflow: hidden;
      text-align: right;
    }
    span {
      img {
        margin: 0;
        text-align: right;
      }
    }
  }
  span.float-left {
    display: block;
    float: left;
    margin-right: 13px;
    overflow: hidden;
    span {
      margin: 13px 0 0;
    }
  }
  span.float-right {
    display: block;
    float: right;
    margin-left: 13px;
    overflow: hidden;
    >span {
      display: block;
      margin: 13px auto 0;
      overflow: hidden;
      text-align: right;
    }
  }
  del {
    code {
      text-decoration: inherit;
    }
  }
  .highlight {
    margin-bottom: 16px;
    pre {
      margin-bottom: 0;
      word-break: normal;
      padding: 16px;
      overflow: auto;
      font-size: 85%;
      line-height: 1.45;
      background-color: #f6f8fa;
      border-radius: 6px;
    }
  }
  .csv-data {
    td {
      padding: 5px;
      overflow: hidden;
      font-size: 12px;
      line-height: 1;
      text-align: left;
      white-space: nowrap;
    }
    th {
      padding: 5px;
      overflow: hidden;
      font-size: 12px;
      line-height: 1;
      text-align: left;
      white-space: nowrap;
      font-weight: 600;
      background: #f6f8fa;
      border-top: 0;
    }
    .blob-num {
      padding: 10px 8px 9px;
      text-align: right;
      background: #ffffff;
      border: 0;
    }
    tr {
      border-top: 0;
    }
  }
  [data-footnote-ref] {
    &::before {
      content: "[";
    }
    &::after {
      content: "]";
    }
  }
  .footnotes {
    font-size: 12px;
    color: #57606a;
    border-top: 1px solid #d0d7de;
    ol {
      padding-left: 16px;
    }
    li {
      position: relative;
      &:target {
        &::before {
          position: absolute;
          top: -8px;
          right: -8px;
          bottom: -8px;
          left: -24px;
          pointer-events: none;
          content: "";
          border: 2px solid #0969da;
          border-radius: 6px;
        }
        color: #24292f;
      }
    }
    .data-footnote-backref {
      g-emoji {
        font-family: monospace;
      }
    }
  }
  .pl-c {
    color: #6e7781;
  }
  .pl-c1 {
    color: #0550ae;
  }
  .pl-s {
    .pl-v {
      color: #0550ae;
    }
    .pl-s1 {
      color: #24292f;
    }
    color: #0a3069;
    .pl-pse {
      .pl-s1 {
        color: #0a3069;
      }
    }
  }
  .pl-e {
    color: #8250df;
  }
  .pl-en {
    color: #8250df;
  }
  .pl-smi {
    color: #24292f;
  }
  .pl-ent {
    color: #116329;
  }
  .pl-k {
    color: #cf222e;
  }
  .pl-pds {
    color: #0a3069;
  }
  .pl-sr {
    color: #0a3069;
    .pl-cce {
      color: #0a3069;
      font-weight: bold;
      color: #116329;
    }
    .pl-sre {
      color: #0a3069;
    }
    .pl-sra {
      color: #0a3069;
    }
  }
  .pl-v {
    color: #953800;
  }
  .pl-smw {
    color: #953800;
  }
  .pl-bu {
    color: #82071e;
  }
  .pl-ii {
    color: #f6f8fa;
    background-color: #82071e;
  }
  .pl-c2 {
    color: #f6f8fa;
    background-color: #cf222e;
  }
  .pl-ml {
    color: #3b2300;
  }
  .pl-mh {
    font-weight: bold;
    color: #0550ae;
    .pl-en {
      font-weight: bold;
      color: #0550ae;
    }
  }
  .pl-ms {
    font-weight: bold;
    color: #0550ae;
  }
  .pl-mi {
    font-style: italic;
    color: #24292f;
  }
  .pl-mb {
    font-weight: bold;
    color: #24292f;
  }
  .pl-md {
    color: #82071e;
    background-color: #FFEBE9;
  }
  .pl-mi1 {
    color: #116329;
    background-color: #dafbe1;
  }
  .pl-mc {
    color: #953800;
    background-color: #ffd8b5;
  }
  .pl-mi2 {
    color: #eaeef2;
    background-color: #0550ae;
  }
  .pl-mdr {
    font-weight: bold;
    color: #8250df;
  }
  .pl-ba {
    color: #57606a;
  }
  .pl-sg {
    color: #8c959f;
  }
  .pl-corl {
    text-decoration: underline;
    color: #0a3069;
  }
  [data-catalyst] {
    display: block;
  }
  [data-catalyst-inline] {
    display: inline;
  }
  g-emoji {
    display: inline-block;
    min-width: 1ch;
    font-family: "Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
    font-size: 1em;
    font-style: normal !important;
    font-weight: 400;
    line-height: 1;
    vertical-align: -0.075em;
    img {
      width: 1em;
      height: 1em;
    }
  }
  [data-a11y-animated-images=system] {
    [data-animated-image] {
      display: none;
    }
  }
  [data-a11y-animated-images=disabled] {
    [data-animated-image] {
      display: none;
    }
  }
  .task-list-item {
    list-style-type: none;
    label {
      font-weight: 400;
    }
    .handle {
      display: none;
    }
  }
  .task-list-item.enabled {
    label {
      cursor: pointer;
    }
  }
  .task-list-item+.task-list-item {
    margin-top: 4px;
  }
  .task-list-item-checkbox {
    margin: 0 .2em .25em -1.6em;
    vertical-align: middle;
  }
  .contains-task-list {
    &:dir(rtl) {
      .task-list-item-checkbox {
        margin: 0 -1.6em .25em .2em;
      }
    }
  }
  &::-webkit-calendar-picker-indicator {
    filter: invert(50%);
  }
  [data-dev-analytics-enabled] {
    [data-feeds-analytics] {
      border: 1px solid #2da44e;
      &::after {
        position: absolute;
        top: 0;
        left: 0;
        display: none;
        overflow: auto;
        font-size: 12px;
        color: #24292f;
        pointer-events: none;
        content: attr(data-feeds-analytics);
        background-color: #ffffff;
      }
      &:hover {
        position: relative;
        &::after {
          display: block;
        }
      }
    }
  }
}
