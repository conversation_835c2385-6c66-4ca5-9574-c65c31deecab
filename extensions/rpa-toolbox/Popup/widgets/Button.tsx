import React from 'react';
import classNames from 'classnames';

import styles from './buttton.less';

interface Props {
  type?: 'default' | 'primary' | 'danger';
  [k: string]: any;
}

function Button(props: Props) {
  const { type = 'default', className, disabled, onClick, ...otherProps } = props;

  const handleClick = disabled ? null : onClick;
  return (
    <div
      className={classNames(styles.btn, type, className, { [styles.disabled]: disabled })}
      onClick={handleClick}
      {...otherProps}
    >
      {props.children}
    </div>
  );
}

Button.Group = (props: any) => {
  return <div className={styles.btnWrap}>{props.children}</div>;
};

export default Button;
