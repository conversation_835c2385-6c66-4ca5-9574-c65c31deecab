export default {
  没有可执行的流程定义: 'No RPA Process to execute',
  执行RPA流程: 'Execute RPA Process',
  该流程未设置流程说明: 'This RPA Process has no readme',
  请选择日期时间: 'Select datetime',
  请输入: 'Input ',
  该流程未设置输入变量: 'The proc script does not specify any input variables',
  立即执行: 'Execute',
  返回: 'Back',
  '等待调度中...': 'Scheduling...',
  '确定要中断流程的执行吗？': 'Stop current RPA task?',
  流程一旦中断无法恢复: 'Once the process is interrupted, it cannot be resumed',
  中断流程成功: 'RPA task has bean interrupted',
  停止执行: 'Stop',
  确定: 'OK',
  取消: 'Cancel',
  流程执行: 'Run',
  流程说明: 'Readme',
  显示控制台: 'Show Console',
  清空鼠标轨迹: 'Clear Mouse Track',
  当前快捷方式不允许执行RPA流程: 'The current shortcut does not allow RPA execution',
  您没有执行RPA流程的权限: 'You do not have permission to execute RPA processes',
};
