.view-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  :global {
    .view {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: absolute;
      width: 100%;
      height: 100%;
      padding: 10px;
      opacity: 0;
      transform: translateX(100%);
      &.view-animate {
        transition: all .4s;
      }
      &.view-show {
        opacity: 1;
        transform: translateX(0);
      }
    }
  }
}

:global {
  .btn-group {
    display: flex;
    gap: 8px;
    width: 100%;
    .btn {
      flex: 1;
      text-align: center;
    }
  }

  .btn {
    display: inline-block;
    padding: 5px 10px;
    border: 1px solid #cccccc;
    border-radius: 3px;
    background-color: #fff;
    cursor: pointer;
    transition: all .4s;
    &:hover {
      color: #1890ff;
      border-color: #1890ff;
    }
    &.block {
      display: block;
      width: 100%;
      text-align: center;
    }
    &.btn-success {
      background-color: #52c41a;
      border-color: #52c41a;
      color: #FFFFFF;
    }
    &.btn-danger {
      background-color: #f5222d;
      border-color: #f5222d;
      color: #FFFFFF;
    }
    &.btn-warning {
      background-color: #fffbe6;
      border-color: #ffe58f;
      color: #333333;
    }
    &.btn-primary {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #FFFFFF;
    }
  }

  .desc-list {
    width: 100%;
    .desc-item {
      margin-bottom: 15px;
    }
  }

  .pause-time-row {
    width: 100%;
    margin-bottom: 15px;
  }

  .pause-reason-row {
    width: 100%;
    margin-bottom: 15px;
    > textarea {
      width: 100%;
      height: 150px;
      resize: none;
    }
  }

  .debug-layer {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, .7);
    z-index: 999;
  }

}
