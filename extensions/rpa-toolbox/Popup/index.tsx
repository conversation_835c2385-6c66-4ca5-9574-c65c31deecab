import React from 'react';
import ReactDOM from 'react-dom';
import { MemoryRouter, Route } from 'react-router-dom';
import { Row, Spin } from 'antd';
import 'antd/dist/antd.css';

import './global.less';
import Popup from './Popup';
import I18N from './I18N';

function App() {
  return (
    <MemoryRouter>
      <Route path="/" component={Popup} />
    </MemoryRouter>
  );
}

ReactDOM.render(
  <Row style={{ height: '100%' }} align="middle" justify="center">
    <Spin />
  </Row>,
  document.getElementById('root'),
);

const timer = setTimeout(() => {
  console.log('timeout, try to reload');
  chrome.runtime.reload();
}, 2000);

chrome.runtime.sendMessage({ action: 'getLanguage' }, (language) => {
  clearTimeout(timer);
  I18N.setLang(language);
  ReactDOM.render(<App />, document.getElementById('root'));
});
