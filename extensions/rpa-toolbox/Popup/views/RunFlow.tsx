import React, {
  useCallback,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useState,
} from 'react';
import type { FormInstance } from 'antd';
import {
  Button,
  Checkbox,
  Collapse,
  DatePicker,
  Empty,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Select,
  Space,
  TimePicker,
  Tooltip,
  Typography,
} from 'antd';
import moment from 'moment';
import styles from './runFlow.less';
import I18N from '../I18N';
import { useHistory, useParams } from 'react-router-dom';
import { donkeyRequest, sendMessage, trimValues } from '../utils';
import Icon from '../widgets/Icon';
import { RpaContext } from '../Popup';

export function renderItemCon(rpaParam: any) {
  let splitChar;
  switch (rpaParam.extra?.typeMode) {
    case 'number-input':
      return (
        <InputNumber
          style={{ width: '100%' }}
          min={rpaParam.extra?.min}
          max={rpaParam.extra?.max}
        />
      );

    case 'string-input':
      return <Input />;
    case 'string-textarea':
      return <Input.TextArea rows={3} />;
    case 'bool-checkbox':
      return <Checkbox />;
    case 'string-select':
    case 'string-multiselect':
      splitChar = (rpaParam.extra?.options || '').includes('\n') ? '\n' : ',';
      return (
        <Select
          mode={rpaParam.extra?.typeMode === 'string-multiselect' ? 'multiple' : undefined}
          dropdownMatchSelectWidth={false}
          options={(rpaParam.extra?.options || '')
            .split(splitChar)
            .map((v: string) => ({ label: v, value: v }))}
        />
      );

    case 'string-radio':
      return (
        <Radio.Group>
          <Space direction="vertical">
            {(rpaParam.extra?.options || '').split('\n').map((v: string, idx) => (
              <Radio key={idx} value={v} style={{ lineHeight: '30px' }}>
                {v}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      );
    case 'string-date':
    case 'string-datetime':
    case 'string-time':
      return <DatePickerCon type={rpaParam.extra?.typeMode} />;
    default:
      return <Input />;
  }
}

export function DatePickerCon({ type, value, onChange }: any) {
  let val = value;
  if (val === 'now') {
    val = new Date();
  }
  switch (type) {
    case 'string-datetime':
      return (
        <DatePicker
          showTime
          value={val ? moment(val) : undefined}
          placeholder={I18N.t('请选择日期时间')}
          onChange={(v) => onChange(v?.format('YYYY-MM-DD HH:mm:ss'))}
        />
      );

    case 'string-time':
      return (
        <TimePicker
          value={val ? moment(val, 'HH:mm:ss') : undefined}
          onSelect={(v) => onChange(v?.format('HH:mm:ss'))}
          onChange={(v) => onChange(v?.format('HH:mm:ss'))}
        />
      );

    default:
      return (
        <DatePicker
          value={val ? moment(val) : undefined}
          onChange={(v) => onChange(v?.format('YYYY-MM-DD'))}
        />
      );
  }
}

export function getValuePropName(rpaParam: any) {
  switch (rpaParam.extra?.typeMode) {
    case 'bool-checkbox':
      return 'checked';
    default:
      return 'value';
  }
}

/**
 * 表单项校验规则
 * @param rpaParam
 */
export function getItemRules(rpaParam: any): any[] {
  const rules = [];
  switch (rpaParam.extra?.typeMode) {
    case 'bool-checkbox':
      rules.push({ required: false });
      break;
    case 'number-input':
      rules.push({
        type: 'number',
        required: rpaParam.required,
        min: rpaParam.extra?.min,
        max: rpaParam.extra?.max,
      });
      break;
    case 'string-input':
    case 'string-textarea':
      rules.push({
        required: rpaParam.required,
        min: rpaParam.extra?.minLength,
        max: rpaParam.extra?.maxLength,
      });
      break;
    default:
      rules.push({
        required: rpaParam.required,
        message: `${I18N.t('请输入')}${rpaParam.label || rpaParam.name}`,
      });
  }
  return rules;
}

function renderFormItem(p: any) {
  let initValue: any = p.val ?? '';
  if (getValuePropName(p) === 'checked') {
    // @ts-ignore
    initValue = !!p.val && p.val !== 'false';
  } else if (p.extra?.typeMode === 'string-multiselect') {
    initValue = p.val || [];
  } else if (p.extra?.typeMode === 'number-input') {
    initValue = ['', undefined, null].includes(p.val) ? p.val : Number(p.val);
  } else if (p.extra?.typeMode === 'string-radio') {
    initValue = p.val || (p.extra?.options || '').split('\n')[0];
  }
  return (
    <Form.Item
      key={p.name}
      label={
        <Tooltip title={p.label || p.name}>
          <Typography.Text ellipsis>{p.label || p.name}</Typography.Text>
        </Tooltip>
      }
      name={p.name}
      initialValue={initValue}
      valuePropName={getValuePropName(p)}
      tooltip={p.description ? p.description : undefined}
      rules={getItemRules(p)}
      messageVariables={{ label: p.label || p.name! }}
    >
      {renderItemCon(p)}
    </Form.Item>
  );
}

const RadioGroupCmp: React.FC<Props & { radioName?: string; onChange?: (v: any) => void }> = (
  props,
) => {
  const { form, rpaParams, rpaParamGroups, radioName, onChange } = props;
  const initSelectedGroupName = useMemo(() => {
    // 变量默认选中分组根据变量的默认值来
    if (radioName) {
      const radioParam = rpaParams.find((p) => p.name === radioName);
      if (radioParam) {
        const radioGroup = rpaParamGroups.find((g) => g.name === radioParam.val);
        if (radioGroup) return radioGroup.name;
      }
    }
    return rpaParamGroups[0].name;
  }, []);
  const [selectedGroupName, setSelectedGroupName] = useState(initSelectedGroupName);

  useLayoutEffect(() => {
    onChange?.(initSelectedGroupName);
  }, [initSelectedGroupName]);

  const selectedGroup = rpaParamGroups.find((group) => group.name === selectedGroupName);
  // 筛选分组下的变量
  const filterParams = rpaParams
    .map((param) => {
      const t = selectedGroup?.params?.find((p) => p.name === param.name);
      if (!t) return null;
      return {
        ...param,
        val: t.val,
      };
    })
    .filter((p) => p !== null) as any[];

  return (
    <Radio.Group
      value={selectedGroupName}
      onChange={(e) => {
        setSelectedGroupName(e.target.value);
        onChange?.(e.target.value);
      }}
    >
      <Space direction="vertical" size={16}>
        {rpaParamGroups.map((group, idx) => {
          return (
            <>
              <Radio key={idx} value={group.name} style={{ lineHeight: '30px' }}>
                <a>{group.name}</a>
              </Radio>
              {selectedGroupName === group.name && (
                <div key={`${idx}-items`} className={styles.selectedRadioFormWrap}>
                  {filterParams.map((p) => {
                    return renderFormItem(p);
                  })}
                </div>
              )}
            </>
          );
        })}
      </Space>
    </Radio.Group>
  );
};

const CollapseCmp: React.FC<{ rpaParams: any[]; rpaParamGroup: any }> = (props) => {
  const { rpaParams, rpaParamGroup } = props;

  // 筛选分组下的变量
  const filterParams = rpaParams
    .map((param) => {
      const t = rpaParamGroup?.params?.find((p) => p.name === param.name);
      if (!t) return null;
      return {
        ...param,
        val: t.val,
      };
    })
    .filter((p) => p !== null);

  return (
    <Collapse ghost>
      <Collapse.Panel key={rpaParamGroup.name ?? ''} forceRender header={rpaParamGroup.name ?? ''}>
        {filterParams.map((p) => {
          return renderFormItem(p);
        })}
      </Collapse.Panel>
    </Collapse>
  );
};

/**
 * 设置输入参数
 * @param props
 * @constructor
 */
const SetParamsForm: React.FC<Props> = (props) => {
  const { form, rpaParams, rpaParamGroups } = props;
  // const [selectedGroupIdx, setSelectedGroupIdx] = useState(0);
  const roots: any[] = useMemo(() => {
    const _rpaParamGroups = rpaParamGroups || [];
    const arr: any[] = [];
    const radioGroups = _rpaParamGroups.filter((group) => group.extra?.type !== 'collapse');
    const collapseGroups = _rpaParamGroups.filter((group) => group.extra?.type === 'collapse');
    // 先筛选出所有在分组中的变量
    const thoseInGroup = new Set();
    _rpaParamGroups.forEach((group) => {
      group.params?.forEach((p) => thoseInGroup.add(p.name));
    });
    rpaParams.forEach((p) => {
      if (!thoseInGroup.has(p.name) && p.type !== 'radioGroup') {
        arr.push(p);
      } else {
        radioGroups.forEach((group) => {
          if (
            group.params?.some((gp) => gp.name === p.name) &&
            !arr.some((item) => item.isGroup && item.groupName === group.name)
          ) {
            // 插入一个分组占位符
            const groupKey = group.extra?.key || '';
            if (arr.some((item) => item.isGroup && item.groupKey === groupKey)) {
              // 已经存在同一 key 的 radio 分组
              const item = arr.find((g) => g.isGroup && g.groupKey === groupKey);
              if (item.groups.every((g) => g.name !== group.name)) {
                item.groups.push(group);
              }
            } else {
              arr.push({
                isGroup: true,
                groupType: 'radio',
                groupKey,
                groupName: group.name,
                groups: [group],
              });
            }
          }
        });
      }
    });
    arr.push(
      ...(collapseGroups.map((group) => ({ isGroup: true, groupType: 'collapse', group })) as any),
    );
    return arr;
  }, [rpaParams, rpaParamGroups]);

  return (
    <Form
      form={form}
      layout="horizontal"
      className={styles.setBatchParamsForm}
      style={{ marginTop: 12 }}
    >
      {roots.map((p: any) => {
        // @ts-ignore
        if (p.isGroup) {
          if (p.groupType === 'radio') {
            return (
              <Form.Item key={`${p.groupKey}-${p.groupName}`} name={p.groupKey}>
                <RadioGroupCmp
                  form={form}
                  rpaParams={rpaParams}
                  rpaParamGroups={p.groups}
                  radioName={p.groupKey}
                />
              </Form.Item>
            );
          }
          if (p.groupType === 'collapse') {
            return <CollapseCmp key={p.group.name} rpaParams={rpaParams} rpaParamGroup={p.group} />;
          }
        }
        return renderFormItem(p);
      })}
    </Form>
  );
};

type Props = {
  form: FormInstance;
  rpaParams: any[];
  rpaParamGroups: any[];
};

/**
 *
 * @param props
 * @constructor
 */
const RunFlow: React.FC = () => {
  const params = useParams<{ flowId: any }>();
  const [form] = Form.useForm();
  const [rpaFlow, setRpaFlow] = useState<any>({});
  const [rpaConfig, setRpaConfig] = useState<any>({});
  const [submitting, setSubmitting] = useState(false);
  const ctx = useContext(RpaContext);
  const history = useHistory();

  useEffect(() => {
    donkeyRequest(`/api/rpa/flow/${params.flowId}`).then((flowDetail) => {
      setRpaFlow(flowDetail);
      donkeyRequest(`/api/rpa/flowConfig/${flowDetail.configId}`).then((data) => {
        console.info('flowConfig', data);
        setRpaConfig(data);
      });
    });
  }, []);

  const start = useCallback(async () => {
    await form.validateFields();
    const values = form.getFieldsValue();
    const _params = trimValues(values);
    setSubmitting(true);
    sendMessage(
      'runTask',
      {
        rpaFlow,
        paramValues: {
          [ctx.shopInfo.id!]: _params,
        },
      },
      (res) => {
        console.info('run task res', res);
        setSubmitting(false);
      },
    );
  }, [rpaFlow, form]);

  const rpaParams = useMemo(
    () => (rpaConfig.params ?? []).filter((p) => !!p.predefine),
    [rpaConfig],
  );

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <Icon fileName="flow_16" />
        <Typography.Text ellipsis style={{ maxWidth: 280 }}>
          {rpaFlow.name}
        </Typography.Text>
      </div>
      <div className={styles.body}>
        {rpaParams.length > 0 ? (
          <SetParamsForm
            form={form}
            rpaParams={rpaParams}
            rpaParamGroups={rpaConfig.paramGroups ?? []}
          />
        ) : (
          <div className={styles.emptyWrap}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={I18N.t('该流程未设置输入变量')}
            />
          </div>
        )}
      </div>
      <div className={styles.footer}>
        <Button
          onClick={() => {
            history.push('/flows');
          }}
        >
          {I18N.t('返回')}
        </Button>
        <Button type="primary" loading={submitting} onClick={start}>
          {I18N.t('立即执行')}
        </Button>
      </div>
    </div>
  );
};

export default RunFlow;
