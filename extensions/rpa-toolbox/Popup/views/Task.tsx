import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { donkeyRequest, sendMessage } from '../utils';
import { useHistory, useParams } from 'react-router-dom';
import { RpaContext } from '../Popup';
import styles from './task.less';
import Icon from '../widgets/Icon';
import { Button, Col, message, Modal, Row, Space, Typography } from 'antd';
import I18N from '../I18N';

type Props = {};

/**
 *
 * @param props
 * @constructor
 */
const Task: React.FC<Props> = (props) => {
  const params = useParams<{ taskId: any }>();
  const [rpaTask, setRpaTask] = useState<any>({});
  const [logs, setLogs] = useState<string[]>([]);
  const ctx = useContext(RpaContext);
  const history = useHistory();
  // const logsRef = useRef(logs);

  const loadRpaTask = useCallback(() => {
    donkeyRequest(`/api/rpa/task/${params.taskId}`).then((res) => {
      setRpaTask(res);
    });
  }, []);

  useEffect(() => {
    setInterval(() => {
      loadRpaTask();
    }, 5 * 1000);
    loadRpaTask();
  }, []);

  // useEffect(() => {
  //   logsRef.current = logs;
  // }, [logs]);

  useEffect(() => {
    const onMessageListener = (msg: any) => {
      const { action, data } = msg;
      switch (action) {
        case 'task-state-update':
          loadRpaTask();
          break;
      }
    };
    chrome.runtime.onMessage.addListener(onMessageListener);
    return () => {
      chrome.runtime.onMessage.removeListener(onMessageListener);
    };
  }, []);

  const isUnfinished = useMemo(() => {
    return ['Scheduling', 'Scheduled', 'NotStart', 'Running'].includes(rpaTask.status!);
  }, [rpaTask.status]);

  const taskContent = useMemo(() => {
    return (
      <Row style={{ height: '100%' }} align="middle" justify="center">
        <Space direction="vertical" align="center">
          <Icon fileName="bot-run" />
          <div>{isUnfinished ? I18N.t('流程执行中') : I18N.t('流程执行完毕')}</div>
          <div style={{ color: '#ccc', textIndent: '2em' }}>
            {I18N.t(
              '花漾RPA采用“拟人化”执行流程，流程执行期间请不要移动/敲击鼠标/键盘，否则有可能导致流程出错',
            )}
          </div>
        </Space>
      </Row>
    );
  }, [rpaTask.status]);

  const footerAction = useMemo(() => {
    if (['Scheduling', 'Scheduled'].includes(rpaTask.status!)) {
      // 调度中
      return (
        <Button type="primary" loading block>
          {I18N.t('等待调度中...')}
        </Button>
      );
    }
    if (['NotStart', 'Running'].includes(rpaTask.status!)) {
      return (
        <Button
          type="primary"
          danger
          block
          onClick={() => {
            Modal.confirm({
              title: I18N.t('确定要中断流程的执行吗？'),
              content: I18N.t('流程一旦中断无法恢复'),
              okText: I18N.t('确定'),
              cancelText: I18N.t('取消'),
              onOk: () => {
                return donkeyRequest(`/api/rpa/task/${params.taskId}/forceEnd`, { method: 'PUT' })
                  .then(() => {
                    message.success(I18N.t('中断流程成功'));
                    sendMessage('clearCurrentTaskId', {});
                    history.push('/flows');
                  })
                  .catch((err) => {
                    message.error(err.message);
                  });
              },
            });
          }}
        >
          {I18N.t('停止执行')}
        </Button>
      );
    }
    return (
      <Button
        type="primary"
        block
        onClick={() => {
          sendMessage('clearCurrentTaskId', {});
          history.push('/flows');
        }}
      >
        {I18N.t('返回')}
      </Button>
    );
  }, [rpaTask.status]);

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <Icon fileName="flow_16" />
        <Typography.Text ellipsis style={{ maxWidth: 280 }}>
          {rpaTask.name}
        </Typography.Text>
      </div>
      <div id="log-container" className={styles.body}>
        {taskContent}
      </div>
      <div className={styles.footer}>
        <Row gutter={8} style={{ width: '100%' }}>
          <Col span={12}>
            <Button
              block
              style={{ color: 'white', backgroundColor: '#52c41a', border: 'none' }}
              onClick={() => {
                chrome.runtime.sendMessage({ action: 'open-side-panel' });
                setTimeout(() => {
                  window.close();
                }, 100);
              }}
            >
              显示控制台
            </Button>
          </Col>
          <Col span={12}>{footerAction}</Col>
        </Row>
      </div>
    </div>
  );
};

export default Task;
