.wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  :global {
    .ant-form-item {
      margin-bottom: 12px !important;
      > .ant-form-item-label {
        flex: 0 0 100px !important;
      }
      > .ant-form-item-control {
        flex: 1 !important;
      }
    }
  }
}

.header {
  flex: 0 0 auto;
  height: 40px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border-bottom: 1px solid #ddd;
}

.body {
  flex: 1;
  overflow: auto;
  padding: 0 8px;
}

.empty-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.footer {
  flex: 0 0 auto;
  height: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border-top: 1px solid #ddd;
}

.group-radio {
  width: 100%;
  overflow: hidden;
  line-height: 30px;
  margin-top: 8px;
  &:first-child {
    margin-top: 0;
  }
  > span:last-child {
    overflow: hidden;
  }
  + .table-wrap {
    margin-left: 24px;
  }
  + :global(.ant-form) {
    margin-left: 24px;
  }
}

.set-batch-params-form {
  :global {
    .ant-form-item-control-input-content {
      > .ant-radio-group {
        width: 100%;
        > .ant-space-vertical {
          width: 100%;
        }
      }
    }
    .ant-collapse-ghost {
      .ant-collapse-header {
        padding-left: 0;
        padding-top: 0;
        color: #3b78f4;
      }
      .ant-collapse-content-box {
        padding-left: 0;
        .ant-form-item-label {
          padding-left: 24px;
        }
      }
    }
  }
  .selected-radio-form-wrap {
    margin-left: 24px;
    > :global(.ant-form-item):last-child {
      margin-bottom: 0;
    }
    > :global(.ant-form-item) > :global(.ant-form-item-label) {
      //padding-left: 24px;
    }
  }
}
