import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Row, Col, Spin, Space, Typography, Drawer, Tooltip, Modal, Button } from 'antd';
import _ from 'lodash';
import { donkeyRequest, sendMessage } from '../utils';
import { RpaContext } from '../Popup';
import Icon from '../widgets/Icon';
import I18N from '../I18N';
import styles from './flows.less';
import MarkdownView from '../widgets/MarkdownView';

type Props = {};

/**
 *
 * @param props
 * @constructor
 */
const Flows: React.FC<Props> = (props) => {
  const {} = props;
  const ctx = useContext(RpaContext);
  const [flows, setFlows] = useState<any[] | null>(null);
  const [rpaEnabled, setRpaEnabled] = useState<boolean | null>(null);
  const [view, setView] = useState<'pending' | 'running'>(
    ctx.currentTaskId ? 'running' : 'pending',
  );
  const [consoleVisible, setConsoleVisible] = useState(ctx.consoleVisible);
  const [mouseTrackVisible, setMouseTrackVisible] = useState(ctx.mouseTrackVisible);
  const [readme, setReadme] = useState<string>('');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const history = useHistory();

  useEffect(() => {
    if (ctx.sscToken) {
      donkeyRequest(`/api/shop/shortcut/${ctx.sscToken}`).then((shopShortcutDetailVo) => {
        setRpaEnabled(shopShortcutDetailVo.rpaEnabled);
      });
    } else {
      setRpaEnabled(true);
    }
    if (ctx.shopInfo) {
      donkeyRequest(`/api/shop/flow/${ctx.shopInfo.id}/getFlows/v20240118`).then((flows) => {
        const _list = _.orderBy(flows || [], (item) => {
          const index = [
            'huayoung.tk.invite',
            'huayoung.tk.extension.sendMessage',
            'huayoung.tk.creator.import',
            'huayoung.tk.creator.fetch',
            'huayoung.tk.creator.catch',
            'huayoung.tk.plan.clean',
          ].indexOf(item.bizCode);
          return index === -1 ? 999 : index;
        });
        setFlows(_list);
      });
    }
    const onMessageListener = (msg: any) => {
      const { action, data } = msg;
      switch (action) {
        case 'rpa-task-started':
          setView('running');
          break;
        case 'task-state-update':
          if (data?.finished) {
            setView('pending');
          }
          break;
      }
    };
    chrome.runtime.onMessage.addListener(onMessageListener);
    return () => {
      chrome.runtime.onMessage.removeListener(onMessageListener);
    };
  }, []);

  const loadFlowReadme = useCallback((flow: any) => {
    setDrawerOpen(true);
    setReadme('');
    donkeyRequest(`/api/rpa/flowConfig/${flow.configId}`).then((res) => {
      setReadme(res.readme);
    });
  }, []);

  const footer = useMemo(() => {
    return (
      <Row gutter={8} wrap={false}>
        <Col span={12}>
          <Button
            type="primary"
            ghost
            block
            icon={
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                style={{ verticalAlign: 'text-top', marginRight: 4 }}
              >
                <path
                  d="M896 170.709333C896 147.157333 875.776 128 850.773333 128H173.226667C148.181333 128 128 147.157333 128 170.709333v469.248C128 663.552 148.224 682.666667 173.226667 682.666667h677.546666c25.002667 0 45.226667-19.114667 45.226667-42.709334V170.709333zM882.517333 768H141.482667C86.912 768 42.666667 724.736 42.666667 671.274667V139.392C42.666667 85.930667 86.954667 42.666667 141.44 42.666667h741.12C937.045333 42.666667 981.333333 85.930667 981.333333 139.392v531.882667C981.333333 724.736 937.088 768 882.517333 768z"
                  fill="#262626"
                ></path>
                <path
                  d="M256 896h512a42.666667 42.666667 0 0 1 0 85.333333H256a42.666667 42.666667 0 0 1 0-85.333333z"
                  fill="#0F7CF4"
                ></path>
              </svg>
            }
            onClick={() => {
              setConsoleVisible(!consoleVisible);
              chrome.runtime.sendMessage({
                action: consoleVisible ? 'close-side-panel' : 'open-side-panel',
              });
              setTimeout(() => {
                window.close();
              }, 100);
            }}
          >
            {consoleVisible ? I18N.t('隐藏控制台') : I18N.t('显示控制台')}
          </Button>
        </Col>
        <Col span={12}>
          <Button
            type="primary"
            ghost
            block
            icon={
              <svg
                className="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                style={{ verticalAlign: 'text-top', marginRight: 4 }}
              >
                <path
                  d="M512.047361 1023.997867a511.972695 511.972695 0 1 1 362.00736-149.965335A508.559543 508.559543 0 0 1 512.047361 1023.997867z"
                  fill="#0B5BFF"
                  opacity=".951"
                ></path>
                <path
                  d="M512.047361 896.004693a383.979521 383.979521 0 1 1 271.516186-112.463335A381.419658 381.419658 0 0 1 512.047361 896.004693z"
                  fill="#0BA6FF"
                  opacity=".951"
                ></path>
                <path
                  d="M512.047361 768.011519a255.986347 255.986347 0 1 1 181.025012-74.961335A254.279772 254.279772 0 0 1 512.047361 768.011519z"
                  fill="#0BE6FF"
                  opacity=".951"
                ></path>
                <path
                  d="M512.047361 640.018346a127.993174 127.993174 0 1 1 0-255.986348 127.993174 127.993174 0 0 1 0 255.986348z"
                  fill="#4652FF"
                  opacity=".951"
                ></path>
              </svg>
            }
            onClick={() => {
              setMouseTrackVisible(!mouseTrackVisible);
              if (mouseTrackVisible) {
                // 隐藏鼠标轨迹时顺便清空鼠标轨迹
                chrome.runtime.sendMessage({ action: 'clearMouseTrack' });
              }
              chrome.runtime.sendMessage({
                action: mouseTrackVisible ? 'hideMouseTrack' : 'showMouseTrack',
              });
            }}
          >
            {mouseTrackVisible ? I18N.t('隐藏鼠标轨迹') : I18N.t('显示鼠标轨迹')}
          </Button>
        </Col>
      </Row>
    );
  }, [consoleVisible, mouseTrackVisible]);

  if (!flows || rpaEnabled === null) {
    return (
      <Row style={{ height: '100%' }} align="middle" justify="center">
        <Spin />
      </Row>
    );
  }
  if (view === 'running') {
    return (
      <div className={styles.wrap}>
        <div id="log-container" className={styles.table} style={{ overflow: 'hidden' }}>
          <Row style={{ height: '100%' }} align="middle" justify="center">
            <Space direction="vertical" align="center">
              <Icon fileName="bot-run" />
              <div>{I18N.t('流程执行中')}</div>
              <Row align="middle" gutter={4} style={{ width: 280 }}>
                <Col flex="0 0 auto">
                  <span>{I18N.t('流程定义')}：</span>
                </Col>
                <Col flex="0 0 auto" style={{ display: 'flex' }}>
                  <svg
                    className="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="13607"
                    width="16"
                    height="16"
                  >
                    <path
                      d="M753.792 469.333333l10.282667 0.213334C883.84 474.026667 981.333333 556.928 981.333333 661.333333c0 104.533333-97.792 187.52-217.770666 191.829334l-9.813334 0.170666H298.666667v-85.333333h455.125333l8.448-0.213333c72.533333-3.413333 128.085333-51.029333 128.085333-106.453334 0-55.466667-55.68-103.168-128.426666-106.496l-8.106667-0.170666H256c-116.650667 0-213.333333-84.608-213.333333-192 0-104.405333 91.392-187.306667 203.648-191.786667L256 170.666667h426.666667v85.333333H256C184.149333 256 128 305.109333 128 362.666667c0 55.424 52.053333 102.997333 120.064 106.453333L256 469.333333h497.792z"
                      fill="#262626"
                      p-id="13608"
                    ></path>
                    <path
                      d="M213.333333 640a170.666667 170.666667 0 1 1 0 341.333333 170.666667 170.666667 0 0 1 0-341.333333z m0 85.333333a85.333333 85.333333 0 1 0 0 170.666667 85.333333 85.333333 0 0 0 0-170.666667zM810.666667 42.666667a170.666667 170.666667 0 1 1 0 341.333333 170.666667 170.666667 0 0 1 0-341.333333z m0 85.333333a85.333333 85.333333 0 1 0 0 170.666667 85.333333 85.333333 0 0 0 0-170.666667z"
                      fill="#0F7CF4"
                      p-id="13609"
                    ></path>
                  </svg>
                </Col>
                <Col flex="1" style={{ overflow: 'hidden' }}>
                  <Typography.Text ellipsis title={ctx.currentFlowName}>
                    {ctx.currentFlowName}
                  </Typography.Text>
                </Col>
              </Row>
              <Row align="middle" gutter={4} style={{ width: 280 }}>
                <Col flex="0 0 auto">
                  <span>{I18N.t('流程任务')}：</span>
                </Col>
                <Col flex="0 0 auto" style={{ display: 'flex' }}>
                  <svg
                    className="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="13803"
                    width="16"
                    height="16"
                  >
                    <path
                      d="M682.666667 298.666667H341.333333a85.333333 85.333333 0 0 1-85.333333-85.333334H170.666667v725.333334h682.666666V213.333333h-85.333333a85.333333 85.333333 0 0 1-85.333333 85.333334z m0-170.666667H341.333333v85.333333h341.333334V128z m170.666666 0a85.333333 85.333333 0 0 1 85.333334 85.333333v725.333334a85.333333 85.333333 0 0 1-85.333334 85.333333H170.666667a85.333333 85.333333 0 0 1-85.333334-85.333333V213.333333a85.333333 85.333333 0 0 1 85.333334-85.333333h85.333333a85.333333 85.333333 0 0 1 85.333333-85.333333h341.333334a85.333333 85.333333 0 0 1 85.333333 85.333333h85.333333z"
                      fill="#262626"
                      p-id="13804"
                    ></path>
                    <path
                      d="M307.072 630.101333a42.666667 42.666667 0 0 1 60.330667-60.330666l120.661333 120.661333 211.2-211.2a42.666667 42.666667 0 0 1 60.330667 60.330667l-241.365334 241.365333a42.666667 42.666667 0 0 1-56.618666 3.328l-3.712-3.328-150.826667-150.826667z"
                      fill="#0F7CF4"
                      p-id="13805"
                    ></path>
                  </svg>
                </Col>
                <Col flex="1" style={{ overflow: 'hidden' }}>
                  <Typography.Text ellipsis title={ctx.currentTaskName}>
                    {ctx.currentTaskName}
                  </Typography.Text>
                </Col>
              </Row>
              <div style={{ color: '#ccc', textIndent: '2em' }}>
                {I18N.t(
                  '花漾RPA采用“拟人化”执行流程，流程执行期间请不要移动/敲击鼠标/键盘，否则有可能导致流程出错',
                )}
              </div>
            </Space>
          </Row>
        </div>
        <div className={styles.footerWrap}>{footer}</div>
      </div>
    );
  }
  if (!ctx.hasAuth) {
    return (
      <div className={styles.wrap}>
        <div id="log-container" className={styles.table} style={{ overflow: 'hidden' }}>
          <Row style={{ height: '100%' }} align="middle" justify="center">
            <Space direction="vertical" align="center">
              <Icon fileName="bot-empty" />
              <div style={{ color: '#ccc' }}>{I18N.t('您没有执行RPA流程的权限')}</div>
            </Space>
          </Row>
        </div>
        <div className={styles.footerWrap}>{footer}</div>
      </div>
    );
  }
  if (!rpaEnabled) {
    return (
      <div className={styles.wrap}>
        <div id="log-container" className={styles.table} style={{ overflow: 'hidden' }}>
          <Row style={{ height: '100%' }} align="middle" justify="center">
            <Space direction="vertical" align="center">
              <Icon fileName="bot-empty" />
              <div style={{ color: '#ccc' }}>{I18N.t('当前快捷方式不允许执行RPA流程')}</div>
            </Space>
          </Row>
        </div>
        <div className={styles.footerWrap}>{footer}</div>
      </div>
    );
  }
  if (flows.length === 0) {
    return (
      <div className={styles.wrap}>
        <div id="log-container" className={styles.table} style={{ overflow: 'hidden' }}>
          <Row style={{ height: '100%' }} align="middle" justify="center">
            <Space direction="vertical" align="center">
              <Icon fileName="bot-empty" />
              <div style={{ color: '#ccc' }}>{I18N.t('没有可执行的流程定义')}</div>
              <a href="https://www.szdamai.com/help/rpa/brief" target="_blank">
                了解更多
              </a>
            </Space>
          </Row>
        </div>
        <div className={styles.footerWrap}>{footer}</div>
      </div>
    );
  }
  return (
    <div className={styles.wrap}>
      <Row style={{ height: 140 }} align="middle" justify="center">
        <Space direction="vertical" align="center">
          <Icon fileName="bot-empty" />
          <div>{I18N.t('执行RPA流程')}</div>
        </Space>
      </Row>
      <div className={styles.table}>
        {flows.map((flow) => {
          return (
            <Row key={flow.id} wrap={false}>
              <Col flex="1" style={{ overflow: 'hidden' }}>
                <Row gutter={4} wrap={false}>
                  <Col flex="0 0 auto">
                    <Icon fileName="flow_16" style={{ marginRight: 4 }} />
                  </Col>
                  <Col style={{ overflow: 'hidden' }}>
                    <Typography.Text ellipsis>{flow.version?.flowName}</Typography.Text>
                  </Col>
                </Row>
              </Col>
              <Col flex="0 0 auto">
                <Space>
                  <Button
                    size="small"
                    type="primary"
                    ghost
                    icon={
                      <Icon
                        fileName="run_16"
                        style={{ verticalAlign: 'text-top', marginRight: 4 }}
                      />
                    }
                    onClick={() => {
                      sendMessage('runTask', {
                        rpaFlow: flow.version,
                      });
                      window.close();
                    }}
                  >
                    执行
                  </Button>
                </Space>
              </Col>
            </Row>
          );
        })}
      </div>
      <div className={styles.footerWrap}>{footer}</div>
      <Drawer
        title={I18N.t('流程说明')}
        visible={drawerOpen}
        width="100%"
        onClose={() => setDrawerOpen(false)}
      >
        {readme ? (
          <MarkdownView text={readme} />
        ) : (
          <Row style={{ height: '100%' }} align="middle" justify="center">
            <Space direction="vertical" align="center">
              <Icon fileName="bot-readme" />
              <div style={{ color: '#ccc' }}>{I18N.t('该流程未设置流程说明')}</div>
            </Space>
          </Row>
        )}
      </Drawer>
    </div>
  );
};

export default Flows;
