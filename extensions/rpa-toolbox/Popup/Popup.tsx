import React, { useCallback, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import _ from 'lodash';
import { sendMessage } from './utils';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import enCN from 'antd/lib/locale/en_US';
import Flows from './views/Flows';

export type RpaContextInterface = {
  shopInfo?: any;
  sscToken?: string;
  currentFlowName?: string;
  currentTaskId?: number;
  currentTaskName?: string;
  currentTaskItemId?: number;
  paused?: boolean;
  consoleVisible?: boolean;
  mouseTrackVisible?: boolean;
  hasAuth?: boolean;
};

export const RpaContext = React.createContext<RpaContextInterface>({});

export default function Popup(props: any) {
  const [env, setEnv] = useState<RpaContextInterface | null>(null);
  const history = useHistory();

  const getEnv = useCallback(() => {
    sendMessage('getEnv', {}, (res) => {
      console.log('getEnv', res);
      setEnv(res);
      if (_.isEmpty(res)) {
        setTimeout(() => {
          getEnv();
        }, 1000);
      }
    });
  }, []);

  useEffect(() => {
    getEnv();
  }, []);

  if (!env) return null;

  return (
    <ConfigProvider locale={navigator.language.startsWith('zh') ? zhCN : enCN}>
      <RpaContext.Provider value={env}>
        <Flows />
      </RpaContext.Provider>
    </ConfigProvider>
  );
}
