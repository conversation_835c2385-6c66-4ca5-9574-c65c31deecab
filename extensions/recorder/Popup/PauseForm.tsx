import React, { FC, useState } from 'react';
import classNames from 'classnames';

import styles from './pauseForm.less';
import Button from './widgets/Button';
import I18N from './I18N';

interface Props {
  visible: boolean;
  onSubmit: (reason: string, timeout: number) => void;
  onCancel: () => void;
}

const PauseForm: FC<Props> = (props) => {
  const { visible, onSubmit, onCancel } = props;
  const [timeout, setTimeout] = useState(20);
  const [reason, setReason] = useState('');

  const updateTimout = (code: string) => {
    let n = parseInt(timeout as any);
    if (isNaN(n)) {
      n = 20;
    }
    if (code === '+1') {
      n++;
    } else if (code === '-1') {
      n--;
    }
    if (n > 0 && n <= 60) {
      setTimeout(n);
    }
  };

  return (
    <div className={classNames(styles.wrap, { visible })}>
      <div className={styles.tips}>
        {I18N.t(
          '您可以填写暂停理由后暂停录像，并在界面中操作相关敏感信息，暂停期间所有操作均不会记录，最长可暂停60秒',
        )}
      </div>
      <div className={styles.inputRow}>
        {I18N.t('暂停时间')}：
        <div className={styles.inputWrap}>
          <input
            value={timeout}
            onChange={(e) => setTimeout(e.target.value as any)}
            onBlur={() => {
              let n = parseInt(timeout as any);
              if (isNaN(n)) {
                setTimeout(20);
              } else {
                setTimeout(Math.max(1, Math.min(60, n)));
              }
            }}
          />
          <div className={styles.arrowWrap}>
            <span className={styles.up} onClick={() => updateTimout('+1')} />
            <span className={styles.down} onClick={() => updateTimout('-1')} />
          </div>
        </div>
        {I18N.t('秒')}
      </div>
      <div className={styles.textareaWrap}>
        <textarea
          value={reason}
          maxLength={120}
          onChange={(e) => setReason(e.target.value)}
          placeholder={I18N.t('请输入暂停录像的原因（最多120字）')}
        />
      </div>
      <Button.Group>
        <Button
          type="primary"
          onClick={() => {
            const n = parseInt(timeout as any);
            if (isNaN(n)) return;
            if (n > 60) return;
            onSubmit(reason, timeout);
          }}
        >
          {I18N.t('确定')}
        </Button>
        <Button onClick={onCancel}>{I18N.t('取消')}</Button>
      </Button.Group>
    </div>
  );
};

export default PauseForm;
