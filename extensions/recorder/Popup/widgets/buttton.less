@import "../variables";

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  height: 32px;
  margin: @gap-base;
  border-radius: 3px;
  cursor: pointer;
  &:global(.default) {
    border: 1px solid @border-color;
  }
  &:global(.primary) {
    color: #fff;
    background-color: @color-primary;
  }
  &:global(.danger) {
    color: #fff;
    background-color: @color-danger;
  }
  &.disabled {
    color: rgba(0, 0, 0, 0.25);
    border: 1px solid #d9d9d9;
    background: #f5f5f5;
    cursor: not-allowed;
  }
}

.btn-wrap {
  display: flex;
  gap: @gap-sm;
  margin: @gap-base;
  .btn {
    flex: 1;
    margin: 0;
    overflow: hidden;
  }
}
