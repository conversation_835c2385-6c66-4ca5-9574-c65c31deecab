import React, { FC } from 'react';

import styles from './recordSatus.less';
import Icon from './Icon';
import I18N from '../I18N';

type Props = {
  state: RecordState;
  pauseRemainSec?: number; // 暂停录像剩余时间（秒）
};

const getStateIcon = (state: RecordState) => {
  switch (state) {
    case 'started':
      return 'recording-bg';
    case 'Pause':
      return 'pause-bg';
    default:
      return 'recorder-bg';
  }
};
const RecordStatus: FC<Props> = (props) => {
  const { state = 'ForceStop', pauseRemainSec } = props;

  let title = null;
  let desc = null;
  if (state === 'started') {
    title = I18N.t('录像中');
    desc = `（${I18N.t('仅记录本浏览器窗口')}）`;
  } else if (['ForceStop', 'Overrun'].includes(state)) {
    desc = I18N.t('当前操作未开启录像');
  } else if (state === 'Pause') {
    title = I18N.t('暂停录像{{sec}}秒', { sec: pauseRemainSec === 0 ? '' : `${pauseRemainSec})` });
  } else if (state === 'error') {
    desc = I18N.t('录像失败，请检查是否授予了屏幕录制权限');
  }

  return (
    <div className={styles.wrap}>
      <Icon fileName={getStateIcon(state)} />
      <div className={styles.title}>{title}</div>
      <div className={styles.desc}>{desc}</div>
    </div>
  );
};

export default RecordStatus;
