@import '../variables';

.wrap {
  position: relative;
  margin: 0 @gap-base;
  padding: @gap-base;
  border-radius: 3px;
  background-color: #FFF9ED;
}

.row {
  margin-bottom: 12px;
  line-height: 22px;
  &:last-child {
    margin-bottom: 0;
  }
  > span {
    color: @text-muted;
  }
  > :global(.highlight) {
    color: @text-warning;
  }
}

.bg {
  position: absolute;
  right: -7px;
  bottom: -7px;
  z-index: 0;
}
