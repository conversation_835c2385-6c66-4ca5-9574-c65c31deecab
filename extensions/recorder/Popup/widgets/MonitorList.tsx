import React, { useState, useEffect, ReactNode } from 'react';
import classNames from 'classnames';
import moment from 'moment';

import styles from './monitorList.less';
import Icon from './Icon';
import { sendMessage } from '../utils';
import I18N from '../I18N';
import { EnvProps } from '../Popup';

interface Props {}

function MonitorList(props: Props & EnvProps) {
  const { shopInfo } = props;
  const [members, setMembers] = useState([]);
  const [apiUrl, setApiUrl] = useState('');

  useEffect(() => {
    (async () => {
      // @ts-ignore
      const url = await chrome.extension.getBackgroundPage().getApiUrl();
      setApiUrl(url.replace(/\/$/, ''));
    })();
    sendMessage('getWatchingUsers');
    const timer = setInterval(() => {
      sendMessage('getWatchingUsers');
    }, 1000);
    chrome.runtime.onMessage.addListener((msg: { action: RecorderBgAction; data: any }) => {
      if (typeof msg !== 'object') return;
      if (msg.action === 'watchingUsers') {
        setMembers(msg.data ?? []);
      }
    });
    return () => {
      clearInterval(timer);
    };
  }, []);

  let con: ReactNode = <EmptyView />;
  if (shopInfo?.monitorPerception && members.length > 0) {
    con = (
      <>
        <div className={styles.monitorTitle}>{I18N.t('有以下用户正在旁观您的操作')}：</div>
        {members.map((m: any) => {
          let avatar = '/assets/images/avatar-default.png';
          if (m.avatar) {
            avatar = `${apiUrl}${m.avatar}`;
          }
          return (
            <div key={m.id} className={styles.monitorRow}>
              <img src={avatar} />
              <span className={styles.monitorName}>{m.nickname}</span>
              {m.startTime && (
                <span className={styles.monitorTime}>
                  {I18N.t('进入时间')}：{moment(m.startTime).format('HH:mm:ss')}
                </span>
              )}
            </div>
          );
        })}
      </>
    );
  }
  return <div className={styles.wrap}>{con}</div>;
}

function EmptyView() {
  return (
    <div className={styles.emptyWrap}>
      <Icon fileName="monitor-bg" />
      <div className={styles.emptyText}>{I18N.t('当前无人旁观您的操作')}</div>
    </div>
  );
}

export default MonitorList;
