import React, { FC, useEffect, useState } from 'react';
import moment from 'moment';
import classNames from 'classnames';

import styles from './duration.less';
import { sendMessage } from '../utils';
import Icon from './Icon';
import I18N from '../I18N';
import { EnvProps } from '../Popup';

const Duration: FC<any & EnvProps> = (props) => {
  const { className, shopInfo, ...otherProps } = props;
  const [startTime, setStartTime] = useState(0);
  const [durationTime, setDurationTime] = useState(0);

  useEffect(() => {
    sendMessage('getSessionStartTime');
    let timer: any = 0;
    const recordPerception =
      shopInfo && !(shopInfo.recordPolicy === 'Forced' && !shopInfo?.recordPerception);
    if (recordPerception) {
      sendMessage('getRecordDuration');
      timer = setInterval(() => {
        sendMessage('getRecordDuration');
      }, 1000);
    }
    chrome.runtime.onMessage.addListener((msg: { action: RecorderBgAction; data: any }) => {
      if (typeof msg !== 'object') return;
      if (msg.action === 'sessionStartTime') {
        setStartTime(msg.data ?? 0);
      }
      if (msg.action === 'recordDuration') {
        setDurationTime(Math.round(msg.data ?? 0));
      }
    });
    return () => {
      clearInterval(timer);
    };
  }, []);

  let durationTimeText = `${durationTime % 60}${I18N.t('秒')}`;
  if (durationTime >= 60) {
    durationTimeText =
      `${Math.floor((durationTime % 3600) / 60)}${I18N.t('分钟')}` + durationTimeText;
  }
  if (durationTime >= 3600) {
    durationTimeText = `${Math.floor(durationTime / 3600)}${I18N.t('小时')}` + durationTimeText;
  }
  return (
    <div className={classNames(styles.wrap, className)} {...otherProps}>
      <Icon fileName="clock-bg" className={styles.bg} />
      <div className={styles.row}>
        <span>{I18N.t('访问开始时间')}：</span>
        {startTime ? moment(startTime).format('MM-DD HH:mm:ss') : '--'}
      </div>
      <div className={styles.row}>
        <span>{I18N.t('录像持续时间')}：</span>
        <span className="highlight">{durationTime ? durationTimeText : '--'}</span>
      </div>
    </div>
  );
};

export default Duration;
