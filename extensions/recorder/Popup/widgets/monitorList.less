@import "../variables";

.wrap {
  position: relative;
  height: 163px;
  overflow: auto;
  :before {
    content: '';
    position: absolute;
    left: @gap-base;
    right: @gap-base;
    top: 0;
    border-top: 1px solid @border-color;
  }
}

.monitor-title {
  margin: 7px 16px 0;
  color: #878787;
}

.monitor-row {
  display: flex;
  align-items: center;
  margin: 14px 16px;
  > img {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    margin-right: 8px;
  }
}

.monitor-name {
  width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
}

.monitor-time {
  margin-left: auto;
  font-size: 12px;
  color: #878787;
}

.empty-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-text {
  margin-top: 16px;
  color: @text-muted;
}
