import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { sendMessage } from './utils';
import RecordStatus from './widgets/RecordStatus';
import Duration from './widgets/Duration';
import Button from './widgets/Button';
import MonitorList from './widgets/MonitorList';
import PauseForm from './PauseForm';
import StopAlert from './StopAlert';
import styles from './widgets/recordSatus.less';
import Icon from './widgets/Icon';
import I18N from './I18N';

export interface EnvProps {
  shopInfo?: ShopInfo;
  hasAdminAuth?: boolean;
}

export default function Popup() {
  const [env, setEnv] = useState<EnvProps>({});
  const [recordState, setRecordState] = useState<RecordState>('ForceStop');
  const [pauseRemain, setPauseRemain] = useState(0);
  const [pauseFormVisible, setPauseFormVisible] = useState(false);
  const [stopAlertVisible, setStopAlertVisible] = useState(false);

  useEffect(() => {
    sendMessage('getEnv', {}, (res) => {
      console.log('getEnv', res);
      setEnv(res);
      sendMessage('getRecordState');
      chrome.runtime.onMessage.addListener((msg: { action: RecorderBgAction; data: any }) => {
        console.log('onMessage', msg);
        if (typeof msg !== 'object') return;
        if (msg.action === 'recordState') {
          // 用户无法感知录像
          if (!(res.shopInfo?.recordPolicy === 'Forced' && !res.shopInfo?.recordPerception)) {
            setRecordState(msg.data);
          }
        }
        if (msg.action === 'pauseRemain') {
          setPauseRemain(msg.data);
        }
      });
    });
  }, []);

  useEffect(() => {
    if (recordState === 'started') {
      setPauseRemain(0);
    }
  }, [recordState]);

  const renderBtns = useCallback(() => {
    const hasParentShop = !!env.shopInfo?.parentShopId;
    const forceRecord = env.shopInfo?.recordPolicy === 'Forced';
    const recordDisabled = env.shopInfo?.recordPolicy === 'Disabled';
    const allowSkip = env.shopInfo?.allowSkip;
    const hasAdminAuth = env.hasAdminAuth;
    const recordPerception = !(
      env.shopInfo?.recordPolicy === 'Forced' && !env.shopInfo?.recordPerception
    );
    if (!recordPerception) {
      return (
        <Button type="primary" disabled>
          {I18N.t('开始录像')}
        </Button>
      );
    }
    if (recordState === 'ForceStop' || recordState === 'Overrun') {
      return (
        <Button type="primary" disabled={recordDisabled} onClick={() => sendMessage('startRecord')}>
          {I18N.t('开始录像')}
        </Button>
      );
    }
    if (recordState === 'started') {
      return (
        <Button.Group>
          <Button disabled={!hasAdminAuth && !allowSkip} onClick={() => setPauseFormVisible(true)}>
            {I18N.t('暂停录像')}
          </Button>
          <Button
            disabled={hasParentShop || (!hasAdminAuth && forceRecord)}
            type="danger"
            onClick={() => setStopAlertVisible(true)}
          >
            {I18N.t('停止录像')}
          </Button>
        </Button.Group>
      );
    }
    if (recordState === 'Pause') {
      return (
        <Button type="primary" onClick={() => sendMessage('startRecord')}>
          {I18N.t('重新恢复录像')}
          {pauseRemain === 0 ? '' : `（${pauseRemain}）`}
        </Button>
      );
    }
  }, [env, recordState]);

  const content = () => {
    if (!env?.shopInfo?.securityPolicyEnabled) {
      return (
        <div
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            display: 'flex',
            overflow: 'hidden',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 24,
          }}
        >
          <Icon fileName={'recorder-bg'} />
          <div style={{ color: '#999' }}>{I18N.t('未开启安全策略，不支持事后审计')}</div>
          <a
            id="help-link"
            href="https://www.szdamai.com/help/account/security"
            target="_blank"
            style={{ color: '#0797E1', textDecoration: 'none' }}
          >
            {I18N.t('了解更多')}
          </a>
        </div>
      );
    }
    return (
      <>
        <RecordStatus state={recordState} pauseRemainSec={pauseRemain} />
        <Duration {...env} />
        {renderBtns()}
        <MonitorList {...env} />
        <PauseForm
          visible={pauseFormVisible}
          onSubmit={(reason, timeout) => {
            sendMessage('stopRecord', {
              endType: 'Pause',
              endReason: reason,
              timeout: timeout,
            });
            setPauseFormVisible(false);
          }}
          onCancel={() => setPauseFormVisible(false)}
        />
        <StopAlert
          visible={stopAlertVisible}
          onSubmit={() => {
            sendMessage('stopRecord', { endType: 'ForceStop', endReason: 'ForceStop' });
            setStopAlertVisible(false);
          }}
          onCancel={() => setStopAlertVisible(false)}
        />
      </>
    );
  };

  return <div>{content()}</div>;
}
