import en from './locales/en';

let lang = 'zh';

const reg = /{{(.*?)}}/g;

const replaceParam = (template: string, params: { [x: string]: any }) => {
  return (
    template?.replace?.(reg, (item, key) => {
      return params[key] || item;
    }) || template
  );
};

export default {
  setLang(code: string) {
    lang = code;
  },

  isCn() {
    return lang.startsWith('zh');
  },

  t(string: string, params?: Record<string, any>) {
    let templateStr = string;
    // @ts-ignore
    if (!this.isCn() && en[string]) {
      // @ts-ignore
      templateStr = en[string];
    }
    return replaceParam(templateStr, params || {});
  },
};
