import React, { <PERSON> } from 'react';
import classNames from 'classnames';

import styles from './stopAlert.less';
import Button from './widgets/Button';
import Icon from './widgets/Icon';
import Duration from './widgets/Duration';
import I18N from './I18N';

interface Props {
  visible: boolean;
  onSubmit: () => void;
  onCancel: () => void;
}

const StopAlert: FC<Props> = (props) => {
  const { visible, onSubmit, onCancel } = props;

  return (
    <div className={classNames(styles.wrap, { visible })}>
      <Icon fileName="confirm-icon" className={styles.icon} />
      <div className={styles.title}>{I18N.t('确定要停止录像吗？')}</div>
      <div className={styles.desc}>
        {I18N.t(
          '如有需要，后续仍然可以重新开始录像，在最终生成的录像文件中，停止录像的片段不会被记录下来',
        )}
      </div>
      <div style={{ width: '100%', flexGrow: 1 }}>
        <Duration className={styles.durationWrap} />
      </div>
      <div style={{ width: '100%' }}>
        <Button.Group>
          <Button type="primary" onClick={onSubmit}>
            {I18N.t('确定')}
          </Button>
          <Button onClick={onCancel}>{I18N.t('取消')}</Button>
        </Button.Group>
      </div>
    </div>
  );
};

export default StopAlert;
