export default {
  开始录像: 'Record',
  暂停录像: 'Pause',
  停止录像: 'Stop',
  重新恢复录像: 'Resume record',
  '未开启安全策略，不支持事后审计': 'Security Policy Disabled',
  '您可以填写暂停理由后暂停录像，并在界面中操作相关敏感信息，暂停期间所有操作均不会记录，最长可暂停60秒':
    'You can pause the recording after filling in the pause reason. All operations during the pause will not be recorded, and the pause can last up to 60 seconds.',
  '请输入暂停录像的原因（最多120字）':
    'Please enter the reason for pausing recording (maximum 120 characters)',
  暂停时间: 'Pause time',
  秒: 's',
  分钟: 'm',
  小时: 'h',
  确定: 'OK',
  取消: 'Cancel',
  '确定要停止录像吗？': 'Stop recording?',
  '如有需要，后续仍然可以重新开始录像，在最终生成的录像文件中，停止录像的片段不会被记录下来':
    'If necessary, you can still restart the recording later. In the final generated recording file, the stopped recording segment will not be recorded.',
  访问开始时间: 'Start time',
  录像持续时间: 'Duration',
  有以下用户正在旁观您的操作: 'Your Viewers:',
  进入时间: 'Enter time',
  当前无人旁观您的操作: 'No Viewers',
  录像中: 'Recording',
  仅记录本浏览器窗口: 'Record this window only',
  当前操作未开启录像: 'Recording is not enabled',
  '暂停录像{{sec}}秒': 'Pause recording {{sec}}s',
  '录像失败，请检查是否授予了屏幕录制权限':
    'Recording failed, please check Screen Recording Permission is granted',
};
