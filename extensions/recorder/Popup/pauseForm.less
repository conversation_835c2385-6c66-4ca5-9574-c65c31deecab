@import "./variables";
.wrap {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  z-index: 1;
  background: #fff;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.4s;
  &:global(.visible) {
    opacity: 1;
    transform: translateX(0);
  }
}
.tips {
  padding: 10px 14px;
  margin: @gap-base;
  color: #666;
  background: #F0F5FF;
  border-radius: 2px;
  border: 1px solid #5DAAFF;
}

.input-row {
  display: flex;
  align-items: center;
  margin: 0 @gap-base @gap-base;
}

.input-wrap {
  display: flex;
  margin: 0 4px;
  > input {
    height: 32px;
    width: 60px;
    border: 1px solid @border-color;
    border-radius: 3px 0 0 3px;
  }
}

.arrow-wrap {
  display: flex;
  flex-direction: column;
  width: 15px;
  > span {
    position: relative;
    flex: 1;
    width: 100%;
    border: 1px solid @border-color;
    border-left: 0;
    cursor: pointer;
    &:after {
      content: '';
      position: absolute;
      width: 4px;
      height: 4px;
      left: 4px;
      top: 6px;
      border: 1px solid @border-color;
      border-right-color: transparent;
      border-bottom-color: transparent;
      transform-origin: center;
      transform: rotateZ(45deg);
    }
    &:first-child {
      border-radius: 0 3px 0 0;
      border-bottom: 0;
    }
    &:last-child {
      border-radius: 0 0 3px 0;
      &:after {
        top: 3px;
        transform: rotateZ(-135deg);
      }
    }
  }
}

.textarea-wrap {
  flex-grow: 1;
  margin: 0 @gap-base;

  textarea {
    width: 100%;
    padding: 4px 12px;
    height: 234px;
    border: 1px solid @border-color;
    resize: none;
  }
}
