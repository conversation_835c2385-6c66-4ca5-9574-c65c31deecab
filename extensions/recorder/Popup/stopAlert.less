@import "./variables";
.wrap {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  background: #fff;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.4s;
  &:global(.visible) {
    opacity: 1;
    transform: translateX(0);
  }
}

.icon {
  margin-top: 60px;
  margin-bottom: 16px;
}

.title {
  font-size: 16px;
}

.desc {
  color: #666;
  margin: @gap-base;
}

.duration-wrap {
  margin-top: @gap-base;
  margin-bottom: 70px - @gap-base;
}
