let shopInfo: ShopInfo | any = null;
let recordInfo: RecordInfo | any = null;
let hasAdminAuth = false;
let recordStatus: RecordState = 'ForceStop';
let pauseRemain: number = 0;
let apiUrl = '';
let language = 'zh';

let ws: WebSocket | null = null;

//functions
const sleep = (delay: number) => new Promise((resolve) => setTimeout(resolve, delay || 0));

chrome.runtime.onMessage.addListener(
  async (
    msg: { action: RecorderPopupAction; data: { [k: string]: any } },
    sender,
    sendResponse,
  ) => {
    if (typeof msg !== 'object') return;
    const { action, data = {} } = msg;
    console.log('onMessage', msg);
    let evt: RecordEvent;
    switch (action) {
      case 'getEnv':
        sendResponse({
          shopInfo,
          hasAdminAuth,
        });
        break;
      case 'getLanguage':
        sendResponse(language);
        break;
      case 'getRecordState':
        sendMessage({ action: 'recordState', data: recordStatus });
        break;
      case 'getSessionStartTime':
        console.log('recordInfo', recordInfo);
        if (recordInfo) {
          sendMessage({ action: 'sessionStartTime', data: recordInfo.sessionStartTime });
        }
        break;
      case 'getRecordDuration':
        const sec = await wsSend({ name: 'getRecordDuration', data: {} });
        sendMessage({ action: 'recordDuration', data: sec });
        break;
      case 'getPauseRemain':
        sendMessage({ action: 'pauseRemain', data: pauseRemain });
        break;
      case 'stopRecord':
        const { endType = 'Pause', endReason = 'Pause', timeout = 0 } = data;
        evt = { name: 'stopRecord', data: { endType, endReason, timeout } };
        wsSend(evt);
        break;
      case 'startRecord':
        wsSend({ name: 'startRecord', data: {} });
        break;
      case 'getWatchingUsers':
        evt = { name: 'getWatchingUsers', data: null };
        const res = await wsSend(evt);
        sendMessage({ action: 'watchingUsers', data: res });
        break;
    }
  },
);

function sendMessage({ action, data }: { action: RecorderBgAction; data: any }) {
  chrome.runtime.sendMessage({ action, data });
}

/**
 * 向 popup 视图发送数据
 * @param payload
 */
async function dispatchEvent(payload: { event: string; data: { [key: string]: any } }) {
  console.log('dispatchEvent', payload);
  if (payload.event === 'recordStatus') {
    // 录像状态发生变化
    recordStatus = payload.data.status;
    sendMessage({ action: 'recordState', data: recordStatus });
    updateFavicon(recordStatus);
  } else if (payload.event === 'pauseRemain') {
    // 暂停剩余时间发生变化
    pauseRemain = payload.data.sec;
    sendMessage({ action: 'pauseRemain', data: pauseRemain });
  }
}

/**
 * 更新图标
 * @param recordStatus
 */
function updateFavicon(recordStatus: RecordState) {
  let fileName = 'record';
  switch (recordStatus) {
    case 'started':
      if (!(shopInfo.recordPolicy === 'Forced' && !shopInfo?.recordPerception)) {
        fileName = 'recording';
      }
      break;
    case 'Pause':
      fileName = 'pause';
      break;
    case 'ForceStop':
    case 'Overrun':
      fileName = 'record';
      break;
    default:
      fileName = 'record';
  }
  chrome.browserAction.setIcon({
    path: {
      16: `assets/images/icon/${fileName}/16.png`,
      32: `assets/images/icon/${fileName}/32.png`,
    },
  });
}

async function doFetch(url: string): Promise<Response> {
  try {
    const res = await fetch(url);
    return res;
  } catch (e) {
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        try {
          const res = await doFetch(url);
          resolve(res);
        } catch (e) {
          reject(e);
        }
      }, 1000);
    });
  }
}

const doInit = async () => {
  await new Promise(async (resolve) => {
    const res = await doFetch('http://szdamai.local/api/getEnv');
    const resObj = await res.json();
    shopInfo = resObj.shopInfo;
    recordInfo = resObj.recordInfo;
    apiUrl = resObj.apiUrl;
    hasAdminAuth = resObj.hasAdminAuth;
    language = resObj.language;
    resolve(true);
  });

  console.log('Connecting Websocket');
  connectRecorderServer(() => {
    console.log('Init success');
    if (shopInfo.securityPolicyEnabled && shopInfo.recordPolicy === 'Forced') {
      startRecord();
    }
  });
};

const connectRecorderServer = (callback = () => {}) => {
  let tryReconnect = () => {
    ws = null;
    setTimeout(() => {
      connectRecorderServer(callback);
    }, 1000);
  };
  ws = new WebSocket(`${recordInfo.wsUrl}?sessionId=${recordInfo.sessionId}`);
  ws.onclose = tryReconnect;
  ws.onmessage = (e) => {
    if (e.data === 'ready') {
      console.log('Websocket Connected');
      callback();
    } else {
      handleWebsocketMsg(e);
    }
  };
};

const cbs = {};
const handleWebsocketMsg = async (e: MessageEvent) => {
  let data = e.data;
  // console.log(data);
  if (typeof data == 'string') {
    let evt: RecordEvent = JSON.parse(data);
    switch (evt.name) {
      case 'send-dispatch':
        dispatchEvent(evt.data);
        break;
      default:
        // @ts-ignore
        if (cbs[evt.name]) {
          // @ts-ignore
          cbs[evt.name].call(null, evt.data);
          // @ts-ignore
          delete cbs[evt.name];
        }
        break;
    }
  }
};

// @ts-ignore
const startRecord = (window.startRecord = async () => {
  let evt: RecordEvent = { name: 'startRecord', data: null };
  return wsSend(evt);
});

const wsSend = (evt: RecordEvent) => {
  if (ws) {
    try {
      ws.send(JSON.stringify(evt));
      console.log('Websocket send', JSON.stringify(evt));
      return new Promise((resolve) => {
        //@ts-ignore
        cbs[evt.name + '-cb'] = resolve;
      });
    } catch (e) {
      console.error(e);
    }
  }
};

// @ts-ignore
window.stopRecord = async (
  endType: string = 'Pause',
  endReason: string = 'Pause',
  timeout: number = 0,
) => {
  let evt: RecordEvent = { name: 'stopRecord', data: { endType, endReason, timeout } };
  let ret = await wsSend(evt);
  return ret;
};

// @ts-ignore
window.getShopInfo = () => {
  return shopInfo;
};
// @ts-ignore
window.getRecordInfo = () => {
  return recordInfo;
};

// @ts-ignore
window.getCurrent = () => {
  let evt: RecordEvent = { name: 'getCurrentSlice', data: null };
  return wsSend(evt);
};

// @ts-ignore
window.remoteRequest = (path, options = {}) => {
  if (options === null) {
    options = {};
  }
  //@ts-ignore
  if (!options.teamId) {
    //@ts-ignore
    options.teamId = shopInfo.teamId;
  }
  let evt: RecordEvent = { name: 'sendDonkeyRequest', data: { path, options } };
  return wsSend(evt);
};

//@ts-ignore
window.getStartTime = async () => {
  let evt: RecordEvent = { name: 'getStartTime', data: null };
  let startTime = await wsSend(evt);
  return startTime;
};

//@ts-ignore
window.getRecordStatus = async () => {
  let evt: RecordEvent = { name: 'getRecordStatus', data: null };
  const res = await wsSend(evt);
  console.log('res', res);
  return Promise.resolve(res);
};

// @ts-ignore
window.getApiUrl = () => {
  return apiUrl;
};

//@ts-ignore
doInit().then(() => {});

/**
 * 心跳
 */
setInterval(() => {
  let evt: RecordEvent = { name: 'heartbeat', data: null };
  wsSend(evt);
}, 3000);
