import moment from 'moment';
import { webRequestBlocker } from './webRequestBlocker';
import { tabMonitor } from './tabMonitor';

const API_URL = 'http://szdamai.local';
const startTime = Date.now();
const userInfo = {
  avatar: chrome.runtime.getURL('/assets/images/128.png'),
  nickname: '',
  phone: '',
  signature: '',
  email: '',
  startTime: moment().format('MM-DD HH:mm:ss'),
  duration: '',
};
let fingerprintConfig: any = null;
let rpaRecorderEnabled = false;
let pageLoadAlertCount = 5;
let pageLoadTimeout = 30;
let envData: Record<string, any> | null = null;
let outboundIp = '';
let showMouseTrackLayer = false;

// 处理 407 代理认证
chrome.webRequest.onAuthRequired.addListener(
  (detail, callback) => {
    return {
      cancel: true,
    };
  },
  { urls: ['http://*/*', 'https://*/*'], types: ['main_frame'] },
  ['blocking', 'responseHeaders', 'extraHeaders'],
);

/**
 * 通过 websocket 来进行消息传递
 */
export class MessageDispatcher {
  private identify: string;
  private wsClient: WebSocket | null;
  constructor(identify: string) {
    this.identify = identify;
    this.wsClient = null;
  }

  start(websocket: WebSocket) {
    this.wsClient = websocket;
    this.wsClient.send('identify=' + this.identify);

    this.wsClient.addEventListener('message', (evt) => {
      console.log('receive message', evt.data);
      if (/^[A-Za-z0-9-_\s]+$/.test(evt.data)) {
      } else {
        try {
          const payload = JSON.parse(evt.data);
          const { action, data } = payload;
          switch (action) {
            case 'pac-script-changed':
              updatePacConfig();
              break;
            case 'white-list-changed':
              getWhitelist();
              break;
            case 'block-page-list-changed':
              getBlockPageList();
              break;
            case 'rpa-recorder':
              rpaRecorderEnabled = data.active;
              sendMessageToTabs(action, data);
              break;
            case 'rpa-recorder-status-update':
              tabMonitor.changeReportActionEnabled(data.active);
              break;
            case 'rpa-find-selector':
            case 'get-pick-selector':
              sendMessageToTabs(action, data, { active: true });
              break;
            case 'set-tabs':
              const targetTabs: string[] = data;
              chrome.tabs.query({}, (currentTabs) => {
                const delta = targetTabs.length - currentTabs.length;
                currentTabs.forEach((tab, idx) => {
                  chrome.tabs.update(tab.id!, {
                    url: targetTabs[idx],
                  });
                });
                if (delta < 0) {
                  // 关闭多余的标签页
                  const extraTabs = currentTabs.slice(delta);
                  chrome.tabs.remove(extraTabs.map((t) => t.id!));
                } else {
                  // 新增标签页
                  for (let i = 0; i < delta; i++) {
                    chrome.tabs.create({
                      url: targetTabs[currentTabs.length + i],
                    });
                  }
                }
              });
              break;
            case 'outbound-ip-changed':
              outboundIp = data;
              sendMessageToTabs(action, data);
              break;
            case 'update-window':
              chrome.windows.getCurrent({ windowTypes: ['normal'] }, (window) => {
                chrome.windows.update(window.id!, data);
              });
              break;
            case 'bring-window-to-front':
              chrome.windows.getCurrent({ windowTypes: ['normal'] }, (window) => {
                chrome.windows.update(
                  window.id!,
                  {
                    state: 'minimized',
                  },
                  () => {
                    chrome.windows.update(window.id!, {
                      state: 'normal',
                      ...data,
                    });
                    chrome.windows.update(window.id!, {
                      state: 'normal',
                      ...data,
                    });
                    chrome.windows.update(window.id!, {
                      state: 'normal',
                    });
                  },
                );
              });
              break;
            case 'show-hy-rpa-user-action-layer':
              showMouseTrackLayer = true;
              sendMessageToTabs(action, data);
              break;
            case 'hide-hy-rpa-user-action-layer':
              showMouseTrackLayer = false;
              sendMessageToTabs(action, data);
              break;
            case 'clear-browser-data':
              console.time('@@@clear-browser-data');
              chrome.browsingData.remove(
                {
                  since: 0,
                },
                {
                  appcache: true,
                  cache: true,
                  cacheStorage: true,
                  cookies: true,
                  // downloads: true,
                  fileSystems: true,
                  // formData: true,
                  // history: true,
                  indexedDB: true,
                  localStorage: true,
                  // passwords: true,
                  serviceWorkers: true,
                  webSQL: true,
                },
                () => {
                  console.timeEnd('@@@clear-browser-data');
                },
              );
              break;
            default:
              sendMessageToTabs(action, data);
          }
        } catch (e) {
          console.error('JSON parse failed', e);
        }
      }
    });
    this.wsClient.addEventListener('close', () => {
      this.stop();
    });
  }

  stop() {
    this.wsClient = null;
  }

  send(payload: any) {
    if (this.wsClient && this.wsClient.readyState === 1) {
      try {
        this.wsClient.send(typeof payload === 'string' ? payload : JSON.stringify(payload));
      } catch (e) {
        console.log('Websocket send msg failed', e);
      }
    }
  }
}
const messageDispatcher = new MessageDispatcher('plugin:userInfo');

async function doFetch(url: string): Promise<Response> {
  try {
    const res = await fetch(url);
    return res;
  } catch (e) {
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        try {
          const res = await doFetch(url);
          resolve(res);
        } catch (e) {
          reject(e);
        }
      }, 1000);
    });
  }
}

const getFingerprintConfig = async function () {
  if (!fingerprintConfig) {
    try {
      fingerprintConfig = await doFetch(`${API_URL}/api/getFingerprint`).then((res) => res.json());
    } catch (e) {
      console.error('GetFingerprint failed', e);
    }
  }
  return fingerprintConfig;
};

function getEnv() {
  return doFetch(`${API_URL}/api/getEnv`).then((res) => res.json());
}

function updatePacConfig(callback = () => {}) {
  try {
    doFetch(`${API_URL}/api/getPacScript`).then(async (res) => {
      let data = await res.json();
      if (data.success) {
        let pacScript = data.pacScript;
        console.log(pacScript);
        const config = {
          mode: 'pac_script',
          pacScript: {
            data: pacScript,
          },
        };
        chrome.proxy.settings.set({ value: config, scope: 'regular' }, callback);
      }
    });
  } catch (err: any) {
    console.error(err);
  }
}

function getWhitelist() {
  doFetch(`${API_URL}/api/getWhitelist`).then(async (res) => {
    const data = await res.json();
    // 更新白名单
    tabMonitor.updateWhitelist(data.whitelist);
  });
}

function getBlockPageList() {
  doFetch(`${API_URL}/api/getActionBlockPages`).then(async (res) => {
    const data = await res.json();
    tabMonitor.updateBlockPageList(data);
  });
}

function sendMessageToTabs(action: string, data: any, tabQueryInfo = {}) {
  chrome.tabs.query(tabQueryInfo, function (tabs) {
    tabs.forEach((tab) => {
      chrome.tabs.sendMessage(tab.id!, {
        action,
        data,
      });
    });
  });
}

function setUpWebrtcStatus(enabled: boolean) {
  if (enabled) {
    chrome.privacy.network.webRTCIPHandlingPolicy.set({
      value: 'default',
    });
  } else {
    chrome.privacy.network.webRTCIPHandlingPolicy.set({
      value: 'disable_non_proxied_udp',
    });
  }
}

// 检查浏览器会话是否存活
let unAliveCount = 0;
function startCheckBrowserAlive(envData: any) {
  setInterval(() => {
    fetch(`${envData.clientApiOrigin}/checkBrowserAlive?sessionId=${envData.sessionId}`)
      .then((res) => res.json())
      .then((json) => {
        console.log('checkBrowserAlive', json);
        if (json.success) {
          if (!json.alive) {
            unAliveCount++;
            if (unAliveCount > 2) {
              chrome.windows.getCurrent((window) => {
                const windowId = window.id;
                console.log('close window', windowId);
                if (windowId) {
                  chrome.windows.remove(windowId);
                }
              });
            }
          } else {
            unAliveCount = 0;
          }
        }
      });
  }, 10 * 1000);
}

function __main__({ proxyPacInit }: { proxyPacInit: () => void }) {
  getEnv().then((data: Record<string, any>) => {
    updatePacConfig(proxyPacInit);
    tabMonitor.setShopInfo(data.shopInfo);
    tabMonitor.setServerUrl(data.serverUrl);
    getWhitelist();
    getBlockPageList();

    console.log('getEnv', data);
    envData = data;
    try {
      const initWS = () => {
        let ws = new WebSocket(data.wsUrl);
        ws.addEventListener('open', () => {
          messageDispatcher.start(ws);
        });
        ws.addEventListener('close', () => {
          setTimeout(() => {
            initWS();
          }, 10);
        });
      };
      initWS();

      // 监听 cookie 变更事件
      chrome.cookies.onChanged.addListener(function () {
        messageDispatcher.send('cookieUpdated');
      });
    } catch (e) {
      console.error('Connect websocket failed', e);
    }
    getUserInfo(envData);
    startCheckBrowserAlive(envData);
    // 资源加载策略
    const types: any = [];
    if (envData.shopInfo?.resourcePolicyVo.image === false) {
      types.push('image');
    }
    if (types.length > 0) {
      webRequestBlocker.start({ types: types, maxImageSize: envData.shopInfo?.imageForbiddenSize });
    }
    tabMonitor.setMsgDispatcher(messageDispatcher);
    tabMonitor.changeReportUpdateEnabled(true);
    pageLoadTimeout = envData.networkConfig?.pageLoadTimeout ?? pageLoadTimeout;
    pageLoadAlertCount = envData.networkConfig?.pageLoadTimeoutAlerts ?? pageLoadAlertCount;
    if (envData.shopInfo?.channels?.length === 0) {
      pageLoadTimeout = 99999;
    }
    setUpWebrtcStatus(
      ![envData.fingerprint.webrtcPublicIp_type, envData.fingerprint.webrtcInnerIp_type].includes(
        'Disabled',
      ),
    );
    outboundIp = envData.outboundIp;
    // chrome.webNavigation.onCompleted.addListener((detail) => {
    //   const { frameId, tabId, url } = detail;
    //   if (frameId === 0 && pageLoadAlertCount > 0 && url.indexOf('http://szdamai.') !== 0) {
    //     console.log('sendMessage', url);
    //     chrome.tabs.sendMessage(tabId, {
    //       action: 'get-page-load-info',
    //     });
    //   }
    // });
  });
}

new Promise((resolve, reject) => {
  chrome.proxy.settings.clear({}, resolve);
}).then(async () => {
  __main__({
    proxyPacInit: () => {
      return;
      chrome.tabs.query({ index: 0 }, (tabs) => {
        let [tab] = tabs;
        let url = tab?.url;
        console.log(url);
        if ('about:blank' == url) {
          chrome.tabs.update(tab.id!, { url: 'http://szdamai.local' });
        }
      });
    },
  });
});

async function getUserInfo(envData: any) {
  return new Promise(async (resolve) => {
    try {
      const { avatar, nickname, phone, signature, email } = envData.userInfo;
      let tmpAvatar = avatar;
      const myCanvas = document.querySelector('#canvas') as HTMLCanvasElement;
      if (avatar && myCanvas) {
        const ctx = myCanvas.getContext('2d');
        const img = new Image();
        img.onload = function () {
          ctx?.save();
          ctx?.beginPath();
          ctx?.arc(16, 16, 16, 0, Math.PI * 2, true);
          ctx?.closePath();
          ctx?.clip();
          ctx?.drawImage(img, 0, 0, 32, 32);
          ctx?.beginPath();
          ctx?.arc(0, 0, 16, 0, Math.PI * 2, true);
          ctx?.clip();
          ctx?.closePath();
          ctx?.restore();
          chrome.browserAction.setIcon({
            imageData: ctx?.getImageData(0, 0, 32, 32),
          });
        };
        try {
          img.src = new URL(avatar, envData.apiUrl).href;
          tmpAvatar = img.src;
          userInfo.avatar = tmpAvatar;
        } catch (e) {
          console.error(e);
        }
      }
      userInfo.nickname = nickname;
      userInfo.phone = phone;
      userInfo.signature = signature;
      userInfo.email = email;
      userInfo.startTime = moment(envData.createTime).format('MM-DD HH:mm:ss');
      resolve(userInfo);
    } catch (e) {
      resolve(userInfo);
    }
  });
}

chrome.runtime.onMessage.addListener(async (msg = {}, sender, sendResponse) => {
  if (typeof msg !== 'object') return;
  const { action, data = {} } = msg;
  let res: any = {};
  switch (action) {
    case 'getUserInfo':
      const time = moment.duration(
        Math.round((new Date().getTime() - startTime) / 1000),
        'seconds',
      );
      userInfo.duration = moment({ h: time.hours(), m: time.minutes(), s: time.seconds() }).format(
        'H小时 m分',
      );
      res = userInfo;
      sendResponse({ action, data: res });
      break;
    case 'getLanguage':
      sendResponse(envData?.language);
      break;
    case 'ipFetched':
      chrome.tabs.query({ url: '*://szdamai.local/*' }, (tabs) => {
        for (const tab of tabs) {
          if (tab.id) {
            chrome.tabs.sendMessage(tab.id, { action, ...data });
          }
        }
      });
      break;
    case 'fingerprint':
      let url = data; //用來判斷白名單
      let inWhiteList = false;
      if (inWhiteList) {
        sendResponse({});
      } else {
        let fingerConfig = (await getFingerprintConfig()) || {};
        let refs = {};
        if (fingerConfig && 'Assign' == fingerConfig.pluginType) {
          // @ts-ignore
          refs['Pref_PluginHide'] = {
            plugins: fingerConfig.plugins || [],
          };
        }
        sendResponse(refs);
      }
      break;
    case 'selector-finder-res':
      chrome.tabs.query({ active: true }, function (tabs) {
        tabs.forEach((tab) => {
          chrome.tabs.sendMessage(tab.id!, {
            action: 'rpa-find-selector',
            data: { active: false },
          });
        });
      });
      messageDispatcher.send(action);
      break;
    case 'get-pick-selector-res':
      messageDispatcher.send(msg);
      break;
    case 'get-rpa-recorder-state':
      sendResponse(rpaRecorderEnabled);
      break;
    case 'motion-recorder-res':
      messageDispatcher.send(msg);
      break;
    case 'should-show-transit-alert':
      if (data.loadTime > pageLoadTimeout * 1000) {
        // 自动切换加速通道
        // doFetch(`${API_URL}/api/autoSwitchTransit`);
        sendResponse(false);
      } else if (data.loaded) {
        sendResponse(false);
      } else {
        sendResponse(null);
      }
      break;
    case 'show-switch-transit-modal':
      messageDispatcher.send(action);
      sendMessageToTabs('hide-page-load-alert', {});
      break;
    case 'hide-page-load-alert':
      pageLoadAlertCount = 0;
      sendMessageToTabs(action, data);
      break;
    case 'get-user-action-layer-option':
      if (envData?.rpaFlowId) {
        sendResponse({
          enabled: true,
          opacity:
            envData.showMouseTrack || envData.rpaPreview || showMouseTrackLayer ? 0.2 : 0.001,
        });
      } else {
        sendResponse({ enabled: false, opacity: showMouseTrackLayer ? 0.2 : 0.001 });
      }
      break;
  }
});

export function donkeyRequest(path: string, options: Record<string, any> = {}): Promise<any> {
  const { method = 'GET', params = {}, data = {}, ...restOpts } = options;
  return new Promise((resolve, reject) => {
    fetch(`${API_URL}/donkeyRequest`, {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        path,
        options: {
          method,
          params,
          data,
          ...restOpts,
        },
      }),
    }).then(async (res) => {
      const { success, message: msg, data: resData } = await res.json();
      if (success) {
        resolve(resData);
      } else {
        reject(new Error(msg));
      }
    });
  });
}

export default {};
