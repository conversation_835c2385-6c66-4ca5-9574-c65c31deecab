import WebResponseHeadersDetails = chrome.webRequest.WebResponseHeadersDetails;
import WebRequestDetails = chrome.webRequest.WebRequestDetails;

interface FilterOpts {
  types: ('image' | 'video')[];
  maxImageSize?: number;
}

class WebRequestBlocker {
  private filterOpts: FilterOpts;

  constructor() {
    this.filterOpts = {
      types: [],
    };
  }

  start(opts: FilterOpts) {
    this.filterOpts = opts;
    const types: any = [];
    if (this.filterOpts.types.includes('image')) {
      types.push('image');
    }
    console.log('resource block types', types);

    chrome.webRequest.onHeadersReceived.addListener(
      this._handleHeadersReceived.bind(this),
      { urls: ['http://*/*', 'https://*/*'], types },
      ['blocking', 'responseHeaders', 'extraHeaders'],
    );
  }

  stop() {
    chrome.webRequest.onHeadersReceived.removeListener(this._handleHeadersReceived);
  }

  private _handleHeadersReceived(details: WebResponseHeadersDetails) {
    if (details.type === 'image' && this.filterOpts.maxImageSize) {
      const contentLenHeader = details.responseHeaders?.find(
        (vo) => vo.name?.toLowerCase() === 'content-length',
      );
      // console.log(details.responseHeaders?.map((v) => `${v.name}:${v.value}`).join('\n'));
      // console.log(
      //   details.url + '\n' + contentLenHeader?.value + '-' + this.filterOpts.maxImageSize + '\n',
      // );
      if (!/^http:\/\/szdamai.(local|tab)/.test(details.url) && contentLenHeader?.value) {
        const bytes = Number(contentLenHeader?.value);
        if (bytes >= this.filterOpts.maxImageSize) {
          return { cancel: true };
        } else {
          return { cancel: false };
        }
      }
    }
    return { cancel: false };
  }
}

export const webRequestBlocker = new WebRequestBlocker();
