import { MessageDispatcher } from './index';

interface Props {}

function wildTest(wildcard: string, str: string) {
  if (/^\*\.\w+/.test(wildcard)) {
    if (wildcard.slice(2) === str) {
      return true;
    }
  }
  let w = wildcard.replace(/[.+^${}()|[\]\\]/g, '\\$&'); // regexp escape
  const re = new RegExp(`^${w.replace(/\*/g, '.*').replace(/\?/g, '.')}$`, 'i');
  return re.test(str);
}

function domainWildCheck(hostname: string, domains: string[] = []) {
  return domains.findIndex((pattern) => wildTest(pattern, hostname)) !== -1;
}

/**
 * 域名是否在白名单中
 * @param hostname
 * @param remoteWhiteList
 */
function isInWhiteList(hostname: string, remoteWhiteList: string[] = []) {
  return domainWildCheck(hostname, remoteWhiteList);
}

/**
 * 监听Tab页签事件、更新
 * 白名单校验、安全策略中的相关规则的校验也在此
 */
export default class TabMonitor {
  tabs: { tabId?: number; index?: number; url?: string }[];
  private props: Props;
  private messageDispatcher?: MessageDispatcher;
  private reportUpdateEnabled: boolean;
  private reportActionEnabled: boolean;
  private updateTimer: any;
  private shopInfo: Record<string, any>;
  private whitelist: string[];
  private blockPageList: string[];
  private webRequestListenerStarted: boolean;
  private serverUrl: string;
  constructor(props: Props) {
    this.tabs = [];
    this.webRequestListenerStarted = false;
    this.props = props;
    this.reportUpdateEnabled = false;
    this.reportActionEnabled = false;
    this.updateTimer = 0;
    this.shopInfo = {};
    this.whitelist = [];
    this.blockPageList = [];
    this.serverUrl = 'http://szdamai.local/';
    this.init();
  }

  setShopInfo(shopInfo: Record<string, any>) {
    this.shopInfo = shopInfo;
    if (!this.webRequestListenerStarted && this.shopInfo.securityPolicyEnabled) {
      if (
        ['Blacklist', 'Whitelist'].includes(this.shopInfo.domainPolicy!) &&
        this.shopInfo.domains?.length > 0
      ) {
        this.startWebRequestListener();
        this.webRequestListenerStarted = true;
      }
    }
  }

  setServerUrl(url: string) {
    this.serverUrl = url;
  }

  setMsgDispatcher(msgDispatcher: MessageDispatcher) {
    this.messageDispatcher = msgDispatcher;
  }

  changeReportUpdateEnabled(enabled: boolean) {
    this.reportUpdateEnabled = enabled;
  }

  changeReportActionEnabled(enabled: boolean) {
    this.reportActionEnabled = enabled;
  }

  updateWhitelist(whitelist: string[]) {
    this.whitelist = whitelist;
  }

  updateBlockPageList(blockPageList: string[]) {
    this.blockPageList = blockPageList;
  }

  _checkShopDomain(url: string) {
    if (!this.shopInfo.securityPolicyEnabled) return true;
    let isInShopDomainList = false;
    if (['Blacklist', 'Whitelist'].includes(this.shopInfo.domainPolicy!)) {
      const shopDomains = this.shopInfo.domains?.map((d: Record<string, any>) => d.domain) ?? [];
      let urlDomain = url;
      try {
        urlDomain = new URL(url).host;
      } catch (e) {}
      isInShopDomainList =
        shopDomains.findIndex((shopDomain: string) => {
          const urlWithoutProtocol = shopDomain.replace(/^https?:\/\//, '');
          if (urlWithoutProtocol.includes('/')) {
            // 按完整 URL 匹配
            return wildTest(urlWithoutProtocol, url.replace(/^https?:\/\//, ''));
          } else {
            let iteratorDomain = shopDomain;
            if (/^https?:\/\//.test(iteratorDomain ?? '')) {
              try {
                iteratorDomain = new URL(iteratorDomain!).host;
              } catch (e) {}
            }
            return iteratorDomain && wildTest(iteratorDomain, urlDomain);
          }
        }) !== -1;
    }
    if (this.shopInfo.domainPolicy === 'Blacklist') {
      // 不在黑名单中
      return !isInShopDomainList;
    } else if (this.shopInfo.domainPolicy === 'Whitelist') {
      // 在白名单中
      return isInShopDomainList;
    }
    return true;
  }

  startWebRequestListener() {
    console.log('@@@startWebRequestListener');
    chrome.webRequest.onBeforeRequest.addListener(
      (details) => {
        // console.log('@@@onBeforeRequest', details);
        try {
          if (details.frameId !== 0) return { cancel: false };
          const urlObj = new URL(details.url);
          const { hostname, origin, pathname } = urlObj;
          if (
            /^szdamai\./.test(hostname) ||
            /^ggbrowser\./.test(hostname) ||
            ['localhost', '127.0.0.1'].includes(hostname) ||
            this.whitelist.length === 0
          ) {
            return { cancel: false };
          }
          if (!this._checkShopDomain(details.url)) {
            if (details.type === 'main_frame') {
              chrome.tabs.update(details.tabId, {
                url: `${this.serverUrl}accessDeny?p=${details.url}`,
              });
            }
            return { cancel: true };
          }
        } catch (e) {
          return { cancel: false };
        }
      },
      {
        urls: ['http://*/*', 'https://*/*'],
        types: ['main_frame', 'sub_frame', 'xmlhttprequest', 'other'],
      },
      ['blocking'],
    );
  }

  init() {
    chrome.webNavigation.onBeforeNavigate.addListener(
      (details) => {
        // console.log('@@@onBeforeNavigate', details);
        try {
          if (details.frameId !== 0) return;
          const urlObj = new URL(details.url);
          const { hostname, origin, pathname } = urlObj;
          if (
            /^szdamai\./.test(hostname) ||
            /^ggbrowser\./.test(hostname) ||
            ['localhost', '127.0.0.1'].includes(hostname) ||
            this.whitelist.length === 0
          ) {
            return;
          }
          if (!isInWhiteList(hostname, this.whitelist)) {
            chrome.tabs.update(details.tabId, {
              url: `${this.serverUrl}accessDeny?s=${hostname}&u=${details.url}`,
            });
          } else if (
            this.blockPageList.some((rule) => {
              let path1 = '';
              let path2 = '';
              try {
                const obj1 = new URL(/^https?:\/\//.test(rule) ? rule : `https://${rule}`);
                path1 = obj1.origin.replace(/^https?:\/\//, '') + obj1.pathname;
                path2 = origin.replace(/^https?:\/\//, '') + pathname;
              } catch (e) {
                console.error('URL compare error', e);
              }
              return path1 === path2 && path1 !== '';
            })
          ) {
            chrome.tabs.update(details.tabId, {
              url: `${this.serverUrl}accessDeny?p=${details.url}`,
            });
          } else if (!this._checkShopDomain(details.url)) {
            chrome.tabs.update(details.tabId, {
              url: `${this.serverUrl}accessDeny?p=${details.url}`,
            });
          }
        } catch (e) {}
      },
      {
        url: [{ schemes: ['http', 'https'] }],
      },
    );
    chrome.tabs.onCreated.addListener((tab) => {
      this.updateTabs();
    });
    chrome.tabs.onUpdated.addListener((tab) => {
      this.updateTabs();
    });
    chrome.tabs.onRemoved.addListener(async (tabId) => {
      if (this.reportActionEnabled) {
        const tab = this.tabs.find((v) => v.tabId === tabId);
        this.messageDispatcher?.send({
          action: 'motion-recorder-res',
          data: {
            type: 'rpa.tab.CloseTab',
            props: {
              tab: tab ? (tab.index ?? 0) + 1 : -1,
            },
          },
        });
      }
      this.updateTabs();
    });
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      this.updateTabs();
      if (this.reportActionEnabled) {
        chrome.tabs.get(activeInfo.tabId, (tab) => {
          this.messageDispatcher?.send({
            action: 'motion-recorder-res',
            data: {
              type: 'rpa.tab.SelectTab',
              props: {
                tab: tab.index + 1,
              },
            },
          });
        });
      }
    });
    chrome.tabs.onMoved.addListener(() => {
      this.updateTabs();
    });
  }

  updateTabs() {
    if (!this.reportUpdateEnabled) return;
    clearTimeout(this.updateTimer);
    this.updateTimer = setTimeout(() => {
      chrome.tabs.query({}, (tabs) => {
        const t = tabs.map((tab) => ({ tabId: tab.id, index: tab.index, url: tab.url }));
        // console.log('tabs', t);
        this.tabs = t;
        this.messageDispatcher?.send({
          action: 'tab-update',
          data: this.tabs.map((v) => v.url),
        });
      });
    }, 1000);
  }
}

export const tabMonitor = new TabMonitor({});
