export default class PageLoadObserver {
  private startTime: number;
  constructor() {
    this.startTime = new Date().getTime();
  }

  getTotalTime() {
    const prerequestTime = performance.timing.requestStart - performance.timing.navigationStart;
    const latencyTime = performance.timing.responseStart - performance.timing.requestStart;
    const serverTime = performance.timing.responseEnd - performance.timing.responseStart;
    const domLoadingTime = performance.timing.domInteractive - performance.timing.responseEnd;
    const domCompleteTime = performance.timing.domComplete - performance.timing.domInteractive;
    const loadTime = performance.timing.loadEventEnd - performance.timing.domComplete;
    const totalTime =
      prerequestTime + latencyTime + serverTime + domLoadingTime + domCompleteTime + loadTime;
    return totalTime;
  }

  getLoadTime() {
    if (performance.timing.domContentLoadedEventEnd) {
      return performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart;
    }
    return new Date().getTime() - this.startTime;
  }

  isLoaded() {
    return !!performance.timing.domContentLoadedEventEnd;
  }

  showAlert() {
    let div = document.getElementById('__huayoung-page-load-alert__');
    if (div) {
      div.remove();
    }
    div = document.createElement('div');
    div.setAttribute('id', '__huayoung-page-load-alert__');
    div.setAttribute(
      'style',
      'position:fixed;z-index:10000;left:50%;top:50%;width:500px;height:250px;margin-left: -250px;margin-top: -125px;line-height: 30px;border:1px solid #ddd;border-radius:5px;background:#fff;box-shadow: 0 0 20px rgb(0 0 0 / 30%)',
    );
    const headDiv = document.createElement('div');
    headDiv.setAttribute(
      'style',
      'padding: 0 10px; margin-bottom: 24px; border-bottom: 1px solid #ddd; font-size: 14px; cursor: move; color: #333; text-align: left !important',
    );
    headDiv.innerText = '网页加载超时';
    headDiv.addEventListener('mousedown', (e) => {
      if (!e.target) return;
      const rect = e.target.getBoundingClientRect();
      const x = e.pageX - rect.x;
      const y = e.pageY - rect.y;
      function handleMouseMove(e) {
        if (div) {
          div.style.right = 'auto';
          div.style.left = e.pageX - x + 'px';
          div.style.top = e.pageY - y + 'px';
          div.style.marginLeft = '0px';
          div.style.marginTop = '0px';
        }
      }
      function removeListener() {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', removeListener);
      }
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', removeListener);
    });
    const icon = document.createElement('img');
    icon.setAttribute(
      'src',
      'data:image/png;base64,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',
    );
    icon.setAttribute(
      'style',
      'float: left; margin-left: 32px; margin-right: 16px; width: 48px; height: 48px;',
    );
    const contentDiv = document.createElement('div');
    contentDiv.setAttribute(
      'style',
      'padding: 0 20px; font-size: 14px; color: #333; text-align: left !important',
    );
    contentDiv.innerText = '当前网页加载时间过长';
    const descDiv = document.createElement('div');
    descDiv.setAttribute(
      'style',
      'padding: 0 20px; padding-left: 96px; font-size: 12px; color: #999; text-align: left !important',
    );
    descDiv.innerText = '针对海外站点，当网页加载过慢时，可通过切换加速通道予以改善';
    const okBtn = document.createElement('button');
    okBtn.setAttribute(
      'style',
      'position: absolute; right: 160px; bottom: 24px; height: 32px; width: 120px; padding: 4px 0; line-height: 1; font-size: 14px; border-radius: 3px;text-align: center;color: #fff; border: 0; background: #52C41A;cursor: pointer;',
    );
    okBtn.innerText = '切换加速通道';
    const cancelBtn = document.createElement('button');
    cancelBtn.setAttribute(
      'style',
      'position: absolute; right: 32px; bottom: 24px; height: 32px; width: 120px; padding: 4px 0; line-height: 1; font-size: 14px; border-radius: 3px;text-align: center;color: #333; border: 1px solid #ccc; background: #fff;cursor: pointer;',
    );
    cancelBtn.innerText = '不再提醒';
    div.appendChild(headDiv);
    div.appendChild(icon);
    div.appendChild(contentDiv);
    div.appendChild(descDiv);
    div.appendChild(okBtn);
    div.appendChild(cancelBtn);
    okBtn.addEventListener('click', () => {
      chrome.runtime.sendMessage({ action: 'show-switch-transit-modal' });
      //@ts-ignore
      document.getElementById('__huayoung-page-load-alert__').remove();
    });
    cancelBtn.addEventListener('click', (e) => {
      //@ts-ignore
      document.getElementById('__huayoung-page-load-alert__').remove();
      chrome.runtime.sendMessage({ action: 'hide-page-load-alert' });
    });
    document.body.appendChild(div);
  }

  hideAlert() {
    let div = document.getElementById('__huayoung-page-load-alert__');
    if (div) {
      div.remove();
    }
  }
}
