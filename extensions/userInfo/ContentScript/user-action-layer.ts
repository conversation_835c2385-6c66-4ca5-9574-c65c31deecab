interface Props {
  opacity?: number;
}

export default class UserActionLayer {
  private props: Props;
  private canvasElm?: HTMLCanvasElement;
  constructor() {
    this.props = {};
  }

  setSize(isUpdate = false) {
    if (!this.canvasElm) return;
    let savedImageData = '';
    if (isUpdate) {
      savedImageData = this.canvasElm.toDataURL();
    }
    this.canvasElm.width = window.innerWidth;
    this.canvasElm.height = window.innerHeight;
    this.canvasElm.style.width = '' + window.innerWidth + 'px';
    this.canvasElm.style.height = '' + window.innerHeight + 'px';
    if (isUpdate) {
      // 将之前保存的绘制内容重新绘制到新的画布上
      const image = new Image();
      image.onload = () => {
        const ctx = this.canvasElm?.getContext('2d');
        ctx?.drawImage(image, 0, 0);
      };
      image.src = savedImageData;
    }
  }

  initCanvas() {
    // @ts-ignore
    if (window.__user_action_layer) {
      return;
    }
    // @ts-ignore
    window.__user_action_layer = true;
    // Add a canvas on top of the document.body > div
    const canvasWrap = document.createElement('div');
    canvasWrap.setAttribute('id', 'hy-rpa-user-action-layer-wrap');
    const canvas = document.createElement('canvas');
    canvasWrap.appendChild(canvas);
    this.canvasElm = canvas;
    canvas.setAttribute('id', 'hy-rpa-user-action-layer');
    canvas.setAttribute('style', 'pointer-events: none !important; user-select: none !important; position: fixed !important; left: 0px !important; top: 0px !important; z-index: 999999 !important;');
    this.setSize();
    // noinspection JSCheckFunctionSignatures
    document.body.appendChild(canvasWrap);
    canvas.style.opacity = String(this.props.opacity ?? 0.01);

    const cxt = canvas.getContext('2d');
    if (!cxt) {
      return;
    }
    //@ts-ignore
    canvas.cls = () => {
      canvas.height = canvas.height;
    };

    // Listening user events and draw
    // document.addEventListener('keydown', (e) => {
    //   // console.log('key DOWN alt:' + e.altKey + ' shift:' + e.shiftKey + ' ctrl:' + e.ctrlKey + ' meta:' + e.metaKey + ' code:' + e.code);
    // });

    // document.addEventListener('keyup', (e) => {
    //   // console.log('key UP alt:' + e.altKey + ' shift:' + e.shiftKey + ' ctrl:' + e.ctrlKey + ' meta:' + e.metaKey + ' code:' + e.code);
    // });

    document.addEventListener('mousemove', (e) => {
      cxt.beginPath();
      cxt.arc(e.clientX, e.clientY, 3, 0, 360, false);
      cxt.fillStyle = 'green';
      cxt.fill();
      cxt.closePath();
    });

    let drawDown = (e: any) => {
      cxt.beginPath();
      cxt.arc(e.clientX, e.clientY, 15, 0, 360, false);
      cxt.fillStyle = 'black';
      cxt.fill();
      cxt.closePath();
    };
    let drawUp = (e: any) => {
      cxt.beginPath();
      cxt.arc(e.clientX, e.clientY, 9, 0, 360, false);
      cxt.fillStyle = 'blue';
      cxt.fill();
      cxt.closePath();
    };
    document.addEventListener('mousedown', drawDown);
    document.addEventListener('touchstart', (e)=>{
      drawDown(e.changedTouches[0]);
    });

    document.addEventListener('mouseup', drawUp);
    document.addEventListener('touchend', (e)=>{
      drawUp(e.changedTouches[0]);
    });

    document.addEventListener('click', (e) => {
      drawDown(e);
      drawUp(e);
    });
    window.addEventListener('resize', () => {
      this.setSize(true);
    });
  }

  start(props: Props = {}) {
    this.props = props;
    if (document.readyState === 'loading') {
      window.addEventListener('DOMContentLoaded', () => {
        this.initCanvas();
      });
    } else {
      this.initCanvas();
    }
  }

  stop() {
    document.getElementById('hy-rpa-user-action-layer-wrap')?.remove();
    // @ts-ignore
    window.__user_action_layer = false;
  }
}
