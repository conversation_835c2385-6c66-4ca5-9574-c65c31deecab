/**
 * 指纹伪装相关代码
 */

/*
 * 	Trace page helper script for before documents load
 * 	Copyright AbsoluteDouble 2018 - 2020
 * 	Written by <PERSON>
 * 	https://absolutedouble.co.uk/
 */

// A general fix for browser that use window.browser instead of window.chrome
if (typeof window.chrome === 'undefined' || !window.chrome.hasOwnProperty('extension'))
  window.chrome = (function () {
    // @ts-ignore
    return window.msBrowser || window.browser || window.chrome;
  })();

// @ts-ignore
const TPage = {
  Settings: {
    prefix: 'tp_',
    Prefs: {},
    Exec: {},
    RunProtections: ['Pref_PluginHide', 'Pref_Fonts'],
    ForceRunProtections: {
      /*Pref_Fonts: 1*/
    }, //就算外部不包含对应setting也执行
  },

  css: 'font-size:1em;line-height:1.4em;color:#fff;background-color:#1a1a1a;border:.1em solid #00af00;',

  startTracePage: function () {
    let pageUrl = window.location.href || document.location.href;
    try {
      // @ts-ignore
      pageUrl = window.top?.location.href;
    } catch (e) {}

    // https://bugs.chromium.org/p/chromium/issues/detail?id=54257
    chrome.runtime.sendMessage(
      {
        action: 'fingerprint',
        data: pageUrl,
      },
      TPage.init,
    );

    // 执行donkeyEnv时内核会自动更新浏览器进程的env，新打开的render进程会自动更新，不需要在页面打开时手动调用了
    // chrome.runtime.sendMessage(
    //   {
    //     action: 'get-outbound-ip',
    //   },
    //   (outboundIp) => {
    //     TPage.updateDonkeyEnv(outboundIp);
    //   },
    // );

    chrome.runtime.onMessage.addListener((msg) => {
      const { action, data = {} } = msg;
      switch (action) {
        case 'outbound-ip-changed':
          TPage.updateDonkeyEnv(data);
          break;
      }
    });
  },

  // Call with data loaded from background page
  init: function (prefs: any) {
    TPage.Settings.Prefs = prefs || {};

    TPage.runProtections();
  },

  updateDonkeyEnv: function (ip: string) {
    document.execCommand('donkeyEnv', false, `donkey-webrtc-host=${ip}`);
  },

  page: {
    createProtId: function () {
      let n = '',
        t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
      for (let a = 0; 11 > a; a++) {
        n += t.charAt(Math.floor(Math.random() * t.length));
      }
      return TPage.Settings.prefix + n;
    },

    runCodeInFrames: function (protId: string) {
      return (
        `
			//console.log(window);
			//console.log(self);
			self['` +
        protId +
        `_func'](window);
			//self['` +
        protId +
        `_func'](self);
			["HTMLIFrameElement","HTMLFrameElement"].forEach(function(el) {
				var wind = self[el].prototype.__lookupGetter__('contentWindow'),
					cont = self[el].prototype.__lookupGetter__('contentDocument');
				Object.defineProperties(self[el].prototype,{
					contentWindow:{
						get:function(){
						  if (this.src && this.src.indexOf('//') !== -1 && location.host !== this.src.split('/')[2]) return wind.apply(this);
							let frame = wind.apply(this);
							if (this.src.indexOf('data:') === 0) return frame;
							if (frame && self['${protId}_func']) self['` +
        protId +
        `_func'](frame);
							return frame;
						}
					},
					contentDocument:{
						get:function(){
							if (this.src && this.src.indexOf('//') !== -1 && location.host !== this.src.split('/')[2]) return cont.apply(this);
							let frame = cont.apply(this);
							if (this.src.indexOf('data:') === 0) return frame;
							if (frame && self['${protId}_func']) self['` +
        protId +
        `_func'](frame);
							return frame;
						}
					}
				});
			});`
      );
    },
    // @ts-ignore
    createWrapper: function (protCode, protParams, protName, protId) {
      let protFunction =
        `self['` +
        protId +
        `_func'] = function(frame){
				if (frame === null) {
					console.error("Frame is null");
					return;
				}
				if (!frame['` +
        protId +
        `_done']) {
					(` +
        protCode +
        `)(frame,` +
        protParams +
        `);
				} else {
					frame['` +
        protId +
        `_done'] = true;
					//console.log(frame);
				}
			};`;

      let protRun = TPage.page.runCodeInFrames(protId);

      return protFunction + protRun;
    },
    // @ts-ignore
    createScript: function (injectCode) {
      let el = document.createElement('script');

      el.type = 'text/javascript';
      el.textContent = injectCode;

      return el;
    },
    // @ts-ignore
    runInjection: function (protCode, protParams, protName) {
      let node = document.documentElement || document.head || document.body;
      let protId = TPage.page.createProtId();
      let injectCode = TPage.page.createWrapper(protCode, protParams, protName, protId);

      try {
        let script1 = TPage.page.createScript(injectCode);
        node.insertBefore(script1, node.firstChild);
        // @ts-ignore
        TPage.Settings.Exec[protName] = true;
        script1.remove();
      } catch (e) {
        try {
          let script2 = TPage.page.createScript(injectCode);
          node.appendChild(script2);
          // @ts-ignore
          TPage.Settings.Exec[protName] = true;
          script2.remove();
        } catch (ae) {
          console.warn(ae);
        }
      }
    },
  },

  runProtections: function () {
    let protectionKeys = TPage.Settings.RunProtections;
    for (let i = 0; i < protectionKeys.length; i++) {
      let protection = protectionKeys[i];
      let host = window.location.hostname || 'Unknown Host';

      // Check if the protection has already run
      // @ts-ignore
      if (TPage.Settings.Exec[protection]) {
        continue;
      }

      // Check if the protection has code to run
      // @ts-ignore
      if (!TPage.protectionFunctions[protection]) continue;

      // Check if we need to add any settings from background page
      // @ts-ignore
      let tempParams = TPage.Settings.Prefs[protection];
      // @ts-ignore
      if (!TPage.Settings.ForceRunProtections[protection] && !tempParams) {
        continue;
      }
      let params = JSON.stringify(tempParams);

      // Run protection code
      // @ts-ignore
      let protFunc = TPage.protectionFunctions[protection];

      TPage.page.runInjection(protFunc, params, protection);
    }
  },

  protectionFunctions: {
    // @ts-ignore
    Pref_PluginHide: function (frame, settings) {
      if (!frame.navigator) {
        return;
      }

      // @ts-ignore
      function definify(struct) {
        const redefinedProps = {};
        Object.keys(struct).forEach((prop) => {
          const fn = () => {
            return struct[prop];
          };
          Object.defineProperties(fn, { name: { value: 'get ' + prop, configurable: true } });
          // @ts-ignore
          redefinedProps[prop] = {
            get: fn,
            configurable: true,
          };
        });
        return redefinedProps;
      }

      // @ts-ignore
      function doUpdateProp(obj, prop, newVal) {
        let props = Object.getOwnPropertyDescriptor(obj, prop) || { configurable: true };

        if (!props['configurable']) {
          return;
        }

        props['value'] = newVal;
        Object.defineProperty(obj, prop, props);

        return props;
      }

      let plugins = settings.plugins;
      // @ts-ignore
      let Plugin = function (name, filename, description) {
        // @ts-ignore
        this['name'] = name;
        // @ts-ignore
        this['filename'] = filename;
        // @ts-ignore
        this['description'] = description;
      };

      let PluginArray = function () {
        // @ts-ignore
        this.__proto__ = frame.PluginArray;
        for (let i = 0; i < plugins.length; i++) {
          // @ts-ignore
          let pluginItem = new Plugin(plugins[i].name, plugins[i].filename, plugins[i].description);
          doUpdateProp(pluginItem, 'toString', function () {
            return '[object Plugin]';
          });
          // @ts-ignore
          this['' + i] = pluginItem;
        }
      };
      // @ts-ignore
      let pluginArray = new PluginArray();
      Object.defineProperties(frame.Navigator.prototype, definify({ plugins: pluginArray }));
      doUpdateProp(pluginArray, 'length', plugins.length);
      doUpdateProp(pluginArray, 'refresh', function () {});
      doUpdateProp(pluginArray, 'item', function (i: any) {
        return pluginArray['' + i];
      });
      doUpdateProp(pluginArray, 'toString', function () {
        return '[object PluginArray]';
      });
      for (let i = 0; i < plugins.length; i++) {
        let pluginItem = pluginArray.item(i);
        doUpdateProp(pluginArray, plugins[i].name, pluginItem);
      }
    },

    // @ts-ignore
    Pref_Fonts: function (frame, settings = {}) {
      let judge = function (font: FontFace) {
        let family = font.family;
        // @ts-ignore
        if ((settings.force || '').toLowerCase().split(',').indexOf(family.toLowerCase()) != -1) {
          return 'white';
        } else if (
          // @ts-ignore
          (settings.excludes || '').toLowerCase().split(',').indexOf(family.toLowerCase()) != -1
        ) {
          return 'black';
        }
        return undefined;
      };

      let load = function () {
        return new Promise((resolve, reject) => {
          // @ts-ignore
          let that = this;
          let flag = judge(that);
          console.log('flag=', flag, that);
          if ('black' === flag) {
            //如果是黑名单列表里的
            reject('DOMException: A network error occurred.');
          } else if ('white' === flag) {
            //如果是force白名单列表里的
            // @ts-ignore
            resolve(that);
          } else {
            // @ts-ignore
            resolve(originLoad.call(that));
          }
        });
      };

      let status = function () {
        // @ts-ignore
        let that = this;
        let flag = judge(that);
        if ('black' === flag) {
          return 'error';
        } else {
          return 'loaded';
        }
      };

      const originLoad = frame.FontFace.prototype.load;
      Object.defineProperties(frame.FontFace.prototype, {
        load: {
          configurable: true,
          value: load,
        },
        status: {
          configurable: true,
          get: status,
          set: () => {},
        },
      });
    },
  },
};

// Let's begin
TPage.startTracePage();
