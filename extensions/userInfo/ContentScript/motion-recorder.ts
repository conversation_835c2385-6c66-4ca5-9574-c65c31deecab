import { finder } from './finder';

type Props = {};

export default class MotionRecorder {
  props: Props;
  constructor(props: Props = {}) {
    this.props = props;
    this.start = this.start.bind(this);
    this.stop = this.stop.bind(this);
    this.handleClick = this.handleClick.bind(this);
  }

  start() {
    document.addEventListener('click', this.handleClick, true);
  }

  stop() {
    document.removeEventListener('click', this.handleClick, true);
  }

  sendMessage(data = {}) {
    chrome.runtime.sendMessage({ action: 'motion-recorder-res', data });
  }

  handleClick(e: MouseEvent) {
    console.log('MouseEvent-Click', finder(e.target as Element));
    if (!e.target) return;
    this.sendMessage({
      motion: 'CLICK',
      selector: finder(e.target as Element),
    });
  }
}
