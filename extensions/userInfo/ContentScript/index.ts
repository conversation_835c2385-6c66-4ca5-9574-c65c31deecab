import SelectorFinder from './selector-finder';
import MotionRecorder from './motion-recorder';
import PageLoadObserver from './page-load-observer';
import UserActionLayer from './user-action-layer';

const pageLoadObserver = new PageLoadObserver();
const selectorFinder = new SelectorFinder();
const userActionLayer = new UserActionLayer();
setTimeout(() => {
  // DEBUG
  // selectorFinder.start('child', '.result > .c-container > div:nth-child(1)');
  // selectorFinder.start('repeat');
  // selectorFinder.start('container');
  // selectorFinder.start();
}, 2000);
const motionRecorder = new MotionRecorder();
chrome.runtime.onMessage.addListener((msg: { action: string; data: any }) => {
  console.log('onMessage', msg);
  const { action, data = {} } = msg;
  const layerCanvas = document.getElementById('hy-rpa-user-action-layer');
  switch (action) {
    case 'rpa-find-selector':
      if (data.active) {
        selectorFinder.start(data.mode, data.container, data.parentMode);
      } else {
        selectorFinder.stop();
      }
      break;
    case 'get-pick-selector':
      chrome.runtime.sendMessage({
        action: 'get-pick-selector-res',
        data: selectorFinder.finderSelector(selectorFinder.currentElm),
      });
      break;
    case 'rpa-recorder':
      if (data.active) {
        motionRecorder.start();
      } else {
        motionRecorder.stop();
      }
      break;
    case 'get-page-load-info':
      const loadTime = pageLoadObserver.getTotalTime();
      chrome.runtime.sendMessage(
        { action: 'should-show-transit-alert', data: { loadTime } },
        (show) => {
          if (show) {
            pageLoadObserver.showAlert();
          }
        },
      );
      break;
    case 'hide-page-load-alert':
      pageLoadObserver.hideAlert();
      break;
    case 'clear-hy-rpa-user-action-layer':
      if (layerCanvas) {
        try {
          // @ts-ignore
          layerCanvas.getContext('2d').clearRect(0, 0, window.innerWidth, window.innerHeight);
        } catch (e) {
          console.error('clear-hy-rpa-user-action-layer error: ', e);
        }
      }
      break;
    case 'show-hy-rpa-user-action-layer':
      userActionLayer.start({ opacity: 0.2 });
      break;
    case 'hide-hy-rpa-user-action-layer':
      userActionLayer.stop();
      break;
  }
});

chrome.runtime.sendMessage({ action: 'get-rpa-recorder-state' }, (enabled) => {
  if (enabled) {
    motionRecorder.start();
  }
});

chrome.runtime.sendMessage({ action: 'get-user-action-layer-option' }, (option) => {
  if (option.enabled) {
    userActionLayer.start({ opacity: option.opacity });
  }
});

// let needShowAlert = false;
// const pageLoadObserverTimer = setInterval(() => {
//   if (
//     window.parent !== window.self ||
//     ['szdamai.local', 'szdamai.tab'].includes(window.location.host)
//   )
//     return;
//   const loaded = pageLoadObserver.isLoaded();
//   const loadTime = pageLoadObserver.getLoadTime();
//   console.log(JSON.stringify({ loaded, loadTime }));
//   if (needShowAlert) {
//     if (loaded) {
//       pageLoadObserver.showAlert();
//       clearInterval(pageLoadObserverTimer);
//     } else {
//       return;
//     }
//   }
//   chrome.runtime.sendMessage(
//     { action: 'should-show-transit-alert', data: { loaded, loadTime } },
//     (show) => {
//       if (show) {
//         needShowAlert = true;
//       } else if (show === false) {
//         clearInterval(pageLoadObserverTimer);
//       }
//     },
//   );
// }, 2000);
