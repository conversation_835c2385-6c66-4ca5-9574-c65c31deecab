import { finder } from './finder';

type Mode = 'single' | 'container' | 'repeat' | 'child';

const overlayCls = '__huayoung-selector-finder-overlay__';
const containerCls = '__huayoung-selector-finder-container__';
const formCls = '__huayoung-selector-form__';
const MAX_HIGHLIGHT_OVERLAY = 100;

const modeNameMap = {
  single: '单个',
  container: '容器',
  repeat: '循环',
  child: '子',
};

/**
 * 元素选择
 */
export default class SelectorFinder {
  private throttleTimer: any;
  currentElm: any;
  private active: boolean;
  private mode: Mode;
  private root: Element | Document;
  private selector: string | string[];
  private containerSelector: string;
  private containerIdx: number = 0;
  private previewTimer: any;
  private activeContainerTimer: any = 0;
  constructor() {
    this.throttleTimer = 0;
    this.currentElm = null;
    this.active = false;
    this.mode = 'single';
    this.root = document;
    this.selector = '';
    this.containerSelector = '';
    this.previewTimer = 0;
    this._handleMouseOver = this._handleMouseOver.bind(this);
    this._handleOverlayClick = this._handleOverlayClick.bind(this);
    this._handleContainerIdxChange = this._handleContainerIdxChange.bind(this);
    this._handleControlBtnClick = this._handleControlBtnClick.bind(this);
  }

  _handleMouseOver(e: any) {
    this.active = true;
    clearTimeout(this.throttleTimer);
    this.throttleTimer = setTimeout(() => {
      const elm = e.target;
      if (!elm || !elm.getBoundingClientRect) return;
      this.currentElm = elm;
      if (this.mode === 'container') {
        this.drawContainer(elm);
        this._cleanOverlay();
        const selector = this.finderSelector(elm);
        if (selector) {
          this.root.querySelectorAll(selector + ' > *').forEach((item, idx) => {
            if (idx < MAX_HIGHLIGHT_OVERLAY) {
              this.highlight(item, false);
            }
          });
        }
        this.root.addEventListener('click', this._handleOverlayClick, true);
      } else {
        this.highlight(elm);
      }
    }, 50);
  }

  _handleOverlayClick(e: any) {
    e.stopPropagation();
    e.preventDefault();
    try {
      this.selector = this.finderSelector(this.currentElm);
      if (this.selector) {
        // this._renderSelectorList();
        // 将获取到的选择器发送给元素捕获器
        // content -> background -> node.js -> rpa selector picker window
        chrome.runtime.sendMessage({ action: 'selector-finder-res', data: this.selector });
        this.stop();
      }
    } catch (e) {
      console.error(e);
    }
    this.pause();
  }

  _handleControlBtnClick(e: any) {
    const { direction } = e.target.dataset;
    const selector = this._getSelector();
    if (!selector) return;
    const currentElm = this.root.querySelector(selector);
    if (!currentElm) return;
    let newElm: any = null;
    if (direction === 'up') {
      if (currentElm.parentElement !== this.root && currentElm.parentElement?.tagName !== 'HTML') {
        newElm = currentElm.parentElement;
      }
    } else if (direction === 'down') {
      if (currentElm.children.length > 0) {
        newElm = currentElm.children[0];
      }
    } else if (direction === 'prev') {
      if (currentElm.previousElementSibling) {
        newElm = currentElm.previousElementSibling;
      }
    } else if (direction === 'next') {
      if (currentElm.nextElementSibling) {
        newElm = currentElm.nextElementSibling;
      }
    }
    if (newElm) {
      try {
        const res = this.finderSelector(newElm);
        if (res) {
          this.selector = res;
          this._renderSelectorList();
          document.getElementById('__huayoung-action-preview__')?.click();
        }
      } catch (e) {
        console.error(e);
      }
    }
  }

  finderSelector(elm: HTMLElement) {
    let selector: string | string[] = '';
    try {
      if (!elm) return '';
      selector = finder(elm, {
        idName: () => this.mode !== 'repeat',
        root: this.root === document ? document.body : (this.root as Element),
        includeRoot: this.mode !== 'child',
        batchSelector: this.mode === 'repeat',
      });
      // console.log('selector', selector);
    } catch (e) {
      console.error('Finder selector error', e);
    }
    return selector;
  }

  highlight(elm: Element, clearOverlay = true) {
    let overlayElm: any = null;
    if (!clearOverlay) {
      overlayElm = document.createElement('div');
      overlayElm.className = overlayCls;
      document.body.append(overlayElm);
    } else {
      overlayElm = document.querySelector(`.${overlayCls}`);
      if (!overlayElm) {
        overlayElm = document.createElement('div');
        overlayElm.className = overlayCls;
        this.root.addEventListener('click', this._handleOverlayClick, true);
        document.body.append(overlayElm);
      }
    }
    const { height, width, top, left } = elm.getBoundingClientRect();
    overlayElm.style.display = 'block';
    overlayElm.style.top = `${top}px`;
    overlayElm.style.left = `${left}px`;
    overlayElm.style.width = `${width}px`;
    overlayElm.style.height = `${height}px`;
  }

  highlightSelector(selector: string) {
    this._cleanOverlay();
    // 绘制蒙层
    this.root.querySelectorAll(selector).forEach((elm, idx) => {
      if (idx < MAX_HIGHLIGHT_OVERLAY) {
        this.highlight(elm, false);
      }
    });
  }

  /**
   * 设置激活的父元素
   * @param index
   */
  setActiveContainer(index = 0) {
    this.containerIdx = index;
    const elm = document.querySelectorAll(this.containerSelector)[this.containerIdx];
    if (elm) {
      this._cleanOverlay();
      this.root.removeEventListener('mouseover', this._handleMouseOver, true);
      this.root = elm;
      this.drawContainer(this.root);
      const picker = document.getElementById('__huayoung-container-picker__') as HTMLInputElement;
      if (picker) {
        picker.value = `${index + 1}`;
      }
      this.root.addEventListener('mouseover', this._handleMouseOver, true);
      clearInterval(this.activeContainerTimer);
      this.activeContainerTimer = setInterval(() => {
        this.drawContainer(this.root as Element);
      }, 1000);
    } else {
      // 未找到父元素
      const placeholder = document.getElementById('__huayoung-container-picker-value__');
      if (placeholder) {
        placeholder.innerHTML = '<span style="color: red">未找到父元素</span>';
      }
    }
  }

  _handleContainerIdxChange(e: any) {
    const index = Number(e.target.value);
    const elm = document.querySelectorAll(this.containerSelector)[index - 1];
    if (elm) {
      this.setActiveContainer(index - 1);
    } else {
      e.target.value = `${this.containerIdx + 1}`;
    }
  }

  drawContainer(elm?: Element | null) {
    if (!elm) return;
    let containerElm: any = document.querySelector(`.${containerCls}`);
    if (!containerElm) {
      containerElm = document.createElement('div');
      containerElm.className = containerCls;
      document.body.append(containerElm);
    }
    const { height, width, top, left } = elm.getBoundingClientRect();
    containerElm.style.display = 'block';
    containerElm.style.top = `${top - 2}px`;
    containerElm.style.left = `${left - 2}px`;
    containerElm.style.width = `${width + 2}px`;
    containerElm.style.height = `${height + 2}px`;
  }

  _renderSelectorList() {
    const list = typeof this.selector === 'string' ? [this.selector] : this.selector;
    if (typeof this.selector === 'string') {
      if (!/:nth-child\(\d+\)$/.test(this.selector)) {
        list.push(`${this.selector}:last-of-type`);
        list.push(`${this.selector}:first-of-type`);
      }
    }
    // console.log('list', list);
    const inputElm: HTMLInputElement | null = document.querySelector(
      '#__huayoung-selector-input__',
    );
    // @ts-ignore
    inputElm.value = list[0];
    inputElm?.addEventListener('blur', () => {
      document.getElementById('__huayoung-action-preview__')?.click();
    });
    inputElm?.addEventListener('keydown', (evt: KeyboardEvent) => {
      if (evt.code === 'Enter') {
        document.getElementById('__huayoung-action-preview__')?.click();
      }
    });
    this._countElements();
    const listElm = document.querySelector('#__huayoung-selector-list__');
    while (listElm?.children?.length! > 0) {
      listElm?.children[0].remove();
    }
    list.forEach((selector) => {
      const item = document.createElement('div');
      item.innerHTML = selector;
      item.addEventListener('click', () => {
        // @ts-ignore
        document.querySelector('#__huayoung-selector-input__').value = selector;
        listElm?.classList.toggle('show');
        document.getElementById('__huayoung-action-preview__')?.click();
      });
      // @ts-ignore
      listElm.appendChild(item);
    });
    if (typeof this.selector === 'string') {
      const controlElm = document.getElementById('__huayoung-selector-control__');
      if (controlElm) {
        // 激活控制器
        controlElm.classList.add('visible');
      }
    }
  }

  _renderForm() {
    const template = document.createElement('template');
    template.innerHTML = `<div class="${formCls} ghost">
<div id="__huayoung-selector-drag-handler__"></div>
<div style="text-align: center; margin-bottom: 14px; font-size: 14px;">选择${
      modeNameMap[this.mode]
    }元素</div>
${
  this.mode === 'child'
    ? `<div class="__huayoung-container-pick-row__">父元素：<span id="__huayoung-container-picker-value__">显示第<input id="__huayoung-container-picker__" type="number" min="1" max="999"/>个</span></div>`
    : ''
}
<div class="__huayoung-input-row__">
  <span>${modeNameMap[this.mode]}元素：</span>
  <div>
    <div class="__huayoung-selector-input-wrap__">
        <input id="__huayoung-selector-input__" />
        <div id="__huayoung-selector-toggle__"></div>
    </div>
    <div id="__huayoung-selector-desc__"></div>
    <div id="__huayoung-selector-list__"></div>
  </div>
</div>
<div id="__huayoung-selector-control__"><button title="选择前一个元素" data-direction="prev">&#8592;</button><button title="选择上一个元素" data-direction="up">&#8593;</button><button title="选择下一个元素" data-direction="down">&#8595;</button><button title="选择后一个元素" data-direction="next">&#8594;</button></div>
<div class="__huayoung-action-row__">
  <button id="__huayoung-action-preview__" style="background-color: #52C41A">预览</button>
  <button id="__huayoung-action-submit__">确定</button>
  <button id="__huayoung-action-reset__" style="background-color: #fff; color: #333; border: 1px solid #ddd">重选</button>
</div>
</div>`;
    document.body.append(template.content.firstChild!);
    // 拖动
    const dragHandler = document.getElementById('__huayoung-selector-drag-handler__');
    dragHandler?.addEventListener('mousedown', (e) => {
      if (!e.target) return;
      // @ts-ignore
      const rect = e.target.getBoundingClientRect();
      const x = e.pageX - rect.x;
      const y = e.pageY - rect.y;
      // @ts-ignore
      const form = e.target.parentElement;
      function handleMouseMove(e: MouseEvent) {
        if (form) {
          form.style.right = 'auto';
          form.style.left = e.pageX - x + 'px';
          form.style.top = Math.max(0, e.pageY - y) + 'px';
        }
      }
      function removeListener() {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', removeListener);
      }
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', removeListener);
    });
    // list toggle
    const toggle = document.getElementById('__huayoung-selector-toggle__');
    toggle?.addEventListener('click', () => {
      const list = document.getElementById('__huayoung-selector-list__');
      if (list) {
        list.classList.toggle('show');
      }
    });
    // 控制器
    const controlElm = document.getElementById('__huayoung-selector-control__');
    controlElm?.querySelectorAll('button').forEach((arrowBtn) => {
      arrowBtn.addEventListener('click', this._handleControlBtnClick);
    });
    // 预览
    document.getElementById('__huayoung-action-preview__')?.addEventListener('click', () => {
      let selector = this._getSelector();
      if (selector) {
        if (this.mode === 'container') {
          selector += ' > *';
        }
        this.highlightSelector(selector);
        clearInterval(this.previewTimer);
        this.previewTimer = setInterval(() => {
          this.highlightSelector(selector);
          if (this.mode === 'container') {
            this.drawContainer(this.root.querySelector(this._getSelector()));
          }
        }, 1000);
      }
      this._countElements();
    });
    // 提交
    document.getElementById('__huayoung-action-submit__')?.addEventListener('click', () => {
      const selector = this._getSelector();
      if (selector) {
        // 将获取到的选择器发送给RPA流程编辑器
        // content -> background -> node.js -> rpa window
        chrome.runtime.sendMessage({ action: 'selector-finder-res', data: selector });
        this.stop();
      }
    });
    // 重置
    document.getElementById('__huayoung-action-reset__')?.addEventListener('click', () => {
      this.resume();
    });
  }

  /**
   * 清除蒙层
   */
  _cleanOverlay() {
    document.querySelectorAll(`.${overlayCls}`).forEach((overlay) => {
      overlay.remove();
    });
  }

  _getSelector() {
    const inputElm: HTMLInputElement | null = document.getElementById(
      '__huayoung-selector-input__',
    ) as HTMLInputElement;
    if (inputElm && inputElm.value.trim()) {
      return inputElm.value.trim();
    }
    return '';
  }

  _countElements() {
    const selector = this._getSelector();
    if (selector) {
      const descEl = document.getElementById('__huayoung-selector-desc__');
      if (descEl) {
        if (this.mode === 'container') {
          const count = this.root.querySelectorAll(selector + ' > *').length;
          descEl.innerText = `在该容器下共找到${count}个直接子元素`;
        } else {
          const count = this.root.querySelectorAll(selector).length;
          descEl.innerText = `共找到${count}个元素`;
        }
      }
    }
  }

  start(mode: Mode = 'single', containerSelector = '', parentMode = 'self') {
    if (!this.active) {
      this.mode = mode;
      // this._renderForm();
      if (mode === 'child' && containerSelector) {
        if (parentMode === 'parent') {
          this.containerSelector = containerSelector + ' > *';
        } else {
          this.containerSelector = containerSelector;
        }
        document.querySelector(`.${formCls}`)?.classList.remove('ghost');
        this.setActiveContainer(0);
        document
          .getElementById('__huayoung-container-picker__')
          ?.addEventListener('change', this._handleContainerIdxChange);
      } else {
        this.root.addEventListener('mouseover', this._handleMouseOver, true);
      }
    }
  }

  pause() {
    if (this.mode !== 'child') {
      document.querySelector(`.${formCls}`)?.classList.remove('ghost');
    }
    clearInterval(this.previewTimer);
    this._cleanOverlay();
    this.active = false;
    this.root.removeEventListener('mouseover', this._handleMouseOver, true);
    this.root.removeEventListener('click', this._handleOverlayClick, true);
  }

  resume() {
    if (this.mode !== 'child') {
      document.querySelector(`.${formCls}`)?.classList.add('ghost');
    }
    clearInterval(this.previewTimer);
    this._cleanOverlay();
    this.active = true;
    this.root.addEventListener('mouseover', this._handleMouseOver, true);
  }

  stop() {
    clearInterval(this.previewTimer);
    this._cleanOverlay();
    document.querySelector(`.${containerCls}`)?.remove();
    document.querySelector(`.${formCls}`)?.remove();
    this.active = false;
    this.root.removeEventListener('mouseover', this._handleMouseOver, true);
    this.root.removeEventListener('click', this._handleOverlayClick, true);
    document
      .getElementById('__huayoung-container-picker__')
      ?.removeEventListener('change', this._handleContainerIdxChange);
    this.root = document;
  }
}
