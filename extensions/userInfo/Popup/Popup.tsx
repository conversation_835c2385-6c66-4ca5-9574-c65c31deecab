import React, { useState, useEffect, useRef } from 'react';

import styles from './popup.less';
import I18N from './I18N';

const Popup: React.FC = () => {
  const [userInfo, setUserInfo] = useState({
    avatar: '',
    nickname: '',
    phone: '',
    signature: '',
    email: '',
    startTime: '',
    duration: '',
  });

  useEffect(() => {
    chrome.runtime.sendMessage({ action: 'getUserInfo' }, (response) => {
      setUserInfo(response.data);
    });
  }, []);

  return (
    <div>
      <div className={styles.header}>
        <img className="avatar" src={userInfo.avatar || ''} />
        <h1 className="nickname">{userInfo.nickname || '--'}</h1>
        <div className="remark">{userInfo.signature || I18N.t('此人很懒，什么都没留下')}</div>
      </div>
      <div className={styles.bottom}>
        <ul>
          <li>
            <span className={styles.label}>{I18N.t('手机')}</span>
            <span className="phone">{userInfo.phone || '--'}</span>
          </li>
          <li>
            <span className={styles.label}>{I18N.t('邮件')}</span>
            <span className="email">{userInfo.email || '--'}</span>
          </li>
          <li>
            <span className={styles.label}>{I18N.t('打开时间')}</span>
            <span className="startTime">{userInfo.startTime || '--'}</span>
          </li>
          <li>
            <span className={styles.label}>{I18N.t('持续时间')}</span>
            <span className="duration">{userInfo.duration || '--'}</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default Popup;
