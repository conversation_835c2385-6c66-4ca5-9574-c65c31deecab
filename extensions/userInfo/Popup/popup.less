// Color variables (appears count calculates by raw css)
@color0: #ffffff; // Appears 2 times
@color1: #d1d1d1; // Appears 2 times

// Width variables (appears count calculates by raw css)
@width1: 260px; // Appears 2 times


html {
  background-color: @color0;
  font-size: 14px;
  margin: 0;
}
body {
  background-color: @color0;
  font-size: 14px;
  margin: 0;
  margin: 0;
  width: 320px;
}
.header {
  align-items: center;
  background-color: #141a2a;
  color: white;
  display: flex;
  flex-direction: column;
  height: 166px;
  justify-content: center;
  :global {
    .avatar {
      background-color: @color1;
      border-radius: 50%;
      border: 2px solid white;
      height: 55px;
      width: 55px;
    }
    .nickname {
      font-size: 18px;
      margin: 10px;
      max-width: @width1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
    }
    .remark {
      color: #eeeeee;
      max-width: @width1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
    }
  }
}
.bottom {
  margin: 0 20px;
}
.bottom>ul {
  font-size: 14px;
  margin: 0;
  padding: 0;
}
.bottom>ul>li {
  border-bottom: 1px solid @color1;
  line-height: 46px;
  list-style: none;
  &:last-child {
    border-bottom: 0;
  }
}
.bottom>ul>li>span {
  &:first-child {
    margin-right: 10px;
  }
}
.label {
  color: #999999;
  display: inline-block;
  position: relative;
  width: 80px;
  &:after {
    content: ':';
    position: absolute;
    right: 0;
  }
}
