.__huayoung-selector-finder-overlay__ {
  position: fixed;
  display: none;
  z-index: 2147483646;
  opacity: 0.4;
  transition: all 0.1s ease 0s;
  border: 1px solid rgb(202, 0, 0) !important;
  background-color: rgb(202, 0, 0) !important;
  pointer-events: none;
  border-image: initial !important;
  transition: opacity 0.3s;
}

.__huayoung-selector-finder-container__ {
  position: fixed;
  display: none;
  z-index: 2147483646;
  border: 2px dashed rgb(202, 0, 0) !important;
  pointer-events: none;
  border-image: initial !important;
}

.__huayoung-selector-form__ {
  position: fixed;
  right: 10px;
  bottom: 10px;
  padding: 10px 10px 0;
  width: 370px;
  height: 220px;
  z-index: 2147483647;
  font-size: 12px !important;
  border-radius: 10px;
  color: white;
  background-color: rgba(15, 124, 244, 0.6);
  box-shadow: 0 3px 15px rgba(15, 124, 244, 0.6);
  backdrop-filter: blur(3px);
}
.__huayoung-selector-form__.ghost {
  pointer-events: none;
}

.__huayoung-selector-form__ .__huayoung-input-row__ {
  display: flex;
  align-items: flex-start;
  line-height: 30px;
}
.__huayoung-selector-form__ .__huayoung-input-row__ > span {
  flex: 0 0 auto;
}

.__huayoung-selector-form__ .__huayoung-input-row__ > div {
  position: relative;
  flex: 1;
}

.__huayoung-selector-input-wrap__ {
  display: flex;
  align-items: center;
}

#__huayoung-selector-input__ {
  flex: 1;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 3px 0 0 3px;
  outline: none;
  box-sizing: border-box;
  background: #fff;
  color: #333;
  padding: 0 5px;
}

#__huayoung-selector-toggle__ {
  position: relative;
  flex: 0 0 30px;
  height: 30px;
  box-sizing: border-box;
  border: 1px solid #ddd;
  border-left: 0;
  background: white;
  border-radius: 0 3px 3px 0;
  cursor: pointer;
}
#__huayoung-selector-toggle__::after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  left: 10px;
  top: 6px;
  border: 1px solid;
  border-color: transparent transparent #999 #999;
  transform-origin: center;
  transform: rotate(-45deg);
}

#__huayoung-selector-desc__ {

}

#__huayoung-selector-list__ {
  display: none;
  position: absolute;
  top: 32px;
  left: 0;
  right: 0;
  max-height: 130px;
  overflow: auto;
  text-align: left;
  border-radius: 0 0 5px 5px;
  background: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, .3);
  z-index: 1;
}
#__huayoung-selector-list__.show {
  display: block;
}
#__huayoung-selector-list__ > div {
  line-height: 30px;
  padding: 0 5px;
  border-bottom: 1px solid #ddd;
  color: #333;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
#__huayoung-selector-list__ > div:hover {
  background-color: #f4f4f4;
}
#__huayoung-selector-list__ > div:last-child {
  border-bottom: 0;
}

.__huayoung-container-pick-row__ {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
#__huayoung-container-picker-value__ > input {
  width: 70px !important;
  height: 30px !important;
  margin: 0 4px !important;
  line-height: 30px !important;
  border: 1px solid #ddd !important;
  border-radius: 3px !important;
  outline: none !important;
  box-sizing: border-box !important;
  background: #fff !important;
  color: #333 !important;
  padding: 0 0 0 5px !important;
}
#__huayoung-container-picker-value__ > input[type=number]::-webkit-inner-spin-button,
#__huayoung-container-picker-value__ > input[type=number]::-webkit-outer-spin-button {
  opacity: 1;
}

#__huayoung-selector-control__ {
  position: absolute;
  right: 10px;
  bottom: 58px;
  display: none;
  justify-content: flex-end;
  gap: 4px;
}
#__huayoung-selector-control__.visible {
  display: flex;
}
#__huayoung-selector-control__ > button {
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 30px !important;
  height: 30px !important;
  line-height: 30px !important;
  border: 0 !important;
  text-align: center !important;
  font-size: 14px !important;
  color: #333 !important;
  background: #fff !important;
  border-radius: 3px !important;
  cursor: pointer !important;
}

.__huayoung-selector-form__ .__huayoung-action-row__ {
  position: absolute;
  right: 10px;
  bottom: 20px;
  display: flex;
  gap: 4px;
}

.__huayoung-action-row__ > button {
  min-width: 65px !important;
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 10px !important;
  border: 0 !important;
  background: #0F7CF4 !important;
  color: white !important;
  text-align: center !important;
  font-size: 14px !important;
  border-radius: 3px !important;
  cursor: pointer !important;
}

#__huayoung-selector-drag-handler__ {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 30px;
  cursor: move;
}
