{"花漾客户端告警": "Huayang client alarm", "您当前设备的规格为：{{spec}}": "Your current equipment specifications are: {{spec}}", "根据不同的浏览器指纹的UA版本，会使用不同版本的花漾浏览器内核": "Depending on the UA version of different browser fingerprints, different versions of Huayang browser kernel will be used", "跟随IP的语言设置": "Follow the IP language settings", "打开浏览器间隔": "Open browser interval", "当批量打开多个浏览器时的间隔，默认最小值为2秒，最大值为8秒，可根据客户端设备的性能进行调整": "When opening multiple browsers in batches, the default minimum value is 2 seconds and the maximum value is 8 seconds, which can be adjusted based on the performance of the client device", "最小": "minimum", "最大": "largest", "海外IP加速": "Overseas IP acceleration", "花漾浏览器直接连接海外IP会面临连接速度慢、不稳定等状况，您可以设置是否使用系统代理连接海外IP，这样会加速海外IP的连接速度": "When Huayang browser connects directly to overseas IPs, the connection speed will be slow and unstable. You can set whether to use a system proxy to connect to overseas IPs, which will speed up the connection speed of overseas IPs.", "海外IP加速设置": "Overseas IP acceleration settings", "检测中": "detection", "正在检测指定的代理方式是否可用，请稍候...": "Please wait while testing whether the specified proxy method is available...", "代理地址可用": "Proxy address available", "延迟为{{delay}}ms": "The delay is {{delay}}ms", "代理地址不可用": "Proxy address is not available", "请检查代理地址是否正确": "Please check whether the agent address is correct", "检测代理地址时发生错误：{{error}}": "An error occurred while detecting the agent address: {{error}}", "使用系统代理": "Using system agents", "指定代理地址": "Specify proxy address", "代理服务地址": "Proxy service address", "代理服务地址格式为": "The proxy service address format is", "示例如下": "example is as follows", "请输入代理服务地址": "Please enter the agent service address", "当前设备": "current device", "备用线路": "backup line", "当无法用主线路访问花漾服务器时，可以启用备用线路": "When the Huayang server cannot be accessed using the primary line, you can enable the backup line", "浏览器内核": "browser kernel", "窗口群控": "Window group control", "Chrome内核已经不再维护Win7版本，强烈建议您升级至Win10/11 ": "The Chrome kernel no longer maintains Win7 versions, and you are strongly recommended to upgrade to Win10/11. ", "无法连接到服务器": "cannot connect to the server", "针对您自有的海外IP（含港澳台），可以设置加速通道以保证访问质量": "For your own overseas IP (including Hong Kong, Macao and Taiwan), you can set up acceleration channels to ensure access quality", "您需要自己获取出口IP": "You need to obtain your own export IP", "通过花漾客户端直连IP（再通过IP访问网站）": "Connect directly to IP via Huayang client (and then access the website via IP)", "当前客户端IP为：{{ip}}": "The current client IP is: {{ip}}", "用户自己的加速通道": "Users 'own acceleration channels", "直连海外IP会面临连接速度慢、不稳定等状况，您可以设置是否使用系统代理连接海外IP，这样会加速海外IP的连接速度": "Directly connecting overseas IP will face problems such as slow and unstable connection speed. You can set whether to use a system proxy to connect to overseas IP, which will speed up the connection speed of overseas IP", "花漾官方加速通道（{{text}}）": "Huayang official acceleration channel ({{text}})", "花漾官方加速通道": "Huayang official acceleration channel", "当前团队未开启花漾官方加速通道": "The current team has not opened Huayang official acceleration channel", "如需开启，请和在线客服联系": "If you need to open it, please contact online customer service", "如何开启": "how to open", "通过花漾官方加速通道访问您的IP，会根据实际产生的流量扣除花瓣": "Access your IP through Huayang official acceleration channel, and petals will be deducted based on the actual traffic generated", "在花漾中购买的平台 IP 无需设置加速通道": "Platform IPs purchased in Huayang do not need to set up acceleration channels", "设置全部自有IP的网络加速设置（注：此处并未展示当前IP的具体设置）": "Set the network acceleration settings for all your own IPs (Note: The specific settings for the current IP are not shown here)", "批量设置自有IP的网络加速，只会对自有IP生效（平台IP会忽略）": "Setting up network acceleration with your own IP in batches will only take effect on your own IP (platform IP will be ignored)", "在花漾中购买的平台 IP ，会由花漾保证其访问质量": "The platform IP purchased in Huayang will be guaranteed by Huayang", "针对您自有的国内 IP，一般采用直连优先即可": "For your own domestic IP, direct connection is generally preferred", "正在更新IP加速通道...": "Updating the IP acceleration channel...", "是否要更新全部自有IP地址的网络加速设置？": "Do you want to update the network acceleration settings for all your own IP addresses?", "系统会忽略所有平台IP，只会更新全部自有IP的网络加速设置": "The system will ignore all platform IPs and only update the network acceleration settings of all its own IPs", "是否要更新所选IP地址的网络加速设置？": "Do you want to update the network acceleration settings for the selected IP address?", "系统会忽略所有平台IP，只会更新自有IP的网络加速设置": "The system will ignore all platform IPs and only update the network acceleration settings of its own IP", "请选择拟使用的加速通道：": "Please select the acceleration channel you want to use:", "连接方式所属类别": "Category of connection method", "如果您的IP开启了白名单，请根据白名单允许的IP数量选择适合的加速通道，只有选中的加速通道才会访问你的IP": "If your IP is whitelisted, please select the appropriate acceleration channel based on the number of IPs allowed in the whitelist. Only the selected acceleration channel will access your IP", "用户自己的加速通道的出口IP": "Exit IP of user's own acceleration channel", "您当前客户端的出口IP": "The export IP of your current client", "如果您的IP服务商需要设置IP白名单，或者您希望指定具体使用哪些线路访问您的代理IP，请点击此处设置": "If your IP service provider needs to set up an IP whitelist, or if you want to specify which lines to use to access your agent IP, please click here to set up", "关闭白名单": "Close the white list", "用户自定义代理": "User-defined agent", "暂无可导入的云手机": "There are currently no cloud phones to import", "确定要重启此手机吗？": "Are you sure you want to restart this phone?", "重启后，手机将重新启动，正在运行的应用将会被关闭": "After restarting, the phone will restart and running apps will be closed", "重启指令下发成功": "Restart command issued successfully", "重启手机": "restart the phone", "确定要断开手机的网络连接吗？": "Are you sure you want to disconnect your phone from the Internet?", "如果您设置了代理，断开网络后将不再消耗代理流量，但依然可以操作手机": "If you set up an agent, you will no longer consume agent traffic after disconnecting the network, but you can still operate your phone", "断开网络": "disconnect the network", "确定要启用手机的网络连接吗？": "Are you sure you want to enable your phone's Internet connection?", "如果您设置了代理，连接网络后将消耗代理流量": "If you set up an agent, agent traffic will be consumed after connecting to the network", "启用网络": "enable network", "网络代理": "network proxy", "给云手机配置网络代理，注：目前仅支持某些指定类型的云手机": "Configure network agents for cloud phones. Note: Currently, only certain specified types of cloud phones are supported", "该手机不支持网络代理的配置": "This phone does not support network proxy configuration", "目前仅支持某些指定类型的云手机配置网络代理": "Currently, only certain specified types of cloud phones are supported to configure network agents", "快捷键：Ctrl + Enter": "shortcut key: Ctrl + <PERSON><PERSON>", "还原": "reduction", "翻译": "translation", "将手机剪贴板的内容复制到电脑剪贴板（仅限文本）": "Copy the contents of your mobile clipboard to your computer clipboard (text only)", "将电脑剪贴板的内容复制到手机剪贴板（仅限文本）": "Copy the contents of your computer clipboard to your mobile clipboard (text only)", "翻译设置": "translation setting", "默认行为": "default behavior", "提取手机内容并翻译": "Extract mobile phone content and translate", "仅提取手机内容": "Extract only mobile content", "翻译引擎": "translation engine", "确定要结束此流程吗？": "Are you sure you want to end this process?", "流程一旦结束不可恢复，除非重新执行": "Once the process ends, it cannot be restored unless it is re-executed", "自动记录窗口的大小和位置 ": "Automatically records window size and position ", "获取Cookie": "Get cookies", "图像理解": "image Understanding", "写入Cookie": "Write a <PERSON>ie", "调用AI能力对图片进行分析，输出结果保存至变量；每次50花瓣": "Call AI capabilities to analyze pictures, and save the output results to variables; 50 petals each time", "提示词": "prompt words", "全部Cookie": "all cookies", "当前页Cookie": "Current page cookies", "获取{{type}}至": "Get {{type}} to", "获取范围": "acquisition range", "可在控制台中输出您期望的内容，日志级别分别是：Debug（详尽）、Info（提示）、Error（简要）和控制台输出；等价于console.log、console.info、console.error、console.print、console.printError": "You can output what you expect in the console. The log levels are: Debug (exhaustive), Info (prompt), Error (summary), and console output; equivalent to console.log, console.info, console.error, console.print, and console.printError", "未输入Cookie": "No <PERSON><PERSON> entered", "请输入 Cookie 列表，格式为 JSON 数组": "Please enter a list of cookies in a JSON array format", "Cookie 结构参考": "Cookie structure reference", "Cookie列表": "Cookie list", "请输入Cookie列表": "Please enter a list of cookies", "{{name}}流程已触发": "{{name}} process has been triggered", "是否立即同步当前店铺的商品列表": "Do you want to sync the product list of the current store immediately", "系统会触发一个自动化流程，帮您执行同步操作，您可在同步完成后，重新选择商品": "The system will trigger an automated process to help you perform synchronization operations. After the synchronization is completed, you can select products again", "立即同步": "synchronize now", "同步店铺商品": "Sync store products", "分身转让失败，请检查下列信息": "Transfer of avatar failed. Please check the following information", "请设定策略": "Please set a strategy", "一次最多打开60个分身": "Open up to 60 clones at a time", "团队其它设置": "Other team settings", "修改失败：": "Modification failed:", "全部跟随IP的语言设置": "All language settings that follow IP", "启用备用线路": "Enable backup line", "邀请链接已失效": "The invitation link has expired", "请和邀请您加入团队的小伙伴联系，让他重新生成新的邀请链接": "Please contact the partner who invited you to join the team and ask him to create a new invitation link"}