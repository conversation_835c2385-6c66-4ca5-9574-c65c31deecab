import DMFormItem from '@/components/Common/DMFormItem';
import IconFontIcon from '@/components/Common/IconFontIcon';
import I18N from '@/i18n';
import { Checkbox, Col, Form, InputNumber, Row, Space, Tooltip, Typography } from 'antd';
import type { FlowForm } from '@/pages/RunFlow/utils/types';
import { forwardRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal'; // tk_shop 同步店铺信息

// tk_shop 同步店铺信息
const SyncShopInfo: FlowForm = forwardRef(() => {
  const [orderModalVisible, setOrderModalVisible] = useState(false);
  const [sampleApplyModalVisible, setSampleApplyModalVisible] = useState(false);

  return (
    <div style={{ paddingTop: 24, minWidth: 'max-content' }}>
      <DMFormItem
        validateFirst
        name={'actions'}
        rules={[
          {
            required: true,
            type: 'array',
            message: I18N.t('请指定工作内容'),
          },
        ]}
        initialValue={[
          'need_sync_product',
          'need_sync_order',
          'need_sync_sample_apply',
          'need_sync_creator',
          'need_sync_target_plan',
        ]}
      >
        <Checkbox.Group>
          <Space direction={'vertical'} size={8}>
            <Checkbox value={'need_sync_product'} style={{ lineHeight: '32px' }}>
              <span>{I18N.t('商品同步')}</span>
              <Tooltip
                title={I18N.t('抓取并保存店铺所售商品信息，方便用户在执行达人邀约时选择商品')}
              >
                <Typography.Link style={{ marginLeft: 32 }}>
                  <IconFontIcon iconName={'bangzhu_24'} />
                </Typography.Link>
              </Tooltip>
            </Checkbox>
            <Checkbox value={'need_sync_order'} style={{ lineHeight: '32px' }}>
              <span>{I18N.t('订单同步')}</span>
              <a
                style={{ marginLeft: 20 }}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  setOrderModalVisible(true);
                }}
              >
                {I18N.t('高级')}
              </a>
              <Tooltip
                title={I18N.t(
                  '抓取店铺所有订单（含买家信息），将买家保存至团队买家库，完成买家的私域管理',
                )}
              >
                <Typography.Link style={{ marginLeft: 32 }}>
                  <IconFontIcon iconName={'bangzhu_24'} />
                </Typography.Link>
              </Tooltip>
            </Checkbox>
            <Checkbox value={'need_sync_sample_apply'} style={{ lineHeight: '32px' }}>
              <span>{I18N.t('索样记录同步')}</span>
              <a
                style={{ marginLeft: 20 }}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  setSampleApplyModalVisible(true);
                }}
              >
                {I18N.t('高级')}
              </a>
              <Tooltip
                title={I18N.t(
                  '抓取店铺所有索样达人，保存至团队达人库并形成沟通记录，既方便用户在私域系统中进行索样审批，也完成达人的私域管理',
                )}
              >
                <Typography.Link style={{ marginLeft: 32 }}>
                  <IconFontIcon iconName={'bangzhu_24'} />
                </Typography.Link>
              </Tooltip>
            </Checkbox>
            <Checkbox value={'need_sync_creator'} style={{ lineHeight: '32px' }}>
              <span>{I18N.t('达人带货信息同步')}</span>
              <Tooltip
                title={I18N.t(
                  '抓取达人最近28天的带货信息，抓取后的数据保存至团队达人库，完成达人的私域管理',
                )}
              >
                <Typography.Link style={{ marginLeft: 32 }}>
                  <IconFontIcon iconName={'bangzhu_24'} />
                </Typography.Link>
              </Tooltip>
            </Checkbox>
            <Checkbox value={'need_sync_target_plan'} style={{ lineHeight: '32px' }}>
              <span>{I18N.t('达人定向邀约计划同步')}</span>
              <Tooltip
                title={I18N.t('抓取店铺所有的定向邀约计划，方便检查筛选后的达人是否能够被邀约')}
              >
                <Typography.Link style={{ marginLeft: 32 }}>
                  <IconFontIcon iconName={'bangzhu_24'} />
                </Typography.Link>
              </Tooltip>
            </Checkbox>
          </Space>
        </Checkbox.Group>
      </DMFormItem>
      <DMModal
        title={I18N.t('高级设置')}
        visible={orderModalVisible}
        forceRender
        onOk={() => {
          setOrderModalVisible(false);
        }}
        onCancel={() => {
          setOrderModalVisible(false);
        }}
      >
        <DMFormItem name="need_buyer_contact_info" valuePropName="checked" initialValue={true}>
          <Checkbox>
            <span>{I18N.t('是否抓取买家的联系方式（如电话号码）')}</span>
            <Tooltip
              title={I18N.t(
                '抓取达人最近28天的带货信息，抓取后的数据保存至团队达人库，完成达人的私域管理',
              )}
            >
              <Typography.Link style={{ marginLeft: 32 }}>
                <IconFontIcon iconName={'bangzhu_24'} />
              </Typography.Link>
            </Tooltip>
          </Checkbox>
        </DMFormItem>
        <DMFormItem shouldUpdate>
          {(f) => {
            const disabled = !f.getFieldValue('need_buyer_contact_info');
            return (
              <Row align="middle" gutter={0}>
                <Col style={{ paddingLeft: 24 }}>{I18N.t('每次抓取数量')}：</Col>
                <Col>
                  <DMFormItem
                    name="query_buyer_contact_info_sum"
                    initialValue={10}
                    rules={[
                      { required: true, message: '请输入抓取数量' },
                      { min: 1, max: 1000, message: '抓取数量在 1 ~ 1000 区间', type: 'number' },
                    ]}
                    noStyle
                  >
                    <InputNumber disabled={disabled} min={0} max={1000} />
                  </DMFormItem>
                </Col>
                <Col style={{ paddingLeft: 4 }}>{I18N.t('个买家')}</Col>
              </Row>
            );
          }}
        </DMFormItem>
        <DMFormItem
          name="need_re_query_affiliate_order"
          valuePropName="checked"
          initialValue={true}
        >
          <Checkbox>{I18N.t('对缺少带货来源的联盟订单进行二次查询')}</Checkbox>
        </DMFormItem>
        <DMFormItem shouldUpdate>
          {(f) => {
            const disabled = !f.getFieldValue('need_re_query_affiliate_order');
            return (
              <Row align="middle" gutter={0}>
                <Col style={{ paddingLeft: 24 }}>{I18N.t('每次查询数量')}：</Col>
                <Col>
                  <DMFormItem
                    name="re_query_affiliate_order_sum"
                    initialValue={200}
                    rules={[
                      { required: true, message: '请输入查询数量' },
                      { min: 1, max: 1000, message: '查询数量在 1 ~ 1000 区间', type: 'number' },
                    ]}
                    noStyle
                  >
                    <InputNumber disabled={disabled} min={1} max={1000} />
                  </DMFormItem>
                </Col>
                <Col style={{ paddingLeft: 4 }}>{I18N.t('笔订单')}</Col>
              </Row>
            );
          }}
        </DMFormItem>
      </DMModal>
      <DMModal
        title={I18N.t('高级设置')}
        visible={sampleApplyModalVisible}
        forceRender
        onOk={() => {
          setSampleApplyModalVisible(false);
        }}
        onCancel={() => {
          setSampleApplyModalVisible(false);
        }}
      >
        <DMFormItem
          name="need_query_sample_request_creator"
          valuePropName="checked"
          initialValue={true}
        >
          <Checkbox>自动更新符合下述条件的索样达人的基础信息</Checkbox>
        </DMFormItem>
        <DMFormItem shouldUpdate>
          {(f) => {
            const disabled = !f.getFieldValue('need_query_sample_request_creator');
            return (
              <Row align="middle" gutter={0}>
                <Col style={{ paddingLeft: 24 }}>基础信息更新时间早于</Col>
                <Col style={{ paddingLeft: 4 }}>
                  <DMFormItem
                    name="query_sample_request_creator_in_days"
                    initialValue={30}
                    rules={[{ required: true, message: '请输入查询条件' }]}
                    noStyle
                  >
                    <InputNumber min={1} max={365} disabled={disabled} />
                  </DMFormItem>
                </Col>
                <Col style={{ paddingLeft: 4 }}>天的达人</Col>
              </Row>
            );
          }}
        </DMFormItem>
        <DMFormItem name="need_query_contents_in_days" valuePropName="checked" initialValue={true}>
          <Checkbox>查询符合下述条件的、已完成的索样记录的视频/直播的产出详情</Checkbox>
        </DMFormItem>
        <DMFormItem shouldUpdate>
          {(f) => {
            const disabled = !f.getFieldValue('need_query_contents_in_days');
            return (
              <Row align="middle" gutter={0}>
                <Col style={{ paddingLeft: 24 }}>查询最近：</Col>
                <Col>
                  <DMFormItem
                    name="query_contents_in_days"
                    initialValue={30}
                    rules={[{ required: true, message: '请输入查询条件' }]}
                    noStyle
                  >
                    <InputNumber min={1} max={365} disabled={disabled} />
                  </DMFormItem>
                </Col>
                <Col style={{ paddingLeft: 4 }}>天创建的索样记录</Col>
              </Row>
            );
          }}
        </DMFormItem>
      </DMModal>
    </div>
  );
});
export default SyncShopInfo;
