import { Checkbox, Col, Input, InputNumber, Radio, Row, Space } from 'antd';
import I18N from '@/i18n/I18N';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import type { FlowForm } from '@/pages/RunFlow/utils/types';
import { forwardRef } from 'react';
import moment from 'moment/moment';

const CollectCreators: FlowForm = forwardRef(() => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0,
        height: '100%',
        paddingTop: 24,
        minWidth: 'max-content',
      }}
    >
      <DMFormItemContext.Provider value={{ labelWidth: 120, disableLabelMuted: true }}>
        <DMFormItem
          name="fetchCount"
          label={<span style={{ whiteSpace: 'nowrap' }}>{I18N.t('拟抓取的达人数量')}</span>}
          initialValue={50}
          rules={[
            { required: true, message: '请输入拟抓取的达人数量' },
            { min: 1, max: 1000, message: '达人数量在 1 ~ 1000 区间', type: 'number' },
          ]}
        >
          <InputNumber min={1} max={1000} />
        </DMFormItem>
        <DMFormItem name="fetchContact" initialValue="default">
          <Radio.Group style={{ overflow: 'hidden' }}>
            <Space direction="vertical" size={24}>
              <Radio value="default">
                {I18N.t('只有当所有联系方式都为空时才会查询并更新联系方式')}
              </Radio>
              <Radio value="force">{I18N.t('强制更新联系方式')}</Radio>
              <Radio value="ignore">{I18N.t('不更新联系方式')}</Radio>
            </Space>
          </Radio.Group>
        </DMFormItem>
        <Row wrap={false}>
          <Col>
            <DMFormItem name="needTag" valuePropName="checked" initialValue={true}>
              <Checkbox>{I18N.t('给抓取的达人打上标签')}</Checkbox>
            </DMFormItem>
          </Col>
          <Col>
            <DMFormItem shouldUpdate>
              {(f) => {
                return (
                  <DMFormItem
                    name="tagName"
                    initialValue={`抓取达人_${moment().format('MMDD')}`}
                    rules={[
                      () => ({
                        validator: (rule, value) => {
                          if (f.getFieldValue('needTag') && !value) {
                            return Promise.reject(I18N.t('请输入标签名称'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <Input disabled={!f.getFieldValue('needTag')} />
                  </DMFormItem>
                );
              }}
            </DMFormItem>
          </Col>
        </Row>
      </DMFormItemContext.Provider>
    </div>
  );
});
export default CollectCreators;
