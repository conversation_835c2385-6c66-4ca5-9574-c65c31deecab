const path = require('path');
const webpack = require('webpack');
const nodeExternals = require('webpack-node-externals');
const CopyWebpackPlugin = require('copy-webpack-plugin');

const isDevelopment = process.env.NODE_ENV === 'development';
const NODE_ENV = isDevelopment ? 'development' : 'production';
const PORTAL_URL = process.env.PORTAL_URL || 'http://localhost:8000';
const API_URL = process.env.API_URL || 'http://localhost:8000';
const DL_URL = process.env.DL_URL || 'http://localhost:8000';
const PORTAL_URL2 = process.env.PORTAL_URL2 || 'http://localhost:8000';
const API_URL2 = process.env.API_URL2 || 'http://localhost:8000';
const DL_URL2 = process.env.DL_URL2 || 'http://localhost:8000';
const WX_URL = process.env.WX_URL || 'https://dev.thinkoncloud.cn';

let config = {
  mode: NODE_ENV,
  target: ['electron-main', 'electron-preload'],
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true,
            },
          },
        ],
        exclude: /node_modules/,
      },
      { test: /\.node$/, use: 'node-loader' },
    ],
  },
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@e': path.resolve(__dirname, 'electron/src/'),
    },
  },
  externals: [nodeExternals()],
  optimization: {
    moduleIds: 'named',
  },
  devtool: isDevelopment ? 'eval-source-map' : false,
};

let mainConfig = Object.assign({}, config, {
  entry: {
    main: './electron/src/main.ts',
    preload: './electron/src/preload.ts',
  },
  output: {
    filename: '[name].js',
    path: path.resolve(__dirname, './electron'),
  },
  plugins: [
    new webpack.EnvironmentPlugin({
      PLATFORM: process.env.PLATFORM || 'Win10',
      NODE_ENV: NODE_ENV,
      PORTAL_URL: PORTAL_URL,
      API_URL: API_URL,
      PORTAL_URL2: PORTAL_URL2,
      API_URL2: API_URL2,
      WX_URL: WX_URL,
      RELEASE_APP_VERSION: process.env.RELEASE_APP_VERSION || '11.2.9999',
      BUILD_NUMBER: process.env.BUILD_NUMBER || '9999',
      RUN_MODE: process.env.RUN_MODE || 'normal',
      LANG: process.env.LANG || 'zh',
      EXT_RPA_NAME: process.env.EXT_RPA_NAME,
      EXT_AI_AGENT_NAME: process.env.EXT_AI_AGENT_NAME,
      UPDATE_URL: process.env.UPDATE_URL || 'https://dl.szdamai.com/downloads/win10_app_zh/',
      BUNDLE_KERNEL_VERSION: process.env.BUNDLE_KERNEL_VERSION || '134',
      BUNDLE_KERNEL_BUILD_NUMBER: process.env.BUNDLE_KERNEL_BUILD_NUMBER || '13401',
      OEM_NAME: process.env.OEM_NAME || 'default',
      PROTOCOL_CODE: process.env.PROTOCOL_CODE || 'huayoung',
    }),
  ],
});

let helperConfig = Object.assign({}, config, {
  entry: {
    recorder_helper: './electron/src/recorder/helper/preload.ts',
    mobile_helper: './electron/src/mobile/helper/preload.ts',
    captcha_helper: './electron/src/rpa/helper/captcha.ts',
    ocr_helper: './electron/src/rpa/helper/ocr.ts',
  },
  output: {
    filename: '[name].js',
    path: path.resolve(__dirname, './electron/helper/'),
  },
  plugins: [
    new webpack.EnvironmentPlugin({
      NODE_ENV: NODE_ENV,
      PORTAL_URL: PORTAL_URL,
      API_URL: API_URL,
      DL_URL: DL_URL,
      PORTAL_URL2: PORTAL_URL2,
      API_URL2: API_URL2,
      DL_URL2: DL_URL2,
      WX_URL: WX_URL,
      RELEASE_APP_VERSION: process.env.RELEASE_APP_VERSION || '8.0.0',
      BUILD_NUMBER: process.env.BUILD_NUMBER || '9999',
      RUN_MODE: process.env.RUN_MODE || 'normal',
      LANG: process.env.LANG || 'zh',
    }),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: './electron/html/helper.html',
          to: path.resolve(__dirname, './electron/helper/index.html'),
        },
      ],
    }),
  ],
});

module.exports = [mainConfig, helperConfig];
