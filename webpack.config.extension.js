const path = require('path');
const fs = require('fs-extra');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const WextManifestWebpackPlugin = require('wext-manifest-webpack-plugin');

// 指定构建 chrome 扩展程序
process.env.TARGET_BROWSER = 'chrome';
const extensionName = process.env.EXT_NAME || '';
const nodeEnv = process.env.NODE_ENV || 'production';
const isDevelopment = nodeEnv === 'development';
const author = process.env.AUTHOR || '';

const basePath = path.resolve(__dirname, 'extensions');
const sourcePath = path.join(basePath, extensionName.replace(/-\d+$/, ''));
const viewsPath = path.join(basePath, 'views');
const destPath = path.join(__dirname, 'extensions-dist', extensionName);

let manifests = [];
function findManifestFiles(dir) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      // 递归处理子目录
      findManifestFiles(fullPath);
    } else if (entry.isFile() && entry.name === 'manifest.json') {
      manifests.push(fullPath);
    }
  }
}

if (author) {
  // OEM
  findManifestFiles(path.join(__dirname, 'extensions'));
  console.log(manifests);
  manifests.forEach((manifestPath) => {
    const text = fs.readFileSync(manifestPath, 'utf8');
    fs.writeFileSync(manifestPath, text.replace(/深圳市云上悦动科技有限公司/g, author));
  });
}

const checkEntry = (dir, file) => {
  if (fs.existsSync(path.join(sourcePath, dir, file))) {
    return path.join(sourcePath, dir, file);
  }
  return false;
};

const entry = {
  manifest: path.join(sourcePath, 'manifest.json'),
  background: checkEntry('Background', 'index.ts'),
  popup: checkEntry('Popup', 'index.tsx'),
  contentScript: checkEntry('ContentScript', 'index.ts'),
  fingerprint: checkEntry('ContentScript', 'fingerprint.ts'),
  options: checkEntry('Options', 'index.tsx'),
  sidePanel: checkEntry('SidePanel', 'index.tsx'),
  hotReload: isDevelopment ? path.join(basePath, 'hot-reload.js') : false,
};
for (const k in entry) {
  if (!entry[k]) {
    delete entry[k];
  }
}

const pages = [];
const manifestStr = fs.readFileSync(path.join(sourcePath, 'manifest.json'), 'utf8');
if (manifestStr.includes('popup.html')) {
  pages.push(
    new HtmlWebpackPlugin({
      template: path.join(viewsPath, 'base.html'),
      inject: 'body',
      chunks: ['popup', isDevelopment ? 'hotReload' : ''],
      hash: true,
      filename: 'popup.html',
    }),
  );
}
if (manifestStr.includes('options.html')) {
  pages.push(
    new HtmlWebpackPlugin({
      template: path.join(viewsPath, 'base.html'),
      inject: 'body',
      chunks: ['options', isDevelopment ? 'hotReload' : ''],
      hash: true,
      filename: 'options.html',
    }),
  );
}
if (manifestStr.includes('background.html')) {
  pages.push(
    new HtmlWebpackPlugin({
      template: path.join(viewsPath, 'background.html'),
      inject: 'body',
      chunks: ['background', isDevelopment ? 'hotReload' : ''],
      hash: true,
      filename: 'background.html',
    }),
  );
}
if (manifestStr.includes('sidepanel.html')) {
  pages.push(
    new HtmlWebpackPlugin({
      template: path.join(viewsPath, 'base.html'),
      inject: 'body',
      chunks: ['sidePanel', isDevelopment ? 'hotReload' : ''],
      hash: true,
      filename: 'sidepanel.html',
    }),
  );
}

const plugins = [
  // Plugin to not generate js bundle for manifest entry
  new WextManifestWebpackPlugin(),
  // delete previous build files
  new CleanWebpackPlugin({
    dry: true,
    cleanOnceBeforeBuildPatterns: [destPath],
    cleanStaleWebpackAssets: false,
    verbose: true,
  }),
  ...pages,
  // write css file(s) to build folder
  new MiniCssExtractPlugin({ filename: 'css/[name].css' }),
  // copy static assets
  new CopyWebpackPlugin({
    patterns: [
      { from: path.join(sourcePath, 'assets'), to: 'assets' },
      { from: path.join(sourcePath, '_locales'), to: '_locales' },
    ],
  }),
];
if (checkEntry('raw', '')) {
  plugins.push(
    new CopyWebpackPlugin({
      patterns: [{ from: path.join(sourcePath, 'raw'), to: '' }],
    }),
  );
}
if (isDevelopment) {
  // Generate sourcemaps
  plugins.push(new webpack.SourceMapDevToolPlugin({ filename: false }));
}

module.exports = {
  devtool: isDevelopment ? 'source-map' : false,

  stats: {
    all: false,
    builtAt: true,
    errors: true,
    hash: true,
  },

  mode: nodeEnv,

  entry,

  output: {
    path: destPath,
    filename: 'js/[name].bundle.js',
  },

  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.json'],
    alias: {},
  },

  module: {
    rules: [
      {
        type: 'javascript/auto', // prevent webpack handling json with its own loaders,
        test: /manifest\.json$/,
        use: {
          loader: 'wext-manifest-loader',
          options: {
            usePackageJSONVersion: false, // set to false to not use package.json version for manifest
          },
        },
        exclude: /node_modules/,
      },
      {
        test: /\.(js|ts)x?$/,
        loader: 'babel-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader, // It creates a CSS file per JS file which contains CSS
          },
          {
            loader: 'css-loader', // Takes the CSS files and returns the CSS with imports and url(...) for Webpack
          },
          'resolve-url-loader', // Rewrites relative paths in url() statements
        ],
      },
      {
        test: /\.less$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader, // It creates a CSS file per JS file which contains CSS
          },
          {
            loader: 'css-loader', // Takes the CSS files and returns the CSS with imports and url(...) for Webpack
            options: {
              sourceMap: true,
              modules: {
                exportLocalsConvention: 'camelCase',
                localIdentName: '[name]__[local]--[hash:base64:5]',
              },
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  [
                    'autoprefixer',
                    {
                      // Options
                    },
                  ],
                ],
              },
            },
          },
          'resolve-url-loader', // Rewrites relative paths in url() statements
          'less-loader', // Takes the Sass/SCSS file and compiles to the CSS
        ],
      },
    ],
  },

  plugins: plugins,

  optimization: {
    minimize: true,
  },
};
