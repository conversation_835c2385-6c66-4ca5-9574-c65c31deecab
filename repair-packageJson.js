const fs = require('fs');
const path = require('path');

const isWin7 = process.env.PLATFORM_Win7 === 'true';
const rootPkgJson = JSON.parse(fs.readFileSync(path.resolve('./package.json'), 'utf8'));
const electronPkgJson = JSON.parse(
  fs.readFileSync(path.resolve('./electron/package.json'), 'utf8'),
);
// win7 的 puppeteer-core 版本需要指定旧版本
const puppeteerCoreVersion = isWin7 ? '20.9.3' : '21.7.2';
if (isWin7) {
  rootPkgJson.devDependencies['donkey-puppeteer-core'] = puppeteerCoreVersion;
  electronPkgJson.dependencies['donkey-puppeteer-core'] = puppeteerCoreVersion;
} else {
  rootPkgJson.devDependencies['donkey-puppeteer-core'] = puppeteerCoreVersion;
  electronPkgJson.dependencies['donkey-puppeteer-core'] = puppeteerCoreVersion;
}
console.log('electron/package.json data：');
console.log(JSON.stringify(electronPkgJson, null, 2));
fs.writeFileSync(path.resolve('./package.json'), JSON.stringify(rootPkgJson, null, 2));
fs.writeFileSync(path.resolve('./electron/package.json'), JSON.stringify(electronPkgJson, null, 2));
