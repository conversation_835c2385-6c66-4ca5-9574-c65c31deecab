/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
:root {
  --background-color-error: #F7DDE1;
  --background-color-secondary: #f7f7f7;
  --background-color-primary: #fff;
  --background-color-ternary: #edf9ff;
  --border-color-secondary: #d2d2d2;
  --border-color-primary: #e6e6e6;
  --border-color-ternary: #c0e6f9;
  --border-color-outline: #acacac;
  --border-radius: 4px;
  --border-radius-primary: 6px;
  --border-style-primary: solid;
  --border-width-thick: 4px;
  --border-width-thin: 1px;
  --box-shadow-primary: 0 2px 4px 0 hsla(0, 0%, 84%, 0.5);
  --color-brand-primary: #ED1E45;
  --color-primary: #585858;
  --color-secondary: #000;
  --color-dimmed: #4A4A4A;
  --color-critical: var(--color-brand-primary);
  --color-default: #FF8F00;
  --color-error: var(--color-brand-primary);
  --color-link: #0797E1;
  --color-info: #0797E1;
  --color-tag: #07E1CF;
  --font-size-big: 17px;
  --font-size-medium: 16px;
  --font-size-primary: 13px;
  --font-size-small: 12px;
  --margin-primary: 16px;
  --margin-secondary: calc(var(--margin-primary) / 2);
  --padding-primary: 16px;
  --padding-secondary: calc(var(--padding-primary) / 2);
  --primary-outline: var(--border-color-outline) dotted 1px; }

/*******************************************************************************
 * General
 ******************************************************************************/
:root {
  --color-content: #FFF;
  --color-footer: #000;
  --color-highlight: var(--color-brand-primary);
  --color-primary: #0797E1;
  --color-primary-hover: #0789CA;
  --color-secondary: #FAFAFA;
  --margin: 60px;
  font-size: 16px; }

body {
  margin: 0; }

a {
  color: var(--color-link); }

a.button {
  display: inline-block;
  padding: 1em 2.5em;
  border: 3px solid var(--color-content);
  border-radius: 0.5em;
  font-weight: 600;
  color: var(--color-content);
  background-color: var(--color-primary);
  text-transform: uppercase;
  text-decoration: none;
  transition: all 200ms; }

a.button:hover {
  background-color: var(--color-primary-hover); }

a.button.secondary {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background-color: var(--color-content); }

a.button.secondary:hover {
  border-color: var(--color-primary-hover);
  color: var(--color-content);
  background-color: var(--color-primary-hover); }

h1,
h2 {
  margin: 0 0 1rem; }

h1 {
  font-size: 60px;
  line-height: 70px; }

h2 {
  font-size: 30px;
  line-height: 40px; }

h2.icon::before {
  display: inline-block;
  width: 23px;
  height: 23px;
  vertical-align: middle;
  background-size: 100%;
  content: ""; }

html[dir="ltr"] h2.icon::before {
  margin-right: 10px; }

html[dir="rtl"] h2.icon::before {
  margin-left: 10px; }

.block-icon {
  --icon-width: 40px;
  position: relative; }

html[dir="ltr"] .block-icon {
  padding-left: calc(var(--icon-width) + 20px); }

html[dir="rtl"] .block-icon {
  padding-right: calc(var(--icon-width) + 20px); }

.block-icon::before {
  position: absolute;
  width: var(--icon-width);
  height: var(--icon-width);
  background-size: 100%;
  background-repeat: no-repeat;
  content: ""; }

html[dir="ltr"] .block-icon::before {
  left: 0; }

html[dir="rtl"] .block-icon::before {
  right: 0; }

/*******************************************************************************
 * Header
 ******************************************************************************/
header p {
  margin: 1.5em 0;
  color: #5A5A5A; }

header .block-icon {
  --icon-width: 50px; }

#logo {
  width: 200px;
  margin-bottom: 70px; }

#hero {
  flex: 0; }

html[dir="ltr"] #hero {
  margin-left: 40px; }

html[dir="rtl"] #hero {
  margin-right: 40px; }

/*******************************************************************************
 * Footer
 ******************************************************************************/
footer {
  padding: 30px;
  font-size: 0.8rem;
  text-align: center;
  color: var(--color-content);
  background-color: var(--color-footer); }

#copyright-notice a {
  font-weight: 600;
  text-decoration: none;
  color: inherit; }

#copyright-notice a:hover {
  text-decoration: underline; }

/*******************************************************************************
 * Content
 ******************************************************************************/
.content {
  position: relative;
  box-sizing: border-box;
  max-width: 940px;
  margin: auto;
  padding: var(--margin) 0 50px; }

.content:not(:first-child) {
  padding-top: 0; }

.content.columns {
  display: flex; }

.content p {
  margin-top: 0;
  line-height: 26px; }

.content.columns > div {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: flex-start;
  justify-content: center; }

.content.columns > div:last-child {
  align-items: flex-end; }

@media (max-width: 960px) {
  .content.columns {
    flex-direction: column; }
  .content.columns > div:last-child {
    margin-top: 1em;
    align-items: flex-start; } }

/*******************************************************************************
 * Sections
 ******************************************************************************/
article {
  position: relative; }

article.primary,
article:last-child {
  margin-bottom: var(--margin); }

article.primary::after,
article.secondary::before,
article:last-child::after {
  position: absolute;
  width: 100%;
  height: var(--margin);
  content: ""; }

article.primary::after,
article:last-child::after {
  top: 100%; }

article.secondary::before {
  top: calc(var(--margin) * -1); }

:not(.columns) > section:not(:first-of-type) {
  margin-top: 75px; }

article.primary {
  margin-top: calc(100px + var(--margin));
  color: var(--color-content);
  background-color: var(--color-primary); }

article.primary::after {
  background-image: linear-gradient(to bottom right, var(--color-primary) 49%, var(--color-content) 50%); }

article.secondary {
  background-color: var(--color-secondary); }

article.secondary::before {
  background-image: linear-gradient(to bottom right, var(--color-content) 49%, var(--color-secondary) 50%); }

article:last-child::after {
  background-image: linear-gradient(to bottom right, var(--color-content) 49%, var(--color-footer) 50%); }

/*******************************************************************************
 * Social
 ******************************************************************************/
#social .content {
  max-width: 230px;
  text-align: center; }

#social p {
  color: #5A5A5A; }

#social a {
  display: inline-block;
  width: 3em;
  height: 3em;
  margin: 1em 10px 0;
  border-radius: 1.5em;
  background-color: #000;
  background-position: center;
  background-repeat: no-repeat; }

#social a.email {
  background-image: url(icons/email-white.svg); }

#social a.facebook {
  background-image: url(icons/facebook-white.svg); }

#social a.twitter {
  background-image: url(icons/twitter-white.svg); }

/*******************************************************************************
 * Header
 ******************************************************************************/
#title::before {
  margin-top: 15px; }

#title.block-icon::before {
  background-image: url(icons/warning.svg); }

.note {
  padding: 1em;
  border: 1px solid #FFC478;
  border-radius: 6px;
  font-size: 0.9em;
  color: #000;
  background-color: #FFF1DF; }

/*******************************************************************************
 * Content
 ******************************************************************************/
#solution > .block-icon::before {
  background-image: url(icons/checkmark-green.svg); }

#solution ol {
  position: relative;
  padding: 0;
  margin-top: 2em;
  color: #5A5A5A; }

#solution ol::before {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 3px;
  height: auto;
  background-color: #E8E8E8;
  content: ""; }

html[dir="ltr"] #solution ol::before {
  left: -2.5em; }

html[dir="rtl"] #solution ol::before {
  right: -2.5em; }

#solution li {
  margin: 0 -0.5em;
  padding: 0 0.5em; }

#solution li:not(:last-child) {
  margin-bottom: 1.5em; }

#solution a {
  font-weight: 700; }

#solution :not(li) > a {
  font-size: 30px; }

#solution a::after {
  display: inline-block;
  width: 0.7em;
  height: 0.7em;
  vertical-align: middle;
  background-image: url(icons/open-link.svg);
  background-size: contain;
  background-repeat: no-repeat;
  content: ""; }

html[dir="ltr"] #solution a::after {
  margin-left: 0.3em; }

html[dir="rtl"] #solution a::after {
  margin-right: 0.3em;
  transform: scaleX(-1); }

#solution .os {
  font-style: normal; }

#solution .os::after {
  display: inline-block;
  width: 0.8em;
  height: 0.8em;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  content: ""; }

html[dir="ltr"] #solution .os::after {
  margin-left: 0.3em; }

html[dir="rtl"] #solution .os::after {
  margin-right: 0.3em; }

#solution .os.mac::after {
  background-image: url(icons/mac.svg); }

#solution .os.windows::after {
  background-image: url(icons/windows.svg); }

body[data-application="opera"] [data-hide="opera"],
body:not([data-application="opera"]) [data-show="opera"] {
  display: none; }
