/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
:root {
  --background-color-error: #F7DDE1;
  --background-color-secondary: #f7f7f7;
  --background-color-primary: #fff;
  --background-color-ternary: #edf9ff;
  --border-color-secondary: #d2d2d2;
  --border-color-primary: #e6e6e6;
  --border-color-ternary: #c0e6f9;
  --border-color-outline: #acacac;
  --border-radius: 4px;
  --border-radius-primary: 6px;
  --border-style-primary: solid;
  --border-width-thick: 4px;
  --border-width-thin: 1px;
  --box-shadow-primary: 0 2px 4px 0 hsla(0, 0%, 84%, 0.5);
  --color-brand-primary: #ED1E45;
  --color-primary: #585858;
  --color-secondary: #000;
  --color-dimmed: #4A4A4A;
  --color-critical: var(--color-brand-primary);
  --color-default: #FF8F00;
  --color-error: var(--color-brand-primary);
  --color-link: #0797E1;
  --color-info: #0797E1;
  --color-tag: #07E1CF;
  --font-size-big: 17px;
  --font-size-medium: 16px;
  --font-size-primary: 13px;
  --font-size-small: 12px;
  --margin-primary: 16px;
  --margin-secondary: calc(var(--margin-primary) / 2);
  --padding-primary: 16px;
  --padding-secondary: calc(var(--padding-primary) / 2);
  --primary-outline: var(--border-color-outline) dotted 1px; }

/*******************************************************************************
 * General
 ******************************************************************************/
:root {
  --color-content: #FFF;
  --color-footer: #000;
  --color-highlight: var(--color-brand-primary);
  --color-primary: #0797E1;
  --color-primary-hover: #0789CA;
  --color-secondary: #FAFAFA;
  --margin: 60px;
  font-size: 16px; }

body {
  margin: 0; }

a {
  color: var(--color-link); }

a.button {
  display: inline-block;
  padding: 1em 2.5em;
  border: 3px solid var(--color-content);
  border-radius: 0.5em;
  font-weight: 600;
  color: var(--color-content);
  background-color: var(--color-primary);
  text-transform: uppercase;
  text-decoration: none;
  transition: all 200ms; }

a.button:hover {
  background-color: var(--color-primary-hover); }

a.button.secondary {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background-color: var(--color-content); }

a.button.secondary:hover {
  border-color: var(--color-primary-hover);
  color: var(--color-content);
  background-color: var(--color-primary-hover); }

h1,
h2 {
  margin: 0 0 1rem; }

h1 {
  font-size: 60px;
  line-height: 70px; }

h2 {
  font-size: 30px;
  line-height: 40px; }

h2.icon::before {
  display: inline-block;
  width: 23px;
  height: 23px;
  vertical-align: middle;
  background-size: 100%;
  content: ""; }

html[dir="ltr"] h2.icon::before {
  margin-right: 10px; }

html[dir="rtl"] h2.icon::before {
  margin-left: 10px; }

.block-icon {
  --icon-width: 40px;
  position: relative; }

html[dir="ltr"] .block-icon {
  padding-left: calc(var(--icon-width) + 20px); }

html[dir="rtl"] .block-icon {
  padding-right: calc(var(--icon-width) + 20px); }

.block-icon::before {
  position: absolute;
  width: var(--icon-width);
  height: var(--icon-width);
  background-size: 100%;
  background-repeat: no-repeat;
  content: ""; }

html[dir="ltr"] .block-icon::before {
  left: 0; }

html[dir="rtl"] .block-icon::before {
  right: 0; }

/*******************************************************************************
 * Header
 ******************************************************************************/
header p {
  margin: 1.5em 0;
  color: #5A5A5A; }

header .block-icon {
  --icon-width: 50px; }

#logo {
  width: 200px;
  margin-bottom: 70px; }

#hero {
  flex: 0; }

html[dir="ltr"] #hero {
  margin-left: 40px; }

html[dir="rtl"] #hero {
  margin-right: 40px; }

/*******************************************************************************
 * Footer
 ******************************************************************************/
footer {
  padding: 30px;
  font-size: 0.8rem;
  text-align: center;
  color: var(--color-content);
  background-color: var(--color-footer); }

#copyright-notice a {
  font-weight: 600;
  text-decoration: none;
  color: inherit; }

#copyright-notice a:hover {
  text-decoration: underline; }

/*******************************************************************************
 * Content
 ******************************************************************************/
.content {
  position: relative;
  box-sizing: border-box;
  max-width: 940px;
  margin: auto;
  padding: var(--margin) 0 50px; }

.content:not(:first-child) {
  padding-top: 0; }

.content.columns {
  display: flex; }

.content p {
  margin-top: 0;
  line-height: 26px; }

.content.columns > div {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: flex-start;
  justify-content: center; }

.content.columns > div:last-child {
  align-items: flex-end; }

@media (max-width: 960px) {
  .content.columns {
    flex-direction: column; }
  .content.columns > div:last-child {
    margin-top: 1em;
    align-items: flex-start; } }

/*******************************************************************************
 * Sections
 ******************************************************************************/
article {
  position: relative; }

article.primary,
article:last-child {
  margin-bottom: var(--margin); }

article.primary::after,
article.secondary::before,
article:last-child::after {
  position: absolute;
  width: 100%;
  height: var(--margin);
  content: ""; }

article.primary::after,
article:last-child::after {
  top: 100%; }

article.secondary::before {
  top: calc(var(--margin) * -1); }

:not(.columns) > section:not(:first-of-type) {
  margin-top: 75px; }

article.primary {
  margin-top: calc(100px + var(--margin));
  color: var(--color-content);
  background-color: var(--color-primary); }

article.primary::after {
  background-image: linear-gradient(to bottom right, var(--color-primary) 49%, var(--color-content) 50%); }

article.secondary {
  background-color: var(--color-secondary); }

article.secondary::before {
  background-image: linear-gradient(to bottom right, var(--color-content) 49%, var(--color-secondary) 50%); }

article:last-child::after {
  background-image: linear-gradient(to bottom right, var(--color-content) 49%, var(--color-footer) 50%); }

/*******************************************************************************
 * Social
 ******************************************************************************/
#social .content {
  max-width: 230px;
  text-align: center; }

#social p {
  color: #5A5A5A; }

#social a {
  display: inline-block;
  width: 3em;
  height: 3em;
  margin: 1em 10px 0;
  border-radius: 1.5em;
  background-color: #000;
  background-position: center;
  background-repeat: no-repeat; }

#social a.email {
  background-image: url(icons/email-white.svg); }

#social a.facebook {
  background-image: url(icons/facebook-white.svg); }

#social a.twitter {
  background-image: url(icons/twitter-white.svg); }

/*******************************************************************************
 * Header
 ******************************************************************************/
header {
  text-align: center; }

header {
  position: relative;
  margin-bottom: var(--margin); }

header a.button {
  border-color: transparent;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1); }

h1 {
  margin-bottom: 0.5em;
  font-size: 2.5rem;
  font-weight: 400;
  line-height: initial; }

h1 em {
  font-style: normal; }

h1 em[data-i18n-index="0"] {
  color: var(--color-highlight); }

h1 em[data-i18n-index="1"] {
  color: var(--color-primary); }

#logo {
  width: 250px;
  margin-bottom: 100px; }

/*******************************************************************************
 * Community Section
 ******************************************************************************/
#community > .content::before {
  --offset-start: 60px;
  position: absolute;
  top: -100px;
  left: calc(var(--offset-start) * -1);
  width: calc(100% + var(--offset-start));
  height: 100px;
  background-image: url(icons/day1/bug.svg), url(icons/day1/wave.svg), url(icons/day1/tower.svg);
  background-repeat: no-repeat;
  background-size: 40px auto, 160px auto 40px auto;
  background-position: 80px 0, 0 bottom, 100% bottom;
  content: ""; }

/*******************************************************************************
 * Block Element Section
 ******************************************************************************/
#popup-dummy {
  width: 340px;
  border: 1px solid #D2D2D2;
  border-radius: 4px;
  margin-top: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); }
