@charset "UTF-8";
/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
:root {
  --z-popout: 10;
  --z-popout-active: 11;
  --z-dialog: 20; }

:root {
  --background-color-error: #F7DDE1;
  --background-color-secondary: #f7f7f7;
  --background-color-primary: #fff;
  --background-color-ternary: #edf9ff;
  --border-color-secondary: #d2d2d2;
  --border-color-primary: #e6e6e6;
  --border-color-ternary: #c0e6f9;
  --border-color-outline: #acacac;
  --border-radius: 4px;
  --border-radius-primary: 6px;
  --border-style-primary: solid;
  --border-width-thick: 4px;
  --border-width-thin: 1px;
  --box-shadow-primary: 0 2px 4px 0 hsla(0, 0%, 84%, 0.5);
  --color-brand-primary: #ED1E45;
  --color-primary: #585858;
  --color-secondary: #000;
  --color-dimmed: #4A4A4A;
  --color-critical: var(--color-brand-primary);
  --color-default: #FF8F00;
  --color-error: var(--color-brand-primary);
  --color-link: #0797E1;
  --color-info: #0797E1;
  --color-tag: #07E1CF;
  --font-size-big: 17px;
  --font-size-medium: 16px;
  --font-size-primary: 13px;
  --font-size-small: 12px;
  --margin-primary: 16px;
  --margin-secondary: calc(var(--margin-primary) / 2);
  --padding-primary: 16px;
  --padding-secondary: calc(var(--padding-primary) / 2);
  --primary-outline: var(--border-color-outline) dotted 1px; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
:root {
  --background-color-error: #F7DDE1;
  --background-color-secondary: #f7f7f7;
  --background-color-primary: #fff;
  --background-color-ternary: #edf9ff;
  --border-color-secondary: #d2d2d2;
  --border-color-primary: #e6e6e6;
  --border-color-ternary: #c0e6f9;
  --border-color-outline: #acacac;
  --border-radius: 4px;
  --border-radius-primary: 6px;
  --border-style-primary: solid;
  --border-width-thick: 4px;
  --border-width-thin: 1px;
  --box-shadow-primary: 0 2px 4px 0 hsla(0, 0%, 84%, 0.5);
  --color-brand-primary: #ED1E45;
  --color-primary: #585858;
  --color-secondary: #000;
  --color-dimmed: #4A4A4A;
  --color-critical: var(--color-brand-primary);
  --color-default: #FF8F00;
  --color-error: var(--color-brand-primary);
  --color-link: #0797E1;
  --color-info: #0797E1;
  --color-tag: #07E1CF;
  --font-size-big: 17px;
  --font-size-medium: 16px;
  --font-size-primary: 13px;
  --font-size-small: 12px;
  --margin-primary: 16px;
  --margin-secondary: calc(var(--margin-primary) / 2);
  --padding-primary: 16px;
  --padding-secondary: calc(var(--padding-primary) / 2);
  --primary-outline: var(--border-color-outline) dotted 1px; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
io-filter-search {
  display: flex;
  flex-direction: row;
  height: 48px; }

io-filter-search > input {
  padding: 0 8px;
  border: 1px solid #bcbcbc;
  border-radius: var(--border-radius) 0 0 0;
  font-size: inherit;
  flex-grow: 1; }

html[dir="rtl"] io-filter-search > input {
  border-radius: 0 var(--border-radius) 0 0; }

io-filter-search > button {
  padding: 0 32px;
  border: 0;
  border-radius: 0 var(--border-radius) 0 0;
  color: #fff;
  background-color: #3a97b9;
  font-weight: 400;
  text-transform: uppercase; }

html[dir="rtl"] io-filter-search > button {
  border-radius: var(--border-radius) 0 0 0; }

io-filter-search > button:disabled {
  opacity: 0.7; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
io-checkbox {
  display: inline-block;
  width: 1.2rem;
  height: 1.2rem;
  margin: 2px;
  padding: 0px;
  border: 0.2rem solid transparent;
  cursor: pointer; }

io-checkbox > button {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  border: 0;
  border-radius: 0;
  background-color: transparent;
  background-image: url(icons/checkbox.svg?off#off);
  background-repeat: no-repeat;
  background-size: contain; }

io-checkbox[disabled] > button {
  outline: none;
  opacity: 0.5;
  cursor: default; }

io-checkbox[checked] > button {
  background-image: url(icons/checkbox.svg?on#on); }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
io-toggle {
  --width: 30px;
  --height: 8px;
  --translateY: -4px;
  --translateX: 14px;
  display: inline-block;
  width: var(--width);
  height: var(--height);
  border-radius: 4px;
  background-color: #9b9b9b;
  cursor: pointer;
  transition: background 0.2s ease-out;
  transform: translateY(calc(var(--translateY) * -1));
  will-change: background; }

html[dir="rtl"] io-toggle {
  --translateX: -14px; }

io-toggle[checked] {
  background-color: #92d3ea; }

io-toggle[disabled] {
  opacity: 0.5;
  cursor: default; }

io-toggle button {
  width: calc(var(--height) * 2);
  height: calc(var(--height) * 2);
  padding: 0;
  border: 2px solid #e1e0e1;
  border-radius: var(--height);
  outline: none;
  cursor: pointer;
  transition: border 0.2s ease-out, box-shadow 0.2s ease-out, transform 0.2s ease-out, width 0.2s ease-out;
  transform: translateY(var(--translateY));
  will-change: border, box-shadow, transform, width; }

io-toggle button[aria-checked="false"] {
  background-color: #f1f1f1;
  box-shadow: 0 1px 2px 0 #e5d1d1; }

io-toggle button[aria-checked="false"]:hover {
  box-shadow: 0 2px 4px 0 #d3b0b0; }

io-toggle button[aria-checked="true"] {
  border: 2px solid #0797E1;
  background-color: #0797E1;
  box-shadow: 0 1px 2px 0 #a6cede;
  transform: translateY(var(--translateY)) translateX(var(--translateX)); }

io-toggle button[aria-checked="true"]:hover {
  box-shadow: 0 2px 4px 0 #a6cede; }

io-toggle button:focus,
io-toggle button[aria-checked="true"]:focus {
  border: 2px solid #87bffe; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
io-scrollbar {
  /*
    related to the container height, as in ░▓░░░░░░░░░░░
    or width, in case it's a vertical scrollbar
  */
  --size: 12px;
  overflow: hidden;
  cursor: default;
  user-select: none; }

io-scrollbar,
io-scrollbar > .slider {
  display: block;
  box-sizing: border-box;
  padding: 0; }

io-scrollbar > .slider {
  margin: 0;
  border: 1px solid #979797;
  background-color: #d8d8d8;
  font-size: 0;
  line-height: 0; }

io-scrollbar[direction="horizontal"] {
  height: var(--size); }

io-scrollbar[direction="vertical"] {
  width: var(--size); }

io-scrollbar[direction="horizontal"] > .slider {
  width: var(--slider-size, var(--size));
  height: 100%;
  transform: translateX(var(--position, 0)); }

io-scrollbar[direction="vertical"] > .slider {
  width: 100%;
  height: var(--slider-size, var(--size));
  transform: translateY(var(--position, 0)); }

/*
  The component depends on its style and it will look for the
  --io-filter-list property to ensure the CSS has been loaded.
  The property is also named like the component on purpose,
  to be sure its an own property, not something inherited.
*/
io-filter-list {
  --io-filter-list: ready;
  width: 100%;
  padding: 0;
  /* used to bootstrap the component once it's visible */
  animation: -io-filter-list 0.001s; }

/* used to bootstrap the component once it's visible */
@keyframes -io-filter-list {
  from {
    --io-filter-list: #fff; }
  to {
    --io-filter-list: #000; } }

io-filter-list,
io-filter-list *,
io-filter-list *::before,
io-filter-list *::after {
  box-sizing: border-box; }

io-filter-list[disabled] io-checkbox,
io-filter-list[disabled] io-toggle {
  pointer-events: none; }

io-filter-list table {
  width: 100%;
  border: 1px solid #bcbcbc;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  color: #505050;
  background-color: #fff;
  user-select: none; }

io-filter-list thead tr,
io-filter-list td {
  border-bottom: 1px solid #bcbcbc; }

io-filter-list tr.empty td,
io-filter-list tr:last-child td {
  border-bottom: 0; }

/* necessary to have scrollable tbody */
io-filter-list thead,
io-filter-list tbody {
  display: block; }

io-filter-list tr {
  display: flex; }

io-filter-list tbody {
  overflow: hidden;
  height: 300px; }

io-filter-list th,
io-filter-list td {
  min-width: 24px;
  padding: 4px 8px;
  text-align: center; }

io-filter-list th {
  display: flex;
  padding: 8px;
  cursor: pointer;
  transition: background 0.2s ease-in;
  align-items: center; }

io-filter-list th:not([data-column="rule"]) {
  justify-content: center; }

io-filter-list th:hover {
  background-color: #f6f6f6; }

io-filter-list tbody tr {
  height: var(--row-height, auto);
  outline: none; }

io-filter-list tbody tr.odd.selected,
io-filter-list tbody tr.selected {
  background-color: #f6f6f6; }

io-filter-list [data-column="rule"] {
  width: var(--rule-width, auto);
  white-space: nowrap;
  flex-grow: 1; }

io-filter-list [data-column="rule"] .content {
  overflow: hidden;
  height: 100%;
  text-overflow: ellipsis;
  font-family: monospace; }

io-filter-list [data-column="rule"] .saved {
  animation-name: saved-animation;
  animation-duration: 0.2s; }

io-filter-list [data-column="rule"] .content:focus {
  text-overflow: initial; }

io-filter-list tbody tr.editing {
  height: auto; }

io-filter-list tbody tr.editing [data-column="rule"] {
  overflow: initial;
  white-space: initial; }

html:not([dir="rtl"]) io-filter-list [data-column="rule"] {
  text-align: left; }

html[dir="rtl"] io-filter-list [data-column="rule"] {
  text-align: right; }

/* stylelint-disable indentation */
io-filter-list tbody tr:not(.empty):not(.editing)
[data-column="rule"] div:hover {
  outline: 1px dashed #d0d0d0;
  cursor: pointer; }

/* stylelint-enable indentation */
io-filter-list [data-column="status"],
io-filter-list [data-column="selected"],
io-filter-list [data-column="warning"] {
  width: 72px; }

io-filter-list [data-column="warning"] img {
  width: 1em;
  height: 1em; }

io-filter-list td[data-column="warning"] img {
  opacity: 0.5; }

io-filter-list thead th:not([data-column="selected"])::after {
  display: inline-block;
  width: 24px;
  padding: 4px;
  opacity: 0.3;
  font-size: 0.7em;
  line-height: 1rem; }

io-filter-list thead th:not([data-column="selected"])::after {
  content: "▲"; }

io-filter-list thead[data-dir="desc"] th:not([data-column="selected"])::after {
  content: "▼"; }

io-filter-list thead[data-sort="status"] th[data-column="status"]::after,
io-filter-list thead[data-sort="rule"] th[data-column="rule"]::after,
io-filter-list thead[data-sort="warning"] th[data-column="warning"]::after {
  opacity: 1; }

io-filter-list table {
  position: relative; }

io-filter-list io-scrollbar {
  position: absolute;
  top: 46px;
  bottom: 8px;
  opacity: 0;
  transition: opacity 0.2s ease-in; }

io-filter-list:hover io-scrollbar {
  opacity: 1; }

html:not([dir="rtl"]) io-filter-list io-scrollbar {
  right: 12px; }

html[dir="rtl"] io-filter-list io-scrollbar {
  left: 12px; }

io-filter-list io-toggle {
  margin-top: 2px;
  vertical-align: top; }

@keyframes saved-animation {
  from {
    background: #bcffbc; }
  to {
    background: default; } }

io-filter-table {
  display: flex;
  flex-direction: column; }

io-filter-table[disabled] {
  opacity: 0.6; }

io-filter-table > io-filter-search {
  z-index: 1; }

io-filter-table > io-filter-search > input {
  padding-right: 24px;
  padding-left: 24px; }

io-filter-table > io-filter-list > table {
  border-top: 0; }

io-filter-table .footer {
  display: flex;
  flex-direction: row;
  margin-top: 16px;
  align-items: center; }

io-filter-table .footer button {
  padding: 4px;
  font-weight: 600; }

io-filter-table .footer button[disabled] {
  visibility: hidden; }

io-filter-table .footer button:not(:last-child) {
  text-transform: uppercase; }

io-filter-table .footer button:not(:first-child) {
  margin: auto 16px; }

io-filter-table .footer .delete {
  border: 2px solid var(--color-brand-primary);
  color: var(--color-brand-primary); }

io-filter-table .footer .copy {
  border: 2px solid #337ba2;
  color: #337ba2; }

io-filter-table .footer button:not(.error)::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  transform: translateY(0.1em); }

io-filter-table .footer .delete::before {
  background-image: url(icons/trash.svg?error#error); }

io-filter-table .footer .copy::before {
  background-image: url(icons/copy.svg); }

io-filter-table .footer .error {
  display: inline;
  border: 0;
  color: var(--color-error);
  text-transform: none;
  flex-grow: 1;
  white-space: pre; }

io-filter-table .footer .error ul {
  padding: 0;
  line-height: 30px;
  list-style: none; }

io-filter-table .footer .error strong {
  border: 1px dashed #bcbcbc;
  border-radius: 5px;
  padding: 5px;
  background-color: #F3F3F3;
  color: #000;
  font-family: monospace; }

io-filter-table .footer .error:not([data-filter]) {
  outline: none;
  cursor: default; }

html:not([dir="rtl"]) io-filter-table .footer .error {
  text-align: left; }

html:not([dir="rtl"]) io-filter-table .footer .delete::before,
html:not([dir="rtl"]) io-filter-table .footer .copy::before {
  margin-right: 4px; }

html[dir="rtl"] io-filter-table .footer .error {
  text-align: right; }

html[dir="rtl"] io-filter-table .footer .delete::before,
html[dir="rtl"] io-filter-table .footer .copy::before {
  margin-left: 4px; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
:root {
  --z-popout: 10;
  --z-popout-active: 11;
  --z-dialog: 20; }

io-list-box {
  display: block;
  position: relative;
  box-sizing: border-box; }

io-list-box,
io-list-box button,
io-list-box [role="listbox"],
io-list-box [role="option"],
io-list-box .group {
  margin: 0;
  padding: 0;
  color: #4a4a4a;
  background: #fff;
  font-family: inherit;
  font-size: inherit;
  text-align: inherit; }

io-list-box button,
io-list-box [role="listbox"] {
  box-sizing: inherit;
  width: var(--width, 100%);
  border: 1px solid #bcbcbc;
  cursor: pointer; }

io-list-box .group,
io-list-box [role="option"] {
  padding: 0.6rem 32px; }

io-list-box button {
  padding: 0.6rem 0.8rem;
  border: 1px solid #bcbcbc;
  border-radius: 0 0 var(--border-radius) var(--border-radius); }

io-list-box button[aria-expanded="false"] {
  color: #0797E1;
  background: #e9f6fc;
  text-transform: uppercase; }

io-list-box[detached] button[aria-expanded="false"] {
  border-radius: var(--border-radius); }

io-list-box button:focus {
  outline: none; }

io-list-box [role="listbox"] {
  overflow: auto;
  position: absolute;
  z-index: var(--z-popout-active);
  bottom: 2.2em;
  max-height: 290px;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  box-shadow: 0 -4px 20px 0 rgba(0, 0, 0, 0.11); }

io-list-box [role="option"].hover {
  background: #e1f2fa; }

io-list-box .group,
io-list-box [role="option"][aria-disabled="true"],
io-list-box [role="option"][aria-disabled="true"].hover {
  background-color: #eee;
  cursor: default; }

io-list-box [role="option"][aria-selected="true"] {
  background-image: url(icons/checkmark.svg?default#default);
  background-repeat: no-repeat;
  background-position: 8px center;
  background-size: 20px 20px; }

io-list-box [role="combobox"] {
  z-index: 1; }

html[dir="rtl"] io-list-box [role="option"][aria-selected="true"] {
  background-position: calc(100% - 8px) center; }

io-list-box .group {
  font-weight: 600;
  text-transform: uppercase; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
:root {
  --z-popout: 10;
  --z-popout-active: 11;
  --z-dialog: 20; }

io-popout {
  --background-color: #FFF;
  --border: 1px solid #0797E1;
  --icon-margin: 0.4rem;
  --icon-size-inner: 1rem;
  --icon-size-outer: calc(var(--icon-size-inner) + var(--icon-margin));
  --pointer-size: 10px;
  --pointer-offset-out: calc(var(--pointer-size) / -2 - 1px);
  --pointer-offset-start: calc(
    var(--icon-size-outer) + var(--pointer-offset-out)
  );
  --content-offset-out: calc(-0.5 * var(--icon-size-outer));
  --content-offset-start: calc(var(--icon-size-outer) + 2 * var(--icon-margin)); }

io-popout[type="dialog"] {
  --icon-size-inner: 3rem; }

/*
 * This component is not keyboard-accessible yet but we need it to be focusable
 * to detect when we can close it
 */
io-popout:focus {
  outline: none; }

/* Prevent pre-rendered content to show up before component has loaded */
io-popout > :not(.wrapper) {
  display: none; }

/*******************************************************************************
 * Z-Index
 ******************************************************************************/
/*
 * z-index on custom elements seem to be ignored, so that
 * this extra z-index for io-popout > .wrapper is needed to avoid issues
 */
io-popout,
io-popout > .wrapper {
  z-index: var(--z-popout); }

/* Ensures that expanded popouts are always shown on top of other popouts */
io-popout[expanded],
io-popout[expanded] > .wrapper {
  /*
   * We can't use calc() for setting the z-index yet because support for it was
   * only added in Firefox 57. Therefore we have to hardcode its value for now.
   * https://developer.mozilla.org/en-US/docs/Mozilla/Firefox/Releases/57#Quantum_CSS_notes
   */
  z-index: var(--z-popout-active); }

/*
 * We need to ensure that any content overlays the anchor pointer or otherwise
 * its inner half becomes visible when the content's background color changes
 */
io-popout > .wrapper > [role] * {
  z-index: 1; }

/*******************************************************************************
 * Anchor
 ******************************************************************************/
io-popout > .wrapper {
  display: inline-block;
  position: relative;
  vertical-align: middle; }

io-popout > .wrapper::before {
  width: var(--icon-size-inner);
  height: var(--icon-size-inner);
  cursor: pointer; }

io-popout[anchor-icon] > .wrapper::before {
  border-radius: 50%;
  background-color: #0797E1;
  background-repeat: no-repeat;
  background-size: 40%;
  background-position: center;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  transition: background-color 100ms, box-shadow 100ms; }

io-popout[anchor-icon][expanded] > .wrapper::before {
  background-color: #4A4A4A;
  box-shadow: none;
  animation-name: tap;
  animation-duration: 500ms; }

io-popout[type="menubar"] > .wrapper::before {
  background-image: url(icons/gear.svg?default#default); }

io-popout[type="tooltip"] > .wrapper::before {
  background-image: url(icons/tooltip.svg); }

io-popout[anchor-icon="heart"] > .wrapper::before {
  background-image: url(icons/heart.svg); }

/*******************************************************************************
 * Anchor pointer
 ******************************************************************************/
io-popout > .wrapper > [role]::before {
  display: block;
  position: absolute;
  width: var(--pointer-size);
  height: var(--pointer-size);
  border: var(--border);
  border-right: none;
  border-bottom: none;
  background-color: var(--background-color);
  content: ""; }

io-popout:not([expanded]) > .wrapper > [role]::before,
io-popout[expanded="above"] > .wrapper > [role]::before,
io-popout[expanded="below"] > .wrapper > [role]::before {
  right: var(--pointer-offset-start); }

html[dir="rtl"] io-popout:not([expanded]) > .wrapper > [role]::before,
html[dir="rtl"] io-popout[expanded="above"] > .wrapper > [role]::before,
html[dir="rtl"] io-popout[expanded="below"] > .wrapper > [role]::before {
  right: auto;
  left: var(--pointer-offset-start); }

io-popout:not([expanded]) > .wrapper > [role]::before,
io-popout[expanded="below"] > .wrapper > [role]::before {
  top: var(--pointer-offset-out);
  transform: rotate(45deg); }

io-popout[expanded="above"] > .wrapper > [role]::before {
  bottom: var(--pointer-offset-out);
  transform: rotate(-135deg); }

io-popout[expanded="start"] > .wrapper > [role]::before {
  top: var(--pointer-offset-start);
  right: var(--pointer-offset-out);
  transform: rotate(135deg); }

html[dir="rtl"] io-popout[expanded="start"] > .wrapper > [role]::before {
  right: auto;
  left: var(--pointer-offset-out);
  transform: rotate(-45deg); }

/*******************************************************************************
 * Content
 ******************************************************************************/
io-popout > .wrapper > [role] {
  position: absolute;
  box-sizing: border-box;
  border: var(--border);
  border-radius: var(--border-radius);
  background-color: var(--background-color);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  cursor: default; }

io-popout [aria-hidden="true"] {
  display: none; }

io-popout:not([expanded]) > .wrapper > [role],
io-popout[expanded="above"] > .wrapper > [role],
io-popout[expanded="below"] > .wrapper > [role] {
  right: var(--content-offset-out); }

html[dir="rtl"] io-popout:not([expanded]) > .wrapper > [role],
html[dir="rtl"] io-popout[expanded="above"] > .wrapper > [role],
html[dir="rtl"] io-popout[expanded="below"] > .wrapper > [role] {
  right: auto;
  left: var(--content-offset-out); }

io-popout:not([expanded]) > .wrapper > [role],
io-popout[expanded="below"] > .wrapper > [role] {
  top: var(--content-offset-start); }

io-popout[expanded="above"] > .wrapper > [role] {
  bottom: var(--content-offset-start); }

io-popout[expanded="start"] > .wrapper > [role] {
  top: var(--content-offset-out);
  right: var(--content-offset-start); }

html[dir="rtl"] io-popout[expanded="start"] > .wrapper > [role] {
  right: auto;
  left: var(--content-offset-start); }

io-popout > .wrapper > [role="dialog"] {
  width: 12rem;
  padding: 20px; }

io-popout > .wrapper > [role="menubar"] {
  width: 12.2rem; }

io-popout > .wrapper > [role="tooltip"] {
  width: 15rem; }

io-popout .close {
  position: absolute;
  top: 10px; }

io-popout .close:focus {
  outline: none; }

io-popout p {
  padding: 0.2rem 1rem; }

io-popout [role="dialog"] p {
  padding: 1em 0;
  margin: 0;
  font-size: 0.9em; }

io-popout [role="tooltip"] p {
  overflow-y: auto;
  /* Approximated to achieve a total tooltip height of 12.5em without
  hiding overflowing anchor pointer */
  max-height: 9em;
  line-height: 1.5rem; }

html[dir="ltr"] io-popout button.close {
  right: 10px;
  left: auto; }

html[dir="rtl"] io-popout button.close {
  right: auto;
  left: 10px; }

io-popout .close + * {
  margin-top: 2em; }

html[dir="ltr"] io-popout button.close {
  right: 10px;
  left: auto; }

html[dir="rtl"] io-popout button.close {
  right: auto;
  left: 10px; }

/*******************************************************************************
 * Animations
 ******************************************************************************/
@keyframes tap {
  0% {
    background-position: center;
    background-size: 40%; }
  20% {
    background-position: center 40%;
    background-size: 50%; }
  100% {
    background-position: center;
    background-size: 40%; } }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
io-popout ul {
  padding: 0; }

io-popout li {
  list-style: none; }

.table.cols io-popout li {
  padding: 0;
  border: 0; }

.table.cols io-popout li > * {
  display: flex;
  width: 100%;
  padding: 0.7rem 0rem;
  border: 0rem;
  color: #0797E1;
  font-size: 1rem;
  font-weight: 400;
  text-decoration: none;
  text-transform: none;
  align-items: center; }

io-popout li > *:hover,
io-popout li > *:focus {
  background-color: #E1F2FA;
  cursor: pointer; }

io-popout li .icon::before {
  width: var(--icon-size-inner);
  height: var(--icon-size-inner);
  margin: 0 var(--icon-size-inner);
  border: none; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
io-toggle {
  --width: 30px;
  --height: 8px;
  --translateY: -4px;
  --translateX: 14px;
  display: inline-block;
  width: var(--width);
  height: var(--height);
  border-radius: 4px;
  background-color: #9b9b9b;
  cursor: pointer;
  transition: background 0.2s ease-out;
  transform: translateY(calc(var(--translateY) * -1));
  will-change: background; }

html[dir="rtl"] io-toggle {
  --translateX: -14px; }

io-toggle[checked] {
  background-color: #92d3ea; }

io-toggle[disabled] {
  opacity: 0.5;
  cursor: default; }

io-toggle button {
  width: calc(var(--height) * 2);
  height: calc(var(--height) * 2);
  padding: 0;
  border: 2px solid #e1e0e1;
  border-radius: var(--height);
  outline: none;
  cursor: pointer;
  transition: border 0.2s ease-out, box-shadow 0.2s ease-out, transform 0.2s ease-out, width 0.2s ease-out;
  transform: translateY(var(--translateY));
  will-change: border, box-shadow, transform, width; }

io-toggle button[aria-checked="false"] {
  background-color: #f1f1f1;
  box-shadow: 0 1px 2px 0 #e5d1d1; }

io-toggle button[aria-checked="false"]:hover {
  box-shadow: 0 2px 4px 0 #d3b0b0; }

io-toggle button[aria-checked="true"] {
  border: 2px solid #0797E1;
  background-color: #0797E1;
  box-shadow: 0 1px 2px 0 #a6cede;
  transform: translateY(var(--translateY)) translateX(var(--translateX)); }

io-toggle button[aria-checked="true"]:hover {
  box-shadow: 0 2px 4px 0 #a6cede; }

io-toggle button:focus,
io-toggle button[aria-checked="true"]:focus {
  border: 2px solid #87bffe; }

html {
  font-size: 16px; }

body {
  display: flex;
  /* We force vertical scrollbars to keep the content centered */
  overflow-y: scroll;
  margin: 1rem 0.3rem;
  color: #4A4A4A;
  background-color: #F3F3F3;
  font-size: 1rem;
  line-height: 1.3rem;
  justify-content: center; }

h1 {
  font-size: 3rem;
  font-weight: 300;
  line-height: 3rem; }

h2 {
  font-size: 1.125rem;
  font-weight: 700; }

a {
  color: #0797E1; }

a:hover {
  color: #5CBCE1; }

ul {
  margin: 0rem; }

main h3 {
  margin-top: 0rem;
  margin-bottom: 0.5rem; }

[aria-hidden="true"] {
  display: none !important; }

input[type="text"],
input[type="url"],
textarea,
main {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

/*
  Normalization
 */
button {
  font-family: inherit; }

button {
  border-radius: 0rem; }

/*
  Buttons and links
 */
button,
.button {
  display: block;
  padding: 0.6rem 0.8rem;
  border-radius: var(--border-radius);
  background-color: transparent;
  font-size: 1rem;
  font-weight: 700;
  text-decoration: none;
  text-transform: uppercase;
  cursor: pointer; }

button:disabled,
button[aria-disabled="true"] {
  cursor: default; }

button.primary:not(.icon),
.button.primary:not(.icon),
button.secondary:not(.icon),
.button.secondary:not(.icon) {
  padding: 0.6rem 2rem; }

/* Ignore .icon to avoid overriding "specific" (primary, secondary) styles */
button.primary:not(.icon),
.button.primary:not(.icon) {
  border: 0px;
  color: #FFF;
  background-color: #0797E1; }

button.primary:not([disabled]):not(.icon):hover,
.button.primary:not(.icon):hover {
  box-shadow: inset 0 0 0 3px #005D80; }

button.primary[disabled]:not(.icon) {
  background-color: #5CBCE1; }

button.secondary:not(.icon),
.button.secondary:not(.icon) {
  border: 2px solid #0797E1;
  color: #0797E1; }

button.secondary:not(.icon):hover,
.button.secondary:not(.icon):hover {
  box-shadow: inset 0 0 0 1px #0797E1; }

button.link,
button.list {
  color: #0797E1; }

button.link {
  padding: 0.2rem;
  border: 0px;
  background-color: transparent;
  font-family: inherit;
  font-weight: 400;
  text-decoration: underline;
  text-transform: none; }

button.link:hover {
  color: #5CBCE1; }

button.list {
  width: 100%;
  border-width: 1px;
  border-style: solid;
  border-color: #CDCDCD;
  background-color: #E1F2FA;
  text-align: initial; }

button.list:hover {
  border-color: #0797E1;
  box-shadow: inset 0 0 0 3px #0797E1; }

button.add.secondary {
  width: 100%;
  border: 1px solid #bcbcbc; }

button.add.secondary:hover {
  border-color: #0797E1; }

button.add::before {
  content: "+ "; }

#filters-box li[aria-selected="true"] {
  background-color: #e1f2fa; }

#filterlist-by-url-wrap {
  position: relative; }

#filterlist-by-url {
  position: absolute;
  /* ensures it overlaps same popout z-indexes */
  z-index: var(--z-popout-active);
  top: -260px;
  width: 100%;
  height: 260px;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  background: #fff;
  box-shadow: 0 -4px 20px 0 rgba(0, 0, 0, 0.11); }

button[data-action="open-filterlist-by-url"] {
  /* needed to avoid being shadowed by #filterlist-by-url */
  position: relative;
  z-index: 1;
  background-color: #FFF; }

.side-controls:not(.wrap) {
  display: flex;
  margin: 0.8rem 0rem;
  justify-content: flex-end; }

.side-controls > * {
  margin: 0rem; }

#filterlist-by-url .main-input,
#filterlist-by-url h3 {
  margin: 0.8rem; }

#filterlist-by-url h3 {
  text-transform: uppercase; }

#filterlist-by-url .side-controls {
  justify-content: flex-start; }

/*
  Due to Edge adoption as new target browser
  we cannot use -moz/webkit-margin-start
  or -moz/webkit-margin-end because
  these lack Edge support.
  Yet we need to preserve html direction
  and potential UI that might swap right to left.
*/
html:not([dir="rtl"]) .side-controls > * {
  margin-left: 0.8rem; }

html[dir="rtl"] .side-controls > * {
  margin-right: 0.8rem; }

.side-controls.wrap > * {
  margin: 0.6rem 0; }

html:not([dir="rtl"]) .side-controls.wrap > * {
  margin-left: auto; }

html[dir="rtl"] .side-controls.wrap > * {
  margin-right: auto; }

/*
  icons
 */
.icon {
  padding: 0px;
  border: 0px;
  background-color: transparent; }

.icon:hover {
  box-shadow: none; }

.icon::before {
  display: block;
  border: 0.2rem solid transparent;
  background-repeat: no-repeat;
  content: ""; }

button[role="checkbox"].icon::before {
  width: 1.2rem;
  height: 1.2rem;
  padding: 0px; }

button[role="checkbox"][disabled].icon:not(.toggle)::before,
button[role="checkbox"][aria-disabled="true"].icon:not(.toggle)::before {
  margin: 0.2rem;
  border: 0rem;
  border-radius: 2px;
  background-color: #ccc; }

button[role="checkbox"].icon:not(.toggle)::before {
  /* Using ?query as a workaround to chromium bug #643716 */
  background-image: url(icons/checkbox.svg?off#off); }

button[role="checkbox"][aria-checked="true"].icon:not(.toggle)::before {
  background-image: url(icons/checkbox.svg?on#on); }

button[role="checkbox"][aria-checked="true"].icon:disabled:not(.toggle)::before {
  background-image: url(icons/checkbox.svg?on-disabled#on-disabled); }

button[role="checkbox"].icon.toggle::before {
  background-image: url(icons/toggle.svg?on#on); }

button[role="checkbox"][aria-checked="false"].icon.toggle::before {
  background-image: url(icons/toggle.svg?off#off); }

button[role="checkbox"].icon.toggle::before {
  width: 1.9rem;
  height: 1rem; }

button[role="checkbox"][disabled].icon.toggle::before {
  background: none; }

.icon.delete::before {
  background-image: url(icons/trash.svg?default#default); }

.icon.delete:hover::before {
  background-image: url(icons/trash.svg?hover#hover); }

.icon.delete::before {
  width: 1rem;
  height: 1rem; }

[data-validation] .main-input input:focus:invalid ~ .icon.attention::before {
  background-image: url(icons/attention.svg); }

[data-validation] .main-input input:valid ~ .icon.attention::before {
  background-image: url(icons/checkmark.svg?approved#approved); }

.icon.update-subscription::before {
  background-image: url(icons/reload.svg); }

.icon.website::before {
  background-image: url(icons/globe.svg); }

.icon.source::before {
  background-image: url(icons/code.svg); }

.icon.delete::before {
  background-image: url(icons/trash.svg?default#default); }

.close.icon::before {
  width: 1rem;
  height: 1rem; }

.icon.close.primary::before {
  background-image: url(icons/delete.svg?primary#primary); }

.icon.close.primary:hover::before {
  background-image: url(icons/delete.svg?primary-hover#primary-hover); }

.icon.close.secondary::before {
  background-image: url(icons/delete.svg?secondary#secondary); }

.icon.close.tertiary::before {
  background-image: url(icons/delete.svg?tertiary#tertiary); }

.icon.close.secondary:hover::before {
  background-image: url(icons/delete.svg?secondary-hover#secondary-hover); }

.icon.close.tertiary:hover::before {
  background-image: url(icons/delete.svg?tertiary-hover#tertiary-hover); }

#dialog .table.list li button.icon::before {
  width: 1.3rem;
  height: 1.3rem;
  margin: 0rem;
  border: 0rem;
  background-image: none; }

#dialog .table.list li button[aria-checked="true"].icon::before {
  background-image: url(icons/checkmark.svg?default#default); }

#social ul li .icon::before {
  width: 2.5rem;
  height: 2.5rem;
  margin: 0em auto; }

.icon.twitter::before {
  background-image: url("icons/twitter.svg"); }

.icon.facebook::before {
  background-image: url("icons/facebook.svg"); }

.new-tag {
  display: none;
  padding: 0rem 0.4rem;
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--background-color-primary);
  background-color: var(--color-tag); }

li[data-recommended="cookies"] .new-tag,
li[data-recommended="notifications"] .new-tag {
  display: inline-block; }

/*
  Forms
 */
.main-input {
  position: relative;
  margin: 1.8rem 0rem 0.5rem;
  padding-top: 0.7rem; }

.main-input input {
  width: 100%;
  padding: var(--padding-primary);
  border: 1px solid #CDCDCD;
  outline: none;
  font-size: 1rem; }

[data-validation] .main-input input ~ .error-msg {
  display: none;
  position: absolute;
  top: calc(var(--padding-primary) * -2);
  right: 0;
  z-index: 10;
  color: var(--color-error); }

[data-validation] .side-controls {
  margin-top: 1.2rem; }

html[dir="rtl"] [data-validation] .main-input input ~ .error-msg {
  right: auto;
  left: 0; }

[data-validation] .main-input input:focus:invalid ~ .error-msg {
  display: block; }

[data-validation] .main-input input:focus:invalid {
  border-color: var(--color-error); }

[data-validation] .main-input input:focus:invalid ~ .attention::before,
[data-validation] .main-input input ~ .attention::before {
  position: absolute;
  top: 0.8rem;
  right: 0rem;
  width: 1.2rem;
  height: 1.2rem;
  margin: 0.8rem; }

/* stylelint-disable indentation */
html[dir="rtl"] [data-validation] .main-input input:focus:invalid
~ .attention::before,
html[dir="rtl"] [data-validation] .main-input input
~ .attention::before {
  right: auto;
  left: 0rem; }

/* stylelint-enable indentation */
/*
  Animations
*/
.highlight-animate {
  animation: highlight 1s 3; }

@keyframes highlight {
  0% {
    background-color: transparent; }
  30% {
    background-color: #ffd7a3; }
  70% {
    background-color: #ffd7a3; }
  100% {
    background-color: transparent; } }

/*
  Sidebar
 */
#sidebar,
#sidebar .fixed,
[role="tablist"] {
  width: 14.3rem; }

#sidebar {
  flex-shrink: 0; }

#sidebar .fixed {
  top: 1.2rem;
  bottom: 0rem;
  height: auto; }

html[dir="ltr"] #sidebar header {
  margin-right: 2rem; }

html[dir="rtl"] #sidebar header {
  margin-left: 2rem; }

#sidebar header h1,
#sidebar header p {
  margin: 0rem;
  user-select: none; }

#sidebar header h1 {
  font-size: 0;
  line-height: 48px;
  color: transparent;
  background-image: url(icons/logo/abp-full.svg);
  background-size: contain;
  background-repeat: no-repeat; }

#sidebar header p {
  margin-top: 30px;
  line-height: 2.6rem;
  opacity: 0.6;
  font-weight: 600;
  text-transform: uppercase; }

html[dir="rtl"] #sidebar header {
  text-align: left; }

html[dir="rtl"] #sidebar header p {
  text-align: right; }

html[dir="rtl"] #sidebar header h1 {
  background-position: center left; }

#sidebar nav,
#sidebar footer {
  margin: 1.4rem 0rem; }

[role="tablist"] {
  position: relative;
  margin: 0rem;
  padding: 0rem;
  font-size: 1rem;
  list-style: none; }

li a[role="tab"] {
  display: flex;
  margin-right: -1px;
  margin-left: -1px;
  padding: 1rem 0.8rem;
  border: 1px solid transparent;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  color: inherit;
  text-decoration: none;
  cursor: pointer; }

li a[role="tab"]:hover {
  background-color: #EAEAEA; }

li a[role="tab"][aria-selected] {
  border-color: #CDCDCD;
  background-color: #FFF;
  font-weight: 700; }

html[dir="rtl"] li a[role="tab"] {
  border-radius: 0 var(--border-radius) var(--border-radius) 0; }

html:not([dir="rtl"]) li a[role="tab"]:hover {
  border-right-color: #CDCDCD; }

html[dir="rtl"] li a[role="tab"]:hover {
  border-left-color: #CDCDCD; }

html:not([dir="rtl"]) li a[role="tab"][aria-selected] {
  border-right-color: transparent; }

html[dir="rtl"] li a[role="tab"][aria-selected] {
  border-left-color: transparent; }

#sidebar footer {
  width: 100%; }

#sidebar footer p {
  display: flex;
  margin: 1rem 0rem;
  justify-content: center; }

#support-us {
  position: fixed;
  bottom: 40px;
  margin: 0 40px; }

#support-us .h2-icon {
  /* Align icon with edges of text based on text's line height and font size */
  width: 2em;
  height: 2em;
  margin-top: 0.3em; }

html:not([dir="rtl"]) #support-us .h2-icon {
  float: left;
  margin-right: 0.5em; }

html[dir="rtl"] #support-us .h2-icon {
  float: right;
  margin-left: 0.5em; }

#support-us h2 {
  margin: 0;
  font-size: 1rem; }

#support-us p {
  text-align: center; }

#support-us a.button {
  width: 100%;
  margin-bottom: 0.5rem;
  padding: 2px;
  border: 1px solid var(--border-color-ternary);
  font-size: var(--font-size-primary);
  color: #0797e1;
  text-transform: uppercase;
  text-align: center;
  transition: background-color 0.2s ease-out; }

#support-us a.button:last-of-type {
  margin-bottom: 0; }

#support-us a.button:hover,
#support-us a.button:focus {
  background-color: #e9f6fc; }

/* This is a stopgap solution of footer overlapping tabs on low resolutions */
@media (min-height: 37rem) {
  #sidebar .fixed {
    position: fixed; }
  #sidebar footer {
    position: absolute;
    bottom: 0px; } }

/*
  Main content
 */
body[data-tab|="general"] #content-general,
body[data-tab|="advanced"] #content-advanced,
body[data-tab|="whitelist"] #content-whitelist,
body[data-tab|="help"] #content-help {
  display: block; }

main {
  width: 46.3rem;
  padding: 0px 0rem 1.4rem;
  border: 1px solid #CDCDCD;
  border-radius: var(--border-radius);
  background-color: #FFF; }

main > div {
  display: none; }

main p {
  margin: 0.8rem 0rem; }

/*
  Sections
 */
[role="tabpanel"] > section,
[role="tabpanel"] > .section {
  margin: 0 2rem;
  padding: 1.4rem 0;
  border-top: 1px solid #CDCDCD; }

[role="tabpanel"] > header h1,
[role="tabpanel"] > header p {
  margin: 1.4rem 0rem;
  padding: 0rem 2rem; }

section h2,
.section h2 {
  margin: 0rem; }

section h2 {
  text-transform: uppercase; }

section,
.section {
  clear: both; }

section.cols {
  display: flex; }

section.cols > *:first-child {
  flex: 1; }

html:not([dir="rtl"]) section.cols > *:first-child {
  margin-right: 2rem; }

html[dir="rtl"] section.cols > *:first-child {
  margin-left: 2rem; }

section.cols > *:last-child {
  flex: 3; }

/*
  Acceptable ads
 */
#tracking-warning {
  position: relative;
  margin-bottom: 1rem;
  padding: 1.5rem;
  border: 2px solid #ffd7a3;
  background-color: #fefbe3; }

#acceptable-ads:not(.show-warning) #tracking-warning {
  display: none; }

#hide-tracking-warning {
  position: absolute;
  top: 0.8rem;
  right: 0.8rem; }

html[dir="rtl"] #hide-tracking-warning {
  right: auto;
  left: 1rem; }

#tracking-warning .link {
  color: inherit;
  font-weight: 700;
  text-decoration: underline; }

#acceptable-ads ul {
  position: relative;
  padding-left: 2.2rem;
  list-style: none; }

html[dir="rtl"] #acceptable-ads ul {
  padding-right: 2.2rem;
  padding-left: 0rem; }

#acceptable-ads ul button {
  position: absolute; }

html[dir="ltr"] button {
  left: 0rem; }

html[dir="rtl"] button {
  right: 0rem; }

#acceptable-ads label {
  font-size: 1rem;
  font-weight: 700; }

#acceptable-ads #acceptable-ads-why-not {
  --background-color: #FFF;
  --border-color: #099CD0;
  position: absolute;
  z-index: 1;
  width: 260px;
  margin-top: 40px;
  padding: 16px;
  box-sizing: border-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--background-color);
  text-align: center; }

html[dir="ltr"] #acceptable-ads #acceptable-ads-why-not {
  left: -117px; }

html[dir="rtl"] #acceptable-ads #acceptable-ads-why-not {
  right: -117px; }

#acceptable-ads #acceptable-ads-why-not::before {
  display: block;
  position: absolute;
  top: -6px;
  left: calc(50% - 5px);
  width: 10px;
  height: 10px;
  border: 1px solid var(--border-color);
  border-right: none;
  border-bottom: none;
  background-color: var(--background-color);
  content: "";
  transform: rotate(45deg); }

#acceptable-ads #acceptable-ads-why-not > a,
#acceptable-ads #acceptable-ads-why-not > button {
  font-size: 0.9rem; }

#acceptable-ads #acceptable-ads-why-not > button {
  position: initial;
  margin: auto;
  border: 0;
  color: #0797E1; }

html:not([dir="rtl"]) #acceptable-ads label {
  margin-right: 0.5rem; }

html[dir="rtl"] #acceptable-ads label {
  margin-left: 0.5rem; }

#dnt {
  padding: 0.8rem;
  border: 1px solid #0797E1; }

/*
  Tables
 */
ul.table,
ul.list {
  margin: 0rem;
  padding: 0rem;
  list-style: none; }

.table li,
.list li {
  display: flex;
  align-items: center; }

.table li {
  margin: 0rem;
  border-width: 0px 1px 1px;
  border-style: solid;
  border-color: #CDCDCD; }

.list li {
  margin-bottom: 0.8rem;
  padding: 0rem; }

.list li [role="checkbox"] {
  flex-shrink: 0; }

.table li:first-of-type {
  border-top: 1px solid #CDCDCD;
  border-radius: var(--border-radius) var(--border-radius) 0 0; }

.table.list li {
  margin: 0rem;
  padding: 0.5rem 1rem; }

.table.list.bottom-control li:last-of-type {
  border-bottom: 0px; }

.list li > span {
  margin: 0rem 1rem; }

.table.list li > span {
  overflow: hidden;
  margin: 0rem;
  flex: 1;
  text-overflow: ellipsis; }

#content-whitelist .table.list li > span {
  white-space: nowrap; }

.table.list li[aria-label^="https://"] > span,
.table.list li[aria-label^="data:"] > span {
  white-space: initial;
  word-break: break-all; }

.table.list li.empty-placeholder,
#all-filter-lists-table .empty-placeholder {
  padding: 1rem 1.4rem; }

.table.list li.empty-placeholder:not(:last-of-type) {
  border-bottom: 0px; }

.table.list button.link {
  font-weight: 700;
  text-decoration: none;
  text-transform: uppercase; }

.table:not(.list):not(.cols) li {
  padding-top: 0px;
  padding-bottom: 6px; }

.table li [data-single="visible"],
.table li:first-of-type:last-of-type [data-single="hidden"] {
  display: none; }

.table li:first-of-type:last-of-type [data-single="visible"] {
  display: block; }

.th {
  display: flex; }

.col5 > * {
  display: inline-block;
  vertical-align: middle; }

.cols .col5,
.th .col5 {
  margin: 0rem 1rem;
  align-self: center; }

.th .col5:nth-of-type(1),
.table .col5:nth-of-type(1) {
  flex: 3;
  text-align: center; }

.th .col5:nth-of-type(2),
.table .col5:nth-of-type(2) {
  flex: 8; }

.table [aria-label^="https://"] .col5:nth-of-type(2),
.table [aria-label^="data:"] .col5:nth-of-type(2) {
  word-break: break-all; }

.th .col5:nth-of-type(3),
.table .col5:nth-of-type(3) {
  flex: 4;
  text-align: center; }

.th .col5:nth-of-type(4),
.table .col5:nth-of-type(4) {
  flex: 1; }

.th .col5:nth-of-type(5),
.table .col5:nth-of-type(5) {
  flex: 1; }

html:not([dir="rtl"]) .th .col5:nth-of-type(5),
html:not([dir="rtl"]) .table .col5:nth-of-type(5) {
  margin-right: 1.8rem;
  margin-left: 0; }

html[dir="rtl"] .th .col5:nth-of-type(5),
html[dir="rtl"] .table .col5:nth-of-type(5) {
  margin-right: 0;
  margin-left: 1.8rem; }

.table.cols > span {
  margin: 0rem; }

.table.cols li {
  padding: 0.5rem 0rem; }

.table.cols .toggle {
  -moz-margin-end: 0.5rem;
  -webkit-margin-end: 0.5rem; }

#dialog .table.list li {
  display: block;
  padding: 0rem;
  border-width: 1px 0px 0px; }

#dialog .table.list li:first-of-type {
  border: 0px; }

#dialog .table.list li button {
  display: flex;
  width: 100%;
  height: auto;
  padding: 1.1rem 1rem;
  background-image: none; }

#dialog .table.list li button:hover,
#dialog .table.list li button:focus {
  background-color: #E1F2FA; }

#dialog .table.list li button[aria-checked="true"],
.table.list li .dimmed {
  color: #BBB; }

#dialog .table.list li button > span {
  margin: 0rem 0.8rem;
  font-weight: 400;
  text-transform: none;
  flex: none; }

li.preconfigured [data-hide="preconfigured"] {
  display: none !important; }

/*
  Tooltips
*/
.tooltip {
  position: relative;
  margin: 0rem;
  line-height: 1.5rem;
  text-decoration: none;
  cursor: help; }

html:not([dir="rtl"]) .tooltip {
  margin-right: 1rem; }

html[dir="rtl"] .tooltip {
  margin-left: 1rem; }

/*
  General tab content
*/
html[lang="de"] #recommended-list-table li[data-recommended="cookies"],
html[lang="fr"] #recommended-list-table li[data-recommended="cookies"] {
  display: none; }

#blocking-languages li button[data-single] {
  padding: 0; }

#blocking-languages-dialog-table {
  border-bottom: none; }

.button-add,
.cancel-button {
  border: 0px;
  color: #3A7BA6;
  background-color: transparent;
  cursor: pointer; }

/*
  Whitelist tab
 */
#content-whitelist form {
  display: flex;
  margin-bottom: 1.4rem; }

#content-whitelist form input {
  padding: 0.5rem 1rem;
  border: 2px solid #0797E1;
  border-radius: var(--border-radius);
  font-size: 1rem;
  flex: 1; }

html:not([dir="rtl"]) #content-whitelist form button {
  margin-left: 0.7rem; }

html[dir="rtl"] #content-whitelist form button {
  margin-right: 0.7rem; }

#whitelisting-table li {
  padding-right: 1.4rem;
  padding-left: 1.4rem;
  border-right: 0rem;
  border-left: 0rem; }

/*
  Advanced tab content
*/
#custom-filters {
  margin-top: 3rem; }

#custom-filters h3 {
  font-size: 1.125rem;
  font-weight: 400;
  text-transform: uppercase; }

#custom-filters .io-filter-table-title {
  font-weight: 600; }

html:not([dir="rtl"]) #custom-filters .io-filter-table-title {
  margin-right: 2rem; }

html[dir="rtl"] #custom-filters .io-filter-table-title {
  margin-left: 2rem; }

#update-all-subscriptions button {
  display: initial; }

#update-all-subscriptions {
  margin: 2rem auto;
  text-align: right; }

html[dir="rtl"] #update-all-subscriptions {
  text-align: left; }

#all-filter-lists-table li.show-message .last-update,
#all-filter-lists-table li:not(.show-message) .message,
#acceptable-ads:not(.show-dnt-notification) #dnt {
  display: none; }

#all-filter-lists-table li.show-message .message.error {
  color: var(--color-error); }

#all-filter-lists-table li.show-message .message.error::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  margin-right: 0.5em;
  vertical-align: middle;
  background-image: url(icons/attention.svg);
  background-size: contain;
  content: ""; }

html[dir="rtl"] #all-filter-lists-table li.show-message .message.error::before {
  margin-right: 0;
  margin-left: 0.5em; }

#all-filter-lists-table {
  margin-bottom: 0.8rem; }

/*
  Help tab content
*/
#social ul {
  padding: 0px;
  list-style: none; }

#social ul li {
  display: inline-block; }

html:not([dir="rtl"]) #social ul li {
  margin-right: 1rem; }

html[dir="rtl"] #social ul li {
  margin-left: 1rem; }

#social ul li a {
  display: block;
  text-align: center;
  text-decoration: none; }

/*
  Dialog
*/
#dialog-background {
  display: none;
  position: fixed;
  z-index: var(--z-dialog);
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  opacity: 0.7;
  background-color: #000; }

body[data-dialog] #dialog-background {
  display: block; }

#dialog {
  overflow: hidden;
  position: fixed;
  z-index: var(--z-dialog);
  top: 100px;
  right: 0;
  left: 0;
  width: 700px;
  margin: auto;
  border-radius: var(--border-radius);
  background-color: #FFF;
  box-shadow: 0 -4px 20px 0 rgba(255, 255, 255, 0.11); }

#dialog header {
  display: flex;
  padding: 1.5em;
  background-color: #0797E1; }

#dialog-title {
  color: #FFF;
  flex: 1; }

#dialog-title h3 {
  margin: 0;
  text-transform: uppercase; }

#dialog button {
  display: inline-block; }

.close {
  margin: 0;
  cursor: pointer; }

#dialog #dialog-body {
  overflow: auto;
  max-height: 60vh;
  padding: 2rem 1rem; }

#dialog-body button {
  margin-top: 2rem; }

#dialog-body h3 {
  margin: 0 0 0.5em;
  line-height: 0.9em;
  font-size: 0.9em; }

#dialog-body h3::before {
  position: absolute;
  width: 18px;
  height: 11px;
  background-size: contain;
  background-repeat: no-repeat;
  content: ""; }

html[dir="ltr"] #dialog-body h3::before {
  margin-left: -26px; }

html[dir="rtl"] #dialog-body h3::before {
  margin-right: -26px; }

#dialog-body .field.title h3::before {
  background-image: url(icons/filter-list-title.svg); }

#dialog-body .field.url h3::before {
  background-image: url(icons/filter-list-url.svg); }

html[dir="ltr"] #dialog-body button + button {
  margin-left: 1rem; }

html[dir="rtl"] #dialog-body button + button {
  margin-right: 1rem; }

#dialog-content-language-add {
  margin: 0rem; }

#dialog-content-about,
#dialog-content-invalid,
#filters-box button[role="combobox"] {
  text-align: center; }

#dialog-content-about p {
  margin: 0.5rem 0rem; }

#dialog-content-import .side-controls {
  margin-top: 2.45rem; }

#dialog-content-invalid {
  color: var(--color-error); }

#dialog-content-invalid .error {
  width: 50px;
  height: 50px;
  margin: 0 auto 1em;
  border-radius: 50%;
  font-size: 25px;
  font-weight: 600;
  line-height: 50px;
  background-color: var(--background-color-error); }

#dialog-content-invalid strong {
  padding: 2px 5px;
  border-radius: var(--border-radius);
  font-style: italic;
  background-color: var(--background-color-error); }

#dialog .field {
  margin-bottom: 1.5em; }

html[dir="ltr"] #dialog .field {
  margin-left: 28px; }

html[dir="rtl"] #dialog .field {
  margin-right: 28px; }

#dialog .field:last-of-type {
  margin-bottom: 0; }

#dialog .table {
  width: 100%; }

#dialog .section:not(:first-child) {
  margin-top: 24px; }

#dialog .url > a {
  word-wrap: break-word;
  text-decoration: none; }

#dialog .url > a:hover {
  text-decoration: underline; }

#dialog .url > a::after {
  display: inline-block;
  width: 0.7em;
  height: 0.7em;
  vertical-align: middle;
  background-image: url(icons/open-link.svg);
  background-size: contain;
  background-repeat: no-repeat;
  content: ""; }

html[dir="ltr"] #dialog .url > a::after {
  margin-left: 0.3em; }

html[dir="rtl"] #dialog .url > a::after {
  margin-right: 0.3em;
  transform: scaleX(-1); }

/* stylelint-disable indentation */
body:not([data-dialog="about"]) #dialog-title-about,
body:not([data-dialog="about"]) #dialog-content-about,
body:not([data-dialog="import"]) #dialog-title-import,
body:not([data-dialog="import"]) #dialog-content-import,
body:not([data-dialog="language-add"]) #dialog-title-language-add,
body:not([data-dialog="language-change"]) #dialog-title-language-change,
body:not([data-dialog="language-add"]):not([data-dialog="language-change"])
#dialog-content-language-add,
body:not([data-dialog="language-add"]) #dialog-body button.add,
body:not([data-dialog="language-change"]) #dialog-body button.change,
body:not([data-dialog="predefined"]) #dialog-title-predefined,
body:not([data-dialog="predefined"]) #dialog-content-predefined,
body:not([data-dialog="invalid"]) #dialog-title-invalid,
body:not([data-dialog="invalid"]) #dialog-content-invalid,
body:not([data-dialog]) #dialog {
  display: none; }

/* stylelint-enable indentation */
/*
  Notification
*/
#notification {
  display: flex;
  position: fixed;
  top: 0rem;
  left: 0rem;
  box-sizing: border-box;
  width: 100%;
  padding: 1rem 1.9rem;
  font-size: 1rem; }

#notification strong {
  text-align: center;
  flex: 1; }

#notification.info {
  color: #0797E1;
  background-color: rgba(225, 242, 250, 0.8); }

#notification.error {
  color: var(--color-error);
  background-color: rgba(235, 199, 203, 0.8); }

#notification.error .close {
  display: none; }

#notification[aria-hidden="false"] {
  animation: show-notification 3s;
  will-change: transform; }

@keyframes show-notification {
  0% {
    transform: translateY(-4.8rem); }
  25% {
    transform: translateY(0); }
  75% {
    transform: translateY(0); }
  100% {
    transform: translateY(-4.8rem); } }
