/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
:root {
  --background-color-error: #F7DDE1;
  --background-color-secondary: #f7f7f7;
  --background-color-primary: #fff;
  --background-color-ternary: #edf9ff;
  --border-color-secondary: #d2d2d2;
  --border-color-primary: #e6e6e6;
  --border-color-ternary: #c0e6f9;
  --border-color-outline: #acacac;
  --border-radius: 4px;
  --border-radius-primary: 6px;
  --border-style-primary: solid;
  --border-width-thick: 4px;
  --border-width-thin: 1px;
  --box-shadow-primary: 0 2px 4px 0 hsla(0, 0%, 84%, 0.5);
  --color-brand-primary: #ED1E45;
  --color-primary: #585858;
  --color-secondary: #000;
  --color-dimmed: #4A4A4A;
  --color-critical: var(--color-brand-primary);
  --color-default: #FF8F00;
  --color-error: var(--color-brand-primary);
  --color-link: #0797E1;
  --color-info: #0797E1;
  --color-tag: #07E1CF;
  --font-size-big: 17px;
  --font-size-medium: 16px;
  --font-size-primary: 13px;
  --font-size-small: 12px;
  --margin-primary: 16px;
  --margin-secondary: calc(var(--margin-primary) / 2);
  --padding-primary: 16px;
  --padding-secondary: calc(var(--padding-primary) / 2);
  --primary-outline: var(--border-color-outline) dotted 1px; }

/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
io-circle-toggle {
  outline: none;
  cursor: pointer; }

io-circle-toggle[disabled] {
  opacity: 0.5;
  cursor: not-allowed; }

/*
 * The following rules belong to the io-circle-toggle.js rendered SVG
 * but since MS Edge does not understand the <style> node within the SVG
 * we have moved these back to this file to normalize the layout in MS Edge too
 */
io-circle-toggle .outer-circle,
io-circle-toggle .on,
io-circle-toggle .off {
  transition: all 0.5s cubic-bezier(0.8, 0, 0.25, 1); }

/*
 * We cannot rely on SVG's masking/clipping to work with CSS transforms
 * so we have to replicate the outline of the outer circle in CSS.
 * https://gitlab.com/eyeo/adblockplus/abpui/adblockplusui/issues/543
 * https://gitlab.com/eyeo/adblockplus/abpui/adblockplusui/issues/598
 */
io-circle-toggle[checked] .on,
io-circle-toggle .off {
  clip-path: circle(100% at 50% 50%); }

io-circle-toggle .on {
  clip-path: circle(100% at -100% 50%); }

io-circle-toggle[checked] .off {
  clip-path: circle(100% at 200% 50%); }

/*
 * Edge, Firefox 52 and 53 don't support
 * basic shape values for clip-path yet
 */
io-circle-toggle:not([checked]) .on,
io-circle-toggle[checked] .off {
  opacity: 0; }

io-circle-toggle .outer-circle {
  transform: translateX(-32.2px);
  fill: #585858;
  stroke-width: 0; }

io-circle-toggle[checked] .outer-circle {
  transform: translateX(-11.4px);
  fill: #0797E1; }

io-circle-toggle:not([checked]) .on {
  transform: translateX(12px); }

io-circle-toggle:not([checked]) .off {
  transform: translateX(14.8px); }

io-circle-toggle:focus .outer-circle {
  stroke: rgba(88, 88, 88, 0.8);
  stroke-width: 5; }

io-circle-toggle[checked]:focus .outer-circle {
  stroke: rgba(6, 136, 203, 0.8);
  stroke-width: 5; }

:root {
  --border-width-thick-doubled: calc(2 * var(--border-width-thick));
  --border-width-thin-negative: calc(-1 * var(--border-width-thin));
  --margin-primary-half: calc(var(--margin-primary) / 2); }

*,
*::before,
*::after {
  box-sizing: border-box; }

[hidden],
[aria-hidden="true"] {
  display: none !important; }

html {
  background-color: var(--background-color-primary); }

html,
body,
h2 {
  margin: 0; }

/*
  We use CSS to hide elements depending on the context:
    disabled: Blocking is not active on page
    ignore: Extension cannot be active on page
    nohtml: Content scripts are not active on page
    private: Total blocked counter is not being updated
 */
/*
  explicitly disable Issue Reporter in EdgeHTML due bug
  https://issues.adblockplus.org/ticket/7175
 */
html[data-platform="edgehtml"] #issue-reporter,
html[data-application="edge"] footer,
body.nohtml #block-element,
body.disabled #block-element,
body.disabled #stats-page,
body.ignore #page-status,
body.ignore #stats-page,
body.ignore #default-container .options,
body.private #counter-panel .share,
body.private #issue-reporter,
body.private #stats-page::after,
body.private #stats-total,
body.private.disabled #page-info,
body.private.ignore #page-info,
body:not(.private) #idle-status,
body.private:not(.ignore) #idle-status {
  display: none; }

html,
body {
  padding: 0; }

body {
  overflow-x: hidden;
  width: 320px;
  color: var(--color-primary);
  font-size: var(--font-size-primary); }

main {
  min-height: 370px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

button {
  border: 0;
  color: var(--color-link);
  background: none;
  cursor: pointer; }

button[disabled] {
  cursor: default; }

header,
#notification,
main > *,
footer {
  padding: var(--padding-primary); }

#notification {
  padding-top: 0; }

header,
#page-status > div:not(.refresh-info),
footer {
  display: flex;
  flex-direction: row; }

header {
  align-items: center;
  justify-content: space-between; }

header img {
  height: 2.5em; }

#notification .content::before {
  --border-top-color: var(--color-default);
  display: block;
  overflow: hidden;
  height: var(--border-width-thick-doubled);
  margin-right: var(--border-width-thin-negative);
  margin-left: var(--border-width-thin-negative);
  border: 0;
  border-top: var(--border-width-thick) var(--border-style-primary) var(--border-top-color);
  border-radius: var(--border-radius-primary);
  content: ""; }

#notification .content.critical::before {
  --border-top-color: var(--color-critical); }

#notification .content.information::before {
  --border-top-color: var(--color-info); }

#notification .content,
main .card {
  border-width: var(--border-width-thin);
  border-style: var(--border-style-primary);
  border-color: var(--border-color-primary);
  border-radius: var(--border-radius-primary);
  box-shadow: var(--box-shadow-primary); }

#notification .content {
  border-top: 0; }

#notification .content > div {
  padding: var(--padding-primary);
  padding-top: var(--padding-secondary); }

#notification .content h3[hidden] + p {
  margin-top: 0; }

#notification .content h3 {
  margin: 0;
  color: var(--color-secondary);
  font-size: var(--font-size-primary);
  background-repeat: no-repeat;
  background-size: 32px; }

#notification .content h3 > span {
  display: inline-flex;
  min-height: 32px;
  align-items: center; }

html:not([dir="rtl"]) #notification .content h3 {
  padding-left: 36px;
  margin-left: -6px;
  background-position: left; }

html[dir="rtl"] #notification .content h3 {
  padding-right: 36px;
  margin-right: -6px;
  background-position: right; }

#notification .content.critical h3 {
  background-image: url(icons/critical.svg); }

#notification .content.information h3 {
  background-image: url(icons/info.svg); }

#notification .content hr {
  height: var(--border-width-thin);
  border: 0;
  background: var(--border-color-primary); }

#notification .content button {
  display: block;
  padding-right: 0;
  padding-left: 0;
  font-size: var(--font-size-primary); }

#notification .content button {
  margin-top: var(--margin-primary-half); }

#options {
  width: var(--font-size-big);
  height: var(--font-size-big);
  border: 0;
  opacity: 0.5;
  background-image: url(icons/gear.svg?background#background);
  background-repeat: no-repeat;
  transition: opacity 0.1s ease-in;
  flex-shrink: 0; }

#options:hover {
  opacity: 1; }

#idle-status {
  margin: var(--margin-primary) 0;
  text-align: center; }

#idle-status img {
  width: 200px;
  height: 200px; }

#page-status {
  height: var(--page-status-height, auto);
  will-change: height;
  padding-top: 0;
  padding-bottom: 0;
  transition: height 0.3s ease-out; }

h2 {
  color: var(--color-dimmed);
  font-size: var(--font-size-small);
  letter-spacing: 0.5px;
  text-transform: uppercase; }

#page-status h2 {
  margin-bottom: 1em; }

#page-status h3 {
  margin: 0;
  color: var(--color-secondary); }

#page-status .domain {
  --toggle-width: 50px; }

#page-status .page {
  --toggle-width: 35px; }

#page-status .domain io-circle-toggle,
#page-status .page io-circle-toggle {
  width: var(--toggle-width); }

#page-status .details {
  flex-grow: 1;
  width: calc(100% - var(--toggle-width)); }

#page-status .page io-circle-toggle {
  padding: 1em 0; }

#page-status .page {
  margin-bottom: var(--margin-primary); }

#page-status .page .details {
  padding: 1em var(--padding-primary); }

html:not([dir="rtl"]) #page-status .page {
  border-left: 4px solid #F7F7F7; }

html[dir="rtl"] #page-status .page {
  border-right: 4px solid #F7F7F7; }

#blocking-domain,
#blocking-page {
  overflow: hidden;
  margin-top: var(--margin-primary-half);
  white-space: nowrap;
  text-overflow: ellipsis; }

#blocking-page {
  margin-bottom: 0; }

a,
a:hover,
a:visited,
a:active {
  color: var(--color-link);
  text-decoration: none; }

body:not(.refresh) #page-refresh {
  display: none; }

#page-refresh .card span {
  display: block;
  padding: 0 var(--padding-primary); }

#page-refresh .card strong {
  display: block;
  margin-bottom: var(--margin-primary-half); }

#page-refresh .card button {
  width: 100%;
  margin-top: var(--margin-primary);
  padding: var(--padding-secondary);
  border: 1px solid #0497E1;
  border-radius: 4px;
  background-color: #EDF9FF;
  text-align: center;
  font-weight: 600; }

#page-info,
#counter-panel {
  padding-top: var(--padding-secondary); }

#page-info,
#page-refresh {
  background-color: white; }

#page-info #block-element-container,
#page-info.blocking #default-container {
  display: none; }

#page-info.blocking #block-element-container,
#page-info #default-container {
  display: block; }

.refresh #page-info {
  display: none; }

.refresh #page-refresh {
  text-align: center; }

.refresh #page-refresh .card {
  animation: refresh-scale 0.5s ease-in-out; }

@keyframes refresh-scale {
  0% {
    transform: scale(1); }
  50% {
    transform: scale(0.9); }
  100% {
    transform: scale(1); } }

main .card {
  margin-top: var(--margin-primary-half);
  padding: var(--padding-primary);
  background-color: var(--background-color-primary); }

main .card strong {
  color: var(--color-secondary); }

main .options button {
  width: 100%;
  padding: var(--padding-secondary);
  border-radius: var(--border-radius-primary);
  font-size: var(--font-size-small);
  text-align: center;
  transition: border 0.3s ease-in; }

#block-element, #block-page {
  width: 100%;
  height: 32px;
  background: #0F7CF4;
  color: white;
  text-align: center;
  border-radius: 4px; }

#block-element > *,
#block-page > * {
  display: block; }

#block-element > strong,
#block-page > strong {
  display: flex;
  justify-content: center;
  font-size: var(--font-size-small);
  font-weight: normal; }

main .options button[disabled] {
  opacity: 0.5; }

footer > span {
  flex-grow: 1; }

footer > button {
  width: var(--font-size-primary);
  margin-right: var(--margin-primary-half);
  margin-left: var(--margin-primary-half);
  opacity: 0.5;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transition: opacity 0.1s ease-in; }

footer > button:hover {
  opacity: 1; }

footer > button.apple {
  background-image: url(icons/apple.svg); }

footer > button.android {
  background-image: url(icons/android.svg); }

#counter-panel {
  margin-top: var(--margin-secondary);
  padding-bottom: 0; }

#counter-panel .stats {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-bottom: 16px; }

#stats-page {
  position: relative; }

#stats-page::after {
  position: absolute;
  top: calc(50% - 10px);
  width: 1px;
  height: 20px;
  background-color: var(--border-color-primary);
  content: ""; }

html:not([dir="rtl"]) #stats-page::after {
  right: 0; }

html[dir="rtl"] #stats-page::after {
  left: 0; }

#stats-page,
#stats-total {
  flex-grow: 1;
  text-align: center; }

#stats-page strong,
#stats-total strong {
  display: block;
  font-size: 1.4em; }

#counter-panel .share {
  --padding: 4px;
  overflow: hidden;
  position: relative;
  padding: var(--padding);
  border-top: 1px solid var(--border-color-primary); }

#counter-panel .share .stack {
  display: flex;
  margin-bottom: var(--padding);
  justify-content: center;
  transition: transform 0.5s cubic-bezier(0.8, 0, 0.25, 1); }

#counter-panel .share.expanded .stack {
  transform: translateY(calc(-1 * 100% - var(--padding))); }

#counter-panel .share .links {
  position: absolute;
  align-items: center;
  width: 100%;
  height: calc(100% - 3 * var(--padding)); }

#counter-panel .links .link,
#counter-panel .links .link a {
  display: flex;
  justify-content: center; }

#counter-panel .links .link a {
  width: 20px; }

#counter-panel .links .link a img {
  margin: auto; }

html:not([lang^="zh"]) #counter-panel .links .link:nth-of-type(2),
html[lang^="zh"] #counter-panel .links .link:last-of-type {
  flex-grow: 1;
  justify-content: flex-start; }

#counter-panel .links .link:first-of-type {
  flex-grow: 1;
  justify-content: flex-end; }

html:not([dir="rtl"]) #counter-panel .links .link + .link {
  margin-left: 4px; }

html[dir="rtl"] #counter-panel .links .link + .link {
  margin-right: 4px; }

#counter-panel .share:not(.expanded) .link a {
  transform: translateY(38px); }

#counter-panel .share .cancel {
  opacity: 0;
  transition: opacity 0.3s ease-out 0.8s; }

#counter-panel .share.expanded .cancel {
  opacity: 1; }

#counter-panel .share a.facebook {
  transition: transform 0.3s ease-out 0.3s; }

#counter-panel .share a.twitter {
  transition: transform 0.3s ease-out 0.5s; }

#counter-panel .share a.weibo {
  transition: transform 0.3s ease-out 0.7s; }

#counter-panel .share .enter,
#counter-panel .links a,
#counter-panel .links button {
  color: var(--color-link); }

#counter-panel .share .enter:focus,
#counter-panel .links a:focus,
#counter-panel .links button:focus {
  outline: var(--primary-outline);
  animation: 0.3s ease-out 0s 1 outline-in; }

#counter-panel .share .enter > * {
  vertical-align: middle; }

@keyframes outline-in {
  from {
    outline: transparent dotted 1px; }
  to {
    outline: var(--primary-outline); } }

html:not([lang^="zh"]) #counter-panel .links .link:last-of-type {
  display: none; }

.dm-hidden {
  display: none !important; }

#state-area {
  text-align: center;
  font-size: 14px;
}

#state-desc {
  margin-top: 10px;
  margin-bottom: 10px;
  color: #999; }

#page-info {
  padding-top: 0; }

#stop-action-block, #start-action-block, #back-btn, #block-element-cancel {
  width: 100%;
  height: 32px;
  background: #0F7CF4;
  color: white;
  text-align: center;
  border-radius: 4px; }

#stop-action-block, #block-element-cancel {
  background: #FF4D4F; }

#auth-tips, .btn-tips {
  margin-top: 18px;
  color: #999; }
  #auth-tips > img, .btn-tips > img {
    height: 18px;
    vertical-align: top; }

#no-auth-view {
  position: absolute;
  top: 0;
  left: 100%;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: white;
  transition: left 400ms; }
  #no-auth-view.show {
    left: 0; }
  #no-auth-view > h2 {
    margin-top: 16px;
    font-size: 16px; }

.history-table {
  padding: 0;
  margin: var(--padding-primary);
  border: 1px solid #ddd; }
  .history-table .table-head, .history-table .table-row {
    display: flex;
    height: 24px;
    line-height: 24px;
    border-bottom: 1px solid #ddd; }
    .history-table .table-head:last-child, .history-table .table-row:last-child {
      border-bottom: 0; }
    .history-table .table-head .table-cell, .history-table .table-row .table-cell {
      flex: 0 0 50px;
      padding: 0 10px;
      border-right: 1px solid #ddd;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap; }
      .history-table .table-head .table-cell:last-child, .history-table .table-row .table-cell:last-child {
        flex: 1;
        border-right: 0; }
  .history-table .table-head {
    background-color: #fafafa; }
  .history-table .table-body {
    height: 120px;
    overflow: auto; }
