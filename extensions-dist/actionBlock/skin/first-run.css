/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */
/*******************************************************************************
 * General
 ******************************************************************************/
:root {
  --background-color-error: #F7DDE1;
  --background-color-secondary: #f7f7f7;
  --background-color-primary: #fff;
  --background-color-ternary: #edf9ff;
  --border-color-secondary: #d2d2d2;
  --border-color-primary: #e6e6e6;
  --border-color-ternary: #c0e6f9;
  --border-color-outline: #acacac;
  --border-radius: 4px;
  --border-radius-primary: 6px;
  --border-style-primary: solid;
  --border-width-thick: 4px;
  --border-width-thin: 1px;
  --box-shadow-primary: 0 2px 4px 0 hsla(0, 0%, 84%, 0.5);
  --color-brand-primary: #ED1E45;
  --color-primary: #585858;
  --color-secondary: #000;
  --color-dimmed: #4A4A4A;
  --color-critical: var(--color-brand-primary);
  --color-default: #FF8F00;
  --color-error: var(--color-brand-primary);
  --color-link: #0797E1;
  --color-info: #0797E1;
  --color-tag: #07E1CF;
  --font-size-big: 17px;
  --font-size-medium: 16px;
  --font-size-primary: 13px;
  --font-size-small: 12px;
  --margin-primary: 16px;
  --margin-secondary: calc(var(--margin-primary) / 2);
  --padding-primary: 16px;
  --padding-secondary: calc(var(--padding-primary) / 2);
  --primary-outline: var(--border-color-outline) dotted 1px; }

* {
  box-sizing: inherit;
  font-size: 16px; }

html,
body,
h1,
h2,
a,
p,
span,
small,
strong,
ol,
li,
div,
section,
main,
nav,
header,
footer,
img {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline; }

html {
  box-sizing: border-box;
  color: #212121;
  background-color: #fff;
  font-family: "Source Sans Pro", Arial, sans-serif;
  line-height: 1.5; }

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  margin: 0;
  /* Start below fixed navbar */
  padding-top: 4em;
  line-height: 1.5; }

main {
  flex-grow: 1; }

h1 {
  font-size: 2em;
  font-weight: 700;
  text-transform: uppercase; }

h2 {
  margin: 2rem 0 1rem;
  font-weight: 700;
  font-size: 1.4em; }

ol {
  padding-left: 1.5em; }

[dir="rtl"] ol {
  padding-right: 2em;
  padding-left: 0; }

li {
  margin: 0.25em 0; }

strong {
  font-weight: bolder; }

small {
  font-size: smaller; }

a,
a:visited {
  color: inherit;
  text-decoration: none;
  cursor: pointer; }

a:hover,
a:active,
a:focus {
  text-decoration: underline; }

a:link,
a:visited {
  color: #555; }

img {
  max-width: 100%; }

a img {
  border: none; }

.container,
.navbar-container {
  width: 1140px;
  max-width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1em;
  padding-left: 1em; }

/* Clear without collapsed margin */
.content::after,
.content::before {
  display: table;
  content: " "; }

.content::after {
  clear: both; }

ol,
.content p {
  margin: 1em 0; }

.content a,
.content a:visited {
  color: var(--color-brand-primary); }

.column {
  float: left;
  position: relative;
  width: 100%;
  min-height: 1px;
  padding: 0 1em; }

[dir="rtl"] .column {
  float: right; }

.column > header > img {
  width: 70px;
  height: 70px; }

.column > header > h2 {
  text-transform: uppercase; }

section {
  margin-top: 50px;
  margin-bottom: 20px; }

section > p {
  font-weight: 300; }

section > p a {
  font-weight: 400; }

#content {
  padding-bottom: 2em; }

@media (min-width: 768px) {
  h2 {
    font-size: 1.6em; } }

@media (min-width: 992px) {
  .one-third {
    width: 33.333333%; }
  .two-thirds {
    width: 66.666667%; } }

@media (max-width: 992px) {
  section {
    margin: 0; }
  .one-third {
    padding: 50px 0;
    border-bottom: 1px solid #d3d3d3; }
  .one-third:last-child {
    border-bottom: none; } }

/*******************************************************************************
 * Button styles taken from landing.scss
 ******************************************************************************/
:root {
  --color-content: #FFF;
  --color-primary: #0797E1;
  --color-primary-hover: #0789CA; }

a.button {
  display: inline-block;
  padding: 1em 2.5em;
  border: 3px solid var(--color-content);
  border-radius: 0.5em;
  font-weight: 600;
  color: var(--color-content);
  background-color: var(--color-primary);
  text-transform: uppercase;
  text-decoration: none;
  transition: all 200ms; }

a.button:hover {
  background-color: var(--color-primary-hover); }

/*******************************************************************************
 * Navbar
 ******************************************************************************/
#navbar {
  position: fixed;
  z-index: 2;
  top: 0;
  width: 100%;
  min-height: 4em;
  background-color: #FFF; }

#navbar .navbar-container {
  display: flex;
  padding-right: 0;
  padding-left: 0;
  align-items: center;
  justify-content: space-between; }

#navbar-logo {
  padding: 0 1em;
  color: #fff; }

#navbar-logo:active,
#navbar-logo:focus,
#navbar-logo:hover {
  text-decoration: none; }

#navbar-logo img {
  display: block;
  height: 4em;
  margin: 0 1em 0 0;
  padding: 0.5em 0; }

[dir="rtl"] #navbar-logo > img {
  margin: 0 0 0 1em; }

#navbar-logo > span {
  font-size: 1.375em;
  /* full-height: 2.91 * 1.375 = ~4em */
  line-height: 2.91em; }

#navbar-donate {
  width: auto;
  padding-top: 0.8em;
  padding-bottom: 0.8em;
  background-color: var(--color-brand-primary); }

#navbar-donate:hover {
  background-color: #AE0013; }

@media (max-width: 991px) {
  #navbar {
    overflow: auto; } }

/*******************************************************************************
 * Content
 ******************************************************************************/
.store-buttons {
  display: flex;
  justify-content: space-between; }

.store-button > img {
  height: 40px; }

#installation-status {
  padding: 20px;
  background-color: #ececec;
  text-align: center; }

#installation-status > img {
  width: 60px;
  height: 60px; }

#installation-status > p {
  font-weight: 300; }

#warnings > section {
  padding: 30px;
  border: 1px solid var(--color-error); }

#warnings > section > p:first-child {
  margin-top: 0; }

#warnings > section > p:last-child {
  margin-bottom: 0; }

body[data-warnings] > main > header,
body:not([data-warnings~="dataCorrupted"]) [data-warning="dataCorrupted"],
body:not([data-warnings~="reinitialized"]) [data-warning="reinitialized"],
html[data-application="edge"] #abb-promotion {
  display: none; }

@media (max-width: 992px) {
  #warnings > section {
    margin-top: 50px; } }

/*******************************************************************************
 * Footer
 ******************************************************************************/
#footer {
  overflow: auto;
  padding: 2em 0;
  color: #ececec;
  background-color: #292929;
  font-size: 0.9em; }

#footer a {
  color: #ececec; }

#footer a:hover,
#footer a:active,
#footer a:focus {
  color: #fff;
  text-decoration: underline; }

@media (min-width: 1200px) {
  #copyright-notice {
    float: left; }
  [dir="rtl"] #copyright-notice {
    float: right; } }
