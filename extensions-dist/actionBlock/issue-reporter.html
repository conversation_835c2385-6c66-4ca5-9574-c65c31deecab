<!DOCTYPE html>
<!--
  - This file is part of Adblock Plus <https://adblockplus.org/>,
  - Copyright (C) 2006-present eyeo GmbH
  -
  - Adblock Plus is free software: you can redistribute it and/or modify
  - it under the terms of the GNU General Public License version 3 as
  - published by the Free Software Foundation.
  -
  - Adblock Plus is distributed in the hope that it will be useful,
  - but WITHOUT ANY WARRANTY; without even the implied warranty of
  - MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  - GNU General Public License for more details.
  -
  - You should have received a copy of the GNU General Public License
  - along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
  -->

<html>
  <head>
    <meta charset="utf-8">
    <script defer src="polyfill.js"></script>
    <script defer src="ext/common.js"></script>
    <script defer src="ext/content.js"></script>
    <script defer src="common.js"></script>
    <script defer src="i18n.js"></script>
    <script defer src="issue-reporter.js"></script>
    <link rel="stylesheet" type="text/css" href="skin/common.css">
    <link rel="stylesheet" type="text/css" href="skin/fonts/font.css">
    <link rel="stylesheet" type="text/css" href="skin/issue-reporter.css">
    <title data-i18n="issueReporter_page_title"></title>
  </head>
  <body>
    <!-- Notification -->
    <div id="notification">
      <span id="notification-text" data-i18n="issueReporter_data_gathering_tab"></span>
      <button class="icon close tertiary" id="hide-notification">
        <span data-i18n="common_notification_hide" class="sr-only"></span>
      </button>
    </div>
    <header>
      <div class="logo">
        <img id="logo" alt="" src="skin/icons/logo/abp-full.svg">
        <p data-i18n="issueReporter_page_title"></p>
      </div>
      <io-steps i18n-labels="
        issueReporter_selectIssueButton_label
        issueReporter_markIssueButton_label
        issueReporter_detailsButton_label
        issueReporter_doneButton_label
      "></io-steps>
    </header>

    <main>

      <div class="page" id="typeSelectorPage">
        <span id="sr-warning" tabindex="0" class="sr-only" data-i18n="issueReporter_screenreader_warning"></span>

        <h1 data-i18n="issueReporter_typeSelector_heading"></h1>
        <p data-i18n="issueReporter_typeSelector_description"></p>
        <div id="typeSelectorGroup">
          <input type="radio" id="typeFalsePositive" value="false positive" name="type">
          <label for="typeFalsePositive" data-i18n="issueReporter_falsePositive_label"></label>
          <p data-i18n="issueReporter_falsePositive_description"></p>
          <input type="radio" id="typeFalseNegative" value="false negative" name="type">
          <label for="typeFalseNegative" data-i18n="issueReporter_falseNegative_label"></label>
          <p data-i18n="issueReporter_falseNegative_description"></p>
        </div>
      </div>

      <div class="page" id="highlighterPage" hidden>
        <h1 data-i18n="issueReporter_highlighter_heading"></h1>
        <p data-i18n="issueReporter_highlighter_description"></p>
      </div>
      <div class="page" id="commentPage" hidden>
        <h1 data-i18n="issueReporter_commentPage_heading"></h1>

        <p data-i18n="issueReporter_email_description"></p>

        <label for="email" data-i18n="issueReporter_email_label"></label>
        <input type="email" id="email" maxlength="200">

        <div id="anonymousSubmissionContainer">
          <input type="checkbox" id="anonymousSubmission">
          <label for="anonymousSubmission" data-i18n="issueReporter_anonymousSubmission_label"></label>
          <div id="anonymousSubmissionWarning" data-invisible="true" data-i18n="issueReporter_anonymousSubmission_warning"></div>
        </div>

        <label for="comment" data-i18n="issueReporter_comment_label"></label>
        <p data-i18n="issueReporter_comment_description"></p>
        <textarea id="comment"></textarea>
        <p id="includeConfigContainer">
          <input id="includeConfig" type="checkbox">
          <label for="includeConfig" data-i18n="issueReporter_config_label"></label>
        </p>
        <p>
          <button id="showData" data-i18n="issueReporter_showData_label" class="link" disabled></button>
        </p>
      </div>

      <div class="page" id="sendPage" hidden>
        <h1 data-i18n="issueReporter_sendPage_heading"></h1>

        <p id="sendReportMessage" data-i18n="issueReporter_sending"></p>

        <div id="sendingProgressContainer">
          <progress id="sendingProgress"></progress>
        </div>

        <p id="error" hidden></p>

        <iframe id="result" sandbox="allow-top-navigation" hidden></iframe>
      </div>

      <div class="modal" id="showDataOverlay" hidden>
        <div class="modalContent">
          <pre id="showDataValue" tabindex="0"></pre>
          <button id="showDataClose" data-i18n="cancel" class="primary"></button>
        </div>
      </div>
    </main>

    <footer>
      <div>
        <div>
          <a id="privacyPolicy" data-i18n="issueReporter_privacyPolicy" data-doclink="reporter_privacy"></a>
        </div>
        <button id="cancel" data-i18n="cancel" class="secondary"></button>
        <button id="continue" data-i18n="continue" class="primary" disabled></button>
        <button id="send" data-i18n="issueReporter_sendButton_label" class="primary" disabled hidden></button>
      </div>
      <div id="other-issues" data-i18n="issueReporter_other_issues"></div>
    </footer>
  </body>
</html>
