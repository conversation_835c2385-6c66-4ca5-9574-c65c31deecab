/*
 * This file is part of Adblock Plus <https://adblockplus.org/>,
 * Copyright (C) 2006-present eyeo GmbH
 *
 * Adblock Plus is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * Adblock Plus is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
 */

/* globals getErrorMessage */

'use strict';

let initialFilterText = '';
let targetPageId = null;
let href = '';
let currentModal = 'element';
const { stripTagsUnsafe } = ext.i18n;

let hearbeat_interval = 0;

function onKeyDown(event) {
  if (event.keyCode == 27) {
    event.preventDefault();
    closeDialog();
  }
}

function doRequest(blockElementVo) {
  try {
    browser.runtime.sendMessage({
      type: 'remote.addActionBlockRule',
      blockElementVo,
    });
  } catch (e) {
    console.error(e);
  }
}

function addFilters() {
  const commentMsg = document.getElementById('filters-comment').value;
  const textareaValue = document.getElementById('filters').value;
  const blockElementVo = {
    description: commentMsg.slice(0, 128),
    enabled: true,
    url: href,
    wholePage: false,
    element: '',
  };
  if (currentModal === 'page' && href) {
    // 更新到服务端
    blockElementVo.wholePage = true;
    blockElementVo.element = '';
    doRequest(blockElementVo);
    closeDialog(false);
    return;
  } else {
    const filters = textareaValue.split('\n');
    for (const filter of filters) {
      blockElementVo.element = filter;
      doRequest(blockElementVo);
    }
  }
  browser.runtime
    .sendMessage({
      type: 'filters.importRaw',
      text: textareaValue,
    })
    .then((errors) => {
      if (errors.length > 0) {
        errors = errors.map(getErrorMessage);
        alert(stripTagsUnsafe(errors.join('\n')));
      } else closeDialog(true);
    });
}

// We'd rather just call window.close, but that isn't working consistently with
// Firefox 57, even when allowScriptsToClose is passed to browser.windows.create
// See https://bugzilla.mozilla.org/show_bug.cgi?id=1418394
// window.close is also broken on Firefox 63.x
// See https://gitlab.com/eyeo/adblockplus/abpui/adblockplusui/-/issues/791#note_374617568
function closeMe() {
  clearInterval(hearbeat_interval);
  browser.runtime
    .sendMessage({
      type: 'app.get',
      what: 'senderId',
    })
    .then((tabId) =>
      browser.tabs.remove(tabId).catch((err) => {
        // Opera 68 throws a "Tabs cannot be edited right now (user may be
        // dragging a tab)." exception when we attempt to close the window
        // using `browser.tabs.remove`.
        window.close();
      }),
    );
}

function closeDialog(success = false) {
  document.getElementById('filters').disabled = true;
  browser.runtime
    .sendMessage({
      type: 'composer.forward',
      targetPageId,
      payload: {
        type: 'composer.content.finished',
        popupAlreadyClosed: true,
        remove: !!success,
      },
    })
    .then(() => {
      closeMe();
    });
}

function resetFilters() {
  browser.tabs
    .sendMessage(targetPageId, {
      type: 'composer.content.finished',
    })
    .then(() => {
      browser.tabs
        .sendMessage(targetPageId, {
          type: 'composer.content.startPickingElement',
        })
        .then(closeMe);
    });
}

function doHeartbeat() {
  let tabsCount = 0;
  chrome.tabs
    .query({})
    .then((tabs) => {
      for (let i = 0; i < tabs.length; i++) {
        let tab = tabs[i];
        if (tab.url && !/^chrome-extension:/.test(tab.url)) {
          tabsCount++;
        }
      }
      if (tabsCount == 0) {
        closeMe();
      }
    })
    .catch((err) => {
      console.error(err);
      closeMe();
    });
}

function previewFilters({ currentTarget }) {
  const { preview } = currentTarget.dataset;
  const wasActive = preview === 'active';

  const filtersTextArea = document.getElementById('filters');

  // if it is inactive, disable the textarea upfront
  if (!wasActive) filtersTextArea.disabled = true;

  browser.runtime
    .sendMessage({
      type: 'composer.forward',
      targetPageId,
      payload: {
        type: 'composer.content.preview',
        // toggle the preview mode
        active: !wasActive,
      },
    })
    .then(() => {
      // if it was active, it's now inactive so the area should be editable
      if (wasActive) filtersTextArea.disabled = false;

      // toggle both data-preview and the button message accordingly
      currentTarget.dataset.preview = wasActive ? 'inactive' : 'active';
      // currentTarget.textContent = browser.i18n.getMessage(
      //   wasActive ? 'composer_preview' : 'composer_undo_preview',
      // );
      if (wasActive) {
        currentTarget.textContent = isCn ? '预览' : 'Preview';
      } else {
        currentTarget.textContent = isCn ? '取消预览' : 'Exit Preview';
      }
    });
}

function updateComposerState({ currentTarget }) {
  const { value } = currentTarget;
  const disabled = !value.trim().length;
  document.getElementById('block').disabled = disabled;
  // document.getElementById("preview").disabled = initialFilterText !== value;
  document.getElementById('preview').disabled = disabled;
}

function init() {
  browser.runtime
    .sendMessage({
      type: 'remote.getLanguage',
    })
    .then((language) => {
      window.isCn = (language || 'zh').startsWith('zh');
      document.title = isCn ? 'ActionBlock采集器' : 'ActionBlock Collector';
      document.querySelector('.i18n-block-node').innerText = isCn
        ? '屏蔽当前选中的节点'
        : 'Hide selected Element';
      document.querySelector('.i18n-block-page').innerText = isCn
        ? '屏蔽整个页面'
        : 'Block whole page';
      document.querySelector('.i18n-preview').innerText = isCn ? '预览' : 'Preview';
      document.querySelector('.i18n-ok').innerText = isCn ? '确定' : 'OK';
      document.querySelector('.i18n-cancel').innerText = isCn ? '取消' : 'Cancel';
      document.querySelector('.i18n-comment-title').innerText = isCn
        ? '可填写备注以供备忘：'
        : 'Input comment:';
      document
        .querySelector('.i18n-comment-placeholder')
        .setAttribute('placeholder', isCn ? '不超过128个字符' : 'No more than 128 characters');
      document.querySelector('.i18n-finish').innerText = isCn ? '完成' : 'Finish';
      document.querySelector('.i18n-discard').innerText = isCn ? '放弃' : 'Discard';
    });
  document.title = 'ActionBlock采集器';
  const selectorForm = document.getElementById('selector-form');
  const commentForm = document.getElementById('comment-form');
  const commentTextArea = document.getElementById('filters-comment');
  const submit = document.getElementById('submit');
  // Attach event listeners
  window.addEventListener('keydown', onKeyDown, false);

  const block = document.getElementById('block');
  block.addEventListener('click', () => {
    selectorForm.classList.add('dm-hidden');
    commentForm.classList.remove('dm-hidden');
    commentTextArea.focus();
  });

  const preview = document.getElementById('preview');
  preview.addEventListener('click', previewFilters);

  const filtersTextArea = document.getElementById('filters');
  filtersTextArea.addEventListener('input', updateComposerState);

  document.getElementById('unselect').addEventListener('click', resetFilters);
  document.getElementById('cancel').addEventListener('click', closeDialog.bind(null, false));
  document.getElementById('close').addEventListener('click', closeDialog.bind(null, false));

  // radio 选择器
  document.querySelectorAll('#modal-selector input').forEach((elm) => {
    elm.addEventListener('change', (e) => {
      currentModal = e.target.value;
      filtersTextArea.disabled = currentModal === 'page';
      // 退出预览
      if (preview.dataset.preview === 'active') {
        preview.click();
      }
      setTimeout(() => {
        browser.tabs.sendMessage(targetPageId, {
          type: 'composer.content.pickElement',
          isBody: currentModal === 'page',
        });
      }, 500);
    });
  });

  commentTextArea.addEventListener('input', (e) => {
    const disabled = e.currentTarget.value.trim().length > 120;
    submit.disabled = disabled;
  });

  submit.addEventListener('click', () => {
    addFilters();
  });

  ext.onMessage.addListener((msg, sender, sendResponse) => {
    switch (msg.type) {
      case 'composer.dialog.init':
        targetPageId = msg.sender;
        initialFilterText = msg.filters.join('\n');
        href = msg.href;
        filtersTextArea.value = initialFilterText;
        filtersTextArea.disabled = false;
        preview.disabled = false;
        block.disabled = false;
        block.focus();
        document.getElementById('selected').dataset.count = msg.highlights;

        // Firefox sometimes tells us this window had loaded before it has[1],
        // to work around that we send the "composer.dialog.init" message again
        // when sending failed. Unfortunately sometimes sending is reported as
        // successful when it's not, but with the response of `undefined`. We
        // therefore send a response here, and check for it to see if the
        // message really was sent successfully.
        // [1] - https://bugzilla.mozilla.org/show_bug.cgi?id=1418655
        sendResponse(true);

        hearbeat_interval = setInterval(() => {
          doHeartbeat();
        }, 1000);
        break;
      case 'composer.dialog.close':
        closeMe();
        break;
    }
  });

  window.removeEventListener('load', init);
}

window.addEventListener('load', init, false);

window.resizeTo(600, 250);
