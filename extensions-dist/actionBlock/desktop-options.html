<!DOCTYPE html>
<!--
  - This file is part of Adblock Plus <https://adblockplus.org/>,
  - Copyright (C) 2006-present eyeo GmbH
  -
  - Adblock Plus is free software: you can redistribute it and/or modify
  - it under the terms of the GNU General Public License version 3 as
  - published by the Free Software Foundation.
  -
  - Adblock Plus is distributed in the hope that it will be useful,
  - but WITHOUT ANY WARRANTY; without even the implied warranty of
  - MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  - GNU General Public License for more details.
  -
  - You should have received a copy of the GNU General Public License
  - along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
  -->

<html>
  <head>
    <meta charset="utf-8">
    <title data-i18n="options_page_document_title"></title>
    <link rel="stylesheet" href="skin/common.css">
    <link rel="stylesheet" href="skin/fonts/font.css">
    <link rel="stylesheet" href="skin/desktop-options.css">
    <script src="polyfill.js"></script>
    <script src="ext/common.js"></script>
    <script src="ext/content.js"></script>
    <script src="common.js"></script>
    <script src="i18n.js"></script>
    <script defer src="desktop-options.js"></script>
  </head>
  <body data-tab="general">
    <!-- Navigation sidebar -->
    <div id="sidebar">
      <div class="fixed">
        <header>
          <h1>Adblock Plus</h1>
          <p data-i18n="options_page_title"></p>
        </header>

        <nav>
          <ul class="tabs"
              role="tablist" data-action="switch-tab"
              data-keys="ArrowLeft ArrowUp ArrowRight ArrowDown">
            <li role="presentation">
              <a href="#general" data-i18n="options_tab_general" role="tab" aria-selected="true" tabindex="-1" aria-controls="content-general"></a>
            </li>
            <li role="presentation">
              <a href="#whitelist" data-i18n="options_tab_whitelist" role="tab" tabindex="-1" aria-controls="content-whitelist"></a>
            </li>
            <li role="presentation">
              <a href="#advanced" data-i18n="options_tab_advanced" role="tab" tabindex="-1" aria-controls="content-advanced"></a>
            </li>
            <li role="presentation">
              <a href="#help" data-i18n="options_tab_help" role="tab" tabindex="-1" aria-controls="content-help"></a>
            </li>
          </ul>
        </nav>

        <footer>
          <p>
            <a data-i18n="options_footer_contribute" class="button secondary"
              data-doclink="contribute"></a>
          </p>
          <p>
            <button data-i18n="options_footer_about" class="link" 
                    data-action="open-dialog" data-dialog="about"></button>
          </p>
        </footer>        
      </div>
    </div>
    <main>
      <!-- General tab content -->
      <div id="content-general" role="tabpanel" aria-labelledby="tab-general">
        <header>
          <h1 data-i18n="options_tab_general"></h1>
          <p data-i18n="options_general_description"></p>
        </header>
        <section class="cols">
          <h2 data-i18n="options_recommended_filters_header"></h2>
          <ul class="list" id="recommended-list-table">
            <template>
              <button data-action="toggle-remove-subscription" data-disable="preconfigured" role="checkbox" class="control icon"></button>
              <span data-display></span>
              <io-popout type="tooltip" data-template-i18n-body="options_recommended_%value%_tooltip"></io-popout>
              <span class="new-tag" data-i18n="options_new_label"></span>
            </template>
          </ul>
        </section>
        <section class="cols">
          <header>
            <h2 data-i18n="options_acceptableAds_header"></h2>
            <p data-i18n="options_acceptableAds_description"></p>
          </header>
          <form id="acceptable-ads">
            <div id="tracking-warning">
              <button id="hide-tracking-warning" class="icon close tertiary" type="button" data-pref="ui_warn_tracking" data-action="toggle-pref">
                <span data-i18n="common_notification_hide" class="sr-only"></span>
              </button>
              <p id="tracking-warning-1"></p>
              <p data-i18n="options_tracking_warning_2"></p>
              <p id="tracking-warning-3"></p>
              <button data-i18n="options_tracking_warning_acknowledgment" class="link" type="button" data-pref="ui_warn_tracking" data-action="toggle-pref"></button>
            </div>
            <ul>
              <li>
                <button id="acceptable-ads-allow" class="icon" data-action="switch-acceptable-ads" type="button" role="checkbox" name="acceptable-ads" value="ads"></button>
                <div id="acceptable-ads-why-not" role="dialog" aria-hidden="true">
                  <p data-i18n="options_aa_opt_out_survey"></p>
                  <a class="button primary" target="_blank" data-i18n="options_aa_opt_out_survey_ok" data-action="hide-acceptable-ads-survey"></a>
                  <button type="button" data-i18n="options_aa_opt_out_survey_no" data-action="hide-acceptable-ads-survey"></button>
                </div>
                <label for="acceptable-ads-allow" data-i18n="options_acceptableAds_ads_label"></label>
                <p id="enable-acceptable-ads-description" data-i18n="options_acceptableAds_ads_description_1"></p>
              </li>
              <ul>
                <li>
                  <button id="acceptable-ads-privacy-allow" class="icon" data-action="switch-acceptable-ads" type="button" role="checkbox" name="acceptable-ads" value="privacy"></button>
                  <label for="acceptable-ads-privacy-allow" data-i18n="options_acceptableAds_privacy_label"></label>
                  <p>
                    <a data-i18n="options_learn_more" data-doclink="privacy_friendly_ads"></a>
                  </p>
                  <p id="dnt" data-i18n="options_acceptableAds_dnt_notification"></p>
                </li>
              </ul>
            </ul>
          </form>
        </section>
        <section class="cols">
          <header>
            <h2 data-i18n="options_language_header"></h2>
            <p data-i18n="options_language_description"></p>
          </header>
          <div id="blocking-languages">
            <ul id="blocking-languages-table" class="table list bottom-control">
              <template>
                <span>
                  <span data-display="title"></span>
                  <span class="dimmed">
                    (<span data-display="originalTitle"></span>)
                  </span>
                </span>
                <button data-single="hidden" data-hide="preconfigured" data-action="remove-subscription" class="icon delete control" data-i18n-title="options_control_remove_title"></button>
                <button data-single="visible" data-hide="preconfigured" data-action="open-languages-box" data-dialog="language-change" data-i18n="options_language_change" class="link"></button>
              </template>
            </ul>
            <io-list-box
              id="languages-box"
              data-text="options_language_add"
              data-expanded="options_dialog_language_title"
              autoclose
            ></io-list-box>
            <p data-i18n="options_language_tip"></p>
          </div>
        </section>
        <section id="more-filters" class="cols">
          <header>
            <h2 data-i18n="options_more_filters_header"></h2>
            <p data-i18n="options_more_filters_description"></p>
          </header>
          <div>
            <ul id="more-list-table" class="table list">
              <template>
                <span data-display="title"></span>
                <button data-hide="preconfigured" data-action="remove-subscription" data-i18n="options_control_remove_title" class="control link" data-i18n-title="options_control_remove_title"></button>
              </template>
            </ul>
            <p data-i18n="options_more_filters_note"></p>
          </div>
        </section>
      </div>

      <!-- Whitelist tab content -->
      <div id="content-whitelist" role="tabpanel" aria-labelledby="tab-whitelist">
        <header>
          <h1 data-i18n="options_tab_whitelist"></h1>
          <p>
            <span data-i18n="options_whitelist_description"></span>
            <a data-i18n="options_learn_more" data-doclink="whitelist"></a>
          </p>
        </header>
        <section>
          <form>
            <input id="whitelisting-textbox" type="text"
              data-i18n-placeholder="options_whitelist_placeholder_example">
            <button id="whitelisting-add-button"
                    type="submit"
                    data-action="add-domain-exception"
                    data-i18n="options_whitelist_add" class="primary" disabled>
            </button>
          </form>
          <ul id="whitelisting-table" class="table list">
            <template>
              <span data-display="title"></span>
              <button data-action="remove-filter" class="icon delete control"
                data-i18n-title="options_control_remove_title">
              </button>
            </template>
          </ul>
        </section>
      </div>

      <!-- Advanced tab content -->
      <div id="content-advanced" role="tabpanel"
          aria-labelledby="tab-advanced">
        <header>
          <h1 data-i18n="options_tab_advanced"></h1>
          <p data-i18n="options_advanced_description"></p>
        </header>
        <section class="cols">
          <h2 data-i18n="options_customize_header"></h2>
          <ul id="customize" class="list">
            <li data-pref="show_statsinicon">
              <button class="icon" role="checkbox" data-action="toggle-pref"></button>
              <span data-i18n="options_customize_iconStats" data-display="title"></span>
            </li>
            <li data-pref="shouldShowBlockElementMenu">
              <button class="icon" role="checkbox" data-action="toggle-pref"></button>
              <span data-i18n="options_customize_blockElement" data-display="title"></span>
              <io-popout type="tooltip" i18n-body="options_customize_blockElement_tooltip"></io-popout>
            </li>
            <li data-pref="show_devtools_panel">
              <button class="icon" role="checkbox" data-action="toggle-pref"></button>
              <span data-i18n="options_customize_showDevToolsPanel" data-display="title"></span>
              <io-popout type="tooltip" i18n-body="options_customize_showDevToolsPanel_tooltip"></io-popout>
            </li>
            <li data-pref="elemhide_debug">
              <button class="icon" role="checkbox" data-action="toggle-pref"></button>
              <span data-i18n="options_customize_elemhideDebug" data-display="title"></span>
              <io-popout type="tooltip" i18n-body="options_customize_elemhideDebug_tooltip"></io-popout>
            </li>
            <li data-pref="notifications_ignoredcategories">
              <button class="icon" role="checkbox" data-action="toggle-pref"></button>
              <span data-i18n="options_customize_showNotifications" data-display="title"></span>
              <io-popout type="tooltip" i18n-body="options_customize_showNotifications_tooltip"></io-popout>
            </li>
          </ul>
        </section>
        <section>
          <header>
            <h2 data-i18n="options_filterList_title"></h2>
            <p>
              <span data-i18n="options_filterList_description"></span>
              <a data-i18n="options_learn_more" data-doclink="subscriptions"></a>
            </p>
          </header>
          <div id="update-all-subscriptions">
            <button id="update" data-i18n="options_filterList_update" class="secondary" data-action="update-all-subscriptions"></button>
          </div>
          <div id="all-filter-lists-table-header" class="th">
            <h3 data-i18n="options_filterList_column_status" class="col5"></h3>
            <h3 data-i18n="options_filterList_column_name" class="col5"></h3>
            <h3 data-i18n="options_filterList_column_update" class="col5"></h3>
            <h3 class="col5"></h3>
            <h3 class="col5"></h3>
          </div>
          <ul class="table cols" id="all-filter-lists-table">
            <template>
              <div class="col5">
                <io-toggle class="control" data-action="toggle-disable-subscription"></io-toggle>
              </div>
              <div class="col5">
                <span data-display="originalTitle"></span>
              </div>
              <div class="col5">
                <span class="last-update"></span>
                <span class="message"></span>
              </div>
              <div class="col5">
                <io-popout type="menubar">
                  <ul class="content" role="menu">
                    <li role="menuitem">
                      <button data-i18n="options_filterList_updateNow" class="icon update-subscription" data-action="update-subscription"></button>
                    </li>
                    <li role="menuitem">
                      <a data-i18n="options_filterList_website" class="icon website" target="_blank"></a>
                    </li>
                    <li role="menuitem">
                      <a data-i18n="options_filterList_source" class="icon source" target="_blank"></a>
                    </li>
                  </ul>
                </io-popout>
              </div>
              <div class="col5">
                <button data-hide="preconfigured" data-action="remove-subscription" class="control icon delete"></button>
              </div>
            </template>
          </ul>
          <div class="side-controls wrap">
            <io-list-box
              id="filters-box"
              data-text="options_builtin_filterList_add"
              data-expanded="options_builtin_filterList_add"
              autoclose detached
            ></io-list-box>
            <div id="filterlist-by-url-wrap" tabindex="-1">
              <div id="filterlist-by-url" aria-hidden="true">
                <h3 data-i18n="options_filterList_add" class="add secondary"></h3>
                <form data-validation="custom" novalidate>
                  <p class="main-input">
                    <!--
                      Core doesn't tell us whether the URL is valid so we have
                      to determine that ourselves and check whether it's using
                      a supported protocol
                      https://gitlab.com/eyeo/adblockplus/adblockpluscore/blob/d3f6b1b7e3880eab6356b132493a4a947c87d33f/lib/downloader.js#L270
                    -->
                    <input id="import-list-url" type="url"
                      required pattern="^(?:data|https):.*$"
                      data-i18n-placeholder="options_dialog_import_subscription_location">
                    <span class="error-msg"></span>
                    <span class="icon attention"></span>
                  </p>
                  <p class="side-controls">
                    <button type="button" data-i18n="options_dialog_import_title" class="primary" data-action="validate-import-subscription"></button>
                    <button type="reset" data-i18n="options_customFilter_cancel" class="secondary" data-action="close-filterlist-by-url"></button>
                  </p>
                </form>
              </div>
              <button data-i18n="options_filterList_add" class="add secondary" data-action="open-filterlist-by-url"></button>
            </div>
          </div>
        </section>

        <section>
          <header>
            <h2 data-i18n="options_customFilters_title"></h2>
            <p id="custom-filters-description" data-i18n="options_customFilters_description"></p>
          </header>
          <div id="custom-filters">
            <h3>
              <span data-i18n="options_customFilters_widget_title" class="io-filter-table-title"></span>
            </h3>
            <io-filter-table></io-filter-table>
          </div>
        </section>
      </div>

      <!-- Help tab content -->
      <div id="content-help" role="tabpanel" aria-labelledby="tab-help">
        <header>
          <h1 data-i18n="options_tab_help"></h1>
          <p data-i18n="options_help_description"></p>
        </header>
        <section class="cols">
          <h2 data-i18n="options_support_title"></h2>
          <ul>
            <li id="help-center" data-i18n="options_help_center"></li>
            <li id="report-bug" data-i18n="options_report_bug"></li>
            <li id="visit-forum" data-i18n="options_report_forum"></li>
            <li>
              <span data-i18n="options_email"></span>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </li>
          </ul>
        </section>
        <section class="cols">
          <header>
            <h2 data-i18n="options_social_title"></h2>
            <p data-i18n="options_social_description"></p>
          </header>
          <div id="social">
            <ul>
              <li>
                <a class="icon twitter" data-doclink="social_twitter">
                  Twitter
                </a>
              </li>
              <li>
                <a class="icon facebook" data-doclink="social_facebook">
                  Facebook
                </a>
              </li>
            </ul>
          </div>
        </section>
      </div>
    </main>

    <!-- Additional sidebar -->
    <aside>
      <io-popout id="support-us" type="dialog" anchor-icon="heart">
        <img class="h2-icon" src="skin/icons/waving.svg">
        <h2 data-i18n="options_supportUs_title"></h2>
        <p data-i18n="options_supportUs_description"></p>
        <a data-i18n="options_rating_button" class="button" target="_blank"></a>
        <a data-i18n="options_donate_button" data-doclink="donate_settings_page" class="button" target="_blank"></a>
      </io-popout>
    </aside>

    <!-- Dialog -->
    <div id="dialog-background"></div>
    <div id="dialog" role="dialog" aria-modal="true" aria-hidden="true">
      <header>
        <span id="dialog-title">
          <h3 id="dialog-title-about" data-i18n="options_dialog_about_title"></h3>
          <h3 id="dialog-title-language-add" data-i18n="options_dialog_language_title"></h3>
          <h3 id="dialog-title-language-change" data-i18n="options_dialog_language_title"></h3>
          <h3 id="dialog-title-predefined" data-i18n="options_dialog_predefined_title"></h3>
          <h3 id="dialog-title-invalid" data-i18n="options_dialog_invalid_title"></h3>
        </span>
        <button class="icon close primary" data-action="close-dialog"></button>
      </header>
      <div id="dialog-body" class="content">
        <!-- About Adblock Plus -->
        <div id="dialog-content-about" class="dialog-content">
          <div id="dialog-description-about">
            <p id="abp-version"></p>
            <p id="copyright"></p>
            <p data-i18n="options_dialog_about_trademark"></p>
            <p>
              <a id="privacy-policy" data-i18n="issueReporter_privacyPolicy" target="_blank"></a>
            </p>
          </div>
          <p>
            <button data-i18n="options_close" class="primary default-focus" data-action="close-dialog"></button>
          </p>
        </div>
        <!-- Add language subscription -->
        <div id="dialog-content-language-add" class="dialog-content">
          <ul id="all-lang-table-add" class="table list default-focus">
            <template>
              <button data-action="add-language-subscription,close-dialog" class="control icon add" role="checkbox">
                <span>
                  <span data-display="title"></span>
                  <span class="dimmed">
                    (<span data-display="originalTitle"></span>)
                  </span>
                </span>
              </button>
              <button data-action="change-language-subscription,close-dialog" class="control icon change" role="checkbox">
                <span>
                  <span data-display="title"></span>
                  <span class="dimmed">
                    (<span data-display="originalTitle"></span>)
                  </span>
                </span>
              </button>
            </template>
          </ul>
        </div>
        <!-- Add predefined subscription -->
        <div id="dialog-content-predefined" class="dialog-content">
          <div id="dialog-description-predefined">
            <div class="field title">
              <h3 data-i18n="options_dialog_predefined_subscription_title"></h3>
              <span></span>
            </div>
            <div class="field url">
              <h3 data-i18n="options_dialog_predefined_subscription_url"></h3>
              <a href="#" target="_blank"></a>
            </div>
          </div>
          <button class="default-focus primary"
            data-action="add-predefined-subscription"
            data-i18n="options_dialog_predefined_confirm"></button>
          <button class="secondary" data-action="close-dialog"
            data-i18n="options_customFilter_cancel"></button>
        </div>
        <!-- Reject invalid subscriptions -->
        <div id="dialog-content-invalid" class="dialog-content">
          <div class="error">!</div>
          <div id="dialog-description-invalid"
            data-i18n="options_dialog_invalid_message"></div>
          <button class="default-focus primary" data-action="close-dialog"
            data-i18n="options_customFilter_cancel"></button>
        </div>
      </div>
      <!-- Placeholder element to determine when to wrap focus around -->
      <span class="focus-last" tabindex="0"></span>
    </div>
     <!-- Notification -->
    <div id="notification" aria-hidden="true" aria-live="polite">
      <strong id="notification-text"></strong>
      <button class="icon close secondary" data-action="hide-notification">
        <span data-i18n="common_notification_hide" class="sr-only"></span>
      </button>
    </div>
  </body>
</html>
