{"extDescription": {"message": "Build-in extension, used to implement the function shielding feature in advance prevention. It cannot be changed or removed."}, "add": {"description": "This is the label for the 'Add' buttons.", "message": "Add"}, "cancel": {"description": "Cancel button label", "message": "Cancel"}, "copy_selected": {"description": "Copy selected items button label", "message": "<PERSON><PERSON> selected"}, "delete": {"description": "Delete button label", "message": "Delete"}, "common_copyright": {"description": "https://gitlab.com/eyeo/specs/spec/blob/e84d55b6bdc36b9d4e25da8fb511af73e2d15231/spec/abp/first-run.md#footer, https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#day-1-footer", "message": "Copyright © $year$ All rights reserved. Adblock Plus® is a registered trademark of <a0>eyeo GmbH</a0>.", "placeholders": {"year": {"content": "$1", "example": "1984"}}}, "common_feature_anti_adblock_title": {"description": "Feature title for anti-adblock blocking filter list", "message": "Hide Adblock Warning Messages"}, "common_feature_circumvention_title": {"description": "Title for the Anti-Circumvention list", "message": "ABP Anti-Circumvention Filter List"}, "common_feature_malware_title": {"description": "Feature title for anti-malware filter list", "message": "Block Malware"}, "common_feature_privacy_title": {"description": "Feature title for privacy filter list", "message": "Block additional tracking"}, "common_feature_social_title": {"description": "Feature title for social media blocking filter list", "message": "Block social media icons tracking"}, "common_feature_notifications_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/52243bc8596b06107461034c745a6ecdaa42f1f6/spec/abp/desktop-settings/general-tab.md#list-of-recommended-filter-lists", "message": "Block push notifications"}, "common_feature_cookies_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/52243bc8596b06107461034c745a6ecdaa42f1f6/spec/abp/desktop-settings/general-tab.md#list-of-recommended-filter-lists", "message": "Block cookie warnings"}, "common_notification_hide": {"description": "Hidden text attached to the close button for screen readers of Notification", "message": "Close notification"}, "filter_action_failed": {"description": "One or more filters could not be saved or updated.", "message": "Something went wrong. Please try again."}, "block_element": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/56bac40a43fdb251d0cdaedf1de41d5c77458550/spec/abp/composer.md", "message": "Block element"}, "composer_loading": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/cccc985f35849ea071b0a2adc951699b47f1d134/spec/abp/composer.md#filter-area", "message": "Loading..."}, "composer_block": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/6077f8f4142302601ee1493b73c6e5b85b25ba9e/spec/abp/composer.md", "message": "Block"}, "composer_cancel": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/6077f8f4142302601ee1493b73c6e5b85b25ba9e/spec/abp/composer.md", "message": "Cancel"}, "composer_elements": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/6077f8f4142302601ee1493b73c6e5b85b25ba9e/spec/abp/composer.md", "message": "element(s) selected"}, "composer_unselect": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/6077f8f4142302601ee1493b73c6e5b85b25ba9e/spec/abp/composer.md", "message": "Unselect"}, "composer_preview": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/6077f8f4142302601ee1493b73c6e5b85b25ba9e/spec/abp/composer.md", "message": "Preview"}, "composer_undo_preview": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/6077f8f4142302601ee1493b73c6e5b85b25ba9e/spec/abp/composer.md", "message": "Exit preview"}, "day1_blockelement_description": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Block them with Adblock Plus's nifty block element feature."}, "day1_blockelement_more": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Learn how"}, "day1_blockelement_screenshot": {"description": "https://gitlab.com/eyeo/specs/spec/blob/f8c79649ef3837c77869ecb11749eb96cf565c15/spec/abp/notifications.md#day-1", "message": "Example of icon pop-up with \"Block element\" button highlighted."}, "day1_blockelement_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Tired of comment sections bringing you down?"}, "day1_community_contact": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Contact us!"}, "day1_community_contact_subject": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Looking for support!"}, "day1_community_description_1": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "As an open source project, we depend on users like you to help us improve your ad-blocking experience."}, "day1_community_description_2": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "What features would you like to see? What can we do better? Is Adblock Plus making some websites misbehave?"}, "day1_community_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Join the Adblock Plus community"}, "day1_header_more": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Learn more about malicious advertising"}, "day1_header_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#landing-page", "message": "Adblock Plus <strong>blocked <em0>$ads$ ads</em0> in <em1>the last half hour</em1>,</strong> some of which might have contained malicious content", "placeholders": {"ads": {"content": "$1", "example": "12,000"}}}, "notification_day1_message": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#push-notification", "message": "That's a lot of annoying ads. <a>Find out what else Adblock Plus can block.</a>"}, "notification_day1_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/d20f444263ae5cfbecda745449481687d183efce/spec/abp/notifications.md#push-notification", "message": "You've already blocked $ads$ ads!", "placeholders": {"ads": {"content": "$1", "example": "12,000"}}}, "options_aa_opt_out_survey": {"description": "https://gitlab.com/eyeo/specs/spec/blob/7daa26676865fc640146c8b5ad4146e6a17e171a/spec/abp/options-page.md#acceptable-ads-opt-out-survey", "message": "To help us improve Adblock Plus, mind sharing why you’ve turned off Acceptable Ads?"}, "options_aa_opt_out_survey_ok": {"description": "https://gitlab.com/eyeo/specs/spec/blob/7daa26676865fc640146c8b5ad4146e6a17e171a/spec/abp/options-page.md#acceptable-ads-opt-out-survey", "message": "Go to the survey"}, "options_aa_opt_out_survey_no": {"description": "https://gitlab.com/eyeo/specs/spec/blob/7daa26676865fc640146c8b5ad4146e6a17e171a/spec/abp/options-page.md#acceptable-ads-opt-out-survey", "message": "No, thanks"}, "options_page_document_title": {"description": "Options page title", "message": "Adblock Plus Options"}, "options_page_title": {"description": "Page title in navigation sidebar", "message": "Settings"}, "options_tab_general": {"description": "Sidebar tab label", "message": "General"}, "options_tab_whitelist": {"description": "Sidebar tab label and main heading inside corresponding tab", "message": "Whitelisted websites"}, "options_tab_advanced": {"description": "Sidebar tab label and main heading inside corresponding tab", "message": "Advanced"}, "options_tab_help": {"description": "Sidebar tab label", "message": "Help"}, "options_footer_contribute": {"description": "Sidebar button label", "message": "Contribute"}, "options_footer_about": {"description": "Sidebar button label", "message": "About Adblock Plus"}, "options_learn_more": {"description": "Text for `Learn more` link", "message": "Learn more"}, "options_control_remove_title": {"description": "Title of remove controls", "message": "remove"}, "options_general_description": {"description": "General tab description", "message": "Determine what Adblock Plus shows and hides on websites"}, "options_new_label": {"description": "Label of the new elements", "message": "New"}, "options_recommended_filters_header": {"description": "https://gitlab.com/eyeo/specs/spec/blob/52243bc8596b06107461034c745a6ecdaa42f1f6/spec/abp/desktop-settings/general-tab.md#recommended-filters-section", "message": "Recommended filters"}, "options_recommended_privacy_tooltip": {"description": "https://gitlab.com/eyeo/specs/spec/blob/52243bc8596b06107461034c745a6ecdaa42f1f6/spec/abp/desktop-settings/general-tab.md#list-of-recommended-filter-lists", "message": "Protect your privacy from known entities that may track your online activity across websites you visit."}, "options_recommended_social_tooltip": {"description": "https://gitlab.com/eyeo/specs/spec/blob/52243bc8596b06107461034c745a6ecdaa42f1f6/spec/abp/desktop-settings/general-tab.md#list-of-recommended-filter-lists", "message": "The social media icons on the websites you visit allow social media networks to build a profile of you based on your browsing habits - even when you don't click on them. Hiding these icons can protect your profile."}, "options_recommended_notifications_tooltip": {"description": "https://gitlab.com/eyeo/specs/spec/blob/52243bc8596b06107461034c745a6ecdaa42f1f6/spec/abp/desktop-settings/general-tab.md#list-of-recommended-filter-lists", "message": "Stop websites from asking you to allow push notifications that could track your online activity."}, "options_recommended_cookies_tooltip": {"description": "https://gitlab.com/eyeo/specs/spec/blob/52243bc8596b06107461034c745a6ecdaa42f1f6/spec/abp/desktop-settings/general-tab.md#list-of-recommended-filter-lists", "message": "By adding this filter you are waiving your right to be notified about cookies and edit cookie settings on individual websites. Please note that some websites may track you by default. More info here: <a0>https://adblockplus.org/block-cookie-warnings</a0>"}, "options_acceptableAds_header": {"description": "Section title in General tab", "message": "Acceptable Ads"}, "options_acceptableAds_description": {"description": "Acceptable Ads section description in General tab", "message": "Acceptable Ads are nonintrusive ads. They are the middle ground between ad blocking and supporting online content because they generate revenue for website owners."}, "options_tracking_warning_1": {"description": "Text of the tracking notification (being shown when both 'Acceptable Ads' and 'EasyPrivacy' are enabled) in General tab", "message": "We noticed you have both <strong>$tracking$</strong> and <strong>$acceptableAds$</strong> enabled.", "placeholders": {"acceptableAds": {"content": "$2", "example": "Allow Acceptable Ads"}, "tracking": {"content": "$1", "example": "Block additional tracking"}}}, "options_tracking_warning_2": {"description": "Text of the tracking notification (being shown when both 'Acceptable Ads' and 'EasyPrivacy' are enabled) in General tab", "message": "We want you to know that in order for advertisers to show you more relevant ads, there <strong>may</strong> be some tracking with Acceptable Ads."}, "options_tracking_warning_3": {"description": "Text of the tracking notification (being shown when both 'Acceptable Ads' and 'EasyPrivacy' are enabled) in General tab", "message": "If you prefer extra privacy, select the <strong>$acceptableAdsPrivacy$</strong> checkbox below.", "placeholders": {"acceptableAdsPrivacy": {"content": "$1", "example": "Only allow ads without third-party tracking"}}}, "options_tracking_warning_acknowledgment": {"description": "Text in the tracking notification (being shown when both 'Acceptable Ads' and 'EasyPrivacy' are enabled) in General tab", "message": "OK, got it"}, "options_acceptableAds_ads_label": {"description": "Acceptable Ads section option label in General tab", "message": "Allow Acceptable Ads"}, "options_acceptableAds_ads_description_1": {"description": "Acceptable Ads section 'Allow some nonintrusive ads' option description in General tab", "message": "Acceptable Ads are not annoying and do not interfere with the content you are viewing. <a>Read more about the Acceptable Ads criteria</a>"}, "options_acceptableAds_privacy_label": {"description": "Acceptable Ads section option label in General tab", "message": "Only allow ads without third-party tracking"}, "options_acceptableAds_dnt_notification": {"description": "Acceptable Ads section's notification (being shown if Do Not Track is not enabled) in General tab", "message": "<strong>Note:</strong> You have <strong>Do Not Track (DNT)</strong> disabled in your browser settings. For this feature to work properly, please enable <strong>DNT</strong> in your browser preferences. <a>Find out how to enable DNT</a>."}, "options_filters_search_or_add": {"description": "The filter search placeholder.", "message": "Search or add filter(s) (e.g. $filter$)", "placeholders": {"filter": {"content": "/ads/track/*"}}}, "options_language_header": {"description": "Section title in General tab", "message": "Language"}, "options_language_description": {"description": "Language section description in General tab", "message": "Optimize Adblock Plus for the language(s) you typically browse websites in."}, "options_language_empty": {"description": "Language section language list empty text in General tab", "message": "You don't have any language-specific filters."}, "options_language_change": {"description": "Language section language list single entry text in General tab", "message": "change"}, "options_language_tip": {"description": "Language section tip in General tab", "message": "<strong>TIP:</strong> Only select the languages you need. Selecting more will slow down the ad blocker and, therefore, your browsing speed."}, "options_language_join": {"description": "Combines language filter list specifications(language names) together, ex: -> https://gitlab.com/eyeo/specs/spec/blob/5a876713adebe79842be51ad6e287b1c675e023a/spec/abp/desktop-settings.md#language-drop-down", "message": "$lang1$, $lang2$", "placeholders": {"lang1": {"content": "$1", "example": "čeština"}, "lang2": {"content": "$2", "example": "slovenčina"}}}, "options_more_filters_header": {"description": "Section title in General tab", "message": "More filters"}, "options_more_filters_description": {"description": "More filters section description in General tab", "message": "These are additional filters you previously added to Adblock Plus."}, "options_more_filters_note": {"description": "More filters section note in General tab", "message": "<strong>Note:</strong> You should only use third party filter lists from authors that you trust."}, "options_english": {"description": "Language section text inside 'Language' table and 'Select language' dialog in General tab", "message": "English"}, "options_language_add": {"description": "Add other language button in General Tab", "message": "Add a language"}, "options_language_filter_list": {"description": "https://gitlab.com/eyeo/specs/spec/blob/dae74279dec45b839455626de2d0d5861d33b016/spec/abp/options-page.md#add-filter-list-modal-window", "message": "Language Filter Lists"}, "options_whitelist_description": {"description": "Whitelist tab description", "message": "You've turned off ad blocking on these websites and, therefore, will see ads on them."}, "options_whitelist_add": {"description": "Add whitelisted domain button", "message": "Add website"}, "options_whitelist_placeholder_example": {"description": "Empty Whitelist input placeholder prefix", "message": "e.g. $domain$", "placeholders": {"domain": {"content": "www.example.com"}}}, "options_whitelist_empty_1": {"description": "Empty Whitelist placeholder", "message": "You don't have any whitelisted websites."}, "options_whitelist_empty_2": {"description": "Empty Whitelist placeholder", "message": "Websites you trust and want to allow ads on will be shown here."}, "options_advanced_description": {"description": "Advanced tab description", "message": "Customize Adblock Plus, add or remove filter lists, create and maintain your own filter lists"}, "options_customize_header": {"description": "Section title in Advanced tab", "message": "Customizations"}, "options_customize_blockElement": {"description": "Option label of customize section in Advanced tab", "message": "Show 'Block element' right-click menu item"}, "options_customize_blockElement_tooltip": {"description": "Tooltip text for Show 'Block element' option item of customize section in Advanced tab", "message": "Temporarily block annoying items on a webpage, e.g. images or animated slideshows."}, "options_customize_elemhideDebug": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/62965fd7d57debe51636b9689aa09091bc9bb6e4/spec/abp/desktop-settings/advanced-tab.md#customize-section", "message": "Turn on debug element hiding filters mode"}, "options_customize_elemhideDebug_tooltip": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/62965fd7d57debe51636b9689aa09091bc9bb6e4/spec/abp/desktop-settings/advanced-tab.md#customize-section", "message": "Highlight elements on a page that are affected by element hiding filters."}, "options_customize_iconStats": {"description": "Option label of customize section in Advanced tab", "message": "Show number of ads blocked in icon"}, "options_customize_showDevToolsPanel": {"description": "Option label of customize section in Advanced tab", "message": "Show 'Adblock Plus' panel in developer tools"}, "options_customize_showDevToolsPanel_tooltip": {"description": "Tooltip text for Show 'Adblock Plus' panel option item of customize section in Advanced tab", "message": "View blocked and whitelisted items from your browser's developer tools panel."}, "options_customize_showNotifications": {"description": "Option label of customize section in Advanced tab", "message": "Show useful notifications"}, "options_customize_showNotifications_tooltip": {"description": "Tooltip text for Show notifications option item of customize section in Advanced tab", "message": "Allow notifications from Adblock Plus (notifications related to critical performance issues will always be shown)."}, "options_filterList_title": {"description": "Section title in Advanced tab", "message": "Filter lists"}, "options_filterList_description": {"description": "Filter lists section description in Advanced tab", "message": "Each Adblock Plus setting functions because of a filter list. Below are the filter lists that correspond to all of your Adblock Plus settings. You can also add additional filters created and maintained by our trusted community."}, "options_filterList_column_status": {"description": "Column name of filter list table in Advanced tab", "message": "Status"}, "options_filterList_column_name": {"description": "Column name of filter list table in Advanced tab", "message": "Filter lists"}, "options_filterList_column_update": {"description": "Column name of filter list table in Advanced tab", "message": "Last updated"}, "options_filterList_empty": {"description": "Empty text of filter list table in Advanced tab", "message": "You have not added any filter lists to Adblock Plus. Filter lists you add will be shown here."}, "options_filterList_now": {"description": "Text inside 'Last update' column in Advanced tab", "message": "Just now"}, "options_filterList_minutes": {"description": "Text inside 'Last update' column in Advanced tab", "message": "minutes ago"}, "options_filterList_hours": {"description": "Text inside 'Last update' column in Advanced tab", "message": "hours ago"}, "options_builtin_filterList_add": {"description": "https://gitlab.com/eyeo/specs/spec/blob/fdab34c8f8dbf0d134b9a848fa559c5001c13601/spec/abp/options-page.md#filter-list-buttons", "message": "Add built-in filter lists"}, "options_filterList_add": {"description": "https://gitlab.com/eyeo/specs/spec/blob/fdab34c8f8dbf0d134b9a848fa559c5001c13601/spec/abp/options-page.md#filter-list-buttons", "message": "Add filter list via URL"}, "options_filterList_update": {"description": "Update button in Advanced tab", "message": "update all filter lists"}, "options_customFilters_title": {"description": "Section title in Advanced tab", "message": "Your custom filters"}, "options_customFilters_description": {"description": "Custom filter lists section description in Advanced tab", "message": "Create and maintain your own filters to further control what content Adblock Plus allows or blocks. <a0>Learn how to write filters (English only)</a0>"}, "options_customFilters_widget_title": {"description": "Custom filter widget title in Advanced tab", "message": "My filter list"}, "options_filterList_lastDownload_invalidURL": {"description": "Error message in advanced tab", "message": "Not a valid address. Check the URL."}, "options_filterList_lastDownload_invalidURLProtocol": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3cc72ea11b4e085a1f791e25c92a8f8776be4dd1/spec/abp/options-page.md#filter-list-error-statuses", "message": "Download failed. URL must start with $protocol$.", "placeholders": {"protocol": {"content": "https://"}}}, "options_filterList_lastDownload_connectionError": {"description": "Error message in advanced tab", "message": "Download failure"}, "options_filterList_lastDownload_invalidData": {"description": "Error message in advanced tab", "message": "Not a valid filter list"}, "options_filterList_lastDownload_checksumMismatch": {"description": "Error message in advanced tab", "message": "Failed, checksum mismatch"}, "options_filterList_lastDownload_inProgress": {"description": "Progress message in advanced tab", "message": "Updating"}, "options_filterList_updateNow": {"description": "Context menu item in advanced tab, appears after click on subscription", "message": "Update now"}, "options_filterList_website": {"description": "Context menu item in advanced tab, appears after click on subscription", "message": "Website"}, "options_filterList_source": {"description": "Context menu item in advanced tab, appears after click on subscription", "message": "Source"}, "options_customFilter_cancel": {"description": "Label below raw view of user's filter list in Advanced tab", "message": "Cancel"}, "options_help_description": {"description": "Help tab description", "message": "Find help or get in touch with us"}, "options_support_title": {"description": "Section title in Help tab", "message": "Support"}, "options_help_center": {"message": "Looking for answers to your questions? <a0>Visit our Help Center (English only)</a0>"}, "options_report_bug": {"description": "'Support' section list item in Help tab", "message": "Found a bug? <a>Send us a bug report</a>"}, "options_report_forum": {"description": "'Support' section list item in Help tab", "message": "Want support from our community? <a>Go to the Forum</a>"}, "options_social_title": {"description": "'Section' title in Help tab", "message": "Get in touch"}, "options_social_description": {"description": "'Get in touch' section description in Help tab", "message": "Have a question or a new idea? We're here to help."}, "options_email": {"description": "Email label in Help tab", "message": "Email:"}, "options_dialog_about_title": {"description": "Title of about ABP dialog", "message": "About Adblock Plus"}, "options_dialog_about_version": {"description": "Version information in 'about Adblock Plus' modal dialog", "message": "Version number $version$", "placeholders": {"version": {"content": "$1", "example": "2.6.7"}}}, "options_dialog_about_copyright": {"description": "Copyright information in 'about Adblock Plus' modal dialog", "message": "Copyright © $currentYear$ <a0>eyeo GmbH</a0>.", "placeholders": {"currentYear": {"content": "$1", "example": "2017"}}}, "options_dialog_about_trademark": {"description": "Trademark information in 'about Adblock Plus' modal dialog", "message": "All rights reserved. Adblock Plus® is a registered trademark of eyeo GmbH."}, "options_dialog_invalid_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/0a80698daaa8097c0cfa5ba1b826bc61b00deace/spec/abp/options-page-advanced.md#adding-filter-lists-via-subscribe-links", "message": "Error occurred"}, "options_dialog_invalid_message": {"description": "https://gitlab.com/eyeo/specs/spec/blob/0a80698daaa8097c0cfa5ba1b826bc61b00deace/spec/abp/options-page-advanced.md#adding-filter-lists-via-subscribe-links", "message": "For security reasons, only <strong>HTTPS</strong> filter lists and <strong>data</strong> URLs can be added."}, "options_dialog_predefined_confirm": {"description": "https://gitlab.com/eyeo/specs/spec/blob/b386db03aad691f10240385e0ddb2d33c695f4d7/spec/abp/options-page-advanced.md#adding-filter-lists-via-subscribe-links", "message": "Yes, add this filter list"}, "options_dialog_predefined_subscription_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/b386db03aad691f10240385e0ddb2d33c695f4d7/spec/abp/options-page-advanced.md#adding-filter-lists-via-subscribe-links", "message": "Title"}, "options_dialog_predefined_subscription_url": {"description": "https://gitlab.com/eyeo/specs/spec/blob/b386db03aad691f10240385e0ddb2d33c695f4d7/spec/abp/options-page-advanced.md#adding-filter-lists-via-subscribe-links", "message": "URL"}, "options_dialog_predefined_title": {"description": "Dialog title for adding a predefined subscription", "message": "Are you sure you want to add this filter list?"}, "options_dialog_import_title": {"description": "Title of 'Add a filter list' dialog", "message": "Add a filter list"}, "options_close": {"description": "Close modal button", "message": "close"}, "options_dialog_import_subscription_location": {"description": "Input label in 'Add a filter list' dialog", "message": "Filter list URL"}, "options_dialog_import_subscription_location_error": {"description": "Error message of 'Filter list URL' input in 'Add a filter list' dialog", "message": "Enter a valid URL"}, "options_dialog_import_subscription_location_error_protocol": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3cc72ea11b4e085a1f791e25c92a8f8776be4dd1/spec/abp/options-page.md#add-custom-subscription-popup-invalid", "message": "URL must start with $protocol$.", "placeholders": {"protocol": {"content": "https://"}}}, "options_dialog_language_title": {"description": "Title of lanugage modal dialog", "message": "Select a language"}, "options_dialog_language_other_empty": {"description": "Text shown when list of available languages is empty", "message": "There are no available languages."}, "options_generic_error": {"description": "https://gitlab.com/eyeo/specs/spec/blob/40a632fc81fec3799018f5640cf7c8eac68df87f/spec/abp/options-page.md#error-state", "message": "Uh oh! Something went wrong. Please try again."}, "options_whitelist_notification": {"description": "Notification that is shown after a new website is whitelisted", "message": "Website has been whitelisted."}, "options_filter_list_rule": {"message": "Filter rule"}, "options_supportUs_description": {"description": "https://gitlab.com/eyeo/specs/spec/blob/677a0a3c68c7e96369301813d8b413c6ecd1b381/spec/abp/desktop-settings/index.md#store-rating", "message": "Please take a moment to rate ABP or donate to the project. Thanks for your support!"}, "options_supportUs_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/8f12811171d57b7499d50514283c6ec2d0247b05/spec/abp/options-page.md#store-rating", "message": "Enjoying Adblock Plus?"}, "options_rating_button": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/53d319c7512cf83e1c2dfd0517885e1e49adecd4/spec/abp/desktop-settings/index.md#store-rating", "message": "Rate us"}, "options_donate_button": {"description": "https://gitlab.com/eyeo/specs/spec/blob/677a0a3c68c7e96369301813d8b413c6ecd1b381/spec/abp/desktop-settings/index.md#store-rating", "message": "Donate"}, "devtools_action_block": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#block-item", "message": "Block item"}, "devtools_action_remove": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#remove-rule", "message": "Remove rule"}, "devtools_action_unblock": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#add-exception", "message": "Add exception"}, "devtools_filter_origin_custom": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#filter-column", "message": "user-defined"}, "devtools_filter_origin_none": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#filter-column", "message": "unnamed subscription"}, "devtools_filter_subtitle": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#filter-column", "message": "Origin"}, "devtools_filter_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#filter-column", "message": "Filter"}, "devtools_footer": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#footer", "message": "<a0>Reload</a0> page to see effect of filter changes"}, "devtools_header": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#header e.g. Show all items of any type; Show blocked items of IMAGE type", "message": "Show <slot0></slot0> items of <slot1></slot1> type"}, "devtools_header_state_all": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#status-dropdown", "message": "all"}, "devtools_header_state_blocked": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#status-dropdown", "message": "blocked"}, "devtools_header_state_whitelisted": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#status-dropdown", "message": "whitelisted"}, "devtools_header_type_any": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#content-type-dropdown", "message": "any"}, "devtools_request_subtitle": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#request-column", "message": "Document domain"}, "devtools_request_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#request-column", "message": "Request"}, "devtools_request_url": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#request-column", "message": "<a0>$original$</a0> rewritten to <a1>$rewritten$</a1>", "placeholders": {"original": {"content": "$1", "example": "http://example.com/foo"}, "rewritten": {"content": "$2", "example": "http://example.com/bar"}}}, "devtools_type_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/28aba213f76265253786a9ed64fdba1377644352/spec/abp/devtools-panel.md#type-column", "message": "Type"}, "filter_elemhideemulation_nodomain": {"message": "No active domain specified for extended element hiding filter"}, "filter_invalid_csp": {"message": "Invalid Content Security Policy"}, "filter_invalid_domain": {"message": "Invalid (or empty) domain specified"}, "filter_invalid_regexp": {"message": "Invalid regular expression"}, "filter_slow": {"description": "A slow filter highlighted through the warning icon", "message": "To ensure a fast filter, please check the length of the pattern and ensure it doesn't contain a regular expression."}, "filter_snippet_nodomain": {"message": "No active domain specified for snippet filter"}, "filter_unknown_option": {"message": "Unknown filter option"}, "line": {"description": "Error message with line number indicator", "message": "Line $number$: $message$", "placeholders": {"message": {"content": "$2", "example": "Some error on this line"}, "number": {"content": "$1", "example": "42"}}}, "unexpected_filter_list_header": {"description": "Error message shown when the user attempts to add a custom filter that has the format of a header (e.g. \"[Adblock Plus 2.0]\") which are only allowed in filter lists", "message": "Filter list headers aren't allowed here"}, "firstRun_browser_description": {"message": "From the team behind Adblock Plus, the most popular ad blocker for desktop browsers, Adblock Browser is now available for your Android and iOS devices."}, "firstRun_browser_store_android": {"message": "Android app on Google Play"}, "firstRun_browser_store_ios": {"message": "Available on the iTunes App Store"}, "firstRun_browser_title": {"message": "Adblock Browser app"}, "firstRun_control_description": {"message": "By default, you may see some nonintrusive ads that adhere to <a0>strict criteria</a0>. We identify these ads as Acceptable Ads. Prefer to block all ads? <a1>Turn off Acceptable Ads</a1> in your <a2>Settings</a2>."}, "firstRun_control_title": {"message": "You're in control"}, "firstRun_donate": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/d8fb4d685ae8b6f264558e506c02f8b89d3a1612/spec/abp/first-run.md#header", "message": "Donate"}, "firstRun_fair_description": {"message": "We encourage you to make fair use of Adblock Plus and the option to whitelist websites. Depending on the filters you use, you understand and accept that unintentional results might occur (e.g. blocked content on a website). Refer to the <a0>Terms of Use</a0> for more information about fair use of Adblock Plus."}, "firstRun_fair_title": {"message": "Fair use"}, "firstRun_subtitle": {"message": "You just took control of your browser."}, "firstRun_title": {"message": "Installation Successful!"}, "firstRun_warning_dataCorrupted_intro": {"description": "Text shown at beginning of warning being shown when data corruption was detected", "message": "Some users are experiencing an issue where this page opens each time their browser is launched. This issue can be resolved by uninstalling and reinstalling Adblock Plus:"}, "firstRun_warning_dataCorrupted_note": {"description": "Footnote at the end of warning being shown when data corruption was detected", "message": "If you still need help, contact us at <a0><EMAIL></a0> or visit our <a1>Help Center</a1>."}, "firstRun_warning_dataCorrupted_step1": {"description": "First step of instructions in warning being shown when data corruption was detected", "message": "Open your browser's Extensions tab."}, "firstRun_warning_dataCorrupted_step2": {"description": "Second step of instructions in warning being shown when data corruption was detected", "message": "Locate Adblock Plus and remove it."}, "firstRun_warning_dataCorrupted_step3": {"description": "Third step of instructions in warning being shown when data corruption was detected", "message": "Reinstall Adblock Plus from <a>adblockplus.org</a>."}, "firstRun_warning_reinitialized": {"message": "It seems that an issue caused all filters to be removed and we were unable to restore a backup. Therefore we had to reset your filters and Acceptable Ads settings. Please check your filter lists and Acceptable Ads settings in the <a>Adblock Plus options</a>."}, "continue": {"description": "Continue button label", "message": "Continue"}, "filters_subscription_lastDownload_connectionError": {"message": "Failed, download failure"}, "issueReporter_anonymousSubmission_label": {"message": "Anonymous submission"}, "issueReporter_anonymousSubmission_warning": {"message": "We won't be able to come back to you and will likely prioritize the report lower."}, "issueReporter_commentPage_heading": {"message": "Leave a comment"}, "issueReporter_comment_description": {"message": "Please help us better understand the issue by leaving a comment below."}, "issueReporter_comment_label": {"message": "Comment (optional):"}, "issueReporter_config_label": {"message": "Include information about my browser configuration, as well as a list of active extensions I'm using."}, "issueReporter_confirmationMessage": {"message": "Your report has been saved. You can access it at the following address:"}, "issueReporter_data_gathering_tab": {"message": "<strong>Note:</strong> An additional tab will temporarily open so the page you are on won't be affected by the Issue Reporter."}, "issueReporter_detailsButton_label": {"message": "Details"}, "issueReporter_closeButton_label": {"message": "Close"}, "issueReporter_doneButton_label": {"message": "Done"}, "issueReporter_email_description": {"message": "It's helpful to enter a valid email address. This allows us to contact you to fix difficult issues like malware. Keep in mind that it's not mandatory to enter an email address, and your address will never be shared."}, "issueReporter_email_label": {"message": "Email:"}, "issueReporter_errorMessage": {"message": "An attempt to send the report failed with error code \"?1?\". Please ensure you are connected to the Internet and retry. If the problem persists please request assistance in the [link]Adblock Plus forum[/link]."}, "issueReporter_falseNegative_description": {"message": "Adblock Plus is enabled, but I still see an ad or ads."}, "issueReporter_falseNegative_label": {"message": "I still see ads"}, "issueReporter_falsePositive_description": {"message": "The page displays incorrectly, fails to function or seems to be missing important content."}, "issueReporter_falsePositive_label": {"message": "The page I'm trying to view is broken"}, "issueReporter_highlighter_description": {"message": "Use the tools to highlight each ad or hide sensitive info on the screenshot. Highlighting ads will help us fix the issue faster."}, "issueReporter_highlighter_heading": {"message": "Highlight issue"}, "issueReporter_knownIssueMessage": {"message": "The issue you reported is probably already known. More information:"}, "issueReporter_markIssueButton_label": {"message": "Mark issue"}, "issueReporter_other_issues": {"description": "https://gitlab.com/eyeo/specs/spec/blob/68b2ce406fde75fb849d8f2732b19b1e4105ce3d/spec/abp/issue-reporter.md#issue-type", "message": "For all other issues, please contact us via <a0><EMAIL></a0>."}, "issueReporter_page_title": {"message": "Issue reporter"}, "issueReporter_privacyPolicy": {"message": "Privacy Policy"}, "issueReporter_processing_screenshot": {"message": "processing"}, "issueReporter_screenreader_warning": {"message": "To help us better understand the issue, a screenshot is required when submitting an issue report. Depending on the website you're currently on, the screenshot might contain personal information. If you're uncomfortable submitting a screenshot, please email your issue to <a0><EMAIL></a0>."}, "issueReporter_screenshot_hide": {"message": "<PERSON>de"}, "issueReporter_screenshot_highlight": {"message": "Highlight"}, "issueReporter_selectIssueButton_label": {"message": "Select issue"}, "issueReporter_sendButton_label": {"message": "Send report"}, "issueReporter_sendPage_heading": {"message": "Send report"}, "issueReporter_sending": {"message": "Please wait while Adblock Plus is submitting your report."}, "issueReporter_showData_label": {"message": "Click this link to review the report data before sending"}, "issueReporter_typeSelector_description": {"message": "Please select an issue:"}, "issueReporter_typeSelector_heading": {"message": "What type of issue are you experiencing?"}, "description": {"description": "https://gitlab.com/eyeo/adblockplus/webstores/blob/d7acfa77af92c53bce1aa5e243bd81768e004e01/spec/chrome-web-store/listing-en_US.md#summary, Note: this cannot be longer than 132 characters", "message": "Block YouTube™ ads, pop-ups & fight malware!"}, "name": {"message": "Adblock Plus"}, "name_releasebuild": {"description": "Extension name to be shown for release builds. Note: this cannot be longer than 45 characters.", "message": "Adblock Plus - free ad blocker"}, "name_devbuild": {"description": "Extension name to be shown for development builds. Note: this cannot be longer than 45 characters.", "message": "Adblock Plus development build"}, "notification_configure": {"message": "Configure notification settings"}, "notification_open_all": {"message": "Open all links from this notification"}, "notification_without_buttons": {"message": "Click on the notification to open all links in it."}, "status_header": {"description": "https://gitlab.com/eyeo/specs/spec/blob/241afce65629c742cfdf6456c06dfcb12f569163/spec/abp/popup.md#toggle", "message": "Block ads on"}, "status_domain": {"description": "https://gitlab.com/eyeo/specs/spec/blob/241afce65629c742cfdf6456c06dfcb12f569163/spec/abp/popup.md#toggle", "message": "This website:"}, "status_page": {"description": "https://gitlab.com/eyeo/specs/spec/blob/241afce65629c742cfdf6456c06dfcb12f569163/spec/abp/popup.md#toggle", "message": "This page:"}, "block_element_description": {"description": "https://gitlab.com/eyeo/specs/spec/blob/5a876713adebe79842be51ad6e287b1c675e023a/spec/abp/popup.md#block-element", "message": "Block specific element on this website"}, "click_element_to_block_it": {"description": "https://gitlab.com/eyeo/specs/spec/blob/5a876713adebe79842be51ad6e287b1c675e023a/spec/abp/popup.md#block-element", "message": "Click an element on the page to block it."}, "easy_create_filter": {"description": "Action in popup UI to add a filter which blocks an element selected from the current page", "message": "Block element"}, "options_refresh": {"description": "refresh text in the main toggle", "message": "Refresh"}, "options_short": {"description": "Link to the options page from the popup balloon.", "message": "Options"}, "overlay_notification_closing_button_hide": {"description": "https://gitlab.com/eyeo/specs/spec/blob/5a876713adebe79842be51ad6e287b1c675e023a/spec/abp/popup.md#notifications", "message": "Close"}, "overlay_notification_closing_button_optout": {"description": "https://gitlab.com/eyeo/specs/spec/blob/5a876713adebe79842be51ad6e287b1c675e023a/spec/abp/popup.md#notifications", "message": "Stop showing notifications"}, "promote_abp_module": {"description": "https://gitlab.com/eyeo/specs/spec/blob/241afce65629c742cfdf6456c06dfcb12f569163/spec/abp/popup.md#index", "message": "Interested in Adblock Plus on mobile?"}, "refresh_this_page": {"description": "https://gitlab.com/eyeo/specs/spec/blob/6f8028d692da33978069972d90417dcd10957f2b/spec/abp/bubble-ui.md#refresh", "message": "Refresh this page"}, "refresh_to_take_effect": {"description": "https://gitlab.com/eyeo/specs/spec/blob/6f8028d692da33978069972d90417dcd10957f2b/spec/abp/bubble-ui.md#refresh", "message": "Click the button below for changes to take effect."}, "social_media_share": {"description": "https://gitlab.com/eyeo/specs/spec/blob/36533b29e70499ed5521564d2330e51392cfb2e9/spec/abp/popup.md#counter-panel", "message": "Share numbers with friends"}, "share_on_facebook": {"description": "https://gitlab.com/eyeo/specs/spec/blob/36533b29e70499ed5521564d2330e51392cfb2e9/spec/abp/popup.md#counter-panel", "message": "Share on Facebook"}, "share_on_twitter": {"description": "https://gitlab.com/eyeo/specs/spec/blob/36533b29e70499ed5521564d2330e51392cfb2e9/spec/abp/popup.md#counter-panel", "message": "Share on Twitter"}, "share_on_weibo": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/a70522b79b1196b7966a0d2f824cb0a525ac4160/spec/abp/popup.md#counter-panel", "message": "Share on Weibo"}, "share_on_twitter_message": {"description": "https://gitlab.com/eyeo/specs/spec/blob/1e5b1524ef5d69849415054a25d100e6bc5a9468/spec/abp/popup.md#counter-panel", "message": "I've blocked $number$ ads with Adblock Plus. Give it a try - it's FREE!", "placeholders": {"number": {"content": "$1"}}}, "share_on_weibo_message": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/a70522b79b1196b7966a0d2f824cb0a525ac4160/spec/abp/popup.md#counter-panel", "message": "I've blocked $number$ ads with Adblock Plus. Give it a try - it's FREE!", "placeholders": {"number": {"content": "$1"}}}, "stats_header": {"description": "Stats description above the page / total stats", "message": "Number of items blocked"}, "stats_label_page": {"description": "https://gitlab.com/eyeo/specs/spec/blob/1e5b1524ef5d69849415054a25d100e6bc5a9468/spec/abp/popup.md#counter-panel", "message": "on this page"}, "stats_label_total": {"description": "https://gitlab.com/eyeo/specs/spec/blob/1e5b1524ef5d69849415054a25d100e6bc5a9468/spec/abp/popup.md#counter-panel", "message": "in total"}, "sendReport": {"description": "https://gitlab.com/eyeo/specs/spec/blob/5a876713adebe79842be51ad6e287b1c675e023a/spec/abp/popup.md#block-element-and-report-issue", "message": "Report an issue on this page"}, "notification_problem_message": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "An issue has caused your ABP settings to be reset to default. <a>Fix the issue and learn more</a>"}, "problem_note": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Note that depending on how you use ABP, you may need to re-configure your settings, custom filters, and whitelisted websites once you reinstall Adblock Plus."}, "problem_os_mac": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "<PERSON>"}, "problem_os_windows": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Windows"}, "problem_social_description": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Experiencing an issue? Contact our support team"}, "problem_social_email": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Email"}, "problem_social_facebook": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Facebook"}, "problem_social_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Still looking for help?"}, "problem_social_twitter": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Twitter"}, "problem_solution_action": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Click here to uninstall and reinstall Adblock Plus"}, "problem_solution_opera_step_1": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Open this link to Opera Addons (it will open in a new tab)"}, "problem_solution_opera_step_2": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "From the Opera toolbar, <em0>right-click</em0> / <em1>control-click</em1> the <strong>Adblock Plus</strong> icon and select <strong>Manage extension</strong>."}, "problem_solution_opera_step_3": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Click the <strong>arrow</strong> next to the Adblock Plus icon."}, "problem_solution_opera_step_4": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Locate Adblock Plus and click the <strong>X</strong> in the upper-right corner."}, "problem_solution_opera_step_5": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Click <strong>Re<PERSON>ve</strong>."}, "problem_solution_opera_step_6": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Refresh the Opera Addons page."}, "problem_solution_opera_step_7": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Click <strong>Add to Opera</strong>."}, "problem_solution_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "The solution:"}, "problem_subtitle": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Because of this, your ABP settings have been reset to default. To resolve the issue, you'll need to uninstall and reinstall Adblock Plus. Click the link below to do this."}, "problem_subtitle_opera": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "Because of this, your ABP settings have been reset to default. To resolve the issue, you'll need to uninstall and reinstall Adblock Plus. Follow the steps below to do this."}, "problem_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/a8e03839c92b789dff36b84c520a57b0eceefb44/spec/abp/notifications.md#warning-data-corruption-and-filters-reset", "message": "A browser issue has caused your ABP settings to be reset."}, "notification_updates_message": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/a4651168848d564bfb9e59ecac1ed7432579d1b1/spec/abp/updates.md#notification-process", "message": "No action is required—continue browsing the web without annoying ads! <a>See what's new</a>"}, "notification_updates_title": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/a4651168848d564bfb9e59ecac1ed7432579d1b1/spec/abp/updates.md#notification-process", "message": "Adblock Plus is up-to-date"}, "updates_subtitle": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#header-content", "message": "We think you'll like the latest updates."}, "updates_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#header-content", "message": "Adblock Plus is up-to-date!"}, "updates_update_i1_description": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/2a69300851f45962e505eb4836e7544b3093d5d0/spec/abp/updates.md#current-updates", "message": "You will see a new section when you open your Settings called <strong>Recommended Filters</strong>. Here you can easily turn on/off recommended filters with a simple click and enhance your web experience."}, "updates_update_i1_title": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/2a69300851f45962e505eb4836e7544b3093d5d0/spec/abp/updates.md#current-updates", "message": "Added new recommended filter lists for easy access"}, "updates_update_i1_image": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/2a69300851f45962e505eb4836e7544b3093d5d0/spec/abp/updates.md#current-updates", "message": "\"Block push notifications\" and \"Block cookie warnings\" filter lists highlighted in \"RECOMMENDED FILTERS\" section of options page"}, "updates_update_i3_description": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/2a69300851f45962e505eb4836e7544b3093d5d0/spec/abp/updates.md#current-updates", "message": "For our more advanced users, we have a new debug-element hiding filters mode that can be turned on in the advanced settings. Elements will then be highlighted for easy debugging."}, "updates_update_i3_title": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/2a69300851f45962e505eb4836e7544b3093d5d0/spec/abp/updates.md#current-updates", "message": "Added debug element-hiding filters mode"}, "updates_contribute_donate_action": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Contribute"}, "updates_contribute_donate_subtitle": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Your support allows us to continue to improve Adblock Plus."}, "updates_contribute_donate_title": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Contribute to the ABP project"}, "updates_contribute_rate_action": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Rate it"}, "updates_contribute_rate_subtitle": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Please take a moment and help spread the word by rating Adblock Plus. It's quick and free!"}, "updates_contribute_rate_title": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Rate it!"}, "updates_contribute_subtitle": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Consider supporting ABP in one of two ways:"}, "updates_contribute_title": {"description": "https://gitlab.com/eyeo/specs/spec/-/blob/019381532ccd93cbba8732de1b90a1ce7fdb7e4a/spec/abp/updates.md#contribute", "message": "Enjoying Adblock Plus?"}, "updates_fixes_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#update-information-content", "message": "Fixes"}, "updates_improvements_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#update-information-content", "message": "Improvements"}, "updates_link": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#update-information-content", "message": "Read more"}, "updates_social_description": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#contact-us-area", "message": "Share your thoughts on the latest ABP update."}, "updates_social_email": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#contact-us-area", "message": "Email"}, "updates_social_facebook": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#contact-us-area", "message": "Facebook"}, "updates_social_twitter": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#contact-us-area", "message": "Twitter"}, "updates_social_title": {"description": "https://gitlab.com/eyeo/specs/spec/blob/3488ef98776e3109710026c8d9e945e67d7bc3ec/spec/abp/updates.md#contact-us-area", "message": "We want to hear from you!"}}