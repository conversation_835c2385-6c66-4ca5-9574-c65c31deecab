<!DOCTYPE html>
<!--
  - This file is part of Adblock Plus <https://adblockplus.org/>,
  - Copyright (C) 2006-present eyeo GmbH
  -
  - Adblock Plus is free software: you can redistribute it and/or modify
  - it under the terms of the GNU General Public License version 3 as
  - published by the Free Software Foundation.
  -
  - Adblock Plus is distributed in the hope that it will be useful,
  - but WITHOUT ANY WARRANTY; without even the implied warranty of
  - MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  - GNU General Public License for more details.
  -
  - You should have received a copy of the GNU General Public License
  - along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
-->
<html>
  <head>
    <title>Adblock Plus</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="skin/fonts/font.css">
    <link rel="stylesheet" href="skin/popup.css">
    <link rel="stylesheet" href="skin/popup-dummy.css">
    <script src="polyfill.js"></script>
    <script src="ext/common.js"></script>
    <script src="ext/content.js"></script>
    <script src="i18n.js"></script>
    <script src="popup-dummy.js" defer></script>
  </head>
  <body>

    <header>
      <img src="skin/icons/logo/abp-full.svg">
      <button id="options"></button>
    </header>
    <main>
      <div id="page-status">
        <h2 data-i18n="status_header"></h2>
        <div class="domain">
          <div class="details">
            <h3 data-i18n="status_domain"></h3>
            <p id="blocking-domain">example.com</p>
          </div>
          <io-circle-toggle checked></io-circle-toggle>
        </div>
        <div class="page">
          <div class="details">
            <h3 data-i18n="status_page"></h3>
            <p id="blocking-page"></p>
          </div>
          <io-circle-toggle checked></io-circle-toggle>
        </div>
      </div>
      <div id="page-info">
        <div id="default-container">
          <h2 data-i18n="stats_header"></h2>
          <div id="counter-panel" class="card">
            <div class="stats">
              <div id="stats-page">
                <span data-i18n="stats_label_page"></span>
                <strong class="amount"></strong>
              </div>
              <div id="stats-total">
                <span data-i18n="stats_label_total"></span>
                <strong class="amount"></strong>
              </div>
            </div>
            <div class="share">
              <div class="stack">
                <button class="enter">
                  <img src="skin/icons/share.svg" alt="">
                  <span data-i18n="social_media_share"></span>
                </button>
              </div>
              <div class="stack links">
                <span class="link">
                  <a class="facebook" tabindex="-1" href="#">
                    <img i18n-alt="share_on_facebook" src="skin/icons/share-on-facebook.svg" height="16">
                  </a>
                </span>
                <span class="link">
                  <a class="twitter" tabindex="-1" href="#">
                    <img i18n-alt="share_on_twitter" src="skin/icons/share-on-twitter.svg" height="14">
                  </a>
                </span>
                <button class="cancel" tabindex="-1" data-i18n="cancel"></button>
              </div>
            </div>
          </div>
          <div class="options">
            <button id="block-element">
              <strong data-i18n="easy_create_filter" class="block background-icon"></strong>
              <span data-i18n="block_element_description" class="block"></span>
            </button>
            <button id="issue-reporter">
              <span data-i18n="sendReport" class="report background-icon"></span>
            </button>
          </div>
        </div>
        <div id="block-element-container">
          <div id="block-element-info" class="card">
            <strong data-i18n="easy_create_filter"></strong>
            <span data-i18n="click_element_to_block_it"></span>
            <div class="options">
              <button id="block-element-cancel">
                <span data-i18n="cancel" class="block background-icon"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div id="page-refresh">
        <div class="card">
          <strong data-i18n="refresh_this_page"></strong>
          <span data-i18n="refresh_to_take_effect"></span>
          <button data-i18n="options_refresh"></button>
        </div>
      </div>
    </main>
    <footer>
      <span data-i18n="promote_abp_module"></span>
      <button class="store apple"></button>
      <button class="store android"></button>
    </footer>

    <div class="overlay"></div>

  </body>
</html>
