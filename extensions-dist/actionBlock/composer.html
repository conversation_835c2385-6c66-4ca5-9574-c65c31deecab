<!DOCTYPE html>
<!--
  - This file is part of Adblock Plus <https://adblockplus.org/>,
  - Copyright (C) 2006-present eyeo GmbH
  -
  - Adblock Plus is free software: you can redistribute it and/or modify
  - it under the terms of the GNU General Public License version 3 as
  - published by the Free Software Foundation.
  -
  - Adblock Plus is distributed in the hope that it will be useful,
  - but WITHOUT ANY WARRANTY; without even the implied warranty of
  - MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  - GNU General Public License for more details.
  -
  - You should have received a copy of the GNU General Public License
  - along with Adblock Plus.  If not, see <http://www.gnu.org/licenses/>.
  -->

<html>
  <head>
    <meta charset="utf-8">
    <title>ActionBlock采集器</title>
    <link rel="stylesheet" href="skin/fonts/font.css">
    <link rel="stylesheet" type="text/css" href="skin/composer.css">
    <script src="polyfill.js"></script>
    <script src="ext/common.js"></script>
    <script src="ext/content.js"></script>
    <script src="common.js"></script>
    <script src="i18n.js"></script>
    <script src="composer.js"></script>
  </head>
  <body>
    <main id="selector-form">
      <div id="modal-selector">
        <div class="radio-row">
          <input id="modal-element" type="radio" name="modal" value="element" checked>
          <label for="modal-element" class="i18n-block-node">屏蔽当前选中的节点</label>
        </div>
        <textarea id="filters" data-i18n="composer_loading"
                  autocomplete="off" autocorrect="off" autocapitalize="off"
                  spellcheck="false" disabled></textarea>
        <div class="radio-row" style="margin-top: 10px">
          <input id="modal-page" type="radio" name="modal" value="page">
          <label for="modal-page" class="i18n-block-page">屏蔽整个页面</label>
        </div>
      </div>
      <div class="spacer">
        <div class="details" aria-hidden="true">
          <span id="selected" data-count="0" data-i18n="composer_elements" aria-hidden="true"></span>
          <span aria-hidden="true">•</span>
          <button id="unselect" data-i18n="composer_unselect"></button>
        </div>
        <div id="preview" data-preview="inactive" disabled class="i18n-preview">预览</div>
        <div id="block" disabled class="i18n-ok">确定</div>
        <div id="cancel" class="i18n-cancel">取消</div>
      </div>
    </main>
    <div id="comment-form" class="dm-hidden">
      <div><img src="skin/icons/success.svg"><span class="i18n-comment-title">可填写备注以供备忘：</span></div>
      <textarea id="filters-comment" class="i18n-comment-placeholder" placeholder="不超过128个字符"
                autocomplete="off" autocorrect="off" autocapitalize="off"
                spellcheck="false"></textarea>
      <div class="btn-area">
        <div id="submit" class="i18n-finish">完成</div>
        <div id="close" class="i18n-discard">放弃</div>
      </div>
    </div>
  </body>
</html>
