# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/dist
/.vscode
/electron-dist

# misc
.DS_Store
npm-debug.log*
yarn-error.log

/coverage
.idea
package-lock.json
*bak
.vscode
# visual studio code
.history
*.log
functions/*
.temp/**

# umi
.umi
.umi-production

# screenshot
screenshot
.firebase
.eslintcache

build
electron/*.js
electron/assets
!electron/build
!electron/scripts
!oem/**/*
browserPage
/tmp/
/.run/

# mock config
config/mockConfig.ts
# 构建生成的js不提交
/electron/extensions/recorder/background.js
/electron/recorder/
/electron/helper/
/electron/logs/
/electron/shop-data/
/electron/extensions/
/extensions-dist/*
!extensions-dist/actionBlock
/Portalstatics.iml
/.yarn/
.yarn
/extra/
/selfIdentity.plist
