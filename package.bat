node -v

set RELEASE_APP_VERSION=%RELEASE_APP_MAIN_VERSION%.%BUILD_NUMBER%
set PLATFORM=Win10
set PLATFORM_Win10=%PLATFORM_Win10%
set PLATFORM_Win7=%PLATFORM_Win7%
set MODE_normal=%MODE_normal%
set MODE_runtime=%MODE_runtime%
set LANG_zh=%LANG_zh%
set LANG_en=%LANG_en%
set CHROMIUM_URL=%CHROMIUM_URL%
set BROWSER_BUILD_VERSION=%BROWSER_BUILD_VERSION%
set PORTAL_URL=%PORTAL_URL%
set API_URL=%API_URL%
set PORTAL_URL2=%PORTAL_URL2%
set API_URL2=%API_URL2%
set SIGN_URL=%SIGN_URL%
set BUILD_NUMBER=%BUILD_NUMBER%
set QUICK_MODE=false
set ELECTRON_MIRROR=https://registry.npmmirror.com/electron/
set ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/
set APPIUM_SKIP_CHROMEDRIVER_INSTALL=1
set OEM_NAME=%OEM_NAME%

call yarn config set registry https://registry.npmmirror.com -g
call yarn config get registry
call yarn electron:repairPackageJson
call yarn install --network-timeout 1000000000
call npm run postinstall
call yarn electron:build
